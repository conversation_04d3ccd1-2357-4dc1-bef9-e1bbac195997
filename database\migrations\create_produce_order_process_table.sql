-- 创建生产订单工序实例表
-- 用于存储每个生产订单的独立工序信息，实现工序差异化定制

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for oa_produce_order_process
-- ----------------------------
DROP TABLE IF EXISTS `oa_produce_order_process`;
CREATE TABLE `oa_produce_order_process` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `step_no` int(11) NOT NULL COMMENT '工序序号',
  `process_code` varchar(50) NOT NULL DEFAULT '' COMMENT '工序编码',
  `process_name` varchar(100) NOT NULL DEFAULT '' COMMENT '工序名称',
  `process_type` varchar(50) DEFAULT '数据记录' COMMENT '工序类型：数据记录/状态记录',
  `standard_time` decimal(10,2) DEFAULT 0.00 COMMENT '标准工时(小时)',
  `standard_price` decimal(10,2) DEFAULT 0.00 COMMENT '标准单价',
  `efficiency` decimal(10,2) DEFAULT 0.00 COMMENT '标准效率(件/小时)',
  `description` text COMMENT '工序描述',
  `quality_standard` text COMMENT '质量标准',
  `equipment_required` varchar(255) DEFAULT '' COMMENT '所需设备',
  `skill_required` varchar(255) DEFAULT '' COMMENT '技能要求',
  `is_outsourced` tinyint(1) DEFAULT 0 COMMENT '是否外协：0=自制，1=外协',
  `processing_type` varchar(50) DEFAULT '自制' COMMENT '加工类型：自制/外协',
  `inspection_method` varchar(50) DEFAULT '免检' COMMENT '检验方式：免检/抽检/全检',
  `time_unit` varchar(20) DEFAULT '小时' COMMENT '时间单位：小时/天/分钟',
  `completion_time` decimal(10,2) DEFAULT 0.00 COMMENT '完成所需时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0=未开始，1=进行中，2=已完成，3=暂停',
  `actual_start_time` int(11) DEFAULT 0 COMMENT '实际开始时间',
  `actual_end_time` int(11) DEFAULT 0 COMMENT '实际结束时间',
  `planned_start_time` int(11) DEFAULT 0 COMMENT '计划开始时间',
  `planned_end_time` int(11) DEFAULT 0 COMMENT '计划结束时间',
  `completed_qty` int(11) DEFAULT 0 COMMENT '已完成数量',
  `qualified_qty` int(11) DEFAULT 0 COMMENT '合格数量',
  `unqualified_qty` int(11) DEFAULT 0 COMMENT '不合格数量',
  `actual_work_time` decimal(10,2) DEFAULT 0.00 COMMENT '实际工时(小时)',
  `worker_ids` varchar(255) DEFAULT '' COMMENT '负责工人ID列表(逗号分隔)',
  `worker_names` varchar(255) DEFAULT '' COMMENT '负责工人姓名列表(逗号分隔)',
  `notes` text COMMENT '备注信息',
  `is_key_process` tinyint(1) DEFAULT 0 COMMENT '是否关键工序：0=否，1=是',
  `predecessor_step` varchar(100) DEFAULT '' COMMENT '前置工序(逗号分隔)',
  `successor_step` varchar(100) DEFAULT '' COMMENT '后续工序(逗号分隔)',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_step` (`order_id`, `step_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_step_no` (`step_no`),
  KEY `idx_process_code` (`process_code`),
  KEY `idx_status` (`status`),
  KEY `idx_is_key_process` (`is_key_process`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单工序实例表';

-- ----------------------------
-- 为生产订单表添加工序相关字段（如果不存在）
-- ----------------------------
-- 检查并添加字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'oa_produce_order' 
     AND COLUMN_NAME = 'total_processes') = 0,
    'ALTER TABLE `oa_produce_order` ADD COLUMN `total_processes` int(11) DEFAULT 0 COMMENT ''总工序数量'';',
    'SELECT ''Column total_processes already exists'';'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'oa_produce_order' 
     AND COLUMN_NAME = 'completed_processes') = 0,
    'ALTER TABLE `oa_produce_order` ADD COLUMN `completed_processes` int(11) DEFAULT 0 COMMENT ''已完成工序数量'';',
    'SELECT ''Column completed_processes already exists'';'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ----------------------------
-- 创建索引
-- ----------------------------
-- 为生产订单表添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'oa_produce_order' 
     AND INDEX_NAME = 'idx_total_processes') = 0,
    'ALTER TABLE `oa_produce_order` ADD KEY `idx_total_processes` (`total_processes`);',
    'SELECT ''Index idx_total_processes already exists'';'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'oa_produce_order' 
     AND INDEX_NAME = 'idx_completed_processes') = 0,
    'ALTER TABLE `oa_produce_order` ADD KEY `idx_completed_processes` (`completed_processes`);',
    'SELECT ''Index idx_completed_processes already exists'';'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- 插入示例数据（可选）
-- ----------------------------
-- 这里可以根据需要插入一些示例工序数据
-- INSERT INTO `oa_produce_order_process` (...) VALUES (...);
