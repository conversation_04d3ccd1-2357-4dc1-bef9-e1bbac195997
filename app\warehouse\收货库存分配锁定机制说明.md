# 收货库存分配锁定机制说明

## 概述
系统已经实现了完整的收货后自动库存分配和锁定机制。当收货单提交入库后，系统会自动触发库存分配，为等待的需求（销售订单、生产订单等）分配和锁定库存。

## 核心流程

### 1. 收货入库流程
```
收货单提交 → 更新库存 → 更新订单状态 → 触发自动分配 → 通知相关业务
```

### 2. 自动分配触发点
在`app/warehouse/controller/Receipt.php`的`submit`方法中：
```php
// 入库完成后，触发自动分配
$this->triggerAutoAllocation($receipt);
```

## 分配机制详解

### 1. 分配服务类
**主要服务**：`app\warehouse\service\InventoryAllocationService`

**核心方法**：
- `autoAllocateOnInbound()` - 入库时自动分配
- `allocateAndLock()` - 统一分配和锁定接口
- `releaseAndRedistribute()` - 释放锁定并重新分配

### 2. 分配逻辑
```php
public function autoAllocateOnInbound($productId, $warehouseId, $inboundQty)
{
    // 1. 获取所有待分配的需求（按优先级排序）
    $pendingRequests = $this->getPendingAllocationRequests($productId, $warehouseId);
    
    // 2. 按优先级逐个分配并锁定
    foreach ($pendingRequests as $request) {
        $allocateQty = min($remainingQty, $pendingQty);
        $result = $this->executeAllocation($request, $allocateQty);
        $remainingQty -= $allocateQty;
    }
}
```

### 3. 优先级规则
```php
private function getDefaultPriority($refType)
{
    $priorities = [
        'customer_order' => 1,      // 销售订单 - 最高优先级
        'production_order' => 2,    // 生产订单 - 中等优先级
        'transfer_order' => 3,      // 调拨订单 - 较低优先级
        'other' => 9               // 其他 - 最低优先级
    ];
    return $priorities[$refType] ?? 9;
}
```

## 锁定机制

### 1. 锁定服务类
**服务类**：`app\warehouse\service\InventoryLockServiceNew`

**核心功能**：
- 检查库存充足性
- 锁定实时库存
- 创建锁定记录
- 释放锁定库存

### 2. 锁定流程
```php
public function lockInventory($params)
{
    // 1. 检查库存是否充足
    if (!InventoryRealtime::hasEnoughStock($productId, $warehouseId, $quantity)) {
        throw new Exception('库存不足，无法锁定');
    }
    
    // 2. 锁定实时库存
    $inventoryService->lockStock($productId, $warehouseId, $quantity, ...);
    
    // 3. 创建锁定记录
    $lockRecord = InventoryLock::createLock([...]);
}
```

## 分配策略

### 1. FIFO策略（先进先出）
- 优先分配较早入库的库存
- 按`create_time`升序排列

### 2. 优先级分配
- 销售订单优先级最高
- 生产订单次之
- 其他需求最低

### 3. 部分分配
- 库存不足时进行部分分配
- 剩余需求继续等待

## 业务场景

### 1. 销售订单缺货
```
场景：客户下单100个，库存不足
流程：订单审核 → 创建分配需求 → 等待入库 → 自动分配 → 锁定库存 → 通知发货
```

### 2. 生产订单缺料
```
场景：生产需要原料200个，库存不足
流程：生产排产 → 创建分配需求 → 等待采购入库 → 自动分配 → 锁定库存 → 通知生产
```

### 3. 多需求竞争
```
场景：多个订单需要同一产品，库存有限
流程：按优先级排序 → 依次分配 → 高优先级优先获得库存
```

## 数据表结构

### 1. 分配需求表
**表名**：`oa_inventory_allocation_request`
```sql
- id: 主键
- product_id: 产品ID
- warehouse_id: 仓库ID
- quantity: 需求数量
- allocated_quantity: 已分配数量
- ref_type: 关联类型（customer_order/production_order）
- ref_id: 关联ID
- ref_no: 关联单号
- priority: 优先级
- status: 状态（1=待分配，2=部分分配，3=完全分配）
```

### 2. 库存锁定表
**表名**：`oa_inventory_lock`
```sql
- id: 主键
- product_id: 产品ID
- warehouse_id: 仓库ID
- quantity: 锁定数量
- ref_type: 关联类型
- ref_id: 关联ID
- ref_no: 关联单号
- status: 状态（1=已锁定，2=已使用，3=已释放）
- expire_time: 过期时间
```

### 3. 实时库存表
**表名**：`oa_inventory_realtime`
```sql
- quantity: 总库存数量
- available_quantity: 可用数量（总数量-锁定数量）
- locked_quantity: 锁定数量
```

## 通知机制

### 1. 分配完成通知
```php
private function notifyAllocationComplete($allocationResult)
{
    foreach ($allocationResult['allocation_details'] as $detail) {
        switch ($detail['ref_type']) {
            case 'customer_order':
                $this->notifyCustomerOrderAllocation($detail);
                break;
            case 'production_order':
                $this->notifyProductionOrderAllocation($detail);
                break;
        }
    }
}
```

### 2. 客户订单通知
- 检查订单是否完全分配
- 更新订单状态为已审核
- 触发发货流程

### 3. 生产订单通知
- 通知生产部门原料已齐套
- 可以开始生产

## 日志记录

### 1. 分配成功日志
```php
\think\facade\Log::info('入库自动分配成功', [
    'receipt_id' => $receipt->id,
    'product_id' => $product['product_id'],
    'warehouse_id' => $product['warehouse_id'],
    'inbound_quantity' => $product['quantity'],
    'allocated_quantity' => $result['total_allocated'],
    'allocation_details' => $result['allocation_details']
]);
```

### 2. 分配失败日志
```php
\think\facade\Log::error('入库自动分配失败', [
    'receipt_id' => $receipt->id,
    'error' => $e->getMessage()
]);
```

## 异常处理

### 1. 分配失败不影响入库
- 自动分配失败只记录日志
- 不影响正常的入库流程
- 可以后续手动分配

### 2. 事务保护
- 分配和锁定在同一事务中
- 确保数据一致性
- 失败时自动回滚

## 手动操作

### 1. 手动分配
- 访问分配管理页面
- 选择待分配需求
- 手动执行分配

### 2. 释放锁定
- 订单取消时自动释放
- 也可手动释放锁定
- 释放后重新分配给其他需求

## 监控和维护

### 1. 分配状态监控
- 查看待分配需求列表
- 监控分配成功率
- 分析分配延迟

### 2. 锁定库存监控
- 查看锁定库存明细
- 监控过期锁定
- 清理无效锁定

### 3. 性能优化
- 定期清理历史数据
- 优化分配算法
- 提高分配效率

通过这套完整的库存分配锁定机制，系统能够自动、高效地处理库存分配，确保业务流程的顺畅进行。
