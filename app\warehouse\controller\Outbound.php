<?php
declare (strict_types = 1);

namespace app\warehouse\controller;
use app\base\BaseController; 
use app\warehouse\model\Outbound as OutboundModel;
use app\warehouse\model\OutboundDetail as OutboundDetailModel;
use app\warehouse\model\InventoryLock;
use app\warehouse\model\Warehouse as WarehouseModel;
use app\warehouse\model\Location as LocationModel;
use app\warehouse\model\Zone as ZoneModel;
use app\customer\model\Delivery as DeliveryModel;
use app\customer\model\DeliveryItem as DeliveryItemModel;
use app\warehouse\model\Inventory as InventoryModel;
use app\warehouse\model\Inventoryreserve as InventoryreserveModel;
use app\Produce\model\ProductionFeedingDetail as ProductionFeedingDetailModel;
use app\engineering\model\Product as ProductModel;
use app\customer\model\Customer as CustomerModel;
use app\user\model\Admin as AdminModel;
use think\facade\View;
use think\facade\Db;
use think\facade\Session;
use think\facade\Request;
use think\facade\Redirect;
use app\service\DeliveryService;
use app\api\model\EditLog;


/**
 * 出库单控制器
 */
class Outbound extends BaseController
{
    protected $request;
    
    public function __construct()
    {
        parent::__construct();
        $this->request = request();
    }

    /**
     * 出库单列表
     */
    public function index()
    {
        
        
        if (request()->isAjax()) {
            $param = input('param.');
            
            // 构建查询条件
            $where = [];
            
            // 关键词搜索
            if (!empty($param['keywords'])) {
                $where[] = ['o.outbound_no|c.name', 'like', '%' . $param['keywords'] . '%'];
            }
            
            // 按出库类型筛选
            if (isset($param['outbound_type']) && $param['outbound_type'] !== '') {
                $where[] = ['o.outbound_type', '=', $param['outbound_type']];
            }
            
            // 按状态筛选
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['o.status', '=', intval($param['status'])];
            }
            
            // 按日期范围筛选
            if (!empty($param['date_range'])) {
                $dateRange = explode(' - ', $param['date_range']);
                if (count($dateRange) == 2) {
                    $where[] = ['o.outbound_date', '>=', $dateRange[0]];
                    $where[] = ['o.outbound_date', '<=', $dateRange[1]];
                }
            }
            
            // 按客户筛选
            if (!empty($param['customer_id'])) {
                $where[] = ['o.customer_id', '=', intval($param['customer_id'])];
            }
            
            // 查询出库单列表
            $list = OutboundModel::alias('o')
                ->leftJoin('customer c', 'o.customer_id = c.id')
                ->leftJoin('warehouse w', 'o.warehouse_id = w.id')
                ->field('o.id, o.outbound_no, o.outbound_type, o.outbound_date, o.customer_id,
                        o.total_quantity, o.total_amount, o.status, o.priority, o.ref_type, o.ref_no,
                        o.created_by, o.create_time, o.update_time,
                        c.name as customer_name, w.name as warehouse_name,
                        FROM_UNIXTIME(o.create_time, "%Y-%m-%d %H:%i") as create_time_formatted')
                ->where($where)
                ->order('o.id desc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                    'page' => isset($param['page']) ? $param['page'] : 1,
                ]);
            
            // 出库类型、状态和优先级文字说明
            foreach ($list as &$item) {
                $item['outbound_type_text'] = OutboundModel::getTypeText($item['outbound_type']);
                $item['status_text'] = OutboundModel::getStatusText($item['status']);
                $item['priority_text'] = OutboundModel::getPriorityText($item['priority']);
                $item['create_time'] = $item['create_time_formatted'];
                $item['lock_time'] = empty($item['lock_time']) || $item['lock_time'] == 0 ? 0 : date('Y-m-d H:i', (int)$item['lock_time']);
                $item['expire_time'] = empty($item['expire_time']) || $item['expire_time'] == 0 ? 0 : date('Y-m-d H:i', (int)$item['expire_time']);
                
                // 获取明细数量
                $item['detail_count'] = OutboundDetailModel::where('outbound_id', $item['id'])->count();
            }
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $list->items()
            ]);
        }
        
        // 获取出库类型列表
        $outboundTypes = [
            ['value' => OutboundModel::TYPE_SALES, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_SALES)],
            ['value' => OutboundModel::TYPE_PRODUCTION, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_PRODUCTION)],
            ['value' => OutboundModel::TYPE_TRANSFER, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_TRANSFER)],
            ['value' => OutboundModel::TYPE_RETURN, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_RETURN)],
            ['value' => OutboundModel::TYPE_OTHER, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_OTHER)],
        ];

        // 获取状态列表
        $statusList = [
            ['value' => OutboundModel::STATUS_DRAFT, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_DRAFT)],
            ['value' => OutboundModel::STATUS_SUBMITTED, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_SUBMITTED)],
            ['value' => OutboundModel::STATUS_APPROVED, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_APPROVED)],
            ['value' => OutboundModel::STATUS_PARTIAL, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_PARTIAL)],
            ['value' => OutboundModel::STATUS_COMPLETED, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_COMPLETED)],
            ['value' => OutboundModel::STATUS_CANCELED, 'label' => OutboundModel::getStatusText(OutboundModel::STATUS_CANCELED)],
        ];
        
        // 获取客户列表
        $customers = CustomerModel::field('id, name')->select();
        
        // 模板赋值
        View::assign([
            'outbound_types' => $outboundTypes,
            'status_list' => $statusList,
            'customers' => $customers
        ]);
        
        return View::fetch();
    }
    
    /**
     * 添加出库单
     */
    public function add()
    {
        if (request()->isAjax()) {
            $data = input('post.');
            if (empty($data['outbound_type'])) {
                return json(['code' => 1, 'msg' => '出库类型不能为空']);
            }
            
            // 开启事务
            Db::startTrans();
            
            try {
                // 生成出库单号 - 只在提交时生成，而不是页面加载时
                $outboundNo = OutboundModel::generateOutboundNo($data['outbound_type']);
                // 确定状态：如果是提交审核则设为已提交状态，否则为草稿状态
                $status = isset($data['submit_type']) && $data['submit_type'] == 'submit'
                    ? OutboundModel::STATUS_SUBMITTED
                    : OutboundModel::STATUS_DRAFT;
                
                // 创建出库单主表记录
                $outbound = new OutboundModel;
                $outbound->outbound_no = $outboundNo;
                $outbound->outbound_type = $data['outbound_type'];
                $outbound->customer_id = isset($data['customer_id']) ? $data['customer_id'] : null;
                $outbound->outbound_date = $data['outbound_date'];
                $outbound->related_bill_type = isset($data['related_bill_type']) ? $data['related_bill_type'] : null;
                $outbound->related_bill_no = isset($data['related_bill_no']) ? $data['related_bill_no'] : null;
                $outbound->total_amount = isset($data['total_amount']) ? $data['total_amount'] : 0;
                $outbound->notes = isset($data['notes']) ? $data['notes'] : '';
                $outbound->status = $status;
                $outbound->created_by = $this->uid;
                $outbound->save();
                
                // 保存出库单明细
                if (!empty($data['details']) && is_array($data['details'])) {
                    $detailsData = [];
                    $sortOrder = 1;
                    
                    foreach ($data['details'] as $detail) {
                        // 获取产品信息
                        $product = ProductModel::find($detail['product_id']);
                        if (!$product) {
                            throw new \Exception('产品不存在');
                        }
                        
                        // 检查库存
                        $inventoryId = isset($detail['inventory_id']) ? $detail['inventory_id'] : null;
                        $warehouseId = $detail['warehouse_id'];
                        $locationId = isset($detail['location_id']) ? $detail['location_id'] : null;
                        $batchNo = isset($detail['batch_no']) ? $detail['batch_no'] : '';
                        $quantity = $detail['quantity'];
                        
                        // 检查是否存在相同产品、仓库、批次的库存记录
                        $inventory = InventoryModel::where([
                            'product_id' => $detail['product_id'],
                            'warehouse_id' => $detail['warehouse_id'],
                            'batch_no' => $detail['batch_no']
                        ])->find();
                        
                        if (!$inventory) {
                            throw new \Exception('产品库存记录不存在，产品ID: ' . $detail['product_id'] . ', 产品名称: ' . $detail['product_name'] . ', 仓库ID: ' . $detail['warehouse_id'] . ', 批次号: ' . ($detail['batch_no'] ?: '无'));
                        }
                        
                        // 校验库存 - 直接使用 quantity 字段而不是 available_quantity
                        if ($inventory->quantity < $detail['quantity']) {
                            throw new \Exception('产品 ' . $detail['product_name'] . ' 在仓库 ' . $detail['warehouse_name'] . 
                                                ' 的库存不足，当前库存: ' . $inventory->quantity . 
                                                '，请求出库: ' . $detail['quantity']);
                        }
                        
                        // 添加明细记录
                        $detailModel = new OutboundDetailModel;
                        $detailModel->outbound_id = $outbound->id;
                        $detailModel->product_id = $detail['product_id'];
                        $detailModel->product_code = $detail['product_code'];
                        $detailModel->product_name = $detail['product_name'];
                        $detailModel->spec = isset($detail['spec']) ? $detail['spec'] : '';
                        $detailModel->unit = isset($detail['unit']) ? $detail['unit'] : '';
                        $detailModel->warehouse_id = $warehouseId;
                        $detailModel->warehouse_name = isset($detail['warehouse_name']) ? $detail['warehouse_name'] : '';
                        $detailModel->location_id = $locationId;
                        $detailModel->location_name = isset($detail['location_name']) ? $detail['location_name'] : '';
                        $detailModel->batch_no = $batchNo;
                        $detailModel->quantity = $quantity;
                        $detailModel->price = isset($detail['price']) ? $detail['price'] : 0;
                        $detailModel->amount = isset($detail['amount']) ? $detail['amount'] : 0;
                        $detailModel->sort_order = $sortOrder++;
                        $detailModel->notes = isset($detail['notes']) ? $detail['notes'] : '';
                        $detailModel->save();
                        
                        // 移除对已废弃的锁表功能的依赖
                        // 不再调用 lockStock 方法，因为该功能已被移除
                    }
                }
                
                // 提交事务
                Db::commit();
                
                // 添加操作日志
                $actionText = $status == OutboundModel::STATUS_SUBMITTED ? '提交审核' : '保存草稿';
                $outbound->addLog($actionText, '新增出库单' . $actionText);

                return json(['code' => 0, 'msg' => $status == OutboundModel::STATUS_SUBMITTED ? '提交成功，请等待审核' : '保存成功']);
                
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code' => 1, 'msg' => '操作失败: ' . $e->getMessage()]);
            }
        }
        
        // 获取出库类型列表
        $outboundTypes = [
            ['value' => OutboundModel::TYPE_SALES, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_SALES)],
            ['value' => OutboundModel::TYPE_PRODUCTION, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_PRODUCTION)],
            ['value' => OutboundModel::TYPE_SAMPLE, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_SAMPLE)],
            ['value' => OutboundModel::TYPE_INVENTORY, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_INVENTORY)],
            ['value' => OutboundModel::TYPE_TRANSFER, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_TRANSFER)],
            ['value' => OutboundModel::TYPE_GIFT, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_GIFT)],
            ['value' => OutboundModel::TYPE_OTHER, 'label' => OutboundModel::getTypeText(OutboundModel::TYPE_OTHER)],
        ];
        
        // 获取仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 获取客户列表
        $customers = CustomerModel::field('id, name')->select();
        
        // 默认出库日期
        $outboundDate = date('Y-m-d');
        
        // 默认出库类型
        $outboundType = input('outbound_type', OutboundModel::TYPE_SALES);
        
        // 模板赋值
        View::assign([
            'outbound_types' => $outboundTypes,
            'warehouses' => $warehouses,
            'customers' => $customers,
            'outbound_date' => $outboundDate,
            'outbound_type' => $outboundType
        ]);
        
        return View::fetch();
    }
    
    /**
     * 出库单编辑页面
     * @param int $id 出库单ID
     * @return mixed
     */
    public function edit($id)
    {
        // 获取出库单信息
        $model = new OutboundModel();
        $outbound = $model->find($id);
       
        if (!$outbound) {
             return json(['code' => 1, 'msg' => '出库单不存在']);
        }
        
        if ($outbound['status'] != 1) {
            return json(['code' => 1, 'msg' => '只有草稿状态的出库单才能编辑']);
        }
        
        // 获取出库单明细（不使用仓库和库位关联，因为明细表中没有warehouse_id字段）
        $detailModel = new OutboundDetailModel();
        $details = $detailModel->alias('d')
            ->field('d.*, "" as warehouse_name, "" as location_name')
            ->where('d.outbound_id', $id)
            ->select();
        
        // 获取出库类型列表 - 使用模型中定义的类型而不是配置文件
        $outboundTypes = [
            OutboundModel::TYPE_SALES => OutboundModel::getTypeText(OutboundModel::TYPE_SALES),
            OutboundModel::TYPE_PRODUCTION => OutboundModel::getTypeText(OutboundModel::TYPE_PRODUCTION),
            OutboundModel::TYPE_SAMPLE => OutboundModel::getTypeText(OutboundModel::TYPE_SAMPLE),
            OutboundModel::TYPE_INVENTORY => OutboundModel::getTypeText(OutboundModel::TYPE_INVENTORY),
            OutboundModel::TYPE_TRANSFER => OutboundModel::getTypeText(OutboundModel::TYPE_TRANSFER),
            OutboundModel::TYPE_GIFT => OutboundModel::getTypeText(OutboundModel::TYPE_GIFT),
            OutboundModel::TYPE_OTHER => OutboundModel::getTypeText(OutboundModel::TYPE_OTHER),
        ];
        
        // 获取客户列表
        $customers = Db::name('customer')->select();
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')->where('status', 1)->select();
        
        // 将明细转为json，便于前端表格使用
        $detailsJson = json_encode($details);
        
        View::assign([
            'outbound' => $outbound,
            'details' => $details,
            'details_json' => $detailsJson,
            'outbound_types' => $outboundTypes,
            'customers' => $customers,
            'warehouses' => $warehouses
        ]);
        
        return View::fetch();
    }
    
    /**
     * 处理出库单编辑
     */
    public function doEdit()
    {
        if (!$this->request->isAjax() || !$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        // 获取 JSON 格式的数据
        $contentType = $this->request->header('Content-Type');
        $data = [];
        
        // 根据 Content-Type 选择不同的数据解析方式
        if (strpos($contentType, 'application/json') !== false) {
            // 使用 ThinkPHP 的 getInput 方法获取 JSON 数据
            $input = $this->request->getInput();
            $data = json_decode($input, true);
            
            if (is_null($data)) {
                // 如果 JSON 解析失败，尝试直接获取 php://input
                $rawInput = file_get_contents('php://input');
                $data = json_decode($rawInput, true);
                
                if (is_null($data)) {
                    return json(['code' => 1, 'msg' => 'JSON 数据解析失败']);
                }
            }
        } else {
            // 普通表单数据
            $data = $this->request->post();
        }
        
        // 验证ID
        if (empty($data['id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 验证基本数据
        $validate = new \think\Validate([
            'outbound_type' => 'require',
            'outbound_date' => 'require|date',
            'details' => 'require|array'
        ]);
        
        if (!$validate->check($data)) {
            return json(['code' => 1, 'msg' => $validate->getError()]);
        }
        
        // 检查出库单状态
        $model = new OutboundModel();
        $outbound = $model->find($data['id']);
        
        if (!$outbound) {
            return json(['code' => 1, 'msg' => '出库单不存在']);
        }
        
        if ($outbound['status'] != 1) {
            return json(['code' => 1, 'msg' => '只有草稿状态的出库单才能编辑']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新出库单基本信息
            $updateData = [
                'outbound_type' => $data['outbound_type'],
                'outbound_date' => $data['outbound_date'],
                'customer_id' => $data['customer_id'] ?? 0,
                'related_bill_type' => $data['related_bill_type'] ?? '',
                'related_bill_no' => $data['related_bill_no'] ?? '',
                'total_amount' => $data['total_amount'] ?? 0,
                'notes' => $data['notes'] ?? '',
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            $model->where('id', $data['id'])->update($updateData);
            
            // 获取现有明细记录
            $detailModel = new OutboundDetailModel();
            $existingDetails = $detailModel->where('outbound_id', $data['id'])->select()->toArray();
            
            // 将现有明细记录转换为以ID为键的数组，方便查找
            $existingDetailsMap = [];
            foreach ($existingDetails as $detail) {
                $existingDetailsMap[$detail['id']] = $detail;
            }
            
            // 记录处理前的明细数量
            \think\facade\Log::info("出库单编辑 - 处理明细: ID={$data['id']}, 现有明细数量=" . count($existingDetails) . ", 新明细数量=" . count($data['details']));
            
            // 跟踪哪些现有明细记录已被处理
            $processedDetailIds = [];
            
            // 处理新明细数据
            foreach ($data['details'] as $index => $detail) {
                // 检查产品是否存在
                $product = ProductModel::find($detail['product_id']);
                if (!$product) {
                    throw new \Exception('产品不存在');
                }
                
                // 检查仓库是否存在
                if (empty($detail['warehouse_id'])) {
                    throw new \Exception('产品 ' . $detail['product_name'] . ' 未指定仓库');
                }
                
                // 检查库存
                $inventory = InventoryModel::where([
                    'product_id' => $detail['product_id'],
                    'warehouse_id' => $detail['warehouse_id']
                ])->find();
                
                if (!$inventory) {
                    // 如果是自动分配的出库单，查找其他仓库的库存
                    if (!empty($data['is_auto_allocated'])) {
                        $otherInventory = InventoryModel::where([
                            'product_id' => $detail['product_id']
                        ])->find();
                        
                        if ($otherInventory) {
                            // 更新明细的仓库信息
                            $detail['warehouse_id'] = $otherInventory->warehouse_id;
                            $detail['warehouse_name'] = Db::name('warehouse')->where('id', $otherInventory->warehouse_id)->value('name');
                            $inventory = $otherInventory;
                        } else {
                            throw new \Exception('产品 ' . $detail['product_name'] . ' 在所有仓库都没有库存记录');
                        }
                    } else {
                        // 查找其他仓库的库存
                        $suggestions = [];
                        $otherInventories = InventoryModel::where('product_id', $detail['product_id'])
                            ->where('quantity', '>=', $detail['quantity'])
                            ->select();
                        
                        foreach ($otherInventories as $inv) {
                            $warehouseName = Db::name('warehouse')->where('id', $inv->warehouse_id)->value('name');
                            $suggestions[] = "仓库[{$warehouseName}]有{$inv->quantity}库存";
                        }
                        
                        if (!empty($suggestions)) {
                            throw new \Exception('产品 ' . $detail['product_name'] . ' 在指定仓库 ' . $detail['warehouse_name'] . 
                                ' 没有库存记录，但在其他仓库有库存：' . implode('，', $suggestions) . '，请修改出库仓库');
                        } else {
                            throw new \Exception('产品 ' . $detail['product_name'] . ' 在所有仓库都没有库存记录');
                        }
                    }
                }
                
                // 校验库存数量
                if ($inventory->quantity < $detail['quantity']) {
                    throw new \Exception('产品 ' . $detail['product_name'] . ' 在仓库 ' . $detail['warehouse_name'] . 
                                    ' 的库存不足，当前库存: ' . $inventory->quantity . 
                                    '，请求出库: ' . $detail['quantity']);
                }
                
                // 准备明细数据
                $detailData = [
                    'outbound_id' => $data['id'],
                    'warehouse_id' => $detail['warehouse_id'],
                    'location_id' => $detail['location_id'] ?? 0,
                    'product_id' => $detail['product_id'],
                    'product_code' => $detail['product_code'],
                    'product_name' => $detail['product_name'],
                    'spec' => $detail['specs'] ?? $detail['spec'] ?? '',  // 兼容处理规格字段
                    'unit' => $detail['unit'],
                    'quantity' => $detail['quantity'],
                    'price' => $detail['price'] ?? 0,
                    'amount' => $detail['quantity'] * ($detail['price'] ?? 0),
                    'notes' => $detail['notes'] ?? '',
                    'sort_order' => $index + 1
                ];
                
                // 如果明细有ID且存在于现有明细中，则更新
                if (!empty($detail['id']) && isset($existingDetailsMap[$detail['id']])) {
                    $detailId = $detail['id'];
                    $detailModel->where('id', $detailId)->update($detailData);
                    $processedDetailIds[] = $detailId;
                    \think\facade\Log::info("出库单编辑 - 更新明细: ID={$detailId}, 产品ID={$detail['product_id']}, 产品名称={$detail['product_name']}, 数量={$detail['quantity']}");
                } else {
                    // 否则插入新记录
                    $detailData['create_time'] = date('Y-m-d H:i:s');
                    $newDetailId = $detailModel->insertGetId($detailData);
                    $processedDetailIds[] = $newDetailId;
                    \think\facade\Log::info("出库单编辑 - 插入明细: 新ID={$newDetailId}, 产品ID={$detail['product_id']}, 产品名称={$detail['product_name']}, 数量={$detail['quantity']}");
                }
            }
            
            // 删除未处理的现有明细（即不再需要的明细）
            $deleteIds = [];
            foreach ($existingDetails as $detail) {
                if (!in_array($detail['id'], $processedDetailIds)) {
                    $deleteIds[] = $detail['id'];
                }
            }
            
            if (!empty($deleteIds)) {
                $deleteCount = $detailModel->whereIn('id', $deleteIds)->delete();
                \think\facade\Log::info("出库单编辑 - 删除不再需要的明细: 删除ID=" . implode(',', $deleteIds) . ", 删除数量={$deleteCount}");
            }
            
            // 添加操作日志
            $outbound->addLog('编辑', '编辑出库单');
            
            Db::commit();
            return json(['code' => 0, 'msg' => '保存成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 出库单详情
     */
    public function detail($id)
    {
        // 获取出库单信息
        $model = new OutboundModel();
        $outbound = $model->with(['customer', 'creator', 'approver'])->find($id);
        
        if (!$outbound) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '出库单不存在']);
            }
            return '<script>parent.layer.msg("出库单不存在", {icon: 2}); parent.layer.closeAll();</script>';
        }
        
        // 出库类型和状态文本转换 - 使用模型中的方法
        $outbound['outbound_type_text'] = OutboundModel::getTypeText($outbound['outbound_type']);
        $outbound['status_text'] = OutboundModel::getStatusText($outbound['status']);

        // 关联客户名称
        $outbound['customer_name'] = $outbound['customer']['name'] ?? '';

        // 创建人和审批人
        $outbound['creator_name'] = $outbound['creator']['nickname'] ?? '';
        $outbound['approver_name'] = $outbound['approver']['nickname'] ?? '';
        
        // 获取明细（不使用仓库和库位关联，因为明细表中没有warehouse_id字段）
        $detailModel = new OutboundDetailModel();
        $details = $detailModel->alias('d')
            ->field('d.*, "" as warehouse_name, "" as location_name')
            ->where('d.outbound_id', $id)
            ->select();
        
        // 计算总数量
        $totalQuantity = 0;
        foreach ($details as $detail) {
            $totalQuantity += $detail['quantity'];
        }
        
        // 判断是否有审批权限
        $canApprove = $this->checkApprovePermission();
        
        View::assign([
            'outbound' => $outbound,
            'details' => $details,
            'total_quantity' => $totalQuantity,
            'can_approve' => $canApprove
        ]);
        
        return View::fetch();
    }
    
    /**
     * 检查当前用户是否有审批权限
     * @return bool
     */
    private function checkApprovePermission()
    {
        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户是否有特定角色或权限
        
        // 简单实现：假设管理员可以审批
        $adminId = session('admin.id');
        $adminInfo = AdminModel::where('id', $adminId)->find();
        
        // 如果是超管，直接返回有权限
        if ($adminInfo && isset($adminInfo['role_id']) && $adminInfo['role_id'] == 1) {
            return true;
        }
        
        // 如果有特定权限，也返回有权限
        // 此处可根据实际权限系统完善
        
        return false;
    }
    
    /**
     * 提交出库
     */
    public function submitApproval()
    {
        if (!$this->request->isAjax() || !$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $id = input('post.id/d', 0);
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 检查出库单状态
            $outbound = OutboundModel::find($id);
            if (!$outbound) {
                throw new \Exception('出库单不存在');
            }
            
            if ($outbound->status != OutboundModel::STATUS_DRAFT) {
                throw new \Exception('只有草稿状态的出库单才能提交出库');
            }
            
            // 获取出库单明细
            $details = OutboundDetailModel::where('outbound_id', $id)->select();
            if ($details->isEmpty()) {
                throw new \Exception('出库单明细不能为空');
            }
            
            // 直接扣减库存
            foreach ($details as $detail) {
                // 检查仓库ID是否有效
                if (empty($detail->warehouse_id) || $detail->warehouse_id <= 0) {
                    throw new \Exception("产品 [{$detail->product_name}] 未指定出库仓库，请先编辑出库单并选择仓库");
                }
                
                // 查找库存记录 - 移除库位条件，只按产品和仓库查询
                $inventoryWhere = [
                    'product_id' => $detail->product_id,
                    'warehouse_id' => $detail->warehouse_id
                ];
                
                // 保留批次号条件，如果有的话
                if (!empty($detail->batch_no)) {
                    $inventoryWhere['batch_no'] = $detail->batch_no;
                }
                
                $inventory = InventoryModel::where($inventoryWhere)->find();
                
                if (!$inventory) {
                    // 尝试查找任何仓库中的库存记录
                    $anyInventory = InventoryModel::where('product_id', $detail->product_id)->find();
                    
                    if ($anyInventory) {
                        // 如果在其他仓库有库存，提示用户
                        $warehouseName = Db::name('warehouse')->where('id', $anyInventory->warehouse_id)->value('name');
                        throw new \Exception("找不到产品 [{$detail->product_name}] 在仓库 [{$detail->warehouse_name}] 的库存记录，但在仓库 [{$warehouseName}] 有库存");
                    } else {
                        // 如果没有任何库存记录
                        throw new \Exception("找不到产品 [{$detail->product_name}] 的任何库存记录");
                    }
                }
                
                // 检查库存是否足够 - 直接使用quantity字段，不再检查available_quantity
                if ($inventory->quantity < $detail->quantity) {
                    throw new \Exception("产品 [{$detail->product_name}] 在仓库 [{$detail->warehouse_name}] 的库存不足，当前库存：{$inventory->quantity}，需要：{$detail->quantity}");
                }
                
                // 减少总库存数量
                $beforeQty = $inventory->quantity;
                $inventory->quantity = $inventory->quantity - $detail->quantity;
                
                // 更新库存数据 - 不再设置available_quantity
                $inventory->save();
                
                // 添加库存流水记录
                Db::name('inventory_log')->insert([
                    'inventory_id' => $inventory->id,
                    'warehouse_id' => $inventory->warehouse_id,
                    'product_id' => $inventory->product_id,
                    'batch_no' => $inventory->batch_no,
                    'direction' => 2, // 2=出库
                    'quantity' => $detail->quantity,
                    'before_quantity' => $beforeQty,
                    'after_quantity' => $inventory->quantity,
                    'ref_type' => 'outbound',
                    'ref_id' => $id,
                    'ref_no' => $outbound->outbound_no,
                    'notes' => "提交出库单 [{$outbound->outbound_no}] 扣减库存",
                    'create_time' => time(),
                    'operation_by' => $this->uid
                ]);
            }
            
            // 更新出库单状态
            $outbound->status = OutboundModel::STATUS_APPROVED; // 已审核状态
            $outbound->submit_time = time();
            $outbound->approve_time = time();
            $outbound->approved_by = session('admin.id');
            $outbound->save();
            
            // 更新关联的待出库项状态为"已完成"
            $pendingIds = [];
            foreach ($details as $detail) {
                if (!empty($detail->pending_id)) {
                    $pendingIds[] = $detail->pending_id;
                }
            }
            
            if (!empty($pendingIds)) {
                // 使用PendingOutboundModel的updateStatus方法更新状态
                \app\warehouse\model\PendingOutbound::updateStatus(
                    $pendingIds, 
                    \app\warehouse\model\PendingOutbound::STATUS_COMPLETED
                );
            }
            
            // 处理关联的发货单（如果有）
            if (!empty($outbound->related_bill_type) && $outbound->related_bill_type == 'customer_order_delivery') {
                // 更新发货单状态
                db::name('customer_order_delivery')
                    ->where('delivery_no', $outbound->related_bill_no)
                    ->update([
                        'status' => 1, 
                        'handle_time' => date('Y-m-d H:i:s'),
                        'handler_id' => session('admin.id')
                    ]);

                $delivery = db::name('customer_order_delivery')
                    ->where('delivery_no', $outbound->related_bill_no)
                    ->find();

                if ($delivery) {
                    // 完成发货流程
                    $result = DeliveryService::completeDelivery(
                        $delivery['id'], 
                        $outbound['logistics_no'], 
                        $outbound['delivery_time']
                    );
                }
            }
            
            // 添加日志
            $outbound->addLog('direct_outbound', '直接出库处理完成');
            
            // 提交事务
            Db::commit();
            return json(['code' => 0, 'msg' => '出库成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '出库失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 驳回出库单
     */
    public function reject()
    {
        if (!$this->request->isAjax() || !$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $id = input('post.id/d', 0);
        $reason = input('post.reason/s', '');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 检查审批权限
        if (!$this->checkApprovePermission()) {
            return json(['code' => 1, 'msg' => '您没有审批权限']);
        }
        
        $outbound = OutboundModel::find($id);
        
        if (!$outbound) {
            return json(['code' => 1, 'msg' => '出库单不存在']);
        }
        
        if ($outbound->status != OutboundModel::STATUS_SUBMITTED) {
            return json(['code' => 1, 'msg' => '只有已提交状态的出库单才能驳回']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 更新状态为草稿
            $outbound->status = OutboundModel::STATUS_DRAFT;
            $outbound->reject_reason = $reason;
            $outbound->reject_time = time();
            $outbound->rejected_by = session('admin.id');
            $outbound->save();
            
            // 获取出库单明细
            $details = OutboundDetailModel::where('outbound_id', $id)->select();
            
            // 恢复库存，不再使用锁库功能
            foreach ($details as $detail) {
                // 查找库存记录
                $inventoryWhere = [
                    'product_id' => $detail->product_id,
                    'warehouse_id' => $detail->warehouse_id,
                    'status' => 1
                ];
                
                if (!empty($detail->location_id)) {
                    $inventoryWhere['location_id'] = $detail->location_id;
                }
                
                if (!empty($detail->batch_no)) {
                    $inventoryWhere['batch_no'] = $detail->batch_no;
                }
                
                $inventory = InventoryModel::where($inventoryWhere)->find();
                
                if (!$inventory) {
                    continue; // 找不到库存记录，跳过处理
                }
                
                // 增加库存数量，恢复原状
                $beforeQty = $inventory->quantity;
                $inventory->quantity = $inventory->quantity + $detail->quantity;
                $inventory->save();
                
                // 添加库存流水记录
                Db::name('inventory_log')->insert([
                    'inventory_id' => $inventory->id,
                    'warehouse_id' => $inventory->warehouse_id,
                    'location_id' => $inventory->location_id,
                    'product_id' => $inventory->product_id,
                    'batch_no' => $inventory->batch_no,
                    'direction' => 1, // 1=入库
                    'quantity' => $detail->quantity,
                    'before_quantity' => $beforeQty,
                    'after_quantity' => $inventory->quantity,
                    'ref_type' => 'outbound_reject',
                    'ref_id' => $id,
                    'ref_no' => $outbound->outbound_no,
                    'notes' => "驳回出库单 [{$outbound->outbound_no}] 恢复库存，原因: {$reason}",
                    'create_time' => time(),
                    'created_by' => session('admin.id')
                ]);
            }
            
            // 移除对预占功能的依赖
            
            // 添加驳回日志
            $outbound->addLog('reject', '驳回了出库单，原因: ' . $reason);
            
            Db::commit();
            return json(['code' => 0, 'msg' => '驳回成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '驳回失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 取消出库单
     */
    public function cancel()
    {
        if (!$this->request->isAjax() || !$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $id = input('post.id/d', 0);
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $outbound = OutboundModel::find($id);
        
        if (!$outbound) {
            return json(['code' => 1, 'msg' => '出库单不存在']);
        }
        
        // 检查是否可以取消
        if ($outbound->status == OutboundModel::STATUS_COMPLETED) {
            return json(['code' => 1, 'msg' => '已完成的出库单不能取消']);
        }

        if ($outbound->status == OutboundModel::STATUS_CANCELED) {
            return json(['code' => 1, 'msg' => '该出库单已经取消']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 获取出库单明细
            $details = OutboundDetailModel::where('outbound_id', $id)->select();

            // 释放库存锁定（如果有的话）
            $lockService = new \app\warehouse\service\InventoryLockService();
            $lockService->releaseByReference('outbound', $id);

            // 更新关联的待出库项状态为"待处理"
            $pendingIds = [];
            foreach ($details as $detail) {
                if (!empty($detail->pending_id)) {
                    $pendingIds[] = $detail->pending_id;
                }
            }

            if (!empty($pendingIds)) {
                // 使用PendingOutboundModel的updateStatus方法更新状态
                \app\warehouse\model\PendingOutbound::updateStatus(
                    $pendingIds,
                    \app\warehouse\model\PendingOutbound::STATUS_PENDING // 恢复为待处理状态
                );
            }

            // 恢复库存（仅对已出库的部分）
            foreach ($details as $detail) {
                if ($detail->actual_quantity > 0) {
                    // 更新实时库存
                    Db::name('inventory_realtime')
                        ->where('product_id', $detail->product_id)
                        ->where('warehouse_id', $detail->warehouse_id)
                        ->inc('available_quantity', $detail->actual_quantity)
                        ->update();
                }
            }

            // 更新状态为取消
            $outbound->status = OutboundModel::STATUS_CANCELED;
            $outbound->save();

            // 添加取消日志（暂时注释，因为日志表不存在）
            // $outbound->addLog('cancel', '取消出库单');

            // 提交事务
            Db::commit();
            return json(['code' => 0, 'msg' => '取消成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['code' => 1, 'msg' => '取消失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取仓库下的库区列表
     */
    public function getZones()
    {
        if (request()->isAjax()) {
            $warehouseId = input('warehouse_id', 0, 'intval');
            
            if (!$warehouseId) {
                return json(['code' => 1, 'msg' => '请选择仓库']);
            }
            
            $zones = ZoneModel::where(['warehouse_id' => $warehouseId, 'status' => 1])
                ->field('id, code, name')
                ->select();
            
            return json(['code' => 0, 'data' => $zones]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取仓库或库区下的库位列表
     */
    public function getLocations()
    {
        if (request()->isAjax()) {
            $warehouseId = input('warehouse_id', 0, 'intval');
            $zoneId = input('zone_id', 0, 'intval');
            
            if (!$warehouseId) {
                return json(['code' => 1, 'msg' => '请选择仓库']);
            }
            
            $where = ['warehouse_id' => $warehouseId, 'status' => 1];
            if ($zoneId) {
                $where['zone_id'] = $zoneId;
            }
            
            $locations = LocationModel::where($where)
                ->field('id, code, name')
                ->select();
            
            return json(['code' => 0, 'data' => $locations]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取产品库存信息
     */
    public function getProductStock()
    {
        if (!$this->request->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $productId = input('product_id', 0, 'intval');
        $warehouseId = input('warehouse_id', 0, 'intval');
        
        if (!$productId) {
            return json(['code' => 1, 'msg' => '请选择产品']);
        }
        
        // 使用API获取库存信息
        $inventoryApi = new \app\api\controller\Inventory();
        return $inventoryApi->getProductInventory();
    }
    
    /**
     * 导出出库单
     */
    public function export()
    {
        $id = input('id', 0, 'intval');
        
        if (!$id) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            return '<script>parent.layer.msg("参数错误", {icon: 2}); parent.layer.closeAll();</script>';
        }
        
        // 获取出库单详情
        $outbound = OutboundModel::find($id);
        if (!$outbound) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '出库单不存在']);
            }
            return '<script>parent.layer.msg("出库单不存在", {icon: 2}); parent.layer.closeAll();</script>';
        }
        
        // 获取出库单明细（不使用库位表，因为系统已确认无库位管理）
        $details = OutboundDetailModel::alias('d')
            ->leftJoin('warehouse w', 'w.id = d.warehouse_id')
            ->field('d.*, w.name as warehouse_name, "" as location_name')
            ->where('d.outbound_id', $id)
            ->order('d.sort_order', 'asc')
            ->select();
        
        // 获取客户信息
        $customerName = '';
        if ($outbound->customer_id) {
            $customer = CustomerModel::find($outbound->customer_id);
            $customerName = $customer ? $customer->name : '';
        }
        
        // 获取创建人信息
        $creatorName = '';
        if ($outbound->created_by) {
            $creator = Db::name('admin')->field('name')->where('id', $outbound->created_by)->find();
            $creatorName = $creator ? $creator['name'] : '';
        }
        
        // 获取审核人信息
        $approverName = '';
        if ($outbound->approved_by) {
            $approver = Db::name('admin')->field('name')->where('id', $outbound->approved_by)->find();
            $approverName = $approver ? $approver['name'] : '';
        }
        
        // 创建Excel对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置标题
        $sheet->setCellValue('A1', '出库单');
        $sheet->mergeCells('A1:H1');
        
        // 设置表头样式
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        
        // 设置出库单基本信息
        $sheet->setCellValue('A2', '出库单号：');
        $sheet->setCellValue('B2', $outbound->outbound_no);
        $sheet->setCellValue('C2', '出库类型：');
        $sheet->setCellValue('D2', OutboundModel::getTypeText($outbound->outbound_type));
        $sheet->setCellValue('E2', '状态：');
        $sheet->setCellValue('F2', OutboundModel::getStatusText($outbound->status));
        
        $sheet->setCellValue('A3', '客户：');
        $sheet->setCellValue('B3', $customerName);
        $sheet->setCellValue('C3', '出库日期：');
        $sheet->setCellValue('D3', $outbound->outbound_date);
        $sheet->setCellValue('E3', '关联单据：');
        $sheet->setCellValue('F3', $outbound->related_bill_no);
        
        // 设置创建和审核信息
        $sheet->setCellValue('A4', '创建人：');
        $sheet->setCellValue('B4', $creatorName);
        $sheet->setCellValue('C4', '创建时间：');
        $sheet->setCellValue('D4', date('Y-m-d H:i:s', $outbound->create_time));
        $sheet->setCellValue('E4', '审核人：');
        $sheet->setCellValue('F4', $approverName);
        
        // 备注
        $sheet->setCellValue('A5', '备注：');
        $sheet->setCellValue('B5', $outbound->notes);
        $sheet->mergeCells('B5:F5');
        
        // 设置明细表头
        $sheet->setCellValue('A7', '序号');
        $sheet->setCellValue('B7', '仓库');
        $sheet->setCellValue('C7', '库位');
        $sheet->setCellValue('D7', '产品编码');
        $sheet->setCellValue('E7', '产品名称');
        $sheet->setCellValue('F7', '规格型号');
        $sheet->setCellValue('G7', '单位');
        $sheet->setCellValue('H7', '批次号');
        $sheet->setCellValue('I7', '数量');
        $sheet->setCellValue('J7', '单价');
        $sheet->setCellValue('K7', '金额');
        $sheet->setCellValue('L7', '备注');
        
        // 设置表头样式
        $headerStyle = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E9E9E9']
            ]
        ];
        $sheet->getStyle('A7:L7')->applyFromArray($headerStyle);
        
        // 填充明细数据
        $row = 8;
        $totalQty = 0;
        $totalAmount = 0;
        
        foreach ($details as $key => $detail) {
            $sheet->setCellValue('A' . $row, $key + 1);
            $sheet->setCellValue('B' . $row, $detail->warehouse_name);
            $sheet->setCellValue('C' . $row, $detail->location_name);
            $sheet->setCellValue('D' . $row, $detail->product_code);
            $sheet->setCellValue('E' . $row, $detail->product_name);
            $sheet->setCellValue('F' . $row, $detail->spec);
            $sheet->setCellValue('G' . $row, $detail->unit);
            $sheet->setCellValue('H' . $row, $detail->batch_no);
            $sheet->setCellValue('I' . $row, $detail->quantity);
            $sheet->setCellValue('J' . $row, $detail->price);
            $sheet->setCellValue('K' . $row, $detail->amount);
            $sheet->setCellValue('L' . $row, $detail->notes);
            
            $totalQty += $detail->quantity;
            $totalAmount += $detail->amount;
            
            $row++;
        }
        
        // 设置合计行
        $sheet->setCellValue('A' . $row, '合计');
        $sheet->mergeCells('A' . $row . ':H' . $row);
        $sheet->setCellValue('I' . $row, $totalQty);
        $sheet->setCellValue('J' . $row, '');
        $sheet->setCellValue('K' . $row, $totalAmount);
        $sheet->setCellValue('L' . $row, '');
        
        // 设置合计行样式
        $totalStyle = [
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ]
            ]
        ];
        $sheet->getStyle('A' . $row . ':L' . $row)->applyFromArray($totalStyle);
        
        // 设置签名行
        $row += 2;
        $sheet->setCellValue('A' . $row, '制单人：');
        $sheet->setCellValue('C' . $row, $creatorName);
        $sheet->setCellValue('E' . $row, '审核人：');
        $sheet->setCellValue('G' . $row, $approverName);
        $sheet->setCellValue('I' . $row, '领料人：');
        $sheet->setCellValue('K' . $row, '');
        
        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(10);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(25);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(10);
        $sheet->getColumnDimension('H')->setWidth(15);
        $sheet->getColumnDimension('I')->setWidth(10);
        $sheet->getColumnDimension('J')->setWidth(10);
        $sheet->getColumnDimension('K')->setWidth(10);
        $sheet->getColumnDimension('L')->setWidth(20);
        
        // 输出Excel文件
        $filename = '出库单_' . $outbound->outbound_no . '_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    /**
     * 获取出库单分页列表
     * 供AJAX请求调用
     */
    public function getPageList()
    {
        $param = input('param.');
        
        // 构建查询条件
        $where = [];
        
        // 关键词搜索
        if (!empty($param['keyword'])) {
            $where[] = ['o.outbound_no|c.name', 'like', '%' . $param['keyword'] . '%'];
        }
        
        // 按出库类型筛选
        if (isset($param['outbound_type']) && $param['outbound_type'] !== '') {
            $where[] = ['o.outbound_type', '=', intval($param['outbound_type'])];
        }
        
        // 按状态筛选
        if (isset($param['status']) && $param['status'] !== '') {
            $where[] = ['o.status', '=', intval($param['status'])];
        }
        
        // 按日期范围筛选
        if (!empty($param['date_range'])) {
            $dateRange = explode(' - ', $param['date_range']);
            if (count($dateRange) == 2) {
                $where[] = ['o.outbound_date', '>=', $dateRange[0]];
                $where[] = ['o.outbound_date', '<=', $dateRange[1]];
            }
        }
        
        // 按客户筛选
        if (!empty($param['customer_id'])) {
            $where[] = ['o.customer_id', '=', intval($param['customer_id'])];
        }
        
        // 查询出库单列表，移除对已废弃的锁表功能的依赖
        $list = OutboundModel::alias('o')
            ->leftJoin('customer c', 'o.customer_id = c.id')
            ->field('o.id, o.outbound_no, o.outbound_type, o.outbound_date, o.customer_id, 
                    o.total_amount, o.status, o.created_by, o.create_time, o.update_time,
                    c.name as customer_name')
            ->where($where)
            ->order('o.id desc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
        
        // 出库类型和状态文字说明
        $items = $list->items();
        foreach ($items as &$item) {
            $item['outbound_type_text'] = OutboundModel::getTypeText($item['outbound_type']);
            $item['status_text'] = OutboundModel::getStatusText($item['status']);
            $item['create_time'] = date('Y-m-d H:i', (int)$item['create_time']);
            
            // 移除对锁定时间和过期时间的处理
            $item['lock_time'] = 0;
            $item['expire_time'] = 0;
            
            // 获取明细数量
            $item['detail_count'] = OutboundDetailModel::where('outbound_id', $item['id'])->count();
            
            // 判断用户是否有审批权限
            $item['can_approve'] = $this->checkApprovePermission();
        }
        
        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $items
        ]);
    }

    /**
     * 自动分配库存
     */
    public function autoAllocateStock()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $productId = input('product_id', 0, 'intval');
        $warehouseId = input('warehouse_id', 0, 'intval');
        $quantity = input('quantity', 0, 'floatval');
        $strategy = input('strategy', 'fifo', 'trim'); // fifo或fefo
        
        // 定义最大允许分配的批次数量
        $maxAllowedRecords = 10; // 最大允许10个批次

        if (!$productId || !$warehouseId || $quantity <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询产品
        $product = ProductModel::find($productId);
        if (!$product) {
            return json(['code' => 1, 'msg' => '产品不存在']);
        }
        
        // 将产品对象转换为数组，避免序列化问题
        $productData = $product->toArray();
        
        // 构建查询条件
        $where = [
            ['i.product_id', '=', $productId],
            ['i.warehouse_id', '=', $warehouseId],
            ['i.quantity', '>', 0],  // 使用 quantity 字段而不是 available_quantity
            ['i.status', '=', 1] // 正常状态
        ];
        
        // 根据策略决定排序方式
        $orderBy = '';
        if ($strategy == 'fifo') {
            // 先进先出：按入库时间排序
            $orderBy = 'i.create_time asc, i.batch_no asc';
        } elseif ($strategy == 'fefo') {
            // 先过期先出：按过期日期排序（非空值优先）
            $orderBy = 'IFNULL(i.expiry_date, "99999999999") asc, i.batch_no asc';
        } else {
            // 默认排序
            $orderBy = 'i.batch_no asc';
        }
        
        // 查询库存记录（不使用库位表，因为系统已确认无库位管理）
        $stocks = Db::name('inventory')
            ->alias('i')
            ->leftJoin('warehouse w', 'i.warehouse_id = w.id')
            ->field('i.*, w.name as warehouse_name, "" as location_name')
            ->where($where)
            ->order($orderBy)
            ->select()
            ->toArray();
        
        if (empty($stocks)) {
            return json(['code' => 1, 'msg' => '该产品在指定仓库中无库存']);
        }
        
        // 计算分配方案
        $allocations = [];
        $remainingQty = $quantity;
        
        foreach ($stocks as $stock) {
            if ($remainingQty <= 0) {
                break;
            }
            
            // 确保数值类型正确，使用 quantity 字段而不是 available_quantity
            $stock['quantity'] = floatval($stock['quantity']);
            
            $allocQty = min($remainingQty, $stock['quantity']);
            $remainingQty -= $allocQty;
            
            // 构建分配结果，确保所有ID和返回值都是正确的类型
            $allocations[] = [
                'inventory_id' => (string)$stock['id'],
                'warehouse_id' => (string)$stock['warehouse_id'],
                'warehouse_name' => (string)($stock['warehouse_name'] ?? ''),
                'location_id' => $stock['location_id'] ? (string)$stock['location_id'] : '',
                'location_name' => (string)($stock['location_name'] ?? ''),
                'batch_no' => (string)($stock['batch_no'] ?? ''),
                'quantity' => (float)$stock['quantity'],
                'available_quantity' => (float)$stock['quantity'],  // 使用 quantity 作为可用库存
                'allocated_quantity' => (float)$allocQty,
                'production_date' => (string)($stock['production_date'] ?? ''),
                'expiry_date' => (string)($stock['expiry_date'] ?? '')
            ];
        }
        
        // 检查是否完全分配
        if ($remainingQty > 0) {
            return json([
                'code' => 1, 
                'msg' => '库存不足，请减少出库数量或选择其他仓库',
                'data' => [
                    'product' => $productData,
                    'allocations' => $allocations,
                    'total_allocated' => (float)($quantity - $remainingQty),
                    'required' => (float)$quantity,
                    'remaining' => (float)$remainingQty
                ]
            ]);
        }
        
        // 在autoAllocateStock方法中添加检查
        if (count($allocations) > $maxAllowedRecords) { // 例如设置最大分配记录数
            return json([
                'code' => 1, 
                'msg' => '分配的库存批次过多(' . count($allocations) . '个批次)，超出系统限制(' . $maxAllowedRecords . '个批次)，请手动选择或减少出库数量',
                'data' => [
                    'product' => $productData,
                    'allocations' => array_slice($allocations, 0, $maxAllowedRecords),
                    'total_allocated' => (float)($quantity - $remainingQty),
                    'required' => (float)$quantity,
                    'remaining' => (float)$remainingQty
                ]
            ]);
        }
        
        return json([
            'code' => 0, 
            'msg' => '库存分配成功',
            'data' => [
                'product' => $productData,
                'allocations' => $allocations,
                'total_allocated' => (float)$quantity,
                'required' => (float)$quantity,
                'remaining' => 0
            ]
        ]);
    }

    /**
     * 自动清理过期的预占记录
     * 
     * @return bool 清理结果
     */
    public static function cleanExpiredLocks()
    {
        // 预占功能已废弃，直接返回 true
        return true;
    }

    /**
     * 处理取消出库单
     * 
     * @param int $outboundId 出库单ID
     * @return bool 处理结果
     */
    public static function handleCancelOutbound($outboundId)
    {
        // 预占功能已废弃，直接返回 true
        return true;
    }

    /**
     * 删除出库单
     */
    public function delete()
    {
        $id = input('id/d', 0);
        if (empty($id)) {
            return error('参数错误');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 查询出库单信息
            $outbound = OutboundModel::find($id);
            if (!$outbound) {
                return json(['code' => 1, 'msg' => '出库单不存在']);
                
            }
            
            // 检查状态，只有草稿和已取消的出库单才能删除
            if ($outbound->status != OutboundModel::STATUS_DRAFT && $outbound->status != OutboundModel::STATUS_CANCELED) {
                return json(['code' => 1, 'msg' => '只有草稿和已取消的出库单才能删除']);
            }
            
            // 如果是草稿状态，处理关联的投料单
            if ($outbound->status == OutboundModel::STATUS_DRAFT) {
                // 移除对已废弃的预占功能的依赖
                
                // 处理关联的投料单
                if ($outbound->related_bill_type=='production_feeding') {
                    // 查询投料单信息
                    $feeding = Db::name('production_feeding')
                        ->where('feeding_no', $outbound->related_bill_no)
                        ->find();
                    
                    if ($feeding) {
                        // 获取生产工单ID和单号
                        $productionId = $feeding['production_order_id'];
                        $productionNo = Db::name('produce_order')
                            ->where('id', $productionId)
                            ->value('order_no');
                        
                        if ($productionId && $productionNo) {
                            // 获取出库单明细 production_feeding
                            $outboundDetails = OutboundDetailModel::where('outbound_id', $id)->select();
                            
                            foreach ($outboundDetails as $detail) {
                                // 处理明细
                            }
                        }
                        
                        // 删除投料单 production_id
                        Db::name('production_feeding')->where('feeding_no', $outbound->related_bill_no)->delete();
                        //删除投料单明细
                        Db::name('production_feeding_detail')->where('feeding_id', $feeding['id'])->delete();
                    }
                }
            }
            
            // 删除出库单明细
            Db::name('warehouse_outbound_detail')->where('outbound_id', $id)->delete();
            
            // 删除出库单日志
            Db::name('warehouse_outbound_log')->where('outbound_id', $id)->delete();
            
            // 删除出库单
            $outbound->delete();
            
            // 提交事务
            Db::commit();
            
            // 返回成功
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 反审核出库单（撤销出库，恢复库存）
     */
    public function unapprove()
    {
        if (!$this->request->isAjax() || !$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        $id = input('post.id/d', 0);
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 检查审批权限
        // if (!$this->checkApprovePermission()) {
        //     return json(['code' => 1, 'msg' => '您没有反审核权限']);
        // }
        
        $outbound = OutboundModel::find($id);
        
        if (!$outbound) {
            return json(['code' => 1, 'msg' => '出库单不存在']);
        }
        
        if ($outbound->status != OutboundModel::STATUS_APPROVED) {
            return json(['code' => 1, 'msg' => '只有已审核状态的出库单才能反审核']);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 获取出库单明细
            $details = OutboundDetailModel::where('outbound_id', $id)->select();
            // 遍历每个明细，恢复库存
            foreach ($details as $detail) {
                $actualQuantity = floatval($detail->actual_quantity); // 确保数据类型正确

                // 先检查库存记录是否存在
                $inventory = Db::name('inventory_realtime')
                    ->where('product_id', $detail->product_id)
                    ->where('warehouse_id', $outbound->warehouse_id) // 使用主表的warehouse_id
                    ->find();

                if ($inventory) {
                    // 更新现有库存记录
                    Db::name('inventory_realtime')
                        ->where('product_id', $detail->product_id)
                        ->where('warehouse_id', $outbound->warehouse_id)
                        ->inc('available_quantity', $actualQuantity)
                        ->inc('quantity', $actualQuantity)
                        ->update();
                } else {
                    // 创建新的库存记录
                    Db::name('inventory_realtime')->insert([
                        'product_id' => $detail->product_id,
                        'warehouse_id' => $outbound->warehouse_id,
                        'quantity' => $actualQuantity,
                        'available_quantity' => $actualQuantity,
                        'locked_quantity' => 0,
                        'unit' => $detail->unit ?? '',
                        'cost_price' => 0,
                        'create_time' => time()
                    ]);
                }

                // 添加库存流水记录
                Db::name('inventory_transaction')->insert([
                    'transaction_no' => 'UNAPPROVE_' . $outbound->outbound_no . '_' . time(),
                    'product_id' => $detail->product_id,
                    'warehouse_id' => $outbound->warehouse_id, // 使用主表的warehouse_id
                    'transaction_type' => 'in',
                    'quantity' => $actualQuantity,
                    'before_quantity' => 0, // 这里可以查询当前库存，但为了简化先设为0
                    'after_quantity' => $actualQuantity,
                    'ref_type' => 'outbound_unapprove',
                    'ref_id' => $id,
                    'ref_no' => $outbound->outbound_no,
                    'notes' => "反审核出库单 [{$outbound->outbound_no}] 恢复库存",
                    'created_by' => $this->uid,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            }
            
            // 更新关联的待出库项状态为"处理中"
            $pendingIds = [];
            foreach ($details as $detail) {
                if (!empty($detail->pending_id)) {
                    $pendingIds[] = $detail->pending_id;
                }
            }
            
            if (!empty($pendingIds)) {
                // 使用PendingOutboundModel的updateStatus方法更新状态
                \app\warehouse\model\PendingOutbound::updateStatus(
                    $pendingIds, 
                    \app\warehouse\model\PendingOutbound::STATUS_PROCESSING, 
                    $id // 保持与出库单的关联
                );
            }
            
            // 更新出库单状态为草稿
            $outbound->status = OutboundModel::STATUS_DRAFT;
            $outbound->unapprove_time = time();
            $outbound->unapproved_by = session('tc_admin');
            $outbound->save();
            
            // 添加反审核日志（暂时注释，因为日志表不存在）
            // $outbound->addLog('unapprove', '反审核出库单，恢复库存');

            //反审如果是订单发货。需要调用DeliveryService::completeDelivery方法，完成发货状态更新
            $delivery = OutboundModel::where('id', $id)->find();
            if ($delivery['related_bill_type']=='customer_order_delivery') {
                 // 更改发货单状态   
                 $delivery = DeliveryModel::where('delivery_no', $outbound['related_bill_no'])->find();
                        if ($delivery) {
                            $delivery['status'] = 0;
                            $delivery['handle_time'] = time();
                            $delivery['handler_id'] = $this->uid;
                            $delivery->save();
                        }
               Db::name('customer_order')->where('id', $delivery['order_id'])->update(['status' => 1, 'update_time' => time()]);

               $log=new EditLog();
			   $log->add_log('customer_order',$delivery['order_id'],'反审出库单->仓库操作');
            }

            // 提交事务
            Db::commit();
            
            return json(['code' => 0, 'msg' => '反审核成功，已恢复库存']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['code' => 1, 'msg' => '反审核失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 打印出库单
     */
    public function print()
    {
        $id = input('id', 0);
        if (!$id) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            return '<script>parent.layer.msg("参数错误", {icon: 2}); parent.layer.closeAll();</script>';
        }
        
        // 获取出库单信息
        $outbound = OutboundModel::alias('o')
           // ->join('warehouse w', 'w.id = o.warehouse_id', 'left')
            ->join('admin a', 'a.id = o.created_by', 'left')
            ->field('o.*,  a.name as admin_name')
            ->where('o.id', $id)
            ->find();
        
        if (!$outbound) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '出库单不存在']);
            }
            return '<script>parent.layer.msg("出库单不存在", {icon: 2}); parent.layer.closeAll();</script>';
        }
        
        // 格式化日期时间
       // $outbound['create_time'] = date('Y-m-d H:i:s', $outbound['create_time']);
       // $outbound['outbound_time'] = $outbound['create_time'] ? date('Y-m-d', $outbound['create_time']) : '';
        
        // 获取出库单类型
        $outboundTypes = config('outbound_type');
        $outbound['type_name'] = isset($outboundTypes[$outbound['type']]) ? $outboundTypes[$outbound['type']] : '';
        
        // 获取出库单状态
        $outboundStatus = config('outbound_status');
        $outbound['status_name'] = isset($outboundStatus[$outbound['status']]) ? $outboundStatus[$outbound['status']] : '';
        
        // 获取出库单明细
        $details = OutboundDetailModel::alias('od')
            ->join('product p', 'p.id = od.product_id', 'left')
            ->field('od.*, p.material_code as product_code, p.title as product_name, p.specs, p.unit')
            ->where('od.outbound_id', $id)
            ->order('od.id', 'asc')
            ->select()
            ->toArray();
        
        // 计算总数量和总金额
        $totalQuantity = 0;
        $totalAmount = 0;
        foreach ($details as $detail) {
            $totalQuantity += $detail['quantity'];
            $totalAmount += $detail['amount'];
        }
        
        // 传递数据到视图
        View::assign([
            'outbound' => $outbound,
            'details' => $details,
            'total_quantity' => $totalQuantity,
            'total_amount' => $totalAmount
        ]);
        
        return View::fetch();
    }

    /**
     * 获取客户订单列表
     */
    public function getCustomerOrders()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $keyword = input('keyword', '');
        $page = input('page', 1);
        $limit = input('limit', 10);

        $where = [];
        $where[] = ['status', '>', 0]; // 已提交的订单

        if ($keyword) {
            $where[] = ['order_no|customer_name', 'like', '%' . $keyword . '%'];
        }

        $list = Db::name('customer_order')
            ->where($where)
            ->field('id,order_no as bill_no,order_date as bill_date,customer_name,total_amount,status,notes')
            ->order('create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $data = $list->items();
        foreach ($data as &$item) {
            $item['status_text'] = $this->getOrderStatusText($item['status']);
        }

        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $data
        ]);
    }

    /**
     * 获取生产订单列表
     */
    public function getProductionOrders()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $keyword = input('keyword', '');
        $page = input('page', 1);
        $limit = input('limit', 10);

        $where = [];
        $where[] = ['status', '>', 0]; // 已提交的订单

        if ($keyword) {
            $where[] = ['order_no|department_name', 'like', '%' . $keyword . '%'];
        }

        $list = Db::name('production_order')
            ->where($where)
            ->field('id,order_no as bill_no,plan_start_date as bill_date,department_name as customer_name,total_amount,status,notes')
            ->order('create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $data = $list->items();
        foreach ($data as &$item) {
            $item['status_text'] = $this->getOrderStatusText($item['status']);
        }

        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $data
        ]);
    }

    /**
     * 获取调拨订单列表
     */
    public function getTransferOrders()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $keyword = input('keyword', '');
        $page = input('page', 1);
        $limit = input('limit', 10);

        $where = [];
        $where[] = ['status', '>', 0]; // 已提交的订单

        if ($keyword) {
            $where[] = ['transfer_no|from_warehouse_name', 'like', '%' . $keyword . '%'];
        }

        $list = Db::name('transfer_order')
            ->where($where)
            ->field('id,transfer_no as bill_no,transfer_date as bill_date,from_warehouse_name as customer_name,total_amount,status,notes')
            ->order('create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $data = $list->items();
        foreach ($data as &$item) {
            $item['status_text'] = $this->getOrderStatusText($item['status']);
        }

        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $data
        ]);
    }

    /**
     * 获取退货订单列表
     */
    public function getReturnOrders()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $keyword = input('keyword', '');
        $page = input('page', 1);
        $limit = input('limit', 10);

        $where = [];
        $where[] = ['status', '>', 0]; // 已提交的订单

        if ($keyword) {
            $where[] = ['return_no|customer_name', 'like', '%' . $keyword . '%'];
        }

        $list = Db::name('return_order')
            ->where($where)
            ->field('id,return_no as bill_no,return_date as bill_date,customer_name,total_amount,status,notes')
            ->order('create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        $data = $list->items();
        foreach ($data as &$item) {
            $item['status_text'] = $this->getOrderStatusText($item['status']);
        }

        return json([
            'code' => 0,
            'msg' => '',
            'count' => $list->total(),
            'data' => $data
        ]);
    }

    /**
     * 获取单据明细
     */
    public function getBillDetails()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $billId = input('bill_id', 0, 'intval');
        $billType = input('bill_type', '');

        if ($billId <= 0 || empty($billType)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        $details = [];

        try {
            switch ($billType) {
                case 'customer_order':
                    $details = $this->getCustomerOrderDetails($billId);
                    break;
                case 'production_order':
                    $details = $this->getProductionOrderDetails($billId);
                    break;
                case 'transfer_order':
                    $details = $this->getTransferOrderDetails($billId);
                    break;
                case 'return_order':
                    $details = $this->getReturnOrderDetails($billId);
                    break;
                default:
                    return json(['code' => 1, 'msg' => '不支持的单据类型：' . $billType]);
            }
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取单据明细失败：' . $e->getMessage()]);
        }

        return json([
            'code' => 0,
            'msg' => '',
            'data' => $details
        ]);
    }

    /**
     * 获取客户订单明细
     */
    private function getCustomerOrderDetails($orderId)
    {
        $details = Db::name('customer_order_detail')
            ->alias('d')
            ->join('product p', 'p.id = d.product_id')
            ->where('d.order_id', $orderId)
            ->where('d.quantity > d.delivered_quantity') // 只获取未完全出库的明细
            ->field('d.id,d.product_id,p.title as product_name,p.material_code as product_code,p.specs,p.unit,d.quantity,d.delivered_quantity,d.price,d.notes')
            ->select();

        $result = [];
        foreach ($details as $detail) {
            $remainQuantity = $detail['quantity'] - $detail['delivered_quantity'];
            if ($remainQuantity > 0) {
                $result[] = [
                    'product' => [
                        'id' => $detail['product_id'],
                        'title' => $detail['product_name'],
                        'material_code' => $detail['product_code'],
                        'specs' => $detail['specs'],
                        'unit' => $detail['unit']
                    ],
                    'quantity' => $remainQuantity,
                    'price' => $detail['price'],
                    'notes' => $detail['notes']
                ];
            }
        }

        return $result;
    }

    /**
     * 获取生产订单明细
     */
    private function getProductionOrderDetails($orderId)
    {
        // 生产订单的物料需求
        $details = Db::name('production_order_material')
            ->alias('m')
            ->join('product p', 'p.id = m.product_id')
            ->where('m.order_id', $orderId)
            ->where('m.required_quantity > m.issued_quantity') // 只获取未完全领料的明细
            ->field('m.id,m.product_id,p.title as product_name,p.material_code as product_code,p.specs,p.unit,m.required_quantity,m.issued_quantity,m.unit_cost as price,m.notes')
            ->select();

        $result = [];
        foreach ($details as $detail) {
            $remainQuantity = $detail['required_quantity'] - $detail['issued_quantity'];
            if ($remainQuantity > 0) {
                $result[] = [
                    'product' => [
                        'id' => $detail['product_id'],
                        'title' => $detail['product_name'],
                        'material_code' => $detail['product_code'],
                        'specs' => $detail['specs'],
                        'unit' => $detail['unit']
                    ],
                    'quantity' => $remainQuantity,
                    'price' => $detail['price'],
                    'notes' => $detail['notes']
                ];
            }
        }

        return $result;
    }

    /**
     * 获取调拨订单明细
     */
    private function getTransferOrderDetails($orderId)
    {
        $details = Db::name('transfer_order_detail')
            ->alias('d')
            ->join('product p', 'p.id = d.product_id')
            ->where('d.order_id', $orderId)
            ->where('d.quantity > d.transferred_quantity') // 只获取未完全调拨的明细
            ->field('d.id,d.product_id,p.title as product_name,p.material_code as product_code,p.specs,p.unit,d.quantity,d.transferred_quantity,d.unit_cost as price,d.notes')
            ->select();

        $result = [];
        foreach ($details as $detail) {
            $remainQuantity = $detail['quantity'] - $detail['transferred_quantity'];
            if ($remainQuantity > 0) {
                $result[] = [
                    'product' => [
                        'id' => $detail['product_id'],
                        'title' => $detail['product_name'],
                        'material_code' => $detail['product_code'],
                        'specs' => $detail['specs'],
                        'unit' => $detail['unit']
                    ],
                    'quantity' => $remainQuantity,
                    'price' => $detail['price'],
                    'notes' => $detail['notes']
                ];
            }
        }

        return $result;
    }

    /**
     * 获取退货订单明细
     */
    private function getReturnOrderDetails($orderId)
    {
        $details = Db::name('return_order_detail')
            ->alias('d')
            ->join('product p', 'p.id = d.product_id')
            ->where('d.order_id', $orderId)
            ->where('d.quantity > d.returned_quantity') // 只获取未完全退货的明细
            ->field('d.id,d.product_id,p.title as product_name,p.material_code as product_code,p.specs,p.unit,d.quantity,d.returned_quantity,d.price,d.notes')
            ->select();

        $result = [];
        foreach ($details as $detail) {
            $remainQuantity = $detail['quantity'] - $detail['returned_quantity'];
            if ($remainQuantity > 0) {
                $result[] = [
                    'product' => [
                        'id' => $detail['product_id'],
                        'title' => $detail['product_name'],
                        'material_code' => $detail['product_code'],
                        'specs' => $detail['specs'],
                        'unit' => $detail['unit']
                    ],
                    'quantity' => $remainQuantity,
                    'price' => $detail['price'],
                    'notes' => $detail['notes']
                ];
            }
        }

        return $result;
    }

    /**
     * 获取订单状态文本
     */
    private function getOrderStatusText($status)
    {
        $statusMap = [
            0 => '草稿',
            1 => '已提交',
            2 => '已审核',
            3 => '执行中',
            4 => '已完成',
            5 => '已取消'
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 执行出库页面
     */
    public function execute()
    {
        $id = input('id/d', 0);
        if (!$id) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            return '<script>parent.layer.msg("参数错误", {icon: 2}); parent.layer.closeAll();</script>';
        }

        // 获取出库单信息
        $outbound = OutboundModel::with(['customer'])->find($id);
        if (!$outbound) {
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '出库单不存在']);
            }
            return '<script>parent.layer.msg("出库单不存在", {icon: 2}); parent.layer.closeAll();</script>';
        }

        // 检查状态是否可以执行出库
        if (!in_array($outbound->status, [2, 3])) { // 已审核或部分出库状态
            if (request()->isAjax()) {
                return json(['code' => 1, 'msg' => '当前状态不允许执行出库']);
            }
            return '<script>parent.layer.msg("当前状态不允许执行出库", {icon: 2}); parent.layer.closeAll();</script>';
        }

        if (request()->isPost()) {
            // 添加调试日志
            \think\facade\Log::info('execute POST请求', [
                'post_data' => input('post.'),
                'all_input' => input(),
                'outbound_id' => $id
            ]);
            return $this->doExecute();
        }

        // 获取出库单明细
        $details = Db::name('outbound_detail')
            ->alias('od')
            ->leftJoin('product p', 'od.product_id = p.id')
            ->field('od.*, p.title as product_name, p.material_code as product_code,
                    p.specs as specification, p.unit')
            ->where('od.outbound_id', $id)
            ->select()
            ->toArray();

        // 获取仓库信息
        $warehouse = Db::name('warehouse')->where('id', $outbound->warehouse_id)->find();

        // 添加文字转换
        $outbound->outbound_type_text = OutboundModel::getTypeText($outbound->outbound_type);
        $outbound->status_text = OutboundModel::getStatusText($outbound->status);
        $outbound->priority_text = OutboundModel::getPriorityText($outbound->priority);

        // 获取每个产品的当前库存和锁定分配信息
        foreach ($details as &$detail) {
            // 使用新的库存系统查询实时库存
            $inventory = \app\warehouse\model\InventoryRealtime::where([
                'product_id' => $detail['product_id'],
                'warehouse_id' => $outbound->warehouse_id
            ])->find();

            $detail['current_stock'] = $inventory ? $inventory->available_quantity : 0;
            $detail['remaining_quantity'] = $detail['quantity'] - $detail['actual_quantity'];

            // 查询锁定信息 - 多种查询策略
            $lockInfo = null;

            // 策略1：通过业务关联查找
            if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
                $lockInfo = Db::name('inventory_lock')
                    ->alias('il')
                    ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
                    ->where('il.product_id', $detail['product_id'])
                    ->where('il.ref_type', $outbound->ref_type)
                    ->where('il.ref_id', $outbound->ref_id)
                    ->where('il.status', 1) // 已锁定状态
                    ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
                    ->find();
            }

            // 策略2：通过出库单关联查找
            if (!$lockInfo) {
                $lockInfo = Db::name('inventory_lock')
                    ->alias('il')
                    ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
                    ->where('il.product_id', $detail['product_id'])
                    ->where('il.ref_type', 'outbound')
                    ->where('il.ref_id', $outbound->id)
                    ->where('il.status', 1) // 已锁定状态
                    ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
                    ->find();
            }

            // 策略3：查找该产品的任何锁定记录（最后的备选方案）
            if (!$lockInfo) {
                $lockInfo = Db::name('inventory_lock')
                    ->alias('il')
                    ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
                    ->where('il.product_id', $detail['product_id'])
                    ->where('il.status', 1) // 已锁定状态
                    ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
                    ->order('il.create_time desc')
                    ->find();
            }

            $detail['lock_info'] = $lockInfo;

            // 查询分配信息 - 多种查询策略
            $allocationInfo = null;

            // 策略1：通过业务关联查找
            if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
                $allocationInfo = Db::name('inventory_allocation_request')
                    ->alias('iar')
                    ->leftJoin('warehouse w', 'iar.warehouse_id = w.id')
                    ->where('iar.product_id', $detail['product_id'])
                    ->where('iar.ref_type', $outbound->ref_type)
                    ->where('iar.ref_id', $outbound->ref_id)
                    ->where('iar.status', 2) // 已分配状态
                    ->field('iar.warehouse_id, w.name as warehouse_name, iar.quantity as allocated_quantity')
                    ->find();
            }

            // 策略2：通过出库单关联查找
            if (!$allocationInfo) {
                $allocationInfo = Db::name('inventory_allocation_request')
                    ->alias('iar')
                    ->leftJoin('warehouse w', 'iar.warehouse_id = w.id')
                    ->where('iar.product_id', $detail['product_id'])
                    ->where('iar.ref_type', 'outbound')
                    ->where('iar.ref_id', $outbound->id)
                    ->where('iar.status', 2) // 已分配状态
                    ->field('iar.warehouse_id, w.name as warehouse_name, iar.quantity as allocated_quantity')
                    ->find();
            }

            // 策略3：查找该产品的任何分配记录（最后的备选方案）
            if (!$allocationInfo) {
                $allocationInfo = Db::name('inventory_allocation_request')
                    ->alias('iar')
                    ->leftJoin('warehouse w', 'iar.warehouse_id = w.id')
                    ->where('iar.product_id', $detail['product_id'])
                    ->where('iar.status', 2) // 已分配状态
                    ->field('iar.warehouse_id, w.name as warehouse_name, iar.quantity as allocated_quantity')
                    ->order('iar.request_time desc')
                    ->find();
            }

            $detail['allocation_info'] = $allocationInfo;
        }

        // 模板赋值
        View::assign([
            'outbound' => $outbound,
            'details' => $details,
            'warehouse' => $warehouse
        ]);

        return View::fetch('execute');
    }

    /**
     * 执行出库操作 - 基于锁库和分配记录
     */
    private function doExecute()
    {
        // 多种方式获取出库单ID
        $outboundId = input('post.outbound_id/d', 0);
        if (!$outboundId) {
            $outboundId = input('post.id/d', 0);
        }
        if (!$outboundId) {
            $outboundId = input('outbound_id/d', 0);
        }
        if (!$outboundId) {
            $outboundId = input('id/d', 0);
        }

        if (!$outboundId) {
            // 调试信息
            $allInput = input();
            \think\facade\Log::error('doExecute参数错误', [
                'all_input' => $allInput,
                'post_data' => input('post.'),
                'get_data' => input('get.')
            ]);
            return json(['code' => 1, 'msg' => '参数错误，未找到出库单ID。传入参数：' . json_encode($allInput)]);
        }

        Db::startTrans();
        try {
            // 获取出库单
            $outbound = OutboundModel::find($outboundId);
            if (!$outbound) {
                throw new \Exception('出库单不存在');
            }

            // 获取出库单明细
            $details = Db::name('outbound_detail')->where('outbound_id', $outboundId)->select();
            if (empty($details)) {
                throw new \Exception('出库单明细不存在');
            }

            // 使用库存服务处理出库
            $inventoryService = new \app\warehouse\service\InventoryRealtimeService();

            foreach ($details as $detail) {
                $productId = $detail['product_id'];
                $quantity = $detail['quantity'];

                // 确定实际的出库仓库ID
                $warehouseId = $this->determineWarehouseId($productId, $outbound);

                // 1. 检查库存是否充足
                if (!$inventoryService->checkStock($productId, $warehouseId, $quantity)) {
                    throw new \Exception("产品【{$detail['product_name']}】库存不足");
                }

                // 2. 检查是否有锁定库存，优先使用锁定库存
                $hasLockedStock = $this->hasLockedStock($productId, $warehouseId, $outbound);

                if ($hasLockedStock) {
                    // 使用锁定库存（会自动减少锁定数量和总库存）
                    $inventoryService->useLockedStock(
                        $productId,
                        $warehouseId,
                        $quantity,
                        'outbound',
                        $outboundId,
                        $outbound->outbound_no,
                        '出库单执行出库（使用锁定库存）',
                        $this->uid
                    );
                } else {
                    // 没有锁定库存，直接减少可用库存
                    $inventoryService->decreaseStock(
                        $productId,
                        $warehouseId,
                        $quantity,
                        'outbound',
                        $outboundId,
                        $outbound->outbound_no,
                        '出库单执行出库',
                        $this->uid
                    );
                }

                // 3. 更新明细的实际出库数量
                Db::name('outbound_detail')
                    ->where('id', $detail['id'])
                    ->update([
                        'actual_quantity' => $quantity,
                        'update_time' => time()
                    ]);

                // 4. 处理相关的锁定记录（如果有）
                $this->processLockRecords($productId, $warehouseId, $quantity, $outbound);

                // 5. 处理相关的分配记录（如果有）
                $this->processAllocationRecords($productId, $warehouseId, $quantity, $outbound);
            }

            // 更新出库单状态为全部出库
            Db::name('outbound')
                ->where('id', $outboundId)
                ->update([
                    'status' => 4, // 全部出库
                    'outbound_by' => $this->uid,
                    'outbound_time' => time(),
                    'update_time' => time()
                ]);

            // 处理关联业务单据状态更新
            $this->updateRelatedBusinessStatus($outbound);

            // 重新查询最新的出库明细数据（包含更新后的actual_quantity）
            $latestDetails = Db::name('outbound_detail')->where('outbound_id', $outboundId)->select();

            // 回写实际出库数量到关联业务单据
            $this->updateRelatedBusinessActualQuantity($outbound, $latestDetails);

            Db::commit();

            return json([
                'code' => 0,
                'msg' => '出库执行成功',
                'data' => [
                    'status' => 4,
                    'outbound_time' => time()
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 更新关联业务单据状态
     * @param object $outbound 出库单对象
     */
    private function updateRelatedBusinessStatus($outbound)
    {
        try {
            // 处理生产领料单出库
            if ($outbound->ref_type == 'production_material_request') {
                $this->updateProductionOrderMaterialStatus($outbound);
            }

            // 处理其他类型的关联业务单据
            // 可以在这里添加其他业务类型的处理逻辑

        } catch (\Exception $e) {
            \think\facade\Log::error('更新关联业务单据状态失败', [
                'outbound_id' => $outbound->id,
                'ref_type' => $outbound->ref_type,
                'ref_no' => $outbound->ref_no,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新生产订单物料状态
     * @param object $outbound 出库单对象
     */
    private function updateProductionOrderMaterialStatus($outbound)
    {
        try {
            // 通过领料单号查找生产订单ID
            $materialRequest = Db::name('production_material_request')
                ->where('request_no', $outbound->ref_no)
                ->find();

            if (!$materialRequest) {
                \think\facade\Log::warning('未找到对应的生产领料单', [
                    'outbound_id' => $outbound->id,
                    'request_no' => $outbound->ref_no
                ]);
                return;
            }

            $productionOrderId = $materialRequest['production_order_id'];

            // 检查该生产订单的所有领料单是否都已出库完成
            $allMaterialRequests = Db::name('production_material_request')
                ->where('production_order_id', $productionOrderId)
                ->select()
                ->toArray();

            $allCompleted = true;
            foreach ($allMaterialRequests as $request) {
                // 检查该领料单对应的出库单是否都已完成
                $outboundCount = Db::name('outbound')
                    ->where('ref_type', 'production_material_request')
                    ->where('ref_no', $request['request_no'])
                    ->where('status', 4) // 已出库
                    ->count();

                $totalOutboundCount = Db::name('outbound')
                    ->where('ref_type', 'production_material_request')
                    ->where('ref_no', $request['request_no'])
                    ->count();

                if ($outboundCount < $totalOutboundCount) {
                    $allCompleted = false;
                    break;
                }
            }

            // 更新生产订单的领料状态
            $feedingFlag = $allCompleted ? 2 : 1; // 2=投料完成, 1=部分投料

            Db::name('produce_order')
                ->where('id', $productionOrderId)
                ->update([
                    'feeding_flag' => $feedingFlag,
                    'update_time' => time()
                ]);

            \think\facade\Log::info('更新生产订单领料状态成功', [
                'production_order_id' => $productionOrderId,
                'feeding_flag' => $feedingFlag,
                'outbound_id' => $outbound->id
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('更新生产订单物料状态失败', [
                'outbound_id' => $outbound->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 检查是否有锁定库存
     */
    private function hasLockedStock($productId, $warehouseId, $outbound)
    {
        // 检查是否有相关的锁定记录
        if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
            $lockCount = Db::name('inventory_lock')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('ref_type', $outbound->ref_type)
                ->where('ref_id', $outbound->ref_id)
                ->where('status', InventoryLock::STATUS_LOCKED) // 已锁定状态
                ->count();

            if ($lockCount > 0) {
                return true;
            }
        }

        // 检查实时库存表中是否有锁定数量
        $inventory = \app\warehouse\model\InventoryRealtime::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();

        return $inventory && $inventory->locked_quantity > 0;
    }

    /**
     * 确定实际的出库仓库ID
     */
    private function determineWarehouseId($productId, $outbound)
    {
        // 优先从锁定记录中获取仓库ID
        if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
            $lockRecord = Db::name('inventory_lock')
                ->where('product_id', $productId)
                ->where('ref_type', $outbound->ref_type)
                ->where('ref_id', $outbound->ref_id)
                ->where('status', InventoryLock::STATUS_LOCKED) // 已锁定状态
                ->find();

            if ($lockRecord && $lockRecord['warehouse_id']) {
                return $lockRecord['warehouse_id'];
            }
        }

        // 其次从分配记录中获取仓库ID
        if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
            $allocationRecord = Db::name('inventory_allocation_request')
                ->where('product_id', $productId)
                ->where('ref_type', $outbound->ref_type)
                ->where('ref_id', $outbound->ref_id)
                ->where('status', 2) // 已分配状态
                ->find();

            if ($allocationRecord && $allocationRecord['warehouse_id']) {
                return $allocationRecord['warehouse_id'];
            }
        }

        // 最后使用出库单默认仓库ID
        return $outbound->warehouse_id;
    }

    /**
     * 处理锁定记录
     */
    private function processLockRecords($productId, $warehouseId, $quantity, $outbound)
    {
        // 查找相关的锁定记录 - 多种策略查找
        $lockRecords = [];

        // 策略1：通过业务关联查找（ref_type + ref_id）
        if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
            $lockRecords = Db::name('inventory_lock')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('ref_type', $outbound->ref_type)
                ->where('ref_id', $outbound->ref_id)
                ->where('status', InventoryLock::STATUS_LOCKED) // 已锁定状态
                ->select()
                ->toArray();
        }

        // 策略2：通过出库单关联查找
        if (empty($lockRecords)) {
            $lockRecords = Db::name('inventory_lock')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('ref_type', 'outbound')
                ->where('ref_id', $outbound->id)
                ->where('status', InventoryLock::STATUS_LOCKED) // 已锁定状态
                ->select()
                ->toArray();
        }

        // 策略3：只通过产品ID和仓库ID查找（最宽松的条件）
        if (empty($lockRecords)) {
            $lockRecords = Db::name('inventory_lock')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->where('status', InventoryLock::STATUS_LOCKED) // 已锁定状态
                ->order('create_time desc')
                ->limit(1) // 只取最新的一条
                ->select()
                ->toArray();
        }

        // 记录调试信息
        \think\facade\Log::info('processLockRecords', [
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'quantity' => $quantity,
            'outbound_ref_type' => $outbound->ref_type,
            'outbound_ref_id' => $outbound->ref_id,
            'found_records' => count($lockRecords),
            'lock_records' => $lockRecords
        ]);

        // 按实际出库数量处理锁定记录
        $remainingQuantity = $quantity;

        foreach ($lockRecords as $lock) {
            if ($remainingQuantity <= 0) {
                break; // 已处理完所有出库数量
            }

            $lockQuantity = floatval($lock['quantity']);
            $useQuantity = min($remainingQuantity, $lockQuantity);
            $newLockQuantity = $lockQuantity - $useQuantity;

            if ($newLockQuantity > 0) {
                // 部分使用：减少锁定数量，保持锁定状态
                $updateResult = Db::name('inventory_lock')
                    ->where('id', $lock['id'])
                    ->update([
                        'quantity' => $newLockQuantity,
                        'update_time' => time()
                    ]);

                \think\facade\Log::info('部分使用锁定记录', [
                    'lock_id' => $lock['id'],
                    'original_quantity' => $lockQuantity,
                    'used_quantity' => $useQuantity,
                    'remaining_quantity' => $newLockQuantity,
                    'update_result' => $updateResult
                ]);
            } else {
                // 完全使用：标记为已使用
                $updateResult = Db::name('inventory_lock')
                    ->where('id', $lock['id'])
                    ->update([
                        'status' => InventoryLock::STATUS_USED,
                        'update_time' => time()
                    ]);

                \think\facade\Log::info('完全使用锁定记录', [
                    'lock_id' => $lock['id'],
                    'used_quantity' => $useQuantity,
                    'update_result' => $updateResult
                ]);
            }

            $remainingQuantity -= $useQuantity;
        }
    }

    /**
     * 处理分配记录
     */
    private function processAllocationRecords($productId, $warehouseId, $quantity, $outbound)
    {
        // 查找相关的分配记录 - 优先通过业务关联查找
        $allocationRecords = [];

        if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
            $allocationRecords = Db::name('inventory_allocation_request')
                ->where('product_id', $productId)
                ->where('ref_type', $outbound->ref_type)
                ->where('ref_id', $outbound->ref_id)
                ->where('status', 2) // 已分配状态
                ->select()
                ->toArray();
        }

        // 如果没有找到，尝试通过出库单关联查找
        if (empty($allocationRecords)) {
            $allocationRecords = Db::name('inventory_allocation_request')
                ->where('product_id', $productId)
                ->where('ref_type', 'outbound')
                ->where('ref_id', $outbound->id)
                ->where('status', 2) // 已分配状态
                ->select()
                ->toArray();
        }

        foreach ($allocationRecords as $allocation) {
            // 更新分配记录为已完成
            Db::name('inventory_allocation_request')
                ->where('id', $allocation['id'])
                ->update([
                    'status' => 3, // 已完成
                    'completed_quantity' => $allocation['quantity'],
                    'completed_time' => time(),
                    'update_time' => time()
                ]);
        }
    }

    /**
     * 回写实际出库数量到关联业务单据
     * @param object $outbound 出库单对象
     * @param array $details 出库明细数组
     */
    private function updateRelatedBusinessActualQuantity($outbound, $details)
    {
        try {
            // 处理生产领料单出库
            if ($outbound->ref_type == 'production_material_request') {
                $this->updateMaterialRequestActualQuantity($outbound, $details);
            }

            // 可以在这里添加其他业务类型的处理逻辑

        } catch (\Exception $e) {
            \think\facade\Log::error('回写实际出库数量失败', [
                'outbound_id' => $outbound->id,
                'ref_type' => $outbound->ref_type,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新生产领料单明细的实际出库数量
     * @param object $outbound 出库单对象
     * @param array $details 出库明细数组
     */
    private function updateMaterialRequestActualQuantity($outbound, $details)
    {
        try {
            \think\facade\Log::info('开始回写生产领料单实际出库数量', [
                'outbound_id' => $outbound->id,
                'request_no' => $outbound->ref_no,
                'details_count' => count($details)
            ]);

            foreach ($details as $detail) {
                $actualQty = floatval($detail['actual_quantity']);

                \think\facade\Log::info('回写明细', [
                    'request_no' => $outbound->ref_no,
                    'material_id' => $detail['product_id'],
                    'detail_array' => $detail,
                    'actual_quantity_raw' => $detail['actual_quantity'],
                    'actual_quantity_float' => $actualQty
                ]);

                // 检查是否存在匹配的记录
                $exists = Db::name('production_material_request_detail')
                    ->where('request_no', $outbound->ref_no)
                    ->where('material_id', $detail['product_id'])
                    ->count();

                \think\facade\Log::info('匹配记录数', [
                    'request_no' => $outbound->ref_no,
                    'material_id' => $detail['product_id'],
                    'exists' => $exists
                ]);

                if ($exists > 0) {
                    // 根据产品ID和出库数量更新领料单明细的实际出库数量
                    $result = Db::name('production_material_request_detail')
                        ->where('request_no', $outbound->ref_no)
                        ->where('material_id', $detail['product_id'])
                        ->update([
                            'actual_quantity' => Db::raw('actual_quantity + ' . $actualQty),
                            'update_time' => time()
                        ]);

                    \think\facade\Log::info('更新结果', [
                        'request_no' => $outbound->ref_no,
                        'material_id' => $detail['product_id'],
                        'affected_rows' => $result
                    ]);
                } else {
                    \think\facade\Log::warning('未找到匹配的领料单明细记录', [
                        'request_no' => $outbound->ref_no,
                        'material_id' => $detail['product_id']
                    ]);
                }
            }

            \think\facade\Log::info('更新生产领料单实际出库数量成功', [
                'outbound_id' => $outbound->id,
                'request_no' => $outbound->ref_no,
                'details_count' => count($details)
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('更新生产领料单实际出库数量失败', [
                'outbound_id' => $outbound->id,
                'request_no' => $outbound->ref_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
 }
