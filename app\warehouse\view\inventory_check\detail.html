{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<style>
    .info-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .info-item {
        display: inline-block;
        margin-right: 30px;
        margin-bottom: 10px;
    }
    .info-label {
        font-weight: bold;
        color: #666;
    }
    .status-pending { color: #FF5722; }
    .status-checking { color: #2196F3; }
    .status-completed { color: #4CAF50; }
    .status-cancelled { color: #9E9E9E; }
    .difference-surplus { color: #4CAF50; font-weight: bold; }
    .difference-deficit { color: #f56c6c; font-weight: bold; }
    .difference-normal { color: #666; }
    .layui-table-cell {
        height: auto !important;
        white-space: normal;
    }
    .quantity-input {
        width: 100px;
    }
</style>

<div class="layui-fluid" style="padding: 20px;">
        <!-- 盘点单基本信息 -->
        <div class="info-card">
            <h3>盘点单信息</h3>
            <div class="info-item">
                <span class="info-label">盘点单号：</span>
                <span id="checkNo"></span>
            </div>
            <div class="info-item">
                <span class="info-label">仓库：</span>
                <span id="warehouseName"></span>
            </div>
            <div class="info-item">
                <span class="info-label">状态：</span>
                <span id="status"></span>
            </div>
            <div class="info-item">
                <span class="info-label">产品总数：</span>
                <span id="totalProducts"></span>
            </div>
            <div class="info-item">
                <span class="info-label">已盘点：</span>
                <span id="checkedProducts"></span>
            </div>
            <div class="info-item">
                <span class="info-label">创建人：</span>
                <span id="creator"></span>
            </div>
            <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span id="createTime"></span>
            </div>
            <div class="info-item" style="width: 100%;">
                <span class="info-label">备注：</span>
                <span id="notes"></span>
            </div>
        </div>

        <!-- 盘点明细 -->
        <div class="layui-card">
            <div class="layui-card-header">
                <h3>盘点明细</h3>
                <div style="float: right;">
                    <button class="layui-btn layui-btn-sm" id="saveAllBtn" style="display: none;">
                        <i class="layui-icon layui-icon-ok"></i> 保存所有修改
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="detailTable" lay-filter="detailTable"></table>
            </div>
        </div>
    </div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
    <!-- 实际数量输入模板 -->
    <script type="text/html" id="actualQuantityTpl">
        {{# if(d.check_status == 1 && !d.readonly) { }}
            <input type="number" class="layui-input quantity-input" value="{{d.actual_quantity || 0}}" 
                   data-id="{{d.id}}" step="0.01" min="0" onchange="updateQuantity(this)">
        {{# } else { }}
            {{d.actual_quantity || 0}}
        {{# } }}
    </script>

    <!-- 差异模板 -->
    <script type="text/html" id="differenceTpl">
        {{# var diff = d.difference || 0; }}
        {{# if(diff > 0) { }}
            <span class="difference-surplus">+{{diff}}</span>
        {{# } else if(diff < 0) { }}
            <span class="difference-deficit">{{diff}}</span>
        {{# } else { }}
            <span class="difference-normal">{{diff}}</span>
        {{# } }}
    </script>

    <!-- 备注输入模板 -->
    <script type="text/html" id="notesTpl">
        {{# if(d.check_status == 1 && !d.readonly) { }}
            <input type="text" class="layui-input" value="{{d.notes || ''}}" 
                   data-id="{{d.id}}" placeholder="备注" onchange="updateNotes(this)">
        {{# } else { }}
            {{d.notes || ''}}
        {{# } }}
    </script>

    <script>
        layui.use(['table', 'layer', 'util', 'tool'], function(){
            var table = layui.table;
            var layer = layui.layer;
            var util = layui.util;
            var tool = layui.tool;

            var checkId = getUrlParam('id');
            var readonly = getUrlParam('readonly') == '1';
            var checkData = null;
            var pendingUpdates = {};

            // 加载盘点详情
            loadCheckDetail();

            // 渲染明细表格
            function renderDetailTable(details) {
                table.render({
                    elem: '#detailTable',
                    data: details.map(function(item) {
                        item.check_status = checkData.status;
                        item.readonly = readonly;
                        return item;
                    }),
                    page: false,
                    cols: [[
                        {field: 'product.title', title: '产品名称', width: 200},
                        {field: 'product.material_code', title: '产品编号', width: 120},
                        {field: 'product.specs', title: '规格', width: 120},
                        {field: 'unit', title: '单位', width: 80, align: 'center'},
                        {field: 'system_quantity', title: '系统库存', width: 100, align: 'center'},
                        {field: 'actual_quantity', title: '实际库存', width: 120, align: 'center', templet: '#actualQuantityTpl'},
                        {field: 'difference', title: '差异', width: 100, align: 'center', templet: '#differenceTpl'},
                        {field: 'cost_price', title: '成本价', width: 100, align: 'center', templet: function(d){
                            return '¥' + (d.cost_price || 0);
                        }},
                        {field: 'notes', title: '备注', minWidth: 150, templet: '#notesTpl'}
                    ]]
                });
            }

            // 加载盘点详情
            function loadCheckDetail() {
                tool.get('/warehouse/inventory_check/detail?id=' + checkId, function(res) {
                    if (res.code == 0) {
                        checkData = res.data.check;
                        var details = res.data.details;

                        // 填充基本信息
                        $('#checkNo').text(checkData.check_no);
                        $('#warehouseName').text(checkData.warehouse ? checkData.warehouse.name : '');
                        $('#status').html(getStatusHtml(checkData.status));
                        $('#totalProducts').text(checkData.total_products || 0);
                        $('#checkedProducts').text(checkData.checked_products || 0);
                        $('#creator').text(checkData.creator ? checkData.creator.name : '');
                        $('#createTime').text(util.toDateString(checkData.create_time * 1000, 'yyyy-MM-dd HH:mm:ss'));
                        $('#notes').text(checkData.notes || '无');

                        // 渲染明细表格
                        renderDetailTable(details);

                        // 显示保存按钮（如果是盘点中状态且非只读）
                        if (checkData.status == 1 && !readonly) {
                            $('#saveAllBtn').show();
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
            }

            // 获取状态HTML
            function getStatusHtml(status) {
                var statusMap = {
                    0: '<span class="status-pending">待盘点</span>',
                    1: '<span class="status-checking">盘点中</span>',
                    2: '<span class="status-completed">已完成</span>',
                    3: '<span class="status-cancelled">已取消</span>'
                };
                return statusMap[status] || '未知';
            }

            // 保存所有修改
            $('#saveAllBtn').click(function() {
                if (Object.keys(pendingUpdates).length === 0) {
                    layer.msg('没有需要保存的修改', {icon: 3});
                    return;
                }

                var updates = [];
                for (var detailId in pendingUpdates) {
                    updates.push({
                        detail_id: detailId,
                        actual_quantity: pendingUpdates[detailId].actual_quantity,
                        notes: pendingUpdates[detailId].notes || ''
                    });
                }

                var index = layer.load(2);
                var completed = 0;
                var total = updates.length;

                updates.forEach(function(update) {
                    tool.post('/warehouse/inventory_check/updateDetail', update, function(res) {
                        completed++;
                        if (completed === total) {
                            layer.close(index);
                            if (res.code == 0) {
                                layer.msg('保存成功', {icon: 1});
                                pendingUpdates = {};
                                loadCheckDetail(); // 重新加载数据
                            } else {
                                layer.msg('保存失败: ' + res.msg, {icon: 2});
                            }
                        }
                    });
                });
            });

            // 获取URL参数
            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            }
        });

        // 更新数量
        function updateQuantity(input) {
            var detailId = $(input).data('id');
            var actualQuantity = parseFloat($(input).val()) || 0;
            
            if (!pendingUpdates[detailId]) {
                pendingUpdates[detailId] = {};
            }
            pendingUpdates[detailId].actual_quantity = actualQuantity;

            // 计算并显示差异
            var row = $(input).closest('tr');
            var systemQuantity = parseFloat(row.find('td').eq(4).text()) || 0;
            var difference = actualQuantity - systemQuantity;
            
            var diffCell = row.find('td').eq(6);
            if (difference > 0) {
                diffCell.html('<span class="difference-surplus">+' + difference + '</span>');
            } else if (difference < 0) {
                diffCell.html('<span class="difference-deficit">' + difference + '</span>');
            } else {
                diffCell.html('<span class="difference-normal">' + difference + '</span>');
            }
        }

        // 更新备注
        function updateNotes(input) {
            var detailId = $(input).data('id');
            var notes = $(input).val();
            
            if (!pendingUpdates[detailId]) {
                pendingUpdates[detailId] = {};
            }
            pendingUpdates[detailId].notes = notes;
        }
    </script>
{/block}
