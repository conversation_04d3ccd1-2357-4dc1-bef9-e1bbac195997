{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>待分配</li>
                <li>部分分配</li>
                <li>完全分配</li>
                <li>已取消</li>
            </ul>
        </div>
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
                    
        <div class="layui-input-inline" style="width:150px;">
            <select name="ref_type">
                <option value="">业务类型</option>
                <option value="customer_order">客户订单</option>
                <option value="production_order">生产订单</option>
                <option value="purchase_order">采购订单</option>
                <option value="transfer">调拨单</option>
                <option value="quality_check">质检</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="status">
                <option value="">分配状态</option>
                <option value="1">待分配</option>
                <option value="2">部分分配</option>
                <option value="3">完全分配</option>
                <option value="4">已取消</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="create_time" placeholder="选择创建时间" readonly name="create_time">
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，产品名称/业务单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_allocation_manage" lay-filter="table_allocation_manage"></table>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="statistics"><i class="layui-icon layui-icon-chart"></i>统计信息</button>
        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="cleanup"><i class="layui-icon layui-icon-delete"></i>清理过期</button>
    </div>
</script>
                    
{/block}

<!-- 脚本 -->
{block name="script"}

<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus;

        //tab切换
        element.on('tab(tab)', function(data){
            var statusMap = {0: "", 1: "1", 2: "2", 3: "3", 4: "4"};
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
            return false;
        });

        //日期范围
        var create_time = new laydatePlus({'target':'create_time'});

        layui.pageTable = table.render({
            elem: "#table_allocation_manage"
            ,title: "库存分配需求列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/warehouse/allocation_manage/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {type: 'checkbox', width: 50},
                {
                    field: 'id',
                    title: 'ID号',
                    align: 'center',
                    width: 80
                },
                {
                    field: 'ref_type_text',
                    title: '业务类型',
                    align: 'center',
                    width: 120
                },
                {
                    field: 'ref_no',
                    title: '业务单号',
                    align: 'center',
                    width: 150
                },
                {
                    field: 'product_name',
                    title: '产品名称',
                    align: 'center',
                    width: 200,
                    templet: function(d){
                        return (d.product && d.product.title) || '未知产品';
                    }
                },
                {
                    field: 'warehouse_name',
                    title: '仓库',
                    align: 'center',
                    width: 120,
                    templet: function(d){
                        return (d.warehouse && d.warehouse.name) || '未知仓库';
                    }
                },
                {
                    field: 'quantity',
                    title: '需求数量',
                    align: 'center',
                    width: 100,
                    sort: true
                },
                {
                    field: 'allocated_quantity',
                    title: '已分配',
                    align: 'center',
                    width: 100,
                    sort: true
                },
                {
                    field: 'pending_quantity',
                    title: '待分配',
                    align: 'center',
                    width: 100,
                    templet: function(d){
                        return d.quantity - d.allocated_quantity;
                    }
                },
                {
                    field: 'priority',
                    title: '优先级',
                    align: 'center',
                    width: 100,
                    sort: true,
                    templet: function(d){
                        if(d.priority >= 90) {
                            return '<span style="color: #FF5722; font-weight: bold;">' + d.priority + '</span>';
                        } else if(d.priority >= 70) {
                            return '<span style="color: #FF9800; font-weight: bold;">' + d.priority + '</span>';
                        } else if(d.priority >= 50) {
                            return '<span style="color: #2196F3;">' + d.priority + '</span>';
                        } else {
                            return '<span style="color: #9E9E9E;">' + d.priority + '</span>';
                        }
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    width: 120,
                    templet: function(d){
                        switch(d.status) {
                            case 1: return '<span class="layui-badge layui-bg-orange">待分配</span>';
                            case 2: return '<span class="layui-badge layui-bg-blue">部分分配</span>';
                            case 3: return '<span class="layui-badge layui-bg-green">完全分配</span>';
                            case 4: return '<span class="layui-badge layui-bg-gray">已取消</span>';
                            default: return '<span class="layui-badge">未知</span>';
                        }
                    }
                },
                {
                    field: 'request_time',
                    title: '请求时间',
                    align: 'center',
                    width: 160,
                    templet: function(d){
                        return layui.util.toDateString(d.request_time * 1000, 'yyyy-MM-dd HH:mm');
                    }
                },
                {
                    field: 'wait_days',
                    title: '等待天数',
                    align: 'center',
                    width: 100,
                    templet: function(d){
                        var days = Math.ceil((Date.now() / 1000 - d.request_time) / 86400);
                        var color = days > 7 ? '#FF5722' : (days > 3 ? '#FF9800' : '#666');
                        return '<span style="color: ' + color + '">' + days + '天</span>';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 200,
                    fixed: 'right',
                    templet: function(d){
                        var html = '<div class="layui-btn-group">';
                        var btn0 = '<button class="layui-btn layui-btn-xs layui-btn-primary" lay-event="view">查看</button>';

                        switch(d.status) {
                            case 1: // 待分配
                            case 2: // 部分分配
                                var btn1 = '';
                                var btn2 = '<button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="cancel">取消</button>';
                                return html + btn0 + btn1 + btn2 + '</div>';
                            case 3: // 完全分配
                                return html + btn0 + '</div>';
                            case 4: // 已取消
                                return html + btn0 + '</div>';
                        }
                        return html + btn0 + '</div>';
                    }
                }
            ]]
        });

        //表头工具栏事件
        table.on('toolbar(table_allocation_manage)', function(obj){
            if (obj.event === 'statistics'){
                getStatistics();
                return;
            }
            if (obj.event === 'cleanup'){
                cleanupExpired();
                return;
            }
        });

        table.on('tool(table_allocation_manage)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/warehouse/allocation_manage/view?id="+data.id);
                return;
            }
            if (obj.event === 'allocate') {
                manualAllocate(data.id);
                return;
            }
            if (obj.event === 'cancel') {
                layer.prompt({title: '请输入取消原因', formType: 2}, function(reason, index){
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/allocation_manage/cancelRequest", {
                        request_id: data.id,
                        reason: reason
                    }, callback);
                    layer.close(index);
                });
                return;
            }
        });

        // 手动分配
        function manualAllocate(requestId) {
            layer.prompt({
                title: '手动分配库存',
                formType: 1,
                value: '',
                area: ['300px', '150px']
            }, function(value, index, elem){
                if(!value || value <= 0) {
                    layer.msg('请输入有效的分配数量');
                    return;
                }

                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layer.close(index);
                        layui.pageTable.reload();
                    }
                }
                tool.post("/warehouse/allocation_manage/manualAllocate", {
                    request_id: requestId,
                    allocate_quantity: value
                }, callback);
            });
        }

        // 获取统计
        function getStatistics() {
            tool.get('/warehouse/allocation_manage/statistics', {}, function(res){
                if(res.code == 0) {
                    var content = '<div style="padding: 20px;">';
                    content += '<h3>分配统计</h3>';
                    content += '<p>今日分配: ' + (res.data.today_stats.count || 0) + ' 次，总数量: ' + (res.data.today_stats.total_allocated || 0) + '</p>';
                    content += '<h4>按状态统计:</h4>';
                    if(res.data.status_stats && res.data.status_stats.length > 0) {
                        res.data.status_stats.forEach(function(item) {
                            var statusText = ['', '待分配', '部分分配', '完全分配', '已取消'][item.status] || '未知';
                            content += '<p>' + statusText + ': ' + item.count + ' 条，总数量: ' + item.total_quantity + '</p>';
                        });
                    }
                    content += '</div>';

                    layer.open({
                        type: 1,
                        title: '分配统计',
                        content: content,
                        area: ['500px', '400px']
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        }

        // 清理过期需求
        function cleanupExpired() {
            layer.confirm('确定要清理过期的分配需求吗？', function(index){
                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layui.pageTable.reload();
                    }
                }
                tool.post("/warehouse/allocation_manage/cleanupExpired", { max_wait_days: 30 }, callback);
                layer.close(index);
            });
        }
    }
</script>
{/block}
