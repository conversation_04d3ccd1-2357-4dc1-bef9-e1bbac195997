<?php
/**
 * 数据库迁移检查脚本
 * 用于检查库存锁定机制迁移的完成情况
 */

// 数据库配置（请根据实际情况修改）
$config = [
    'host' => 'localhost',
    'dbname' => 'sem_erp_oa',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}", 
        $config['username'], 
        $config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 库存锁定机制数据库迁移检查 ===\n\n";
    
    // 1. 检查预占表是否已删除
    echo "1. 检查预占表删除情况：\n";
    $tables = ['oa_inventory_reserve', 'oa_inventory_reserve_log'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "   ❌ 表 {$table} 仍然存在，需要删除\n";
        } else {
            echo "   ✅ 表 {$table} 已删除\n";
        }
    }
    
    // 2. 检查库存锁定表是否存在
    echo "\n2. 检查库存锁定表：\n";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'oa_inventory_lock'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "   ✅ 表 oa_inventory_lock 已存在\n";
        
        // 检查字段完整性
        $stmt = $pdo->prepare("DESCRIBE oa_inventory_lock");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = [
            'id', 'inventory_id', 'product_id', 'warehouse_id', 'location_id', 
            'batch_no', 'quantity', 'unit', 'cost_price', 'ref_type', 'ref_id', 
            'ref_no', 'lock_time', 'expire_time', 'auto_release', 'release_condition', 
            'status', 'created_by', 'create_time', 'update_time', 'remark'
        ];
        
        foreach ($requiredColumns as $column) {
            if (in_array($column, $columns)) {
                echo "   ✅ 字段 {$column} 存在\n";
            } else {
                echo "   ❌ 字段 {$column} 缺失\n";
            }
        }
    } else {
        echo "   ❌ 表 oa_inventory_lock 不存在，需要创建\n";
    }
    
    // 3. 检查库存锁定日志表
    echo "\n3. 检查库存锁定日志表：\n";
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'oa_inventory_lock_log'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "   ✅ 表 oa_inventory_lock_log 已存在\n";
    } else {
        echo "   ❌ 表 oa_inventory_lock_log 不存在，需要创建\n";
    }
    
    // 4. 检查库存表中的预占字段
    echo "\n4. 检查库存表中的预占字段：\n";
    $stmt = $pdo->prepare("DESCRIBE oa_inventory");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $reserveColumns = ['reserved_quantity', 'allocated_quantity'];
    foreach ($reserveColumns as $column) {
        if (in_array($column, $columns)) {
            echo "   ⚠️  字段 {$column} 仍然存在，建议删除\n";
        } else {
            echo "   ✅ 字段 {$column} 已删除或不存在\n";
        }
    }
    
    // 5. 检查索引
    echo "\n5. 检查索引：\n";
    $stmt = $pdo->prepare("SHOW INDEX FROM oa_inventory_lock");
    $stmt->execute();
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredIndexes = ['idx_product_warehouse', 'idx_ref_type_id', 'idx_status_expire'];
    $existingIndexes = array_column($indexes, 'Key_name');
    
    foreach ($requiredIndexes as $index) {
        if (in_array($index, $existingIndexes)) {
            echo "   ✅ 索引 {$index} 存在\n";
        } else {
            echo "   ❌ 索引 {$index} 缺失\n";
        }
    }
    
    echo "\n=== 检查完成 ===\n";
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置信息\n";
}