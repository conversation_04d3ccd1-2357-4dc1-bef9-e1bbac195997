{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	{eq name="$id" value="0"}
	<h3 class="pb-3">新增公司新闻</h3>
	{else/}
	<h3 class="pb-3">编辑公司新闻</h3>
	{/eq}
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">新闻标题<font>*</font></td>
			<td colspan="3">
				<input type="text" name="title" lay-verify="required" lay-reqText="请输入新闻标题" placeholder="请输入新闻标题" class="layui-input" value="{$detail.title|default=''}">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">关联链接</td>
			<td><input type="text" name="src" placeholder="请输入链接" class="layui-input" value="{$detail.src|default=''}"></td>
			<td class="layui-td-gray">排序</td>
			<td><input type="text" name="sort" placeholder="请输入排序，数字" class="layui-input" value="{$detail.sort|default=0}"></td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">相关附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					<input type="hidden" data-type="file" name="file_ids" value="{$detail.file_ids|default=''}">
					{notempty name="$detail.file_ids"}
					{volist name="$detail.file_array" id="vo"}
						<div class="layui-col-md4" id="uploadFile{$vo.id}">{:file_card($vo)}</div>
					{/volist}
					{/notempty}
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray" style="vertical-align:top;">新闻内容<font>*</font></td>
			<td colspan="3">
				<textarea name="content" placeholder="请输入内容" class="layui-textarea" id="container" style="border:0;padding:0">{$detail.content|default=''}</textarea>
			</td>
		</tr>
	</table>

	<div class="pt-4">
		<input type="hidden" name="id" value="{$id}" />
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','uploadPlus','tinymce'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,uploadPlus = layui.uploadPlus;
		//相关附件上传
		var attachment = new uploadPlus();
		//编辑器初始化
		var editor = layui.tinymce;
		var edit = editor.render({
			selector: "#container",
			images_upload_url: '/api/index/upload/sourse/tinymce',//图片上传接口
			height: 500
		});
		
		//监听提交
		form.on('submit(webform)', function (data) {
			data.field.content = tinyMCE.editors['container'].getContent();
			if (data.field.content == '') {
				layer.msg('请先完善新闻内容');
				return false;
			}
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			let clickbtn = $(this);
			tool.post("/adm/news/add", data.field, callback,clickbtn);
			return false;
		});
}
</script>
{/block}
<!-- /脚本 -->