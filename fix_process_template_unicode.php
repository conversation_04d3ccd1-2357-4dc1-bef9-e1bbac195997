<?php
/**
 * 修复工艺模板中的Unicode转义字符问题
 * 运行方式：php fix_process_template_unicode.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化应用
$app = new \think\App();
$app->initialize();

// 加载数据库配置
Config::load(__DIR__ . '/config/database.php', 'database');

echo "开始修复工艺模板Unicode转义字符问题...\n";

try {
    // 查询所有工艺模板数据
    $templates = Db::name('process_template')
        ->field('id, template_no, name, steps')
        ->select()
        ->toArray();
    
    $fixedCount = 0;
    $totalCount = count($templates);
    
    echo "找到 {$totalCount} 条工艺模板记录\n";
    
    foreach ($templates as $template) {
        $originalSteps = $template['steps'];
        
        if (empty($originalSteps)) {
            continue;
        }
        
        // 检查是否包含Unicode转义字符
        if (strpos($originalSteps, '\\u') === false) {
            echo "模板 {$template['template_no']} ({$template['name']}) - 无需修复\n";
            continue;
        }
        
        echo "修复模板 {$template['template_no']} ({$template['name']})...\n";
        
        // 修复Unicode转义字符
        $fixedSteps = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function($matches) {
            return json_decode('"\u' . $matches[1] . '"');
        }, $originalSteps);
        
        // 验证修复后的JSON是否有效
        $testDecode = json_decode($fixedSteps, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "  警告：修复后的JSON仍然无效，跳过此记录\n";
            continue;
        }
        
        // 重新编码为标准JSON格式
        $normalizedSteps = json_encode($testDecode, JSON_UNESCAPED_UNICODE);
        
        // 更新数据库
        $result = Db::name('process_template')
            ->where('id', $template['id'])
            ->update(['steps' => $normalizedSteps]);
        
        if ($result !== false) {
            $fixedCount++;
            echo "  ✓ 修复成功\n";
            
            // 显示修复前后的对比（仅显示前100个字符）
            echo "  修复前: " . substr($originalSteps, 0, 100) . "...\n";
            echo "  修复后: " . substr($normalizedSteps, 0, 100) . "...\n";
        } else {
            echo "  ✗ 修复失败\n";
        }
    }
    
    echo "\n修复完成！\n";
    echo "总记录数: {$totalCount}\n";
    echo "修复记录数: {$fixedCount}\n";
    echo "跳过记录数: " . ($totalCount - $fixedCount) . "\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
