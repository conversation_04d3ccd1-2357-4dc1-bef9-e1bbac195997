<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use app\warehouse\model\InventoryRealtime;
use app\warehouse\model\InventoryTransaction;
use think\facade\Db;
use think\Exception;

/**
 * 实时库存服务类
 */
class InventoryRealtimeService
{
    /**
     * 检查库存是否充足
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 需要数量
     * @return bool
     */
    public function checkStock($productId, $warehouseId, $quantity)
    {
        return InventoryRealtime::hasEnoughStock($productId, $warehouseId, $quantity);
    }
    
    /**
     * 获取库存状态
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID（可选，0表示所有仓库）
     * @return array
     */
    public function getInventoryStatus($productId, $warehouseId = 0)
    {
        if ($warehouseId > 0) {
            // 获取指定仓库的库存
            $inventory = InventoryRealtime::where([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId
            ])->with(['warehouse'])->find();
            
            if (!$inventory) {
                return [
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'quantity' => 0,
                    'available_quantity' => 0,
                    'locked_quantity' => 0,
                    'unit' => '',
                    'cost_price' => 0
                ];
            }
            
            return [
                'product_id' => $inventory->product_id,
                'warehouse_id' => $inventory->warehouse_id,
                'warehouse_name' => $inventory->warehouse->name ?? '',
                'quantity' => $inventory->quantity,
                'available_quantity' => $inventory->available_quantity,
                'locked_quantity' => $inventory->locked_quantity,
                'unit' => $inventory->unit,
                'cost_price' => $inventory->cost_price
            ];
        } else {
            // 获取所有仓库的库存汇总
            return InventoryRealtime::getProductInventorySummary($productId);
        }
    }
    
    /**
     * 增加库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 增加数量
     * @param string $unit 单位
     * @param float $costPrice 成本价
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function increaseStock($productId, $warehouseId, $quantity, $unit = '', $costPrice = 0, 
                                $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('增加数量必须大于0');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 增加库存
            $result = InventoryRealtime::increaseStock($productId, $warehouseId, $quantity, $unit, $costPrice);
            
            // 记录流水
            InventoryTransaction::recordInbound(
                $productId, 
                $warehouseId, 
                $quantity, 
                $result['before_quantity'], 
                $result['after_quantity'],
                $refType, 
                $refId, 
                $refNo, 
                $notes, 
                $createdBy
            );
            
            Db::commit();
            return $result;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 减少库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 减少数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function decreaseStock($productId, $warehouseId, $quantity, 
                                $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('减少数量必须大于0');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 减少库存
            $result = InventoryRealtime::decreaseStock($productId, $warehouseId, $quantity);
            
            // 记录流水
            InventoryTransaction::recordOutbound(
                $productId, 
                $warehouseId, 
                $quantity, 
                $result['before_quantity'], 
                $result['after_quantity'],
                $refType, 
                $refId, 
                $refNo, 
                $notes, 
                $createdBy
            );
            
            Db::commit();
            return $result;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 仓库间调拨库存
     * 
     * @param int $productId 产品ID
     * @param int $fromWarehouseId 源仓库ID
     * @param int $toWarehouseId 目标仓库ID
     * @param float $quantity 调拨数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function transferStock($productId, $fromWarehouseId, $toWarehouseId, $quantity,
                                $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('调拨数量必须大于0');
        }
        
        if ($fromWarehouseId == $toWarehouseId) {
            throw new Exception('源仓库和目标仓库不能相同');
        }
        
        // 检查源仓库库存是否充足
        if (!$this->checkStock($productId, $fromWarehouseId, $quantity)) {
            throw new Exception('源仓库库存不足');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 从源仓库减少库存
            $fromResult = InventoryRealtime::decreaseStock($productId, $fromWarehouseId, $quantity);
            
            // 获取源仓库的库存信息（用于目标仓库的成本价）
            $fromInventory = InventoryRealtime::where([
                'product_id' => $productId,
                'warehouse_id' => $fromWarehouseId
            ])->find();
            
            $unit = $fromInventory ? $fromInventory->unit : '';
            $costPrice = $fromInventory ? $fromInventory->cost_price : 0;
            
            // 向目标仓库增加库存
            $toResult = InventoryRealtime::increaseStock($productId, $toWarehouseId, $quantity, $unit, $costPrice);
            
            // 记录调拨流水
            InventoryTransaction::recordTransfer(
                $productId,
                $fromWarehouseId,
                $toWarehouseId,
                $quantity,
                $fromResult['before_quantity'],
                $fromResult['after_quantity'],
                $toResult['before_quantity'],
                $toResult['after_quantity'],
                $refType,
                $refId,
                $refNo,
                $notes,
                $createdBy
            );
            
            Db::commit();
            
            return [
                'from_warehouse' => $fromResult,
                'to_warehouse' => $toResult
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 锁定库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 锁定数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function lockStock($productId, $warehouseId, $quantity, 
                            $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('锁定数量必须大于0');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 锁定库存
            $result = InventoryRealtime::lockStock($productId, $warehouseId, $quantity);
            
            // 记录流水
            InventoryTransaction::createTransaction([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'transaction_type' => InventoryTransaction::TYPE_LOCK,
                'quantity' => 0, // 锁定不改变总库存数量
                'before_quantity' => $result['before_available'],
                'after_quantity' => $result['after_available'],
                'ref_type' => $refType,
                'ref_id' => $refId,
                'ref_no' => $refNo,
                'notes' => $notes . ' (锁定数量: ' . $quantity . ')',
                'created_by' => $createdBy
            ]);
            
            Db::commit();
            return $result;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 释放锁定库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 释放数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function unlockStock($productId, $warehouseId, $quantity, 
                              $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('释放数量必须大于0');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 释放锁定库存
            $result = InventoryRealtime::unlockStock($productId, $warehouseId, $quantity);
            
            // 记录流水
            InventoryTransaction::createTransaction([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'transaction_type' => InventoryTransaction::TYPE_UNLOCK,
                'quantity' => 0, // 释放锁定不改变总库存数量
                'before_quantity' => $result['before_locked'],
                'after_quantity' => $result['after_locked'],
                'ref_type' => $refType,
                'ref_id' => $refId,
                'ref_no' => $refNo,
                'notes' => $notes . ' (释放数量: ' . $quantity . ')',
                'created_by' => $createdBy
            ]);
            
            Db::commit();
            return $result;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 使用锁定库存（实际出库）
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 使用数量
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 操作人
     * @return array
     * @throws Exception
     */
    public function useLockedStock($productId, $warehouseId, $quantity, 
                                 $refType = '', $refId = 0, $refNo = '', $notes = '', $createdBy = 0)
    {
        if ($quantity <= 0) {
            throw new Exception('使用数量必须大于0');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 使用锁定库存
            $result = InventoryRealtime::useLockedStock($productId, $warehouseId, $quantity);
            
            // 记录流水
            InventoryTransaction::recordOutbound(
                $productId, 
                $warehouseId, 
                $quantity, 
                $result['before_quantity'], 
                $result['after_quantity'],
                $refType, 
                $refId, 
                $refNo, 
                $notes . ' (使用锁定库存)', 
                $createdBy
            );
            
            Db::commit();
            return $result;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
