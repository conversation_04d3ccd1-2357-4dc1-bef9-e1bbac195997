# 生产物料申请JavaScript错误修复

## 问题描述
在生产物料申请页面点击删除按钮时出现错误：
```
Uncaught ReferenceError: materialData is not defined
```

## 问题原因
JavaScript变量作用域问题：
1. `materialData`变量在`gouguInit()`函数内部定义
2. `removeMaterial`函数在全局作用域定义
3. 全局函数无法访问函数内部的局部变量

## 解决方案

### 1. 将变量移到全局作用域
```javascript
// 修改前：在函数内部定义
function gouguInit() {
    var materialData = []; // 局部变量
    var currentOrder = null;
    // ...
}

// 修改后：在全局作用域定义
var materialData = []; // 全局变量
var currentOrder = null;

function gouguInit() {
    // ...
}
```

### 2. 将函数移到正确的作用域
```javascript
function gouguInit() {
    // ... 其他代码

    // 更新申请数量
    window.updateRequestQuantity = function(materialId, quantity) {
        quantity = parseFloat(quantity) || 0;
        
        // 更新数据
        for (var i = 0; i < materialData.length; i++) {
            if (materialData[i].material_id == materialId) {
                materialData[i].request_quantity = quantity;
                break;
            }
        }
        
        // 重新渲染表格
        layui.table.reload('materialTable', {
            data: materialData
        });
    }

    // 删除物料
    window.removeMaterial = function(materialId) {
        layer.confirm('确定要删除这个物料吗？', {
            icon: 3,
            title: '确认删除'
        }, function(index) {
            // 从数组中删除
            for (var i = 0; i < materialData.length; i++) {
                if (materialData[i].material_id == materialId) {
                    materialData.splice(i, 1);
                    break;
                }
            }
            
            // 重新渲染表格
            renderMaterialTable();
            layer.close(index);
            layer.msg('物料已删除');
        });
    }
}
```

### 3. 删除重复的函数定义
删除了在`gouguInit`函数外部重复定义的函数，避免冲突。

## 修复后的功能

### 1. 删除物料功能
- 点击删除按钮弹出确认对话框
- 确认后从`materialData`数组中删除对应物料
- 重新渲染表格显示最新数据
- 显示删除成功提示

### 2. 更新申请数量功能
- 修改申请数量输入框时自动更新数据
- 实时重新渲染表格
- 保持数据同步

## 技术要点

### 1. JavaScript作用域
- 全局变量：在任何地方都可以访问
- 局部变量：只能在定义的函数内部访问
- 函数内部可以访问外部变量，但外部无法访问内部变量

### 2. 变量提升
- `var`声明的变量会被提升到函数顶部
- 但赋值操作不会被提升
- 建议在使用前明确声明和初始化变量

### 3. 全局函数定义
```javascript
// 方式1：直接定义全局函数
function globalFunction() { }

// 方式2：通过window对象定义
window.globalFunction = function() { }

// 方式3：在函数内部定义全局函数
function init() {
    window.globalFunction = function() { }
}
```

## 测试验证

### 1. 删除功能测试
1. 加载生产订单和BOM物料
2. 点击任意物料的删除按钮
3. 确认删除操作
4. 验证物料从列表中消失
5. 验证物料计数更新

### 2. 数量更新测试
1. 修改任意物料的申请数量
2. 验证数据实时更新
3. 验证超领状态正确显示
4. 验证库存状态正确显示

### 3. 表单提交测试
1. 删除部分物料后提交表单
2. 验证只提交剩余的物料
3. 验证后端正确处理数据

## 注意事项

### 1. 变量命名
- 使用有意义的变量名
- 避免全局变量污染
- 考虑使用命名空间

### 2. 函数定义位置
- 全局函数应该在适当的位置定义
- 避免在多个地方重复定义
- 考虑使用模块化方式组织代码

### 3. 错误处理
- 添加必要的错误检查
- 提供用户友好的错误提示
- 记录调试信息便于排查问题

通过这些修复，生产物料申请页面的删除功能现在可以正常工作了。
