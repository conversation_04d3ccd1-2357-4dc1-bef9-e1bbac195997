<?php
declare (strict_types = 1);

namespace app\material\validate;

use think\Validate;

class Archive extends Validate
{
    protected $rule = [
        'material_code'    => 'require|max:100|unique:product',
        'title'            => 'require|max:100',
        'cate_id'          => 'require|integer|gt:0',
        'unit'             => 'require|max:50|checkUnit',
        'source_type'      => 'require|integer|in:1,2',
        'specs'            => 'max:100',
        'barcode'          => 'max:100',
        'brand'            => 'max:100',
        'producer'         => 'max:100',
        'purchase_cycle'   => 'integer|egt:0',
        'stock'            => 'integer|egt:0',
        'description'      => 'max:1000',
        'remark'           => 'max:1000',
        'material_source'  => 'max:50',
        'model'            => 'max:100',
        'category'         => 'max:50',
        'color'            => 'max:50',
        'material_level'   => 'max:10',
        'default_warehouse' => 'integer|egt:0',
        'min_order_qty'    => 'float|egt:0',
        'min_package_qty'  => 'float|egt:0',
        'reference_cost'   => 'float|egt:0',
        'sales_price'      => 'float|egt:0',
        'min_sales_price'  => 'float|egt:0',
        'max_sales_price'  => 'float|egt:0',
        'status'           => 'integer|in:0,1',
        'type'             => 'integer|in:0,1',
    ];

    protected $message = [
        'material_code.require'    => '物料编号不能为空',
        'material_code.max'        => '物料编号不能超过100个字符',
        'material_code.unique'     => '物料编号已存在',
        'title.require'            => '物料名称不能为空',
        'title.max'                => '物料名称不能超过100个字符',
        'cate_id.require'          => '物料分类不能为空',
        'cate_id.integer'          => '物料分类必须为整数',
        'cate_id.gt'               => '请选择有效的物料分类',
        'unit.require'             => '基本单位不能为空',
        'unit.max'                 => '基本单位不能超过50个字符',
        'source_type.require'      => '物料类型不能为空',
        'source_type.integer'      => '物料类型必须为整数',
        'source_type.in'           => '物料类型只能是自产或外购',
        'specs.max'                => '物料规格不能超过100个字符',
        'barcode.max'              => '条形码不能超过100个字符',
        'brand.max'                => '品牌不能超过100个字符',
        'producer.max'             => '生产商不能超过100个字符',
        'purchase_cycle.integer'   => '采购周期必须为整数',
        'purchase_cycle.egt'       => '采购周期不能为负数',
        'stock.integer'            => '库存数量必须为整数',
        'stock.egt'                => '库存数量不能为负数',
        'description.max'          => '物料描述不能超过1000个字符',
        'remark.max'               => '备注不能超过1000个字符',
        'material_source.max'      => '物料来源不能超过50个字符',
        'model.max'                => '型号不能超过100个字符',
        'category.max'             => '物料类别不能超过50个字符',
        'color.max'                => '颜色不能超过50个字符',
        'material_level.max'       => '物料等级不能超过10个字符',
        'default_warehouse.integer' => '默认仓库必须为整数',
        'default_warehouse.egt'    => '默认仓库不能为负数',
        'min_order_qty.float'      => '最小起订量必须为数字',
        'min_order_qty.egt'        => '最小起订量不能为负数',
        'min_package_qty.float'    => '最小包装量必须为数字',
        'min_package_qty.egt'      => '最小包装量不能为负数',
        'reference_cost.float'     => '参考成本必须为数字',
        'reference_cost.egt'       => '参考成本不能为负数',
        'sales_price.float'        => '销售单价必须为数字',
        'sales_price.egt'          => '销售单价不能为负数',
        'min_sales_price.float'    => '最低销售单价必须为数字',
        'min_sales_price.egt'      => '最低销售单价不能为负数',
        'max_sales_price.float'    => '最高销售单价必须为数字',
        'max_sales_price.egt'      => '最高销售单价不能为负数',
        'status.integer'           => '状态必须为整数',
        'status.in'                => '状态只能是启用或禁用',
        'type.integer'             => '产品类型必须为整数',
        'type.in'                  => '产品类型只能是成品或半成品',
    ];

    protected $scene = [
        'add'  => [
            'material_code', 'title', 'cate_id', 'unit', 'source_type', 'specs', 'barcode',
            'brand', 'producer', 'purchase_cycle', 'stock', 'description', 'remark',
            'material_source', 'model', 'category', 'color', 'material_level',
            'default_warehouse', 'min_order_qty', 'min_package_qty',
            'reference_cost', 'sales_price', 'min_sales_price', 'max_sales_price',
            'status', 'type'
        ],
        'edit' => [
            'title', 'cate_id', 'unit', 'source_type', 'specs', 'barcode',
            'brand', 'producer', 'purchase_cycle', 'stock', 'description', 'remark',
            'material_source', 'model', 'category', 'color', 'material_level',
            'default_warehouse', 'min_order_qty', 'min_package_qty',
            'reference_cost', 'sales_price', 'min_sales_price', 'max_sales_price',
            'status', 'type'
        ],
    ];

    /**
     * 验证单位是否存在
     */
    protected function checkUnit($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 空值由require规则处理
        }

        $unit = \think\facade\Db::name('unit')
            ->where('name', $value)
            ->where('status', 1)
            ->find();

        if (!$unit) {
            return '选择的单位不存在或已禁用';
        }

        return true;
    }
}