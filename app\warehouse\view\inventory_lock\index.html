{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>锁定中</li>
                <li>已使用</li>
                <li>已释放</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="warehouse_id">
                <option value="">选择仓库</option>
                {volist name="warehouses" id="warehouse"}
                <option value="{$warehouse.id}">{$warehouse.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="ref_type">
                <option value="">关联类型</option>
                {volist name="refTypeArr" id="refType" key="k"}
                <option value="{$k}">{$refType}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="status">
                <option value="">锁定状态</option>
                {volist name="statusArr" id="status" key="k"}
                <option value="{$k}">{$status}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="create_time" placeholder="选择创建时间" readonly name="create_time">
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，产品名称/编码/关联单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_inventory_lock" lay-filter="table_inventory_lock"></table>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="lock"><i class="layui-icon layui-icon-add-1"></i>手动锁定</button>
        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="batchUnlock"><i class="layui-icon layui-icon-release"></i>批量释放</button>
    </div>
</script>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus;
        
        //tab切换
        element.on('tab(tab)', function(data){
            var statusMap = {0: "", 1: "1", 2: "2", 3: "3"};
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
            return false;
        });
        
        //日期范围
        var create_time = new laydatePlus({'target':'create_time'});
        
        layui.pageTable = table.render({
            elem: "#table_inventory_lock"
            ,title: "库存锁定列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/warehouse/InventoryLock/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {type: 'checkbox', width: 50},
                {
                    field: 'id',
                    title: 'ID号',
                    align: 'center',
                    width: 80
                },{ 
                    field: 'product_name', 
                    title: '产品名称', 
                    align: 'left', 
                    width: 200,
                    templet: function (d) {
                        var html = '<div style="line-height:18px;">';
                        html += '<div style="font-weight:bold;">' + (d.product_name || '') + '</div>';
                        html += '<div style="color:#999;font-size:12px;">' + (d.material_code || '') + '</div>';
                        html += '</div>';
                        return html;
                    }
                },{ 
                    field: 'warehouse_name', 
                    title: '仓库', 
                    align: 'center', 
                    width: 120
                },{ 
                    field: 'quantity', 
                    title: '锁定数量', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        return d.quantity + ' ' + (d.unit || '');
                    }
                },{ 
                    field: 'ref_type', 
                    title: '关联类型', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        var refTypeMap = {
                            'order': '销售订单',
                            'production': '生产订单',
                            'transfer': '调拨单',
                            'outbound': '出库单',
                            'manual': '手动锁定',
                            'other': '其他'
                        };
                        return refTypeMap[d.ref_type] || d.ref_type;
                    }
                },{ 
                    field: 'ref_no', 
                    title: '关联单号', 
                    align: 'center', 
                    width: 150
                },{ 
                    field: 'status', 
                    title: '锁定状态', 
                    align: 'center', 
                    width: 100, 
                    templet: function (d) {
                        var statusClass = {
                            1: 'layui-bg-orange', // 锁定中
                            2: 'layui-bg-green',  // 已使用
                            3: 'layui-bg-gray'    // 已释放
                        };
                        var statusText = {
                            1: '锁定中',
                            2: '已使用',
                            3: '已释放'
                        };
                        var html = '<span class="layui-badge ' + statusClass[d.status] + '">' + statusText[d.status] + '</span>';
                        return html;
                    }
                },{ 
                    field: 'creator_name', 
                    title: '创建人', 
                    align: 'center', 
                    width: 100
                },{ 
                    field: 'create_time', 
                    title: '创建时间', 
                    align: 'center', 
                    width: 160,
                    templet: function (d) {
                        return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm');
                    }
                },{ 
                    field: 'notes', 
                    title: '备注', 
                    align: 'left', 
                    width: 200,
                    templet: function (d) {
                        return d.notes ? '<span title="' + d.notes + '">' + (d.notes.length > 20 ? d.notes.substr(0, 20) + '...' : d.notes) + '</span>' : '';
                    }
                },{
                    title: '操作',
                    align: 'center',
                    width: 200,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0 = '<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="view">查看</span>';
                        var btn1 = '<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="unlock">释放</span>';
                        var btn2 = '';
                       // var btn2 = '<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="use">使用</span>';
                        
                        switch(d.status){
                            case 1: // 锁定中
                                return html + btn0 + btn1 + btn2 + '</div>';
                            case 2: // 已使用
                            case 3: // 已释放
                                return html + btn0 + '</div>';
                        }
                        return html + btn0 + '</div>';
                    }
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_inventory_lock)', function(obj){
            if (obj.event === 'lock'){
                tool.side("/warehouse/InventoryLock/lock");
                return;
            }
            if (obj.event === 'batchUnlock'){
                var checkStatus = table.checkStatus('table_inventory_lock');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要释放的锁定记录');
                    return;
                }
                
                var ids = [];
                for(var i = 0; i < data.length; i++){
                    if(data[i].status == 1){ // 只能释放锁定中的记录
                        ids.push(data[i].id);
                    }
                }
                
                if(ids.length === 0){
                    layer.msg('所选记录中没有可释放的锁定记录');
                    return;
                }
                
                layer.confirm('确定要批量释放选中的锁定记录吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/InventoryLock/batchUnlock", { ids: ids.join(',') }, callback);
                    layer.close(index);
                });
                return;
            }
        });	
            
        table.on('tool(table_inventory_lock)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/warehouse/InventoryLock/view?id="+data.id);
                return;
            }
            if (obj.event === 'unlock') {
                layer.confirm('确定要释放该锁定记录吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 3}); // 更新状态为已释放
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/InventoryLock/unlock", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'use') {
                layer.confirm('确定要使用该锁定库存吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 2}); // 更新状态为已使用
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/InventoryLock/useLock", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
        });
    }
</script>
{/block}
