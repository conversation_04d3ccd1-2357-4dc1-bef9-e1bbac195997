# 库存流水系统问题分析与修复方案

## 发现的问题

### 1. 搜索功能问题
- **前端类型映射错误**：页面中使用的流水类型与模型常量不匹配
- **Tab切换逻辑错误**：调拨记录需要包含 `transfer_in` 和 `transfer_out` 两种类型
- **搜索表单绑定缺失**：表格没有正确绑定搜索表单

### 2. 库存流水双轨制问题
系统中存在两套库存流水记录系统：

#### 新系统（标准化）
- **表名**：`oa_inventory_transaction`
- **模型**：`InventoryTransaction`
- **使用模块**：仓库管理（入库单、出库单、调拨等）
- **流水类型**：`in`, `out`, `transfer_in`, `transfer_out`, `adjust`, `lock`, `unlock`

#### 旧系统（遗留）
- **表名**：`oa_inventory_log`
- **使用模块**：采购管理、外发加工等
- **流水类型**：数字类型（1=入库, 2=出库, 4=调拨等）

### 3. 数据一致性问题
- 不同模块创建的库存流水记录在不同的表中
- 流水查询页面只显示新系统的数据
- 导致库存流水记录不完整

## 修复方案

### 1. 前端搜索功能修复 ✅

#### 修复内容：
- 修正流水类型映射关系
- 修复Tab切换逻辑，正确处理调拨记录
- 添加搜索表单绑定
- 修正数量变动显示逻辑

#### 修复代码：
```javascript
// 正确的类型映射
var typeMap = {
    'in': '入库',
    'out': '出库', 
    'transfer_in': '调拨入库',
    'transfer_out': '调拨出库',
    'adjust': '盘点调整',
    'lock': '锁定',
    'unlock': '解锁'
};

// Tab切换逻辑
var typeMap = {
    0: "",                    // 全部流水
    1: "in",                  // 入库记录
    2: "out",                 // 出库记录
    3: "transfer_in,transfer_out", // 调拨记录
    4: "adjust"               // 盘点记录
};
```

### 2. 库存流水统一化方案

#### 方案A：数据迁移（推荐）
1. **创建数据迁移脚本**，将 `oa_inventory_log` 中的数据迁移到 `oa_inventory_transaction`
2. **统一所有模块**使用新的 `InventoryTransaction` 模型
3. **逐步废弃**旧的 `oa_inventory_log` 表

#### 方案B：双表查询
1. **修改查询逻辑**，同时查询两个表的数据
2. **统一数据格式**，在控制器中合并显示
3. **保持向后兼容**，不影响现有功能

### 3. 数据迁移脚本设计

```php
// 迁移 oa_inventory_log 到 oa_inventory_transaction
$oldLogs = Db::name('inventory_log')->select();

foreach ($oldLogs as $log) {
    // 类型映射
    $typeMap = [
        1 => 'in',           // 入库
        2 => 'out',          // 出库
        3 => 'transfer_in',  // 调拨入库
        4 => 'transfer_out', // 调拨出库
        5 => 'adjust'        // 盘点调整
    ];
    
    $transactionType = $typeMap[$log['type']] ?? 'other';
    
    // 生成流水号
    $transactionNo = 'IT' . date('YmdHis', $log['create_time']) . 
                     str_pad($log['id'], 4, '0', STR_PAD_LEFT);
    
    // 插入新表
    Db::name('inventory_transaction')->insert([
        'transaction_no' => $transactionNo,
        'product_id' => $log['product_id'],
        'warehouse_id' => $log['warehouse_id'],
        'transaction_type' => $transactionType,
        'quantity' => $log['quantity'],
        'before_quantity' => $log['before_quantity'],
        'after_quantity' => $log['after_quantity'],
        'ref_type' => $log['related_bill_type'],
        'ref_id' => $log['related_bill_id'],
        'ref_no' => $log['related_bill_no'],
        'notes' => $log['notes'],
        'created_by' => $log['operation_by'],
        'create_time' => $log['create_time']
    ]);
}
```

### 4. 模块统一化改造

#### 需要改造的模块：
1. **采购管理**：`app/purchase/controller/Receipt.php`
2. **外发加工**：`app/outsource/controller/Index.php`
3. **其他使用 inventory_log 的模块**

#### 改造方法：
```php
// 旧代码
Db::name('inventory_log')->insert([...]);

// 新代码
InventoryTransaction::recordInbound(
    $productId, $warehouseId, $quantity,
    $beforeQuantity, $afterQuantity,
    $refType, $refId, $refNo, $notes, $createdBy
);
```

## 实施步骤

### 第一阶段：修复搜索功能 ✅
- [x] 修复前端类型映射
- [x] 修复Tab切换逻辑
- [x] 添加搜索表单绑定
- [x] 修正显示逻辑

### 第二阶段：数据统一化
1. **创建数据迁移脚本**
2. **执行数据迁移**
3. **验证数据完整性**

### 第三阶段：模块改造
1. **改造采购管理模块**
2. **改造外发加工模块**
3. **改造其他相关模块**

### 第四阶段：清理优化
1. **废弃旧表和旧代码**
2. **优化查询性能**
3. **完善文档**

## 预期效果

1. **搜索功能正常**：所有筛选条件都能正确工作
2. **数据完整性**：显示所有模块的库存流水记录
3. **系统一致性**：统一的库存流水记录标准
4. **维护便利性**：单一的数据源和处理逻辑

## 风险评估

### 低风险
- 前端搜索功能修复：不影响数据，只是显示优化

### 中等风险
- 数据迁移：需要充分测试，建议先在测试环境执行

### 高风险
- 模块改造：涉及业务逻辑变更，需要全面测试

## 建议

1. **立即执行**第一阶段修复，解决当前搜索问题
2. **谨慎规划**后续阶段，确保数据安全
3. **充分测试**每个阶段的改动
4. **保留备份**，确保可以回滚
