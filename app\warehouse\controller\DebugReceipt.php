<?php

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\model\Receipt as ReceiptModel;
use app\warehouse\model\ReceiptDetail as ReceiptDetailModel;
use think\facade\Db;

/**
 * 入库单调试控制器
 */
class DebugReceipt extends BaseController
{
    /**
     * 检查入库单状态
     */
    public function checkReceiptStatus()
    {
        $id = input('id', 0, 'intval');
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '请提供入库单ID']);
        }
        
        // 获取入库单信息
        $receipt = ReceiptModel::with('details')->find($id);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '入库单不存在']);
        }
        
        $result = [
            'receipt_info' => [
                'id' => $receipt->id,
                'receipt_no' => $receipt->receipt_no,
                'status' => $receipt->status,
                'inspection_status' => $receipt->inspection_status,
                'warehouse_id' => $receipt->warehouse_id,
            ],
            'details' => [],
            'inventory_status' => [],
            'allocation_requests' => [],
            'lock_records' => []
        ];
        
        // 检查明细信息
        foreach ($receipt->details as $detail) {
            $detailInfo = [
                'id' => $detail->id,
                'product_id' => $detail->product_id,
                'product_name' => $detail->product_name,
                'quantity' => $detail->quantity,
                'quality_status' => $detail->quality_status,
                'quality_status_text' => $this->getQualityStatusText($detail->quality_status),
                'is_qualified' => $detail->quality_status == ReceiptDetailModel::QUALITY_GOOD
            ];
            $result['details'][] = $detailInfo;
            
            // 检查库存状态
            $inventory = Db::name('inventory_realtime')
                ->where('product_id', $detail->product_id)
                ->where('warehouse_id', $receipt->warehouse_id)
                ->find();
                
            if ($inventory) {
                $result['inventory_status'][] = [
                    'product_id' => $detail->product_id,
                    'warehouse_id' => $receipt->warehouse_id,
                    'quantity' => $inventory['quantity'],
                    'available_quantity' => $inventory['available_quantity'],
                    'locked_quantity' => $inventory['locked_quantity']
                ];
            }
            
            // 检查分配需求
            $requests = Db::name('inventory_allocation_request')
                ->where('ref_type', 'purchase_receipt')
                ->where('ref_id', $receipt->id)
                ->where('product_id', $detail->product_id)
                ->select();
                
            foreach ($requests as $request) {
                $result['allocation_requests'][] = [
                    'id' => $request['id'],
                    'product_id' => $request['product_id'],
                    'quantity' => $request['quantity'],
                    'allocated_quantity' => $request['allocated_quantity'],
                    'status' => $request['status'],
                    'priority' => $request['priority']
                ];
            }
            
            // 检查锁定记录
            $locks = Db::name('inventory_lock')
                ->where('ref_type', 'purchase_receipt')
                ->where('ref_id', $receipt->id)
                ->where('product_id', $detail->product_id)
                ->select();
                
            foreach ($locks as $lock) {
                $result['lock_records'][] = [
                    'id' => $lock['id'],
                    'product_id' => $lock['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'quantity' => $lock['quantity'],
                    'status' => $lock['status'],
                    'lock_type' => $lock['lock_type']
                ];
            }
        }
        
        return json(['code' => 0, 'data' => $result]);
    }
    
    /**
     * 获取质检状态文本
     */
    private function getQualityStatusText($status)
    {
        $statusMap = [
            0 => '未设置',
            1 => '待检',
            2 => '合格', 
            3 => '不合格'
        ];
        
        return $statusMap[$status] ?? '未知';
    }
    
    /**
     * 强制更新质检状态
     */
    public function updateQualityStatus()
    {
        $id = input('id', 0, 'intval');
        $status = input('status', 2, 'intval'); // 默认设为合格
        
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '请提供入库单ID']);
        }
        
        $receipt = ReceiptModel::with('details')->find($id);
        if (!$receipt) {
            return json(['code' => 1, 'msg' => '入库单不存在']);
        }
        
        Db::startTrans();
        try {
            // 更新入库单质检状态
            $receipt->inspection_status = 2; // 质检通过
            $receipt->save();
            
            // 更新所有明细的质检状态
            foreach ($receipt->details as $detail) {
                $detail->quality_status = $status;
                $detail->save();
            }
            
            Db::commit();
            return json(['code' => 0, 'msg' => '质检状态更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
}
