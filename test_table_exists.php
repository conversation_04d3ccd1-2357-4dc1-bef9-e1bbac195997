<?php
// 测试数据库表是否存在的脚本

// 需要检查的表名列表
$tables_to_check = [
    'purchase_receipt',
    'inventory_log',
    'oa_inventory_log',
    'purchase_order'
];

echo "数据库表存在性检查：\n";
echo "===================\n";

foreach ($tables_to_check as $table) {
    echo "检查表: {$table}\n";
    echo "SQL: SELECT COUNT(*) FROM {$table} LIMIT 1\n";
    echo "-------------------\n";
}

echo "\n注意事项：\n";
echo "1. 如果 inventory_log 表不存在，需要创建该表\n";
echo "2. 如果是 oa_inventory_log 表，需要修改代码中的表名\n";
echo "3. 确保表结构包含以下字段：\n";
echo "   - related_bill_type (varchar)\n";
echo "   - related_bill_id (int)\n";
echo "   - product_id (int)\n";
echo "   - warehouse_id (int)\n";
echo "   - quantity (decimal)\n";
echo "   - create_time (int)\n";

echo "\n建议的 inventory_log 表结构：\n";
echo "CREATE TABLE IF NOT EXISTS `inventory_log` (\n";
echo "  `id` int(11) NOT NULL AUTO_INCREMENT,\n";
echo "  `product_id` int(11) NOT NULL COMMENT '产品ID',\n";
echo "  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',\n";
echo "  `batch_no` varchar(100) DEFAULT NULL COMMENT '批次号',\n";
echo "  `type` tinyint(1) NOT NULL COMMENT '类型：1=入库，2=出库',\n";
echo "  `quantity` decimal(10,2) NOT NULL COMMENT '数量',\n";
echo "  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',\n";
echo "  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',\n";
echo "  `unit` varchar(20) DEFAULT '' COMMENT '单位',\n";
echo "  `related_bill_type` varchar(50) DEFAULT '' COMMENT '关联单据类型',\n";
echo "  `related_bill_id` int(11) DEFAULT 0 COMMENT '关联单据ID',\n";
echo "  `related_bill_no` varchar(100) DEFAULT '' COMMENT '关联单据号',\n";
echo "  `notes` text COMMENT '备注',\n";
echo "  `operation_by` int(11) DEFAULT 0 COMMENT '操作人',\n";
echo "  `operation_time` int(11) DEFAULT 0 COMMENT '操作时间',\n";
echo "  `create_time` int(11) NOT NULL COMMENT '创建时间',\n";
echo "  PRIMARY KEY (`id`),\n";
echo "  KEY `idx_product` (`product_id`),\n";
echo "  KEY `idx_warehouse` (`warehouse_id`),\n";
echo "  KEY `idx_related` (`related_bill_type`,`related_bill_id`)\n";
echo ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存变动日志表';\n";
?>
