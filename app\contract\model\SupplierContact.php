<?php


namespace app\contract\model;

use think\facade\Db;
use think\Model;

class SupplierContact extends Model
{
	// 获取详情
    public function detail($id)
    {
        $detail = Db::name('SupplierContact')->where(['id' => $id])->find();
        if (!empty($detail)) {
            $detail['create_time'] = date('Y-m-d H:i:s', $detail['create_time']);
			$detail['supplier'] = Db::name('Supplier')->where(['id' => $detail['sid']])->value('title');
        }
        return $detail;
    }
}
