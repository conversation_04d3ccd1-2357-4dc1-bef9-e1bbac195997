{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>执行出库 - {$outbound.outbound_no}</h3>
        </div>
        <div class="layui-card-body">
            <!-- 出库单基本信息 -->
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md3">
                    <strong>出库单号：</strong>{$outbound.outbound_no}
                </div>
                <div class="layui-col-md3">
                    <strong>出库类型：</strong>{$outbound.outbound_type_text}
                </div>
                <div class="layui-col-md3">
                    <strong>出库日期：</strong>{$outbound.outbound_date}
                </div>
                <div class="layui-col-md3">
                    <strong>状态：</strong>{$outbound.status_text}
                </div>
            </div>
            
            <div class="layui-row layui-col-space10" style="margin-top: 10px;">
                <div class="layui-col-md3">
                    <strong>业务单号：</strong>{$outbound.ref_no|default='-'}
                </div>
                <div class="layui-col-md3">
                    <strong>客户：</strong>{$outbound.customer.name|default='-'}
                </div>
                <div class="layui-col-md3">
                    <strong>总数量：</strong>{$outbound.total_quantity}
                </div>
                <div class="layui-col-md3">
                    <strong>优先级：</strong>{$outbound.priority_text}
                </div>
            </div>

            <!-- 仓库分布信息 -->
            <div class="layui-card" style="margin-top: 15px;">
                <div class="layui-card-header">
                    <h4><i class="layui-icon layui-icon-location"></i> 涉及仓库分布</h4>
                </div>
                <div class="layui-card-body" style="padding: 10px 15px;">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <strong>锁定仓库：</strong>
                            <span id="lock-warehouses" class="warehouse-tags"></span>
                        </div>
                        <div class="layui-col-md6">
                            <strong>分配仓库：</strong>
                            <span id="allocation-warehouses" class="warehouse-tags"></span>
                        </div>
                    </div>
                </div>
            </div>

            <form class="layui-form" lay-filter="executeForm" style="margin-top: 20px;">
                <input type="hidden" name="id" value="{$outbound.id}">
                
                <!-- 出库明细 -->
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h4>出库明细确认</h4>
                        <div style="float: right; color: #666;">
                            <i class="layui-icon layui-icon-tips"></i> 数量已通过锁库和分配确定，请确认执行出库
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-table" lay-skin="line">
                            <thead>
                                <tr>
                                    <th>产品编码</th>
                                    <th>产品名称</th>
                                    <th>规格</th>
                                    <th>单位</th>
                                    <th>出库数量</th>
                                    <th>锁定仓库</th>
                                    <th>分配仓库</th>
                                    <th>当前库存</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="details" id="detail"}
                                <tr>
                                    <td>{$detail.product_code}</td>
                                    <td>{$detail.product_name}</td>
                                    <td>{$detail.specification}</td>
                                    <td>{$detail.unit}</td>
                                    <td class="text-center"><strong>{$detail.quantity}</strong></td>
                                    <td class="text-center">
                                        {if isset($detail.lock_info) && $detail.lock_info}
                                            <span class="layui-badge layui-bg-orange" title="锁定数量: {$detail.lock_info.locked_quantity}">
                                                {$detail.lock_info.warehouse_name}
                                            </span>
                                        {else/}
                                            <span class="layui-text-muted">未锁定</span>
                                        {/if}
                                    </td>
                                    <td class="text-center">
                                        {if isset($detail.allocation_info) && $detail.allocation_info}
                                            <span class="layui-badge layui-bg-blue" title="分配数量: {$detail.allocation_info.allocated_quantity}">
                                                {$detail.allocation_info.warehouse_name}
                                            </span>
                                        {else/}
                                            <span class="layui-text-muted">未分配</span>
                                        {/if}
                                    </td>
                                    <td class="text-center">{$detail.current_stock}</td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="layui-form-item" style="margin-top: 20px;">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal layui-btn-lg" lay-submit lay-filter="execute">
                            <i class="layui-icon layui-icon-ok"></i> 确认执行出库
                        </button>
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-lg" onclick="parent.layer.closeAll()">
                            <i class="layui-icon layui-icon-close"></i> 取消
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{/block}

{block name="script"}
<script>
const moduleInit = ['form'];
function gouguInit() {
    var form = layui.form, layer = layui.layer;

    // 简化的执行出库逻辑，无需数量输入

    // 汇总显示涉及的仓库
    function summarizeWarehouses() {
        var lockWarehouses = new Set();
        var allocationWarehouses = new Set();

        // 遍历表格行，收集仓库信息
        $('tbody tr').each(function() {
            var lockWarehouse = $(this).find('td:eq(5) .layui-badge').text().trim();
            var allocationWarehouse = $(this).find('td:eq(6) .layui-badge').text().trim();

            if (lockWarehouse && lockWarehouse !== '未锁定') {
                lockWarehouses.add(lockWarehouse);
            }

            if (allocationWarehouse && allocationWarehouse !== '未分配') {
                allocationWarehouses.add(allocationWarehouse);
            }
        });

        // 显示锁定仓库
        var lockHtml = '';
        lockWarehouses.forEach(function(warehouse) {
            lockHtml += '<span class="layui-badge layui-bg-orange" style="margin-right: 5px;">' + warehouse + '</span>';
        });
        $('#lock-warehouses').html(lockHtml || '<span class="layui-text-muted">无</span>');

        // 显示分配仓库
        var allocationHtml = '';
        allocationWarehouses.forEach(function(warehouse) {
            allocationHtml += '<span class="layui-badge layui-bg-blue" style="margin-right: 5px;">' + warehouse + '</span>';
        });
        $('#allocation-warehouses').html(allocationHtml || '<span class="layui-text-muted">无</span>');
    }

    // 页面加载完成后执行
    $(document).ready(function() {
        summarizeWarehouses();
    });

    // 表单提交
    form.on('submit(execute)', function(data) {
        // 确认执行出库
        layer.confirm('确定要执行出库操作吗？<br><br>系统将根据锁库和分配记录自动处理库存扣减。', {
            icon: 3,
            title: '确认执行出库',
            area: ['400px', '200px']
        }, function(index) {
            layer.close(index);

            // 显示加载层
            var loadIndex = layer.load(2, {shade: [0.3, '#000']});

            // 提交数据
            $.ajax({
                url: '{:url("warehouse/outbound/execute")}',
                type: 'POST',
                data: data.field,
                success: function(res) {
                    layer.close(loadIndex);
                    if (res.code === 0) {
                        layer.msg('出库执行成功！', {icon: 1, time: 2000}, function() {
                            parent.layer.closeAll();
                        });
                    } else {
                        layer.msg(res.msg || '出库执行失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadIndex);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
        });

        return false;
    });
}
</script>

<style>
.text-danger {
    color: #ff5722 !important;
    font-weight: bold;
}

.text-center {
    text-align: center;
}

.layui-table td {
    padding: 8px 10px;
}

.layui-card-header h4 {
    margin: 0;
    display: inline-block;
}

.warehouse-tags .layui-badge {
    margin-right: 5px;
    margin-bottom: 3px;
    cursor: pointer;
}

.warehouse-tags .layui-badge:hover {
    opacity: 0.8;
}

/* 仓库分布卡片样式 */
.layui-card .layui-card-body {
    background-color: #fafafa;
}

/* 表格中的仓库标签样式 */
.layui-table .layui-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
}

/* 出库数量加粗显示 */
.layui-table td strong {
    font-size: 14px;
    color: #333;
}
</style>
{/block}
