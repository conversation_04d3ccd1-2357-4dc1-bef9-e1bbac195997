{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">编辑销售订单</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="orderForm">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_no" value="{$order.order_no|default=''}" readonly class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_date" id="orderDate" placeholder="请选择订单日期" value="{$order.order_date|default=''}" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户</label>
                            <div class="layui-input-block">
                                <select name="customer_id" lay-verify="required" lay-filter="customer" lay-search>
                                    <option value="">请选择客户</option>
                                    {volist name=":get_customer_list()" id="vo"}
                                    <option value="{$vo.id}" {if $order.customer_id == $vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单类型</label>
                            <div class="layui-input-block">
                                <select name="order_type" lay-verify="required">
                                    <option value="">请选择订单类型</option>
                                    <option value="1" {if $order.order_type == 1}selected{/if}>现货单</option>
                                    <option value="2" {if $order.order_type == 2}selected{/if}>账期单</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
               
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">预计交期</label>
                            <div class="layui-input-block">
                                <input type="text" name="delivery_date" id="deliveryDate" placeholder="请选择交货日期" value="{$order.delivery_date|default=''}" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">税率</label>
                            <div class="layui-input-block">
                                <select name="tax_rate" lay-verify="required" lay-filter="tax_rate">
                                    <option value="0" {if $order.tax_rate == 0}selected{/if}>不含税</option>
                                    <option value="3" {if $order.tax_rate == 3}selected{/if}>3%</option>
                                    <option value="9" {if $order.tax_rate == 9}selected{/if}>9%</option>
                                    <option value="13" {if $order.tax_rate == 13}selected{/if}>13%</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注说明</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入备注说明" class="layui-textarea">{$order.remark|default=''}</textarea>
                    </div>
                </div>
                
                <fieldset class="layui-elem-field">
                    <legend>商品明细</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <button type="button" class="layui-btn layui-btn-normal" id="btnAddProduct">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加商品
                                </button>
                                <button type="button" class="layui-btn layui-btn-danger" id="btnRemoveProduct">
                                    <i class="layui-icon layui-icon-delete"></i> 删除选中
                                </button>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="productTable" lay-filter="productTable">
                            <thead>
                                <tr>
                                    <th lay-data="{type:'checkbox', fixed:'left'}"></th>
                                    <th lay-data="{field:'material_code', width:180}">材料编码</th>
                                    <th lay-data="{field:'product_name', width:180}">商品名称</th>
                                    <th lay-data="{field:'product_specs', width:120}">规格型号</th>
                                    <th lay-data="{field:'unit', width:80}">单位</th>
                                    <th lay-data="{field:'quantity', width:100, edit:'text'}">数量</th>
                                    <th lay-data="{field:'price', width:120, edit:'text'}">单价</th>
                                    <th lay-data="{field:'tax_rate', width:100, edit:'text'}">税率(%)</th>
                                    <th lay-data="{field:'amount', width:120}">金额</th>
                                    <th lay-data="{field:'tax_amount', width:120}">税额</th>
                                    <th lay-data="{field:'total_amount', width:120}">价税合计</th>
                                    <th lay-data="{field:'notes', width:180, edit:'text'}">备注</th>
                                    <th lay-data="{width:80, toolbar:'#productBar', fixed:'right'}">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加的商品行 -->
                            </tbody>
                        </table>
                        
                        <script type="text/html" id="productBar">
                            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
                        </script>
                    </div>
                </fieldset>
                
                <div class="layui-form-item text-right">
                    <div class="layui-input-block">
                        <span>订单总金额: </span>
                        <span id="totalAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">总税额: </span>
                        <span id="totalTaxAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">价税合计: </span>
                        <span id="totalAmountWithTax" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="hidden" name="__token__" value="{:token()}">
                        <input type="hidden" name="id" value="{$order.id}">
                        <button type="button" class="layui-btn" lay-submit lay-filter="saveOrder">保存订单</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    
    // 订单明细数据
    var itemsData = {$items|json_encode|raw};
    
    // 定义全局计算总金额函数，供子页面调用
    window.calculateTotal = function(){
        var table = layui.table;
        var data = table.cache.productTable || [];
        var total = 0;
        var totalTax = 0;
        var totalWithTax = 0;
        
        for(var i = 0; i < data.length; i++){
            if(data[i].amount){
                total += parseFloat(data[i].amount);
            }
            if(data[i].tax_amount){
                totalTax += parseFloat(data[i].tax_amount);
            }
            if(data[i].total_amount){
                totalWithTax += parseFloat(data[i].total_amount);
            }
        }
        
        document.getElementById('totalAmount').innerText = '￥' + total.toFixed(2);
        document.getElementById('totalTaxAmount').innerText = '￥' + totalTax.toFixed(2);
        document.getElementById('totalAmountWithTax').innerText = '￥' + totalWithTax.toFixed(2);
        
        return {
            total: total,
            tax: totalTax,
            withTax: totalWithTax
        };
    };
    
    function gouguInit() {
        var form = layui.form;
        var table = layui.table;
        var laydate = layui.laydate;
        var util = layui.util;
        var tool = layui.tool;
        var $ = layui.jquery;
        
        // 获取订单明细数据
        var orderItems = [];
        if(typeof itemsData !== 'undefined' && itemsData && itemsData.length > 0) {
            orderItems = itemsData;
            console.log("已加载订单明细数据:", orderItems.length, "条");
        } else {
            console.log("未找到订单明细数据");
        }
        
        // 需要处理明细数据，使其符合表格要求
        for (var i = 0; i < orderItems.length; i++) {
            // 确保数据字段和表格一致
            if (!orderItems[i].price && orderItems[i].unit_price) {
                orderItems[i].price = orderItems[i].unit_price;
            }
            
            if (!orderItems[i].total_amount) {
                var amount = parseFloat(orderItems[i].amount || 0);
                var taxAmount = parseFloat(orderItems[i].tax_amount || 0);
                orderItems[i].total_amount = (amount + taxAmount).toFixed(2);
            }
            
            // 添加LAY_TABLE_INDEX字段，避免表格操作错误
            orderItems[i].LAY_TABLE_INDEX = i;
        }
        
        // 渲染日期选择器
        laydate.render({
            elem: '#orderDate'
        });
        
        laydate.render({
            elem: '#deliveryDate'
        });
        
        // 初始化表格
        table.init('productTable', {
            limit: 1000,
            data: orderItems,
            done: function(res, curr, count){
                if(orderItems.length > 0) {
                    console.log("表格加载成功，共", orderItems.length, "条数据");
                } else {
                    console.log("表格数据为空");
                }
                calculateTotal();
            }
        });
        
        // 监听单元格编辑
        table.on('edit(productTable)', function(obj){
            var value = obj.value;
            var field = obj.field;
            var data = obj.data;
            
            // 数量、单价或税率变更时，计算金额
            if(field === 'quantity' || field === 'price'){
                // 将输入转为数值并验证
                var val = parseFloat(value);
                if(isNaN(val) || val < 0){
                    layer.msg('请输入有效的数字', {icon: 5});
                    // 恢复原值
                    $(this).val(data[field]);
                    return;
                }
                
                if(field === 'quantity'){
                    data.quantity = val;
                }else if(field === 'price'){
                    data.price = val;
                }
                
                // 获取当前选择的税率（如果行中未设置税率）
                if (!data.tax_rate) {
                    data.tax_rate = parseFloat($('select[name="tax_rate"]').val() || 0);
                }
                
                // 计算金额
                data.amount = (data.quantity * data.price).toFixed(2);
                data.tax_amount = (data.amount * data.tax_rate / 100).toFixed(2);
                data.total_amount = (parseFloat(data.amount) + parseFloat(data.tax_amount)).toFixed(2);
                
                // 更新表格这一行的数据
                obj.update({
                    quantity: data.quantity,
                    price: data.price,
                    tax_rate: data.tax_rate,
                    amount: data.amount,
                    tax_amount: data.tax_amount,
                    total_amount: data.total_amount
                });
                
                // 重新计算总金额
                calculateTotal();
            }
        });
        
        // 监听工具条
        table.on('tool(productTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                obj.del();
                calculateTotal();
            }
        });
        
        // 监听税率变更
        form.on('select(tax_rate)', function(data){
            var taxRate = parseFloat(data.value);
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            
            // 更新所有商品的税率和税额
            for(var i = 0; i < tableData.length; i++){
                var item = tableData[i];
                item.tax_rate = taxRate;
                item.tax_amount = (item.amount * taxRate / 100).toFixed(2);
                item.total_amount = (parseFloat(item.amount) + parseFloat(item.tax_amount)).toFixed(2);
            }
            
            // 重新加载表格
            table.reload('productTable', {
                data: tableData
            });
            
            // 重新计算总金额
            calculateTotal();
        });
        
        // 添加商品
        $('#btnAddProduct').on('click', function(){
            var taxRate = parseFloat($('select[name="tax_rate"]').val() || 0);
            
            tool.side('selectProduct', {
                title: '选择商品',
                width: '100%',
                success: function(layero, index){
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var table = iframeWindow.layui.table;
                    
                    // 监听选择事件
                    iframeWindow.layui.table.on('tool(productTable)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'select'){
                            var table = layui.table;
                            var tableData = table.cache.productTable || [];
                            
                            // 检查是否已添加该商品
                            var exists = tableData.some(function(item){
                                return item.product_id === data.id;
                            });
                            
                            if(exists){
                                layer.msg('该商品已添加', {icon: 2});
                                return;
                            }
                            
                            // 使用当前选择的税率
                            var price = data.price || 0;
                            var amount = (parseFloat(price) * 1).toFixed(2);
                            var tax_amount = (amount * taxRate / 100).toFixed(2);
                            var total_amount = (parseFloat(amount) + parseFloat(tax_amount)).toFixed(2);
                            
                            // 添加新商品
                            tableData.push({
                                product_id: data.id,
                                product_name: data.title,
                                material_code: data.material_code,
                                product_specs: data.specs,
                                unit: data.unit,
                                quantity: 1,
                                price: price,
                                tax_rate: taxRate,
                                amount: amount,
                                tax_amount: tax_amount,
                                total_amount: total_amount,
                                delivery_date: '',
                                notes: ''
                            });
                            
                            table.reload('productTable', {
                                data: tableData
                            });
                            
                            // 计算总金额
                            calculateTotal();
                            
                            layer.close(index);
                        }
                    });
                }
            });
        });
        
        // 删除选中商品
        $('#btnRemoveProduct').on('click', function(){
            var table = layui.table;
            var checkStatus = table.checkStatus('productTable');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请选择要删除的商品', {icon: 2});
                return;
            }
            layer.confirm('确定要删除选中的商品吗？', function(index){
                var tableData = table.cache.productTable || [];
                var newData = tableData.filter(function(item){
                    return !data.some(function(selected){
                        return selected.LAY_TABLE_INDEX === item.LAY_TABLE_INDEX;
                    });
                });
                table.reload('productTable', {
                    data: newData
                });
                layer.close(index);
                calculateTotal();
            });
        });
        
        // 取消按钮
        $('#btnCancel').on('click', function(){
            history.back();
        });
        
        // 保存订单
        form.on('submit(saveOrder)', function(data){
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            if(tableData.length === 0){
                layer.msg('请添加商品明细', {icon: 2});
                return false;
            }
            
            var formData = data.field;
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据，将每行的税率设置为全局税率
            for(var i = 0; i < tableData.length; i++){
                // 清理不需要的字段
                delete tableData[i].LAY_TABLE_INDEX;
                delete tableData[i].LAY_CHECKED;
                
                tableData[i].tax_rate = globalTaxRate;
                // 重新计算税额和价税合计
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                // 确保单价字段正确
                tableData[i].unit_price = tableData[i].price;
            }
            
            formData.items = tableData;
            
            console.log("准备提交订单数据:", formData);
            
            tool.post('/customer/order/edit?id=' + formData.id, formData, function(res){
                if(res.code === 0){
                    layer.msg('保存成功', {icon: 1}, function(){
                        window.location.href = '/customer/order/view?id=' + formData.id;
                    });
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            });
            return false;
        });
    }
</script>
{/block} 