{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>新增领料单</h3>
        </div>
        <div class="layui-card-body">
                    <form class="layui-form" lay-filter="requestForm">
                        <!-- 基本信息 -->
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <label class="layui-form-label">生产订单</label>
                                <div class="layui-input-block">
                                    <select name="production_order_id" lay-verify="required" lay-search lay-filter="productionOrder">
                                        <option value="">请选择生产订单</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <label class="layui-form-label">说明</label>
                                <div class="layui-input-block">
                                    <input type="text" name="notes" placeholder="请输入备注说明" class="layui-input">
                                </div>
                            </div>
                        </div>

                        <!-- 生产订单信息显示 -->
                        <div class="layui-row layui-col-space10" id="orderInfo" style="display: none; margin-top: 15px;">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">生产订单信息</div>
                                    <div class="layui-card-body">
                                        <div class="layui-row">
                                            <div class="layui-col-md3">
                                                <strong>订单号：</strong><span id="orderNo"></span>
                                            </div>
                                            <div class="layui-col-md3">
                                                <strong>产品名称：</strong><span id="productName"></span>
                                            </div>
                                            <div class="layui-col-md3">
                                                <strong>生产数量：</strong><span id="productionQuantity"></span>
                                            </div>
                                            <div class="layui-col-md3">
                                                <strong>订单状态：</strong><span id="orderStatus"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 物料清单 -->
                        <div class="layui-row" style="margin-top: 15px;">
                            <div class="layui-col-md12">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        物料清单
                                        <span class="layui-badge layui-bg-blue" id="materialCount">0</span>
                                    </div>
                                    <div class="layui-card-body">
                                        <table class="layui-hide" id="materialTable" lay-filter="materialTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注 -->
                        <div class="layui-row" style="margin-top: 15px;">
                            <div class="layui-col-md12">
                                <label class="layui-form-label">备注说明</label>
                                <div class="layui-input-block">
                                    <textarea name="notes" placeholder="请输入备注说明" class="layui-textarea"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="layui-row" style="margin-top: 20px;">
                            <div class="layui-col-md12 layui-text-center">
                                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="save">保存领料单</button>
                                <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
                            </div>
                        </div>
                    </form>
        </div>
    </div>
</div>

<!-- 申请数量编辑模板 -->
<script type="text/html" id="requestQuantityTpl">
    <input type="number" class="layui-input" value="{{d.request_quantity}}" 
           onchange="updateRequestQuantity({{d.material_id}}, this.value)" 
           step="0.0001" min="0" style="width: 100px;">
</script>

<!-- 超领状态模板 -->
<script type="text/html" id="excessStatusTpl">
    {{# if (d.request_quantity > d.standard_quantity) { }}
        <span style="color: red; font-weight: bold;">超领 +{{(d.request_quantity - d.standard_quantity).toFixed(4)}}</span>
    {{# } else { }}
        <span style="color: green;">正常</span>
    {{# } }}
</script>

<!-- 库存状态模板 -->
<script type="text/html" id="stockStatusTpl">
    {{# if (d.available_stock < d.request_quantity) { }}
        <span style="color: red; font-weight: bold;">库存不足</span>
    {{# } else { }}
        <span style="color: green;">库存充足</span>
    {{# } }}
</script>

<!-- 操作模板 -->
<script type="text/html" id="operationTpl">
    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger"
            onclick="removeMaterial({{d.material_id}})" title="删除物料">
        <i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

{/block}

{block name="script"}
<script>
    // 全局变量
    var materialData = []; // 存储物料数据
    var currentOrder = null; // 当前选择的生产订单

    const moduleInit = ['tool','tablePlus','form'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool, layer = layui.layer;

        // 加载生产订单列表
        loadProductionOrders();

    // 监听生产订单选择
    form.on('select(productionOrder)', function(data) {
        var orderId = data.value;
        if (orderId) {
            loadBomMaterials(orderId);
        } else {
            $('#orderInfo').hide();
            materialData = [];
            renderMaterialTable();
        }
    });

    // 表单提交
    form.on('submit(save)', function(data) {
        if (materialData.length === 0) {
            layer.msg('请先选择生产订单并加载物料清单');
            return false;
        }

        // 检查是否有申请数量为0的物料
        var invalidMaterials = materialData.filter(function(item) {
            return item.request_quantity <= 0;
        });

        if (invalidMaterials.length > 0) {
            layer.msg('存在申请数量为0的物料，请检查');
            return false;
        }

        // 检查库存不足的物料
        var insufficientMaterials = materialData.filter(function(item) {
            return item.available_stock < item.request_quantity;
        });

        if (insufficientMaterials.length > 0) {
            var materialNames = insufficientMaterials.map(function(item) {
                return item.material_name;
            }).join('、');
            layer.msg('以下物料库存不足：' + materialNames);
            return false;
        }

        // 提交数据
        var submitData = data.field;
        submitData.materials = materialData;
        submitData.production_order_no = currentOrder.order_no;
        submitData.product_id = currentOrder.product_id;
        submitData.product_name = currentOrder.product_name;
        submitData.production_quantity = currentOrder.quantity;

        tool.post('/Produce/MaterialRequest/save', submitData, function(res) {
            if (res.code === 0) {
                layer.msg('保存成功');
                setTimeout(function() {
                    parent.layer.closeAll();
                }, 1500);
            } else {
                layer.msg(res.msg);
            }
        });

            return false;
        });

        // 加载生产订单列表
        function loadProductionOrders() {
            tool.get('/Produce/MaterialRequest/getProductionOrders', {}, function(res) {
            if (res.code === 0) {
                var html = '<option value="">请选择生产订单</option>';
                res.data.forEach(function(order) {
                    html += '<option value="' + order.id + '">' + order.order_no + ' - ' + order.product_name + ' (' + order.status_text + ')</option>';
                });
                $('select[name="production_order_id"]').html(html);
                form.render('select');
            }
            });
        }

        // 加载BOM物料
        function loadBomMaterials(orderId) {
            layer.load();
            tool.get('/Produce/MaterialRequest/getBomMaterials', {production_order_id: orderId}, function(res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                currentOrder = res.data.order;
                materialData = res.data.materials;
                
                // 显示订单信息
                $('#orderNo').text(currentOrder.order_no);
                $('#productName').text(currentOrder.product_name);
                $('#productionQuantity').text(currentOrder.quantity);
                $('#orderStatus').text(getOrderStatusText(currentOrder.status));
                $('#orderInfo').show();
                
                // 渲染物料表格
                renderMaterialTable();
            } else {
                layer.msg(res.msg);
            }
            });
        }

        // 渲染物料表格
        function renderMaterialTable() {
        $('#materialCount').text(materialData.length);
        
        table.render({
            elem: '#materialTable',
            data: materialData,
            cols: [[
                {field: 'material_code', title: '物料编码', width: 120},
                {field: 'material_name', title: '物料名称', width: 180},
                {field: 'material_specs', title: '规格', width: 100},
                {field: 'unit', title: '单位', width: 60},
                {field: 'bom_quantity', title: 'BOM用量', width: 90, align: 'right'},
                {field: 'standard_quantity', title: '标准需求', width: 90, align: 'right'},
                {field: 'received_quantity', title: '已领数量', width: 90, align: 'right', templet: function(d) {
                    return '<span style="color: #1E9FFF;">' + (d.received_quantity || 0) + '</span>';
                }},
                {field: 'remaining_quantity', title: '剩余需求', width: 90, align: 'right', templet: function(d) {
                    var remaining = d.remaining_quantity || 0;
                    var color = remaining > 0 ? '#FF5722' : '#5FB878';
                    return '<span style="color: ' + color + ';">' + remaining + '</span>';
                }},
                {field: 'available_stock', title: '可用库存', width: 90, align: 'right'},
                {field: 'request_quantity', title: '申请数量', width: 120, align: 'center', templet: '#requestQuantityTpl'},
                {title: '超领状态', width: 100, align: 'center', templet: '#excessStatusTpl'},
                {title: '库存状态', width: 90, align: 'center', templet: '#stockStatusTpl'},
                {title: '操作', width: 80, align: 'center', templet: '#operationTpl'}
            ]],
            page: false,
            height: 400,
            cellMinWidth: 80
            });
        }

        // 获取订单状态文本
        function getOrderStatusText(status) {
        var statusMap = {
            0: '待排产',
            1: '已排产',
            2: '生产中'
        };
            return statusMap[status] || '未知';
        }

        // 更新申请数量
        window.updateRequestQuantity = function(materialId, quantity) {
            quantity = parseFloat(quantity) || 0;

            // 更新数据
            for (var i = 0; i < materialData.length; i++) {
                if (materialData[i].material_id == materialId) {
                    materialData[i].request_quantity = quantity;
                    break;
                }
            }

            // 重新渲染表格
            layui.table.reload('materialTable', {
                data: materialData
            });
        }

        // 删除物料
        window.removeMaterial = function(materialId) {
            layer.confirm('确定要删除这个物料吗？', {
                icon: 3,
                title: '确认删除'
            }, function(index) {
                // 从数组中删除
                for (var i = 0; i < materialData.length; i++) {
                    if (materialData[i].material_id == materialId) {
                        materialData.splice(i, 1);
                        break;
                    }
                }

                // 重新渲染表格
                renderMaterialTable();
                layer.close(index);
                layer.msg('物料已删除');
            });
        }
    }
</script>
{/block}
