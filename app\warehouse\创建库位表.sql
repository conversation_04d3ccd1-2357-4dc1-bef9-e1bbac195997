-- 创建仓库库位表
-- 用于管理仓库内的具体存储位置

CREATE TABLE IF NOT EXISTS `oa_warehouse_location` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库位ID',
  `warehouse_id` int(11) NOT NULL COMMENT '所属仓库ID',
  `code` varchar(50) NOT NULL COMMENT '库位编码',
  `name` varchar(100) NOT NULL COMMENT '库位名称',
  `type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '库位类型：1=普通存储区,2=拣货区,3=收货区,4=发货区,5=退货区,6=质检区,7=溢出区,8=残次品区',
  `zone` varchar(50) DEFAULT '' COMMENT '区域编码',
  `rack` varchar(50) DEFAULT '' COMMENT '货架编号',
  `level` varchar(50) DEFAULT '' COMMENT '层级编号',
  `position` varchar(50) DEFAULT '' COMMENT '位置编号',
  `length` decimal(8,2) DEFAULT 0.00 COMMENT '长度(米)',
  `width` decimal(8,2) DEFAULT 0.00 COMMENT '宽度(米)',
  `height` decimal(8,2) DEFAULT 0.00 COMMENT '高度(米)',
  `max_weight` decimal(10,2) DEFAULT 0.00 COMMENT '最大承重(公斤)',
  `max_capacity` decimal(10,2) DEFAULT 0.00 COMMENT '最大容量(立方米)',
  `current_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '当前库存数量',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用,1=启用',
  `is_pickable` tinyint(1) DEFAULT 1 COMMENT '是否可拣货：0=否,1=是',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_warehouse_code` (`warehouse_id`, `code`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库库位表';

-- 插入一些默认库位数据
INSERT INTO `oa_warehouse_location` (`warehouse_id`, `code`, `name`, `type`, `zone`, `rack`, `level`, `position`, `status`, `is_pickable`, `notes`, `created_by`, `create_time`, `update_time`) VALUES
(1, 'A01-01-01', 'A区1号货架1层1位', 1, 'A01', '01', '01', '01', 1, 1, '默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'A01-01-02', 'A区1号货架1层2位', 1, 'A01', '01', '01', '02', 1, 1, '默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'A01-02-01', 'A区1号货架2层1位', 1, 'A01', '01', '02', '01', 1, 1, '默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'B01-01-01', 'B区1号货架1层1位', 1, 'B01', '01', '01', '01', 1, 1, '默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'RECV-01', '收货区1号位', 3, 'RECV', '', '', '01', 1, 0, '收货专用区域', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, 'SHIP-01', '发货区1号位', 4, 'SHIP', '', '', '01', 1, 1, '发货专用区域', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 如果有其他仓库，也可以添加对应的库位
-- 这里假设仓库ID为2和3的仓库也存在
INSERT INTO `oa_warehouse_location` (`warehouse_id`, `code`, `name`, `type`, `zone`, `rack`, `level`, `position`, `status`, `is_pickable`, `notes`, `created_by`, `create_time`, `update_time`) VALUES
(2, 'A01-01-01', 'A区1号货架1层1位', 1, 'A01', '01', '01', '01', 1, 1, '分仓库A默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'A01-01-02', 'A区1号货架1层2位', 1, 'A01', '01', '01', '02', 1, 1, '分仓库A默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'A01-01-01', 'A区1号货架1层1位', 1, 'A01', '01', '01', '01', 1, 1, '分仓库B默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'A01-01-02', 'A区1号货架1层2位', 1, 'A01', '01', '01', '02', 1, 1, '分仓库B默认库位', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 显示创建结果
SELECT '库位表创建完成！' as message;
SELECT COUNT(*) as location_count FROM `oa_warehouse_location`;
