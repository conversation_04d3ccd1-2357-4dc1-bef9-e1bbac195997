<?php
/**
 * 实时库存API控制器
 * 为移动端和第三方系统提供库存管理接口
 */

namespace app\warehouse\controller\api;

use app\api\BaseController;
use app\warehouse\service\InventoryRealtimeService;
use app\warehouse\service\InventoryLockServiceNew;
use think\Request;
use think\response\Json;

class InventoryRealtimeApi extends BaseController
{
    protected $inventoryService;
    protected $lockService;

    public function initialize()
    {
        parent::initialize();
        $this->inventoryService = new InventoryRealtimeService();
        $this->lockService = new InventoryLockServiceNew();
    }

    /**
     * 获取库存列表
     * @param Request $request
     * @return Json
     */
    public function index(Request $request): Json
    {
        try {
            $params = $request->param();
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $warehouseId = $params['warehouse_id'] ?? '';
            $productId = $params['product_id'] ?? '';
            $keywords = $params['keywords'] ?? '';

            $where = [];
            if (!empty($warehouseId)) {
                $where[] = ['warehouse_id', '=', $warehouseId];
            }
            if (!empty($productId)) {
                $where[] = ['product_id', '=', $productId];
            }

            $result = $this->inventoryService->getInventoryList($where, $page, $limit, $keywords);

            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取库存详情
     * @param Request $request
     * @return Json
     */
    public function detail(Request $request): Json
    {
        try {
            $id = $request->param('id');
            if (empty($id)) {
                return $this->error('参数错误');
            }

            $detail = $this->inventoryService->getInventoryDetail($id);
            if (!$detail) {
                return $this->error('库存记录不存在');
            }

            return $this->success('获取成功', $detail);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 库存入库
     * @param Request $request
     * @return Json
     */
    public function inbound(Request $request): Json
    {
        try {
            $params = $request->param();
            
            // 验证必要参数
            $required = ['product_id', 'warehouse_id', 'quantity', 'unit', 'cost_price'];
            foreach ($required as $field) {
                if (empty($params[$field])) {
                    return $this->error("参数{$field}不能为空");
                }
            }

            // 验证数量和价格
            if ($params['quantity'] <= 0) {
                return $this->error('入库数量必须大于0');
            }
            if ($params['cost_price'] < 0) {
                return $this->error('成本价不能小于0');
            }

            $data = [
                'product_id' => $params['product_id'],
                'warehouse_id' => $params['warehouse_id'],
                'quantity' => $params['quantity'],
                'unit' => $params['unit'],
                'cost_price' => $params['cost_price'],
                'ref_type' => $params['ref_type'] ?? 'manual',
                'ref_no' => $params['ref_no'] ?? '',
                'ref_id' => $params['ref_id'] ?? 0,
                'notes' => $params['notes'] ?? '',
                'operator_id' => $this->uid
            ];

            $result = $this->inventoryService->increaseStock($data);
            
            return $this->success('入库成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 库存出库
     * @param Request $request
     * @return Json
     */
    public function outbound(Request $request): Json
    {
        try {
            $params = $request->param();
            
            // 验证必要参数
            $required = ['product_id', 'warehouse_id', 'quantity'];
            foreach ($required as $field) {
                if (empty($params[$field])) {
                    return $this->error("参数{$field}不能为空");
                }
            }

            // 验证数量
            if ($params['quantity'] <= 0) {
                return $this->error('出库数量必须大于0');
            }

            $data = [
                'product_id' => $params['product_id'],
                'warehouse_id' => $params['warehouse_id'],
                'quantity' => $params['quantity'],
                'ref_type' => $params['ref_type'] ?? 'manual',
                'ref_no' => $params['ref_no'] ?? '',
                'ref_id' => $params['ref_id'] ?? 0,
                'notes' => $params['notes'] ?? '',
                'operator_id' => $this->uid
            ];

            $result = $this->inventoryService->decreaseStock($data);
            
            return $this->success('出库成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 库存锁定
     * @param Request $request
     * @return Json
     */
    public function lock(Request $request): Json
    {
        try {
            $params = $request->param();
            
            // 验证必要参数
            $required = ['product_id', 'warehouse_id', 'quantity', 'lock_reason'];
            foreach ($required as $field) {
                if (empty($params[$field])) {
                    return $this->error("参数{$field}不能为空");
                }
            }

            // 验证数量
            if ($params['quantity'] <= 0) {
                return $this->error('锁定数量必须大于0');
            }

            $data = [
                'product_id' => $params['product_id'],
                'warehouse_id' => $params['warehouse_id'],
                'quantity' => $params['quantity'],
                'lock_reason' => $params['lock_reason'],
                'ref_type' => $params['ref_type'] ?? 'manual',
                'ref_no' => $params['ref_no'] ?? '',
                'ref_id' => $params['ref_id'] ?? 0,
                'notes' => $params['notes'] ?? '',
                'operator_id' => $this->uid
            ];

            $result = $this->lockService->lockInventory($data);
            
            return $this->success('锁定成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 释放库存锁定
     * @param Request $request
     * @return Json
     */
    public function unlock(Request $request): Json
    {
        try {
            $lockId = $request->param('lock_id');
            if (empty($lockId)) {
                return $this->error('锁定ID不能为空');
            }

            $result = $this->lockService->releaseLock($lockId, $this->uid);
            
            return $this->success('释放成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 使用锁定库存
     * @param Request $request
     * @return Json
     */
    public function useLocked(Request $request): Json
    {
        try {
            $params = $request->param();
            
            $lockId = $params['lock_id'] ?? '';
            $useQuantity = $params['use_quantity'] ?? 0;
            
            if (empty($lockId)) {
                return $this->error('锁定ID不能为空');
            }
            if ($useQuantity <= 0) {
                return $this->error('使用数量必须大于0');
            }

            $data = [
                'lock_id' => $lockId,
                'use_quantity' => $useQuantity,
                'ref_type' => $params['ref_type'] ?? 'manual',
                'ref_no' => $params['ref_no'] ?? '',
                'ref_id' => $params['ref_id'] ?? 0,
                'notes' => $params['notes'] ?? '',
                'operator_id' => $this->uid
            ];

            $result = $this->lockService->useLock($data);
            
            return $this->success('使用成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查库存是否充足
     * @param Request $request
     * @return Json
     */
    public function checkStock(Request $request): Json
    {
        try {
            $params = $request->param();
            
            $productId = $params['product_id'] ?? '';
            $warehouseId = $params['warehouse_id'] ?? '';
            $quantity = $params['quantity'] ?? 0;
            
            if (empty($productId) || empty($warehouseId)) {
                return $this->error('产品ID和仓库ID不能为空');
            }
            if ($quantity <= 0) {
                return $this->error('检查数量必须大于0');
            }

            $result = $this->inventoryService->checkStock($productId, $warehouseId, $quantity);
            
            return $this->success('检查完成', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取库存统计
     * @param Request $request
     * @return Json
     */
    public function statistics(Request $request): Json
    {
        try {
            $params = $request->param();
            $warehouseId = $params['warehouse_id'] ?? '';
            $productId = $params['product_id'] ?? '';

            $result = $this->inventoryService->getInventoryStatistics($warehouseId, $productId);
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取库存预警列表
     * @param Request $request
     * @return Json
     */
    public function alerts(Request $request): Json
    {
        try {
            $params = $request->param();
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $warehouseId = $params['warehouse_id'] ?? '';

            $result = $this->inventoryService->getInventoryAlerts($warehouseId, $page, $limit);
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
