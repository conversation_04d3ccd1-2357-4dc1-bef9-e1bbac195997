{extend name="../../base/view/common/base" /}

{block name="head"}
<style>
    .delivery-error .layui-table-cell {
        background-color: #FFD0D0 !important;
        color: #FF0000 !important;
    }
    .delivery-warning .layui-table-cell {
        background-color: #FFFFD0 !important;
        color: #FF6600 !important;
    }

    /* 库存显示样式 */
    .stock-info {
        text-align: center;
        min-width: 180px;
    }
    .stock-qty {
        font-weight: bold;
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    .text-success {
        color: #52c41a;
    }
    .text-warning {
        color: #faad14;
    }
    .text-danger {
        color: #f5222d;
    }
    .stock-detail {
        font-size: 9px !important;
        color: #666 !important;
        line-height: 1.2 !important;
        margin-top: 3px !important;
        max-width: 260px;
        overflow: visible;
    }
    .stock-detail div {
        margin: 1px 0;
        white-space: nowrap;
    }
    .stock-detail strong {
        color: #333;
        font-weight: 600;
    }

    /* 库存状态颜色 */
    .stock-available { color: #52c41a; }
    .stock-locked-order { color: #1890ff; }
    .stock-locked-other { color: #fa8c16; }
    .stock-pending { color: #f5222d; }



    /* 父子商品关系样式 - 增强版 */
    .child-product {
        background-color: #f8f8f8 !important;
        border-left: 4px solid #1E9FFF !important;
    }
    .child-product td {
        border-bottom: 1px solid #e6e6e6 !important;
    }
    .child-product-name {
        padding-left: 35px !important;
        position: relative;
        color: #333;
        font-weight: normal;
    }
    .child-product-name:before {
        content: "└─";
        position: absolute;
        left: 10px;
        color: #1E9FFF;
        font-weight: bold;
        font-size: 16px;
    }
    /* 父商品标记 */
    .parent-product-marker {
        display: inline-block;
        background-color: #1E9FFF;
        color: white;
        font-size: 12px;
        padding: 0 5px;
        border-radius: 3px;
        margin-right: 5px;
        vertical-align: middle;
    }

    /* 表格行样式 - 简化版本 */
    .layui-table tbody tr.child-product {
        background-color: #f8f8f8 !important;
        border-left: 3px solid #1E9FFF !important;
    }

    .layui-table tbody tr.child-product td {
        border-bottom: 1px solid #f0f0f0 !important;
    }

    /* 序号列样式 */
    .sequence-main {
        text-align: center;
        font-weight: bold;
        font-size: 14px;
        color: #1E9FFF;
        background-color: #e3f2fd;
        padding: 4px;
        border-radius: 4px;
    }
    .sequence-sub {
        height: 20px;
    }

    /* 表格样式 */
    #itemTable {
        width: 100%;
        table-layout: fixed;
    }

    /* 增加表格行高以显示详细库存信息 */
    .layui-table tbody tr {
        height: auto !important;
        min-height: 80px !important;
    }

    .layui-table tbody tr td {
        height: auto !important;
        min-height: 80px !important;
        vertical-align: top !important;
        padding: 8px 15px !important;
    }

    /* 确保库存详情可见 */
    .layui-table tbody tr td .stock-info {
        height: auto !important;
        min-height: 70px !important;
        overflow: visible !important;
    }

    /* 强制显示库存详情 */
    .stock-info .stock-main {
        margin-bottom: 5px !important;
    }

    .stock-info div[style*="background: #f5f5f5"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        min-height: 40px !important;
        background: #f5f5f5 !important;
        border: 1px solid #ddd !important;
        padding: 5px !important;
        margin-top: 5px !important;
    }
    .delivery-error {
        background-color: #FFE4E1;
    }
    .delivery-warning {
        background-color: #FFFACD;
    }

    /* 单位显示样式 */
    .unit-info {
        text-align: center;
    }
    .unit-main {
        font-weight: bold;
        font-size: 13px;
        color: #333;
    }
    .unit-specs {
        font-size: 11px;
        color: #999;
        margin-top: 2px;
    }
</style>
{/block}

{block name="body"}
<div class="p-page">
    <form class="layui-form" id="deliveryForm" lay-filter="deliveryForm" method="post">
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">基本信息</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="text" id="order_no" name="order_no" class="layui-input" readonly placeholder="请选择销售订单" />
                                    <div class="layui-input-split layui-input-suffix" style="cursor: pointer;" onclick="selectOrder()">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </div>
                                </div>
                                <input type="hidden" id="order_id" name="order_id" value="{$Request.param.order_id|default=''}" />
                            </div>
                        </div>
                    
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户名称</label>
                            <div class="layui-input-block">
                                <input type="text" id="customer_name" name="customer_name" class="layui-input" readonly />
                                <input type="hidden" id="customer_id" name="customer_id" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">收货地址</label>
                            <div class="layui-input-block">
                                <input type="text" id="address" name="address" class="layui-input" required lay-verify="required" placeholder="请输入收货地址" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系人</label>
                            <div class="layui-input-block">
                                <input type="text" id="contact" name="contact" class="layui-input" required lay-verify="required" placeholder="请输入联系人" />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">联系电话</label>
                            <div class="layui-input-block">
                                <input type="text" id="phone" name="phone" class="layui-input" required lay-verify="required|phone" placeholder="请输入联系电话" />
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">物流信息</div>
                    <div class="layui-card-body">
                        <div class="layui-form-item">
                            <label class="layui-form-label">发货方式</label>
                            <div class="layui-input-block">
                                <select name="delivery_type" lay-verify="required">
                                    <option value="">请选择发货方式</option>
                                    <option value="1">公司物流</option>
                                    <option value="2">外部物流</option>
                                    <option value="3">客户自提</option>
                                </select>
                            </div>
                        </div>
                       
                        <div class="layui-form-item">
                            <label class="layui-form-label">发货日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="expect_time" id="expect_time" lay-verify="required" placeholder="请选择预计发货日期" class="layui-input" />
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">特殊要求</label>
                            <div class="layui-input-block">
                                <textarea name="remark" placeholder="请输入特殊要求或备注信息" class="layui-textarea"></textarea>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card">
            <div class="layui-card-header">发货商品</div>
            <div class="layui-card-body">
                <div class="layui-btn-group" style="margin-bottom:10px;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="removeSelectedItems"><i class="layui-icon layui-icon-delete"></i> 删除选中</button>
                </div>
                <table class="layui-table" id="itemTable" lay-filter="itemTable">
                    <!-- 表格内容将通过Layui table组件渲染 -->
                </table>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveDelivery">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
            </div>
        </div>
    </form>
</div>
{/block}



{block name="script"}

<!-- 库存显示模板 -->
<script type="text/html" id="stockTpl">
    <div class="stock-info" style="text-align: center;">
        <!-- 可用库存数量 -->
        <div class="stock-main" style="margin-bottom: 5px;" title="可发货数量 = 系统可用库存 + 当前订单锁定库存">
            <span class="stock-qty {{# if(d.available_qty >= (d.quantity - d.delivered_qty)) { }}text-success{{# } else if(d.available_qty > 0) { }}text-warning{{# } else { }}text-danger{{# } }}" style="font-weight: bold; font-size: 16px;">
                {{ d.available_qty || 0 }}
            </span>
            <div style="font-size: 10px; color: #999; margin-top: 2px;">
                <i class="layui-icon layui-icon-tips" style="font-size: 12px;"></i> 可发货数量
            </div>
            <div style="font-size: 9px; color: #666; margin-top: 3px; line-height: 1.2;">
                <div>总库存: {{ d.inventory_qty }}</div>
                <div style="color: #28a745;">系统可用: {{ d.system_available_qty }}</div>
            </div>
        </div>


    </div>
</script>

<!-- 订单锁定数量显示模板 -->
<script type="text/html" id="lockedTpl">
    <div style="text-align: center;">
        <div style="margin-bottom: 3px;">
            <span style="color: #17a2b8; font-weight: bold; font-size: 14px;">
                {{ d.current_order_locked || 0 }}
            </span>
        </div>
        <div style="font-size: 10px; color: #999;">
            当前订单锁定
        </div>
        {{# if(d.system_locked_qty && parseFloat(d.system_locked_qty) > 0) { }}
        <div style="font-size: 9px; color: #fd7e14; margin-top: 2px;">
            其他锁定: {{ d.system_locked_qty }}
        </div>
        {{# } }}
    </div>
</script>

<!-- 操作列模板 -->
<script type="text/html" id="operationTpl">
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">
        <i class="layui-icon layui-icon-delete"></i>
    </a>
</script>

<!-- 已发货数量显示模板 -->
<script type="text/html" id="deliveredTpl">
    <div style="text-align: center;">
        <span style="color: #1E9FFF; font-weight: bold; font-size: 14px;">
            {{ parseFloat(d.delivered_qty || 0) }}
        </span>
        <div style="font-size: 10px; color: #999; margin-top: 2px;">
            累计发货
        </div>
    </div>
</script>

<!-- 本次发货输入框模板 -->
<script type="text/html" id="deliveryQtyTpl">
    <div style="text-align: center;">
        {{#
            var orderQty = parseFloat(d.quantity || 0);
            var deliveredQty = parseFloat(d.delivered_qty || 0);
            var availableStock = parseFloat(d.available_qty || 0);
            var remainingQty = orderQty - deliveredQty;
            var maxDelivery = Math.min(remainingQty, availableStock);
            var currentDelivery = parseFloat(d.delivery_qty || 0);
        }}

        {{# if(remainingQty > 0 && availableStock > 0) { }}
            <input type="number"
                   value="{{ currentDelivery }}"
                   min="0"
                   max="{{ maxDelivery }}"
                   step="0.01"
                   style="width: 120px; height: 32px; border: 2px solid #1E9FFF; border-radius: 4px; text-align: center; font-weight: bold; color: #1E9FFF; background: linear-gradient(135deg, #f8fcff 0%, #e8f4ff 100%); box-shadow: 0 1px 3px rgba(30,159,255,0.2);"
                   placeholder="输入数量"
                   data-index="{{ d.LAY_TABLE_INDEX }}"
                   data-product-id="{{ d.product_id }}"
                   data-order-item-id="{{ d.id }}"
                   data-max="{{ maxDelivery }}"
                   data-remaining="{{ remainingQty }}"
                   data-stock="{{ availableStock }}"
                   class="delivery-qty-input" />
            <div style="font-size: 10px; color: #666; margin-top: 2px;">
                最多: {{ maxDelivery }}
            </div>
        {{# } else { }}
            <div style="text-align: center; padding: 6px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 4px;">
                <div style="color: #cf1322; font-size: 12px; font-weight: bold;">无法发货</div>
                <div style="font-size: 10px; color: #8c8c8c;">
                    {{# if(remainingQty <= 0) { }}
                        订单已发完
                    {{# } else { }}
                        库存不足
                    {{# } }}
                </div>
            </div>
        {{# } }}
    </div>
</script>

<!-- 序号显示模板 -->
<script type="text/html" id="sequenceTpl">
    <div style="text-align: center;">
        {{# if(!d.parent_product_id || d.parent_product_id == 0) { }}
            <span style="font-weight: bold;">{{ d.sequence_number || '' }}</span>
        {{# } else { }}

        {{# } }}
    </div>
</script>

<!-- 商品名称显示模板 -->
<script type="text/html" id="productNameTpl">
    {{# if(d.parent_product_id == 0) { }}
        <!-- 主品显示 -->
        <div style="font-weight: 500;">
            <span class="layui-badge layui-bg-blue" style="margin-right: 8px; font-size: 11px;">主品</span>
            {{# if(d.child_count > 0) { }}
                <span class="layui-badge layui-bg-green" style="margin-right: 8px; font-size: 10px;" title="包含{{ d.child_count }}个子商品">{{ d.child_count }}</span>
            {{# } }}
            {{ d.product_name }}
        </div>
    {{# } else { }}
        <!-- 子品显示 -->
        <div class="child-product-name">
            <span class="layui-badge layui-bg-orange" style="margin-right: 8px; font-size: 11px;">子品</span>
            {{ d.product_name }}
        </div>
    {{# } }}
</script>

<!-- 单位显示模板 -->
<script type="text/html" id="unitTpl2">
    <div style="text-align: center;">
        {{# if(d.parent_product_id == 0) { }}
            <!-- 主品不显示单位，显示横线 -->
            <span style="color: #999; font-weight: bold; font-size: 16px;">-</span>
        {{# } else { }}
            <!-- 子品显示数量 -->
            <span style="color: #333; font-weight: 500;">{{ d.quantity }}</span>
        {{# } }}
    </div>
</script>

<script>
    // 扩展Date对象的format方法
    Date.prototype.format = function(fmt) {
        var o = {
            "M+": this.getMonth() + 1, // 月份
            "d+": this.getDate(), // 日
            "H+": this.getHours(), // 小时
            "m+": this.getMinutes(), // 分
            "s+": this.getSeconds(), // 秒
            "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
            "S": this.getMilliseconds() // 毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };

    const moduleInit = ['tool'];
    
    function gouguInit() {
        var tool = layui.tool,
        form = layui.form,
        laydate = layui.laydate,
        $ = layui.jquery,
        table = layui.table;
        var orderId = $("#order_id").val();

        // 选择订单函数
        window.selectOrder = function() {
            // 设置全局回调函数
            window._layui_layer_callback = function(data) {
                if (data && data.length > 0) {
                    var order = data[0]; // 取第一个选中的订单
                    $("#order_id").val(order.id);
                    $("#order_no").val(order.order_no);
                    $("#customer_id").val(order.customer_id);
                    $("#customer_name").val(order.customer_name || (order.customer ? order.customer.name : ''));

                    // 加载订单商品
                    loadOrderItems(order.id);

                    layer.msg('订单选择成功', {icon: 1});
                }
            };

            // 打开选择订单窗口
            layer.open({
                type: 2,
                title: '选择销售订单',
                area: ['80%', '90%'],
                content: '/customer/delivery/selectOrder',
                maxmin: true
            });
        };
        
        // 生成随机发货单号
        var deliveryNo = 'FH' + new Date().format('yyyyMMddHHmmss') + Math.floor(Math.random() * 9000 + 1000);
        $("input[name='delivery_no']").val(deliveryNo);
        
        // 初始化日期选择器
        laydate.render({
            elem: '#expect_time',
            type: 'date'
        });
        
        // 如果有订单ID，则加载订单信息
        if (orderId) {
             loadOrderInfo(orderId);
        } else {
            // 没有订单ID，弹出选择订单窗口
            layer.open({
                type: 2,
                title: '选择订单',
                area: ['900px', '600px'],
                btn: false,
                content: '/customer/delivery/selectOrder',
                success: function(layero, index) {
                    // 页面会处理关闭窗口的逻辑
                }
            });
        }
        
        // 加载订单信息
        function loadOrderInfo(orderId) {
            $.get('/customer/delivery/getOrderInfo', {id: orderId}, function(res) {
                if (res.code == 0) {
                    var order = res.data.order;
                    // 设置订单基础信息
                    $("#order_no").val(order.order_no);
                    $("#customer_id").val(order.customer_id);
                    $("#customer_name").val(order.customer ? order.customer.name : '');
                    
                    // 如果有收货地址信息，则填充
                    if (order.address) {
                        $("#address").val(order.address);
                    }
                    if (order.contact) {
                        $("#contact").val(order.contact);
                    }
                    if (order.phone) {
                        $("#phone").val(order.phone);
                    }
                    
                    // 加载订单商品
                    loadOrderItems(orderId);
                } else {
                    layer.msg(res.msg);
                }
            });
        }
        
        // 加载订单商品
        function loadOrderItems(orderId) {
            $.get('/customer/delivery/getOrderItems', {order_id: orderId}, function(res) {
                if (res.code == 0) {
                    var items = res.data.items;
 
                    // 检查每个商品的关键字段
                    items.forEach(function(item, index) {
                        if (index < 2) { // 只输出前2个商品的详细信息
                            console.log('商品' + index + '完整对象:', item);
                            console.log('商品' + index + '库存字段:', {
                                available_qty: item.available_qty,
                                inventory_qty: item.inventory_qty,
                                current_order_locked: item.current_order_locked,
                                allocation_status: item.allocation_status,
                                stock_qty: item.stock_qty,
                                total_stock: item.total_stock,
                                locked_qty: item.locked_qty
                            });
                        }
                    });

                    // 检查模板是否存在
                 
                    // 使用Layui table组件渲染数据
                    table.render({
                        elem: '#itemTable',
                        data: items,
                        cols: [[
                            {type:'checkbox', width:50},
                            {field:'sequence_number', width:60, templet:'#sequenceTpl', title:'序号'},
                            {field:'material_code', width:150, title:'材料编码'},
                            {field:'product_name', width:300, templet:'#productNameTpl', title:'产品名称'},
                            {field:'quantity', width:90, title:'数量'},
                            {field:'unit', width:80, templet:'#unitTpl2', title:'每套/件'},
                            {field:'delivered_qty', width:100, templet:'#deliveredTpl', title:'已发货'},
                            {field:'available_qty', width:200, templet:'#stockTpl', title:'可用库存', align:'center'},
                            {field:'current_order_locked', width:120, templet:'#lockedTpl', title:'库存锁定', align:'center'},
                            {field:'delivery_qty', width:150, templet:'#deliveryQtyTpl', title:'本次发货', align:'center'},
                            {width:80, align:'center', toolbar: '#operationTpl', title:'操作'}
                        ]],
                        skin: 'line',
                        even: false,
                        page: false,
                        limit: 1000,
                        height: 'full-200', // 自适应高度
                        cellMinWidth: 80, // 最小列宽
                        size: 'lg', // 大尺寸，增加行高
                        done: function(res, curr, count) {
                            console.log('表格渲染完成，数据:', res);
                            console.log('第一行数据:', res.data && res.data[0]);

                            // 显示库存说明提示
                            layer.tips('库存说明：<br/>• 可发货数量 = 系统可用 + 订单锁定<br/>• 绿色：库存充足<br/>• 黄色：库存紧张<br/>• 红色：库存不足',
                                $('.layui-table-header th:contains("可用库存")'), {
                                tips: [1, '#3595CC'],
                                time: 5000
                            });

                            // 为子品行添加特殊样式
                            setTimeout(function() {
                                $('.layui-table tbody tr').each(function(index) {
                                    var item = items[index];
                                    if (item && item.is_child) {
                                        $(this).addClass('child-product');
                                    }
                                });
                            }, 100);
                        }
                    });
                } else {
                    layer.msg(res.msg);
                }
            });
        }

        // 监听本次发货输入框的变化
        $(document).on('input change blur', '.delivery-qty-input', function() {
            var $input = $(this);
            var value = parseFloat($input.val()) || 0;
            var maxQty = parseFloat($input.data('max')) || 0;
            var remainingQty = parseFloat($input.data('remaining')) || 0;
            var stockQty = parseFloat($input.data('stock')) || 0;
            var index = $input.data('index');

            // 验证输入值
            if (value < 0) {
                $input.val(0);
                layer.msg('发货数量不能为负数', {icon: 2, time: 2000});
                return;
            }

            // 检查是否超过订单剩余数量
            if (value > remainingQty) {
                $input.val(remainingQty);
                layer.msg('发货数量不能超过订单剩余数量: ' + remainingQty, {icon: 2, time: 3000});
                return;
            }

            // 检查是否超过库存数量
            if (value > stockQty) {
                $input.val(stockQty);
                layer.msg('发货数量不能超过库存数量: ' + stockQty, {icon: 2, time: 3000});
                return;
            }

            // 检查是否超过最大可发货数量
            if (value > maxQty) {
                $input.val(maxQty);
                var limitReason = remainingQty < stockQty ? '订单剩余数量' : '库存数量';
                layer.msg('发货数量受' + limitReason + '限制，最多可发: ' + maxQty, {icon: 2, time: 3000});
                return;
            }

            // 更新表格数据
            if (typeof index !== 'undefined' && table.cache.itemTable && table.cache.itemTable[index]) {
                table.cache.itemTable[index].delivery_qty = value;
            }

            // 如果输入有效，给予正面反馈
            if (value > 0 && value <= maxQty) {
                $input.css('border-color', '#52c41a');
                setTimeout(function() {
                    $input.css('border-color', '#1E9FFF');
                }, 1000);
            }
        });

        // 阻止表单默认提交
        $('#deliveryForm').on('submit', function(e) {
            e.preventDefault();
            return false;
        });

        // 提交表单
        form.on('submit(saveDelivery)', function(data) {
            // 阻止默认表单提交
            if (data && data.elem) {
                data.elem.preventDefault && data.elem.preventDefault();
            }
            // 获取表单数据
            var formData = data.field;
             
            // 获取商品数据
            var tableData = table.cache.itemTable || [];
            var items = [];

            console.log('原始表格数据:', tableData);
            console.log('输入框数量:', $('.delivery-qty-input').length);

            // 手动收集输入框中的发货数量
            $('.delivery-qty-input').each(function() {
                var $input = $(this);
                var orderItemId = $input.data('order-item-id');
                var value = parseFloat($input.val()) || 0;
                console.log('输入框数据:', {orderItemId: orderItemId, value: value, inputVal: $input.val()});

                // 根据order_item_id找到对应的数据项
                for (var j = 0; j < tableData.length; j++) {
                    if (tableData[j].id == orderItemId) {
                        tableData[j].delivery_qty = value;
                        console.log('更新后的数据项:', tableData[j]);
                        break;
                    }
                }
            });

            console.log('更新后表格数据:', tableData);

            // 验证库存是否充足
            var hasInsufficientStock = false;
            var insufficientStockItems = [];

            for (var i = 0; i < tableData.length; i++) {
                var item = tableData[i];
                // 只检查有发货数量的商品
                if (item.delivery_qty && parseFloat(item.delivery_qty) > 0) {
                    // 检查库存是否充足
                    if (parseFloat(item.delivery_qty) > parseFloat(item.available_qty || 0)) {
                        hasInsufficientStock = true;
                        insufficientStockItems.push({
                            name: item.product_name,
                            code: item.material_code,
                            inventory: item.available_qty || 0,
                            delivery: item.delivery_qty
                        });
                    }
                    
                    items.push({
                        order_item_id: item.id,
                        delivery_qty: item.delivery_qty,
                        remark: item.remark || ''
                    });
                }
            }
            
            // 如果有库存不足的商品，阻止表单提交
            if (hasInsufficientStock) {
                var errorMsg = '以下商品库存不足，无法提交：<br>';
                for (var j = 0; j < insufficientStockItems.length; j++) {
                    var errorItem = insufficientStockItems[j];
                    errorMsg += '- ' + errorItem.name + ' (' + errorItem.code + '): 库存' + errorItem.inventory + '，发货' + errorItem.delivery + '<br>';
                }
                layer.alert(errorMsg, {icon: 2, title: '库存不足'});
                return false;
            }
            
             
            // 验证是否有发货商品
            if (items.length === 0) {
                layer.msg('请至少选择一件商品发货', {icon: 2});
                return false;
            }
            
            // 提交数据
            $.ajax({
                url: '/customer/delivery/save',
                type: 'POST',
                data: {
                    delivery: formData,
                    items: items
                },
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        layer.msg('提交成功，已添加到待出库清单', {icon: 1});
                        setTimeout(function() {
                            parent.layer.closeAll();
                        }, 1500);
                    } else {
                        layer.msg(res.msg || '提交失败', {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('提交错误:', xhr.responseText);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });

            return false;
        });

        // 监听工具条事件
        table.on('tool(itemTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定删除这个商品吗？', function(index) {
                    obj.del();
                    layer.close(index);
                    layer.msg('删除成功', {icon: 1});
                });
            }
        });

        // 删除选中商品
        $('#removeSelectedItems').click(function() {
            var checkStatus = table.checkStatus('itemTable');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请选择要删除的商品', {icon: 2});
                return;
            }

            layer.confirm('确定删除选中的 ' + data.length + ' 个商品吗？', function(index) {
                // 获取选中行的索引
                var selectedIndexes = [];
                for (var i = 0; i < data.length; i++) {
                    var tableData = table.cache.itemTable || [];
                    for (var j = 0; j < tableData.length; j++) {
                        if (tableData[j] === data[i]) {
                            selectedIndexes.push(j);
                            break;
                        }
                    }
                }

                // 从后往前删除，避免索引变化
                selectedIndexes.sort(function(a, b) { return b - a; });
                for (var k = 0; k < selectedIndexes.length; k++) {
                    table.cache.itemTable.splice(selectedIndexes[k], 1);
                }

                // 重新渲染表格
                table.reload('itemTable', {
                    data: table.cache.itemTable
                });

                layer.close(index);
                layer.msg('删除成功', {icon: 1});
            });
        });

        // 取消按钮
        $("#btnCancel").click(function() {
            tool.tabChange('/customer/delivery/index');
        });
    }
</script>
{/block} 