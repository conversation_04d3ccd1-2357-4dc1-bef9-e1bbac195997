# 客户订单编辑页面缺少字段问题修复

## 问题描述
客户订单编辑页面（/customer/order/edit）缺少管销费（glf）和运费（yunfei）字段，而添加页面（/customer/order/add）中有这两个字段。

## 问题分析
1. **数据库表结构**：`oa_customer_order` 表中确实存在 `yunfei`（运费）和 `glf`（管理费）字段
2. **添加页面**：`app/customer/view/order/add.html` 中包含这两个字段的表单输入项
3. **编辑页面**：`app/customer/view/order/edit.html` 中缺少这两个字段的表单输入项

## 解决方案
在编辑页面的表单中添加管销费和运费字段，保持与添加页面一致的布局和功能。

### 需要修改的文件
1. `app/customer/view/order/edit.html` - 添加管销费和运费表单字段

### 实现步骤
1. 在编辑页面的适当位置添加管销费和运费的表单输入项
2. 确保字段名称与数据库字段一致（glf 和 yunfei）
3. 设置默认值为订单数据中的对应字段值
4. 保持与添加页面相同的样式和布局

## 技术细节
- 字段名称：`glf`（管销费）、`yunfei`（运费）
- 数据类型：decimal(10,3)
- 表单类型：text input
- 布局：使用 layui 的两列布局（layui-col-md6）

## 修复完成情况

### ✅ 已完成的修改

1. **视图文件修改** - `app/customer/view/order/edit.html`
   - 在第141-161行添加了管销费和运费的表单字段
   - 使用与添加页面相同的布局和样式
   - 设置了正确的字段名称和默认值

2. **控制器修改** - `app/customer/controller/Order.php`
   - 在第362-363行的 `$orderData` 数组中添加了 `glf` 和 `yunfei` 字段
   - 使用了默认值处理（`?? 0`）防止空值错误

### 修改详情

**视图文件添加的代码：**
```html
<div class="layui-row layui-col-space15">
    <div class="layui-col-md6">
        <div class="layui-form-item">
            <label class="layui-form-label">管销费</label>
            <div class="layui-input-block">
                <input type="text" name="glf" id="glf" placeholder="管销费" value="{$order.glf|default=''}" class="layui-input">
            </div>
        </div>
    </div>

    <div class="layui-col-md6">
        <div class="layui-form-item">
            <label class="layui-form-label">运费</label>
            <div class="layui-input-block">
                <input type="text" name="yunfei" id="yunfei" placeholder="运费" value="{$order.yunfei|default=''}" class="layui-input">
            </div>
        </div>
    </div>
</div>
```

**控制器添加的字段：**
```php
'glf' => $data['glf'] ?? 0,
'yunfei' => $data['yunfei'] ?? 0,
```

## 测试建议
1. 访问订单编辑页面，确认管销费和运费字段正常显示
2. 编辑一个现有订单，修改管销费和运费值，保存后验证数据是否正确更新
3. 对比添加页面和编辑页面，确保字段布局和功能一致
