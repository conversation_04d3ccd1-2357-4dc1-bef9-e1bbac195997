# 出库系统设计文档

## 系统概述
基于现有入库系统设计，创建完整的出库管理系统，支持多种出库业务场景，包括销售出库、生产出库、调拨出库、退货出库等。

## 数据表设计

### 1. 核心表结构

#### 1.1 出库单主表 (oa_outbound)
- **用途**：记录出库业务的主要信息
- **关键字段**：
  - `outbound_type`: 出库类型（sales/production/transfer/return/other）
  - `ref_type/ref_id`: 关联业务类型和ID
  - `status`: 状态流转（草稿→已提交→已审核→部分出库→全部出库）
  - `priority`: 优先级控制

#### 1.2 出库单明细表 (oa_outbound_detail)
- **用途**：记录出库的具体产品信息
- **关键字段**：
  - `quantity`: 计划出库数量
  - `actual_quantity`: 实际出库数量
  - `shortage_quantity`: 缺货数量
  - `batch_no`: 批次管理
  - `quality_status`: 质量状态

### 2. 作业管理表

#### 2.1 拣货任务表 (oa_outbound_picking_task)
- **用途**：管理仓库拣货作业
- **功能**：任务分配、进度跟踪、绩效统计

#### 2.2 拣货明细表 (oa_outbound_picking_detail)
- **用途**：记录具体的拣货操作
- **功能**：库位指导、数量确认、缺货处理

### 3. 包装发货表

#### 3.1 包装表 (oa_outbound_package)
- **用途**：管理出库商品的包装信息
- **功能**：包装规格、重量体积、包装员管理

#### 3.2 发货表 (oa_outbound_shipment)
- **用途**：管理出库商品的物流发货
- **功能**：承运商管理、运单跟踪、到货确认

## 业务流程设计

### 1. 出库流程
```
创建出库单 → 审核通过 → 生成拣货任务 → 拣货作业 → 
包装作业 → 发货作业 → 出库完成
```

### 2. 状态流转
```
0.草稿 → 1.已提交 → 2.已审核 → 3.部分出库 → 4.全部出库
                                ↓
                              5.已取消
```

### 3. 库存扣减时机
- **预扣减**：审核通过时锁定库存
- **实际扣减**：拣货确认时扣减实际库存
- **差异处理**：缺货时释放多余锁定

## 与入库系统的对应关系

| 入库系统 | 出库系统 | 说明 |
|---------|---------|------|
| oa_purchase_receipt | oa_outbound | 主单据表 |
| oa_purchase_receipt_detail | oa_outbound_detail | 明细表 |
| 质检功能 | 拣货功能 | 作业环节 |
| 供应商管理 | 客户管理 | 业务对象 |
| 入库审核 | 出库审核 | 审批流程 |

## 库存集成设计

### 1. 库存锁定
- **时机**：出库单审核通过时
- **数量**：按计划出库数量锁定
- **释放**：拣货完成或取消时释放

### 2. 库存扣减
- **时机**：拣货确认时
- **数量**：按实际拣货数量扣减
- **记录**：生成库存流水记录

### 3. 分配需求
- **创建**：销售订单等业务创建出库需求
- **满足**：出库完成时满足分配需求
- **优先级**：按业务优先级分配库存

## 反审核设计

### 1. 反审核条件
- 出库单状态为已审核或部分出库
- 未开始拣货作业或可撤销拣货
- 相关库存充足恢复

### 2. 反审核处理
- 释放锁定库存
- 取消拣货任务
- 恢复分配需求
- 更新关联业务状态

## 接口设计

### 1. 核心接口
- `POST /warehouse/Outbound/create` - 创建出库单
- `POST /warehouse/Outbound/submit` - 提交出库单
- `POST /warehouse/Outbound/approve` - 审核出库单
- `POST /warehouse/Outbound/pick` - 拣货操作
- `POST /warehouse/Outbound/package` - 包装操作
- `POST /warehouse/Outbound/ship` - 发货操作

### 2. 查询接口
- `GET /warehouse/Outbound/index` - 出库单列表
- `GET /warehouse/Outbound/detail` - 出库单详情
- `GET /warehouse/Outbound/picking` - 拣货任务列表
- `GET /warehouse/Outbound/tracking` - 发货跟踪

## 权限设计

### 1. 角色权限
- **仓库管理员**：全部权限
- **出库员**：创建、提交出库单
- **拣货员**：拣货作业权限
- **包装员**：包装作业权限
- **发货员**：发货作业权限

### 2. 数据权限
- 按仓库隔离数据
- 按部门控制访问
- 按业务类型分权

## 报表统计

### 1. 出库统计
- 出库数量统计
- 出库金额统计
- 出库效率分析
- 缺货分析报告

### 2. 作业统计
- 拣货效率统计
- 包装效率统计
- 发货及时率统计
- 员工绩效统计

## 系统集成

### 1. 销售系统
- 销售订单自动生成出库单
- 出库完成回写销售订单状态
- 发货信息同步到销售系统

### 2. 生产系统
- 生产订单领料出库
- 生产完工入库
- 物料消耗统计

### 3. 财务系统
- 出库成本核算
- 销售成本结转
- 库存价值变动

## 技术实现

### 1. 模型设计
- Outbound（出库单模型）
- OutboundDetail（出库明细模型）
- PickingTask（拣货任务模型）
- Package（包装模型）
- Shipment（发货模型）

### 2. 服务设计
- OutboundService（出库业务服务）
- PickingService（拣货服务）
- PackageService（包装服务）
- ShipmentService（发货服务）

### 3. 队列任务
- 库存锁定队列
- 拣货任务分配队列
- 发货通知队列
- 统计计算队列

## 扩展功能

### 1. 移动端支持
- 拣货员移动端应用
- 扫码拣货功能
- 实时状态更新

### 2. 智能优化
- 拣货路径优化
- 包装方案推荐
- 发货批次优化

### 3. 第三方集成
- 物流公司接口
- 电商平台对接
- WMS系统集成
