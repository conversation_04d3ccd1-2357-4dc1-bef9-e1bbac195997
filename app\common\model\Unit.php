<?php
namespace app\common\model;

use think\Model;

class Unit extends Model
{
    protected $table = 'oa_unit';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'name'        => 'string',
        'precision'   => 'int',
        'type'        => 'string',
        'remark'      => 'string',
        'status'      => 'tinyint',
        'admin_id'    => 'int',
        'create_time' => 'int',
        'update_time' => 'int',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // 获取器 - 格式化创建时间
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化更新时间
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }
    
    // 获取器 - 类型文本
    public function getTypeTextAttr($value, $data)
    {
        return $data['type'] ?? '四舍五入';
    }
}
