# 工艺选择功能实现文档

## 功能概述

在物料档案管理中实现工艺选择功能，允许用户为产品选择对应的工艺路线模板，并将工艺模板ID保存到产品表中。

## 实现内容

### 1. 数据库结构调整

#### 1.1 添加工艺模板ID字段
为 `oa_product` 表添加 `process_template_id` 字段：

```sql
-- 为产品表添加工艺模板ID字段
ALTER TABLE `oa_product` 
ADD COLUMN `process_template_id` int(11) NOT NULL DEFAULT '0' COMMENT '工艺模板ID' AFTER `source_type`;

-- 添加索引
ALTER TABLE `oa_product` 
ADD KEY `idx_process_template_id` (`process_template_id`);
```

### 2. 前端界面优化

#### 2.1 工艺选择界面
- **删除选择工艺按钮**：简化界面设计
- **"选择工艺"变为可点击链接**：蓝色文字，点击触发弹窗
- **选中状态显示**：显示已选择的工艺名称和编号

#### 2.2 工艺选择弹窗
- **弹窗尺寸**：600px × 500px
- **搜索功能**：顶部搜索框，支持实时搜索工艺名称或编号
- **工艺卡片显示**：
  - 工艺名称（主标题）
  - 工艺编号（副标题）
  - 工序数量（如：5个工序）
  - 备注信息（描述）

#### 2.3 交互体验
- 卡片式布局，美观易读
- 悬停效果和选中状态
- 点击选择，自动关闭弹窗
- 实时搜索，即时过滤

### 3. 后端功能实现

#### 3.1 工艺模板数据获取
修改 `getProcessTemplates()` 方法：
- 从 `oa_process_template` 表获取完整数据
- 支持按工艺名称或编号搜索
- 解析工序数量信息

#### 3.2 数据保存逻辑
修改物料档案保存逻辑：
- **新增时**：将 `process_template_id` 添加到可选字段列表
- **编辑时**：确保 `process_template_id` 字段被正确更新

#### 3.3 编辑模式支持
- 页面加载时检查是否已有工艺模板ID
- 如果有，自动显示已选择的工艺信息
- 加载对应的工序列表

### 4. 核心文件修改

#### 4.1 控制器文件
- `app/material/controller/Archive.php`
  - 修改 `getProcessTemplates()` 方法
  - 更新保存逻辑，添加 `process_template_id` 字段

#### 4.2 视图文件
- `app/material/view/archive/add.html`
  - 修改工艺选择界面
  - 实现工艺选择弹窗
  - 添加编辑模式支持

#### 4.3 数据库脚本
- `add_process_template_to_product.sql`
  - 添加工艺模板ID字段到产品表

### 5. 功能特点

#### 5.1 数据完整性
- 显示工艺的详细信息（名称、编号、工序数量、备注）
- 从工艺路线模板表获取权威数据

#### 5.2 用户体验
- 搜索便捷：实时搜索，防抖处理
- 界面美观：现代化卡片式设计
- 交互流畅：平滑动画和状态反馈

#### 5.3 业务价值
- 建立产品与工艺的关联关系
- 为后续生产管理提供数据基础
- 统一工艺管理，避免数据冗余

### 6. 测试场景

1. **新增产品**：选择工艺，保存后检查数据库中的 `process_template_id` 字段
2. **编辑产品**：修改工艺选择，验证更新是否正确
3. **搜索功能**：测试工艺搜索的准确性和响应速度
4. **编辑模式**：打开已有工艺的产品，检查是否正确显示已选择的工艺

### 7. 注意事项

1. **数据库字段**：确保 `oa_product` 表已添加 `process_template_id` 字段
2. **权限控制**：确保用户有访问工艺路线模板的权限
3. **数据一致性**：删除工艺模板时需要考虑关联产品的处理
4. **性能优化**：大量工艺数据时考虑分页加载

## 部署步骤

1. **执行数据库脚本**：运行 `add_process_template_to_product.sql`
2. **更新代码文件**：部署修改后的控制器和视图文件
3. **清理缓存**：清理应用缓存确保更改生效
4. **功能测试**：验证工艺选择功能是否正常工作

## 后续扩展

1. **批量设置工艺**：支持批量为多个产品设置工艺
2. **工艺版本管理**：支持工艺模板的版本控制
3. **工艺成本计算**：基于工艺信息计算产品成本
4. **生产排程集成**：与生产计划模块集成，自动获取工艺信息
