{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-1">编辑资产</h3>
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">资产名称<font>*</font></td>
			<td colspan="3">
				<input type="text" name="title" value="{$detail.title}"  placeholder="请输入资产名称" lay-verify="required" lay-reqText="请输入资产名称" class="layui-input">
			</td>
			<td class="layui-td-gray-2" rowspan="5">
			缩略图
			<button type="button" class="layui-btn layui-btn-xs" id="uploadImg">+ 上传缩略图</button>
			</td>
			<td rowspan="5" style="width:252px;">
				<div class="layui-upload-list" id="demo1" style="width: 240px; height:136px; overflow: hidden;">
					<img src="{:get_file($detail.thumb)}" style="max-width: 100%; height:136px;" />
					<input type="hidden" name="thumb" value="{$detail.thumb}">
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">资产编码<font>*</font></td>
			<td>
				<input type="text" name="code" value="{$detail.code}" class="layui-input" lay-verify="required">
			</td>
			<td class="layui-td-gray">资产型号<font>*</font></td>
			<td><input type="text" name="model"  value="{$detail.model}"  class="layui-input" lay-verify="required"></td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">资产分类<font>*</font></td>
			<td>
			  <select name="cate_id" lay-verify="required" lay-reqText="请选择分类">
				<option value="">请选择分类</option>
				{volist name=":set_recursion(get_base_data('PropertyCate'))" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.cate_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
			<td class="layui-td-gray">资产品牌<font>*</font></td>
			<td>
				<select name="brand_id" lay-verify="required" lay-reqText="请选择品牌">
				<option value="">请选择品牌</option>
				{volist name=":get_base_data('PropertyBrand')" id="v"}
				<option value="{$v.id}" {eq name="v.id" value="$detail.brand_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">质保到期日<font>*</font></td>
			<td><input type="text" name="quality_time" value="{$detail.quality_time|date='Y-m-d'}" readonly placeholder="请选择质保到期日" class="layui-input tool-time" lay-verify="required" lay-reqText="请完善质保到期日"></td>
			<td class="layui-td-gray">单位<font>*</font></td>
			<td>
				<select name="unit_id" lay-verify="required" lay-reqText="请选择单位">
				<option value="">请选择单位</option>
				{volist name=":get_base_data('PropertyUnit')" id="v"}
				<option value="{$v.id}" {eq name="v.id" value="$detail.unit_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">使用人员</td>
			<td>
				<input type="text" name="users_name" value="{$detail.users_name|default=''}"  readonly placeholder="请选择使用人员" class="layui-input picker-admin">
				<input type="hidden" name="users" value="{$detail.user_ids}">
			</td>
			<td class="layui-td-gray">使用部门</td>
			<td><select id="user_dids" name="user_dids" xm-selected="{$detail.user_dids}" xm-select="select1" xm-select-skin="default"></select></td>
		</tr>
		<tr>
			<td class="layui-td-gray">购买价格<font>*</font></td>
			<td><input type="text" name="price" value="{$detail.price}"  placeholder="请输入购买价格" lay-verify="required|number" lay-reqText="请输入购买价格" class="layui-input"></td>
			<td class="layui-td-gray">购买日期<font>*</font></td>
			<td><input type="text" name="buy_time" value="{$detail.buy_time|date='Y-m-d'}"  readonly placeholder="请选择购买日期" lay-verify="required" lay-reqText="请选择购买日期" class="layui-input tool-time"></td>
			<td class="layui-td-gray">年折旧率(%)<font>*</font></td>
			<td><input type="text" name="rate" value="{$detail.rate}"  placeholder="请输入年折旧率，不需要输入%" lay-verify="required|number" lay-reqText="请输入年折旧率，不需要%" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">资产状态<font>*</font></td>
			<td colspan="5">
				{volist name="$status" id="vo"}
				<input type="radio" name="status" value="{$key}" title="{$vo}" {eq name="$key" value="$detail.status"} checked{/eq}>
				{/volist}
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">资产来源<font>*</font></td>
			<td colspan="5">
				{volist name="$source" id="vo"}
				{gt name="$key" value="0"}
				<input type="radio" name="source" value="{$key}" title="{$vo}" {eq name="$key" value="$detail.source"} checked{/eq}>
				{/gt}
				{/volist}
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">放置地址</td>
			<td colspan="5"><input type="text" name="address" value="{$detail.address}"  placeholder="请输入放置地址" class="layui-input"></td>
		</tr> 
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">资产附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					<input type="hidden" data-type="file" name="file_ids" value="{$detail.file_ids}">
					{notempty name="$detail.fileArray"}
					{volist name="$detail.fileArray" id="vo"}
					<div class="layui-col-md4">{:file_card($vo)}</div>
					{/volist}
					{/notempty}
				</div>			
			</td>
		</tr> 
		<tr>
			<td class="layui-td-gray" style="vertical-align:top;">资产描述</td>
			<td colspan="5">
				<textarea name="content" placeholder="请输入内容" class="layui-textarea" id="container">{$detail.content}</textarea>
			</td>
		</tr>
	</table>
	<div class="pt-2">
		<input type="hidden" name="id" value="{$detail.id}">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','formSelects','uploadPlus','oaPicker'];
function gouguInit() {
	var form = layui.form,tool=layui.tool,uploadPlus = layui.uploadPlus, formSelects = layui.formSelects;
	
	//选择部门
	var selected = $('#user_dids').attr('xm-selected');
	formSelects.data('select1', 'server', {
		url: '/api/index/get_department_select',
		keyword: selected
	});
    //单图上传
	var photoUpload = new uploadPlus({
		'use':'single',
		'target':'uploadImg',
		'callback':function(res){
			layer.msg(res.msg);
			if (res.code == 0) {
				//上传成功
				$('#demo1 input').attr('value', res.data.id);
				$('#demo1 img').attr('src', res.data.filepath);
			}
		}
	});

	//相关附件上传
	var attachment = new uploadPlus();
	
	//监听提交
	form.on('submit(webform)', function(data){
		let callback = function (e) {
			layer.msg(e.msg);
			if (e.code == 0) {
				tool.sideClose(1000);				
			}
		}
		if(!data.field.status){
			layer.msg('请选择资产状态');
			return false;
		}
		if(!data.field.source){
			layer.msg('请选择资产来源');
			return false;
		}
		let clickbtn = $(this);
		tool.post("/adm/property/add", data.field, callback,clickbtn);
		return false;
	});
}
</script>
{/block}
<!-- /脚本 -->