<?php
/**
 * 修复生产订单领料状态脚本
 * 用于修复已经出库但领料状态没有正确更新的生产订单
 */

namespace app\warehouse;

use think\facade\Db;
use think\facade\Log;

class FixProductionOrderFeedingStatus
{
    /**
     * 执行修复
     */
    public static function fix()
    {
        echo "开始修复生产订单领料状态...\n";
        
        try {
            // 1. 查找所有有领料单的生产订单
            $materialRequests = Db::name('production_material_request')
                ->field('production_order_id, request_no')
                ->group('production_order_id')
                ->select()
                ->toArray();
            
            $fixedCount = 0;
            $totalCount = count($materialRequests);
            
            echo "找到 {$totalCount} 个有领料单的生产订单\n";
            
            foreach ($materialRequests as $request) {
                $productionOrderId = $request['production_order_id'];
                
                // 2. 检查该生产订单的所有领料单是否都已出库完成
                $allMaterialRequests = Db::name('production_material_request')
                    ->where('production_order_id', $productionOrderId)
                    ->select()
                    ->toArray();
                
                $allCompleted = true;
                $hasAnyOutbound = false;
                
                foreach ($allMaterialRequests as $materialRequest) {
                    // 检查该领料单对应的出库单是否都已完成
                    $outboundCount = Db::name('outbound')
                        ->where('ref_type', 'production_material_request')
                        ->where('ref_no', $materialRequest['request_no'])
                        ->where('status', 4) // 已出库
                        ->count();
                    
                    $totalOutboundCount = Db::name('outbound')
                        ->where('ref_type', 'production_material_request')
                        ->where('ref_no', $materialRequest['request_no'])
                        ->count();
                    
                    if ($totalOutboundCount > 0) {
                        $hasAnyOutbound = true;
                    }
                    
                    if ($outboundCount < $totalOutboundCount) {
                        $allCompleted = false;
                    }
                }
                
                // 3. 确定新的领料状态
                $newFeedingFlag = 0; // 默认未投料
                if ($hasAnyOutbound) {
                    $newFeedingFlag = $allCompleted ? 2 : 1; // 2=投料完成, 1=部分投料
                }
                
                // 4. 获取当前状态
                $currentOrder = Db::name('produce_order')
                    ->where('id', $productionOrderId)
                    ->field('id, order_no, feeding_flag')
                    ->find();
                
                if (!$currentOrder) {
                    echo "生产订单 {$productionOrderId} 不存在，跳过\n";
                    continue;
                }
                
                // 5. 如果状态需要更新
                if ($currentOrder['feeding_flag'] != $newFeedingFlag) {
                    Db::name('produce_order')
                        ->where('id', $productionOrderId)
                        ->update([
                            'feeding_flag' => $newFeedingFlag,
                            'update_time' => time()
                        ]);
                    
                    $statusText = [
                        0 => '未投料',
                        1 => '部分投料', 
                        2 => '投料完成'
                    ];
                    
                    echo "✅ 更新生产订单 {$currentOrder['order_no']} (ID:{$productionOrderId}) 领料状态: {$statusText[$currentOrder['feeding_flag']]} → {$statusText[$newFeedingFlag]}\n";
                    
                    $fixedCount++;
                    
                    // 记录日志
                    Log::info('修复生产订单领料状态', [
                        'production_order_id' => $productionOrderId,
                        'order_no' => $currentOrder['order_no'],
                        'old_feeding_flag' => $currentOrder['feeding_flag'],
                        'new_feeding_flag' => $newFeedingFlag,
                        'has_any_outbound' => $hasAnyOutbound,
                        'all_completed' => $allCompleted
                    ]);
                } else {
                    echo "生产订单 {$currentOrder['order_no']} (ID:{$productionOrderId}) 状态正确，无需更新\n";
                }
            }
            
            echo "\n修复完成！\n";
            echo "总计检查: {$totalCount} 个生产订单\n";
            echo "修复数量: {$fixedCount} 个\n";
            
        } catch (\Exception $e) {
            echo "修复过程中发生错误: " . $e->getMessage() . "\n";
            echo "错误详情: " . $e->getTraceAsString() . "\n";
            
            Log::error('修复生产订单领料状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 检查特定生产订单的领料状态
     */
    public static function checkOrder($orderId)
    {
        echo "检查生产订单 {$orderId} 的领料状态...\n";
        
        try {
            // 获取生产订单信息
            $order = Db::name('produce_order')
                ->where('id', $orderId)
                ->field('id, order_no, feeding_flag')
                ->find();
            
            if (!$order) {
                echo "生产订单 {$orderId} 不存在\n";
                return;
            }
            
            echo "当前订单: {$order['order_no']}, 领料状态: {$order['feeding_flag']}\n";
            
            // 获取所有领料单
            $materialRequests = Db::name('production_material_request')
                ->where('production_order_id', $orderId)
                ->field('request_no')
                ->select()
                ->toArray();
            
            echo "领料单数量: " . count($materialRequests) . "\n";
            
            foreach ($materialRequests as $request) {
                echo "  领料单: {$request['request_no']}\n";
                
                // 查询出库单
                $outbounds = Db::name('outbound')
                    ->where('ref_type', 'production_material_request')
                    ->where('ref_no', $request['request_no'])
                    ->field('id, outbound_no, status')
                    ->select()
                    ->toArray();
                
                foreach ($outbounds as $outbound) {
                    $statusText = [
                        0 => '草稿',
                        1 => '已提交',
                        2 => '已审核',
                        3 => '部分出库',
                        4 => '全部出库',
                        5 => '已取消'
                    ];
                    
                    echo "    出库单: {$outbound['outbound_no']}, 状态: {$statusText[$outbound['status']]}\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "检查过程中发生错误: " . $e->getMessage() . "\n";
        }
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli') {
    // 命令行运行
    if (isset($argv[1]) && $argv[1] === 'check' && isset($argv[2])) {
        // 检查特定订单: php 修复生产订单领料状态脚本.php check 订单ID
        FixProductionOrderFeedingStatus::checkOrder($argv[2]);
    } else {
        // 执行修复: php 修复生产订单领料状态脚本.php
        FixProductionOrderFeedingStatus::fix();
    }
} else {
    // Web运行
    echo "请在命令行中运行此脚本\n";
    echo "修复所有: php 修复生产订单领料状态脚本.php\n";
    echo "检查订单: php 修复生产订单领料状态脚本.php check 订单ID\n";
}
