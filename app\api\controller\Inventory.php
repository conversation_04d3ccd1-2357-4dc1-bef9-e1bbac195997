<?php
declare(strict_types = 1);

namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;
use think\facade\View;
use app\warehouse\controller\Outbound;
use app\api\service\InventoryReserveService;
use think\facade\Log;

/**
 * 库存API控制器
 */
class Inventory extends BaseController
{
    /**
     * 获取库存列表
     * 
     * @return \think\Response
     */
    public function getList()
    {
        // 获取参数
        $param = get_params();
        
        // 构建查询条件
        $where = [];
        
        // 按产品ID搜索
        if (!empty($param['product_id'])) {
            $where[] = ['i.product_id', '=', intval($param['product_id'])];
        }
        
        // 按产品编码搜索
        if (!empty($param['product_code'])) {
            $where[] = ['p.material_code', 'like', '%' . $param['product_code'] . '%'];
        }
        
        // 按产品名称搜索
        if (!empty($param['product_name'])) {
            $where[] = ['p.title', 'like', '%' . $param['product_name'] . '%'];
        }
        
        // 按仓库ID搜索
        if (!empty($param['warehouse_id'])) {
            $where[] = ['i.warehouse_id', '=', intval($param['warehouse_id'])];
        }
        
        // 按库存数量范围搜索
        if (isset($param['min_quantity']) && $param['min_quantity'] !== '') {
            $where[] = ['i.quantity', '>=', floatval($param['min_quantity'])];
        }
        if (isset($param['max_quantity']) && $param['max_quantity'] !== '') {
            $where[] = ['i.quantity', '<=', floatval($param['max_quantity'])];
        }
        
        // 执行查询
        $list = Db::name('inventory')
            ->alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->where($where)
            ->field('i.id, i.product_id, p.material_code as product_code, p.title as product_name, 
                    i.warehouse_id, w.name as warehouse_name, i.quantity, i.unit, 
                    i.batch_no, i.create_time, i.cost_price')
            ->order('i.create_time', 'desc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
        
        $data = $list->toArray();
        
        // 处理数据格式
        foreach ($data['data'] as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            
            // 计算在途库存
            $item['in_transit_quantity'] = $this->getInTransitQuantity($item['product_id']);
            
            // 计算总库存（实际库存+在途库存）
            $item['total_quantity'] = $item['quantity'] + $item['in_transit_quantity'];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $data['total'],
            'data' => $data['data']
        ]);
    }
    
    /**
     * 获取产品库存详情
     * 
     * @return \think\Response
     */
    public function getProductInventory()
    {
        // 获取参数
        $param = get_params();
        $product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
        
        // 参数验证
        if (empty($product_id)) {
            return json([
                'code' => 1,
                'msg' => '产品ID不能为空',
                'data' => []
            ]);
        }
        
        // 获取产品信息
        $product = Db::name('product')
            ->field('id, material_code as product_code, title as product_name, specs, unit')
            ->where('id', $product_id)
            ->find();
        
        if (!$product) {
            return json([
                'code' => 1,
                'msg' => '产品不存在',
                'data' => []
            ]);
        }
        
        // 获取产品库存列表
        $inventories = Db::name('inventory')
            ->alias('i')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->where('i.product_id', $product_id)
            ->field('i.id, i.warehouse_id, w.name as warehouse_name, i.quantity, 
                    i.batch_no, i.unit, i.cost_price, i.create_time')
            ->order('i.create_time', 'desc')
            ->select()
            ->toArray();
            
        foreach ($inventories as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        // 计算在途库存
        $inTransitQuantity = $this->getInTransitQuantity($product_id);
        
        // 计算总库存数量
        $totalQuantity = array_sum(array_column($inventories, 'quantity'));
        
        // 构建返回数据
        $data = [
            'product' => $product,
            'inventories' => $inventories,
            'in_transit_quantity' => $inTransitQuantity,
            'total_quantity' => $totalQuantity,
            'grand_total' => $totalQuantity + $inTransitQuantity // 总库存（实际+在途）
        ];
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }
    
    /**
     * 获取产品在途库存数量
     * 
     * @param int $product_id 产品ID
     * @return float 在途库存数量
     */
    private function getInTransitQuantity($product_id)
    {
        // 查询在途采购数量（采购订单中未完全入库的数量）
        $inTransitQuantity = Db::name('purchase_order_detail')
            ->alias('d')
            ->join('purchase_order o', 'd.order_id = o.id')
            ->where('d.product_id', $product_id)
            ->where('o.status', 'in', [2, 3, 4]) // 已审核，部分入库，未完成的订单
            ->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
            ->sum(Db::raw('d.quantity - d.received_quantity'));
        
        return $inTransitQuantity ?: 0;
    }
    
    /**
     * 获取产品在途库存明细
     * 
     * @return \think\Response
     */
    public function getInTransitDetail()
    {
        // 获取参数
        $param = get_params();
        $product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
        
        // 参数验证
        if (empty($product_id)) {
            return json([
                'code' => 1,
                'msg' => '产品ID不能为空',
                'data' => []
            ]);
        }
        
        // 获取产品信息
        $product = Db::name('product')
            ->field('id, material_code as product_code, title as product_name, specs, unit')
            ->where('id', $product_id)
            ->find();
        
        if (!$product) {
            return json([
                'code' => 1,
                'msg' => '产品不存在',
                'data' => []
            ]);
        }
        
        // 查询在途采购明细（采购订单中未完全入库的记录）
        $inTransitDetails = Db::name('purchase_order_detail')
            ->alias('d')
            ->join('purchase_order o', 'd.order_id = o.id')
            ->join('purchase_supplier s', 'o.supplier_id = s.id')
            ->where('d.product_id', $product_id)
            ->where('o.status', 'in', [2, 3, 4]) // 已审核，部分入库，未完成的订单
            ->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
            ->field('o.id as order_id, o.order_no, o.expected_receipt_date, 
                    s.name as supplier_name, d.quantity, d.received_quantity, 
                    (d.quantity - d.received_quantity) as in_transit_quantity,
                    d.unit_price, d.total_amount, o.create_time')
            ->order('o.expected_receipt_date', 'asc')
            ->select()
            ->toArray();
            
        foreach ($inTransitDetails as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['expected_receipt_date'] = $item['expected_receipt_date'] ? date('Y-m-d', strtotime($item['expected_receipt_date'])) : null;
        }
        
        // 计算总在途数量
        $totalInTransitQuantity = array_sum(array_column($inTransitDetails, 'in_transit_quantity'));
        
        // 构建返回数据
        $data = [
            'product' => $product,
            'in_transit_details' => $inTransitDetails,
            'total_in_transit_quantity' => $totalInTransitQuantity
        ];
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }
    
    /**
     * 获取产品总库存（实际库存+在途库存）
     * 
     * @return \think\Response
     */
    public function getTotalInventory()
    {
        // 获取参数
        $param = get_params();
        $product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
        
        // 参数验证
        if (empty($product_id)) {
            return json([
                'code' => 1,
                'msg' => '产品ID不能为空',
                'data' => []
            ]);
        }
        
        // 获取产品信息
        $product = Db::name('product')
            ->field('id, material_code as product_code, title as product_name, specs, unit')
            ->where('id', $product_id)
            ->find();
        
        if (!$product) {
            return json([
                'code' => 1,
                'msg' => '产品不存在',
                'data' => []
            ]);
        }
        
        // 计算实际库存总量
        $actualInventory = Db::name('inventory')
            ->where('product_id', $product_id)
            ->sum('quantity');
        
        // 计算在途库存
        $inTransitQuantity = $this->getInTransitQuantity($product_id);
        
        // 计算总库存
        $totalInventory = $actualInventory + $inTransitQuantity;
        
        // 构建返回数据
        $data = [
            'product' => $product,
            'actual_inventory' => $actualInventory,
            'in_transit_inventory' => $inTransitQuantity,
            'total_inventory' => $totalInventory
        ];
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $data
        ]);
    }
    
    /**
     * 获取多个产品的库存摘要
     * 
     * @return \think\Response
     */
    public function getBatchInventory()
    {
        // 获取参数
        $param = get_params();
        $product_ids = isset($param['product_ids']) ? $param['product_ids'] : '';
        
        // 参数验证
        if (empty($product_ids)) {
            return json([
                'code' => 1,
                'msg' => '产品ID列表不能为空',
                'data' => []
            ]);
        }
        
        // 转换产品ID字符串为数组
        if (is_string($product_ids)) {
            $product_ids = explode(',', $product_ids);
        }
        
        // 转换为整数数组
        $product_ids = array_map('intval', $product_ids);
        
        // 获取产品信息
        $products = Db::name('product')
            ->field('id, material_code as product_code, title as product_name, specs, unit')
            ->whereIn('id', $product_ids)
            ->select()
            ->toArray();
        
        $productMap = [];
        foreach ($products as $product) {
            $productMap[$product['id']] = $product;
        }
        
        // 获取实际库存
        $inventoryMap = [];
        $inventories = Db::name('inventory')
            ->whereIn('product_id', $product_ids)
            ->field('product_id, SUM(quantity) as total_quantity')
            ->group('product_id')
            ->select()
            ->toArray();
        
        foreach ($inventories as $inventory) {
            $inventoryMap[$inventory['product_id']] = $inventory['total_quantity'];
        }
        
        // 获取在途库存
        $inTransitMap = [];
        $inTransitQuantities = Db::name('purchase_order_detail')
            ->alias('d')
            ->join('purchase_order o', 'd.order_id = o.id')
            ->whereIn('d.product_id', $product_ids)
            ->where('o.status', 'in', [2, 3, 4]) // 已审核，部分入库，未完成的订单
            ->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
            ->field('d.product_id, SUM(d.quantity - d.received_quantity) as in_transit_quantity')
            ->group('d.product_id')
            ->select()
            ->toArray();
        
        foreach ($inTransitQuantities as $inTransit) {
            $inTransitMap[$inTransit['product_id']] = $inTransit['in_transit_quantity'];
        }
        
        // 构建结果
        $result = [];
        foreach ($product_ids as $product_id) {
            if (isset($productMap[$product_id])) {
                $actualQuantity = isset($inventoryMap[$product_id]) ? $inventoryMap[$product_id] : 0;
                $inTransitQuantity = isset($inTransitMap[$product_id]) ? $inTransitMap[$product_id] : 0;
                
                $result[] = [
                    'product_id' => $product_id,
                    'product_code' => $productMap[$product_id]['product_code'],
                    'product_name' => $productMap[$product_id]['product_name'],
                    'specs' => $productMap[$product_id]['specs'],
                    'unit' => $productMap[$product_id]['unit'],
                    'actual_inventory' => $actualQuantity,
                    'in_transit_inventory' => $inTransitQuantity,
                    'total_inventory' => $actualQuantity + $inTransitQuantity
                ];
            }
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $result
        ]);
    }
    
    /**
     * 获取库存汇总报表
     * 
     * @return \think\Response
     */
    public function getInventorySummary()
    {
        // 获取参数
        $param = get_params();
        $warehouse_id = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
        
        // 构建查询条件
        $where = [];
        if ($warehouse_id > 0) {
            $where[] = ['i.warehouse_id', '=', $warehouse_id];
        }
        
        // 按产品分类搜索
        if (!empty($param['category_id'])) {
            $where[] = ['p.category_id', '=', intval($param['category_id'])];
        }
        
        // 按产品编码搜索
        if (!empty($param['product_code'])) {
            $where[] = ['p.material_code', 'like', '%' . $param['product_code'] . '%'];
        }
        
        // 按产品名称搜索
        if (!empty($param['product_name'])) {
            $where[] = ['p.title', 'like', '%' . $param['product_name'] . '%'];
        }
        
        // 获取库存汇总数据
        $list = Db::name('inventory')
            ->alias('i')
            ->join('product p', 'i.product_id = p.id')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->join('product_category c', 'p.category_id = c.id', 'left')
            ->where($where)
            ->field('p.id as product_id, p.material_code as product_code, p.title as product_name, 
                    p.specs, c.name as category_name, p.unit, 
                    SUM(i.quantity) as total_quantity, AVG(i.cost_price) as avg_cost_price')
            ->group('p.id')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
        
        $data = $list->toArray();
        
        // 处理数据，添加在途库存
        foreach ($data['data'] as &$item) {
            $item['in_transit_quantity'] = $this->getInTransitQuantity($item['product_id']);
            $item['total_with_transit'] = $item['total_quantity'] + $item['in_transit_quantity'];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $data['total'],
            'data' => $data['data']
        ]);
    }
} 