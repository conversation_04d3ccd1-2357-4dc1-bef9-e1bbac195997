<?php
declare (strict_types = 1);

namespace app\Produce\model;

use think\Model;
use think\facade\Db;

class DrawingCategoryModel extends Model
{
    protected $table = 'oa_drawing_category';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'name'            => 'string',
        'parent_id'       => 'int',
        'sort'            => 'int',
        'remark'          => 'text',
        'create_time'     => 'int',
        'update_time'     => 'int',
        'delete_time'     => 'int'
    ];

    /**
     * 获取分类树形结构
     * @param int $parentId 父级ID
     * @return array
     */
    public function getCategoryTree($parentId = 0)
    {
        $categories = $this->where('parent_id', $parentId)
            ->order('sort asc, id asc')
            ->select()
            ->toArray();
        
        $tree = [];
        foreach ($categories as $category) {
            // 获取该分类下的图纸数量
            $drawingCount = Db::name('drawing')
                ->where('category_id', $category['id'])
                ->count();
            
            $category['drawing_count'] = $drawingCount;
            
            // 递归获取子分类
            $children = $this->getCategoryTree($category['id']);
            if (!empty($children)) {
                $category['children'] = $children;
            }
            
            $tree[] = $category;
        }
        
        return $tree;
    }

    /**
     * 获取分类选项列表（用于下拉选择）
     * @return array
     */
    public function getCategoryOptions()
    {
        $categories = $this->order('sort asc, id asc')->select()->toArray();
        
        $options = [];
        $this->buildCategoryOptions($categories, $options, 0, 0);
        
        return $options;
    }
    
    /**
     * 构建分类选项（递归）
     * @param array $categories 所有分类
     * @param array $options 选项数组
     * @param int $parentId 父级ID
     * @param int $level 层级
     */
    private function buildCategoryOptions($categories, &$options, $parentId = 0, $level = 0)
    {
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $prefix = str_repeat('　', $level);
                $options[] = [
                    'id' => $category['id'],
                    'name' => $prefix . $category['name']
                ];
                
                // 递归处理子分类
                $this->buildCategoryOptions($categories, $options, $category['id'], $level + 1);
            }
        }
    }

    /**
     * 获取分类路径
     * @param int $categoryId 分类ID
     * @return string
     */
    public function getCategoryPath($categoryId)
    {
        $path = [];
        $currentId = $categoryId;
        
        while ($currentId > 0) {
            $category = $this->find($currentId);
            if (!$category) {
                break;
            }
            
            array_unshift($path, $category['name']);
            $currentId = $category['parent_id'];
        }
        
        return implode(' > ', $path);
    }
}