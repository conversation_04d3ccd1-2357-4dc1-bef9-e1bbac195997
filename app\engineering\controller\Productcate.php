<?php

declare (strict_types = 1);

namespace app\engineering\controller;

use app\base\BaseController;
use app\engineering\validate\ProductCateValidate;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Productcate extends BaseController
{    
    /**
     * 分类列表
     */
    public function datalist()
    {
        if (request()->isAjax()) {
            $cate = Db::name('ProductCate')
                ->field('id, pid, title, code, sort, status, source_type, create_time, update_time')
                ->where('delete_time', 0)
                ->order('sort asc, create_time asc')
                ->select();
                
            // 添加默认展开标识
            foreach ($cate as &$item) {
                $item['open'] = false; // 默认不展开
                // 计算子分类数量
                $child_count = Db::name('ProductCate')
                    ->where('pid', $item['id'])
                    ->where('delete_time', 0)
                    ->count();
                $item['has_child'] = $child_count > 0;
                $item['child_count'] = $child_count;
            }
            
            $list = generateTree($cate);
            return to_assign(0, '获取成功', $list);
        } else {
            return view();
        }
    }

    /**
     * 分类添加&编辑
     */
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(ProductCateValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                $note_array = get_cate_son('ProductCate', $param['id']);
                if (in_array($param['pid'], $note_array)) {
                    return to_assign(1, '父级分类不能是该分类本身或其子分类');
                } else {
                    $param['update_time'] = time();
                    
                    // 处理编码继承
                    if (isset($param['pid']) && $param['pid'] > 0) {
                        $parent = Db::name('ProductCate')->where('id', $param['pid'])->find();
                        if ($parent && !empty($parent['code'])) {
                            // 如果子分类编码没有包含父级编码前缀，则自动添加
                            $parent_code = $parent['code'];
                            if (!empty($param['code'])) {
                                // 检查编码是否已经包含父级编码前缀
                                if (strpos($param['code'], $parent_code . '-') !== 0) {
                                    $param['code'] = $parent_code . '-' . $param['code'];
                                }
                            } else {
                                // 如果子分类编码为空，则设置为父级编码+短横线
                                $param['code'] = $parent_code . '-';
                            }
                        }
                    }
                    
                    $res = Db::name('ProductCate')->strict(false)->field(true)->update($param);
                    if ($res) {
                        add_log('edit', $param['id'], $param);
                        return to_assign();
                    }
                }
            } else {
                try {
                    validate(ProductCateValidate::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    // 验证失败 输出错误信息
                    return to_assign(1, $e->getError());
                }
                
                // 处理编码继承
                if (isset($param['pid']) && $param['pid'] > 0) {
                    $parent = Db::name('ProductCate')->where('id', $param['pid'])->find();
                    if ($parent && !empty($parent['code'])) {
                        // 如果子分类编码没有包含父级编码前缀，则自动添加
                        $parent_code = $parent['code'];
                        if (!empty($param['code'])) {
                            // 检查编码是否已经包含父级编码前缀
                            if (strpos($param['code'], $parent_code . '-') !== 0) {
                                $param['code'] = $parent_code . '-' . $param['code'];
                            }
                        } else {
                            // 如果子分类编码为空，则设置为父级编码+短横线
                            $param['code'] = $parent_code . '-';
                        }
                    }
                }
                
                $param['create_time'] = time();
                $insertId = Db::name('ProductCate')->strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign();
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $pid = isset($param['pid']) ? $param['pid'] : 0;
            $cate = Db::name('ProductCate')->order('id desc')->select()->toArray();
            $cates = set_recursion($cate);
            if ($id > 0) {
                $detail = Db::name('ProductCate')->where(['id' => $id])->find();
                View::assign('detail', $detail);
            }
            View::assign('id', $id);
            View::assign('pid', $pid);
            View::assign('cates', $cates);
            return view();
        }
    }

    /**
     * 删除分类
     */
    public function del()
    {
        $id = get_params("id");
        $cate_count = Db::name('ProductCate')->where(["pid" => $id])->count();
        if ($cate_count > 0) {
            return to_assign(1, "该分类下还有子分类，无法删除");
        }
        $product_count = Db::name('Product')->where(["cate_id" => $id, 'delete_time' => 0])->count();
        if ($product_count > 0) {
            return to_assign(1, "该分类下还有产品，无法删除");
        }
        if (Db::name('ProductCate')->delete($id) !== false) {
            add_log('delete', $id);
            return to_assign(0, "删除分类成功");
        } else {
            return to_assign(1, "删除失败");
        }
    }

    /**
     * 获取产品分类树
     */
    public function getcatetree()
    {
        if (request()->isAjax()) {
            // 只获取正常状态的分类，并按排序和创建时间排序
            $cate = Db::name('ProductCate')
                ->where('status', 1)
                ->where('delete_time', 0)
                ->field('id, pid, title, code, sort, source_type')
                ->order('sort asc, create_time asc')
                ->select()
                ->toArray();
            
            // 使用递归函数生成树形结构
            $list = generateTree($cate);
            
            // 返回结果
            return to_assign(0, '获取成功', $list);
        } else {
            return to_assign(1, '请求方式错误');
        }
    }

    /**
     * 分类管理页面
     */
    public function manage()
    {
        return view();
    }

    /**
     * 修改分类状态
     */
    public function set()
    {
        $param = get_params();
        if (!isset($param['id']) || empty($param['id'])) {
            return to_assign(1, '参数错误');
        }
        
        $id = $param['id'];
        $status = isset($param['status']) ? $param['status'] : 0;
        
        try {
            $res = Db::name('ProductCate')
                ->where('id', $id)
                ->update(['status' => $status, 'update_time' => time()]);
                
            if ($res) {
                add_log('edit', $id, ['status' => $status]);
                return to_assign(0, '状态修改成功');
            } else {
                return to_assign(1, '状态修改失败');
            }
        } catch (\Exception $e) {
            return to_assign(1, '操作失败：' . $e->getMessage());
        }
    }

    /**
     * 获取上级分类编码
     */
    public function getparentcode()
    {
        if (request()->isAjax() || request()->isPost()) {
            $id = get_params('id');
            if (!$id) {
                return to_assign(1, '参数错误');
            }
            
            // 获取当前分类
            $category = Db::name('ProductCate')->where('id', $id)->find();
            if (!$category) {
                return to_assign(1, '分类不存在');
            }
            
            // 返回分类编码
            $parent_code = $category['code'] ?: '';
            $source_type = $category['source_type'] ?: 1;
            
            return to_assign(0, '获取成功', [
                'parent_code' => $parent_code,
                'source_type' => $source_type
            ]);
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 生成物料编号
     * 规则：分类代码-自增5位数字
     */
    public function generateProductCode($categoryCode)
    {
        // 确保分类编码存在
        if (empty($categoryCode)) {
            return '';
        }
        
        // 查找相同前缀的最大编号
        $prefix = $categoryCode . '-';
        $lastProduct = Db::name('Product')
            ->where('material_code', 'like', $prefix . '%')
            ->where('delete_time', 0)
            ->order('material_code', 'desc')
            ->find();
            
        // 初始化序号
        $sequence = '00001';
        
        if ($lastProduct && !empty($lastProduct['material_code'])) {
            // 从现有编码中提取序号部分
            $parts = explode('-', $lastProduct['material_code']);
            $lastSequence = end($parts);
            
            // 如果是数字，则递增
            if (is_numeric($lastSequence) && strlen($lastSequence) == 5) {
                $nextSequence = intval($lastSequence) + 1;
                $sequence = str_pad((string)$nextSequence, 5, '0', STR_PAD_LEFT);
            }
        }
        
        // 返回完整编码
        return $prefix . $sequence;
    }

        /**
     * 生成物料编号
     * 规则：分类代码-指定编码
     */
    public function generateProductCode_zd($categoryCode,$code)
    {
        // 确保分类编码存在
        if (empty($categoryCode)) {
            return '';
        }
        
        // 检查是否有指定编码
        if (!empty($code)) {
            $prefix = $categoryCode . '-';
            // 检查指定编码是否已存在
            $existingProduct = Db::name('Product')
                ->where('material_code', $prefix . $code)
                ->where('delete_time', 0)
                ->find();
                
            if ($existingProduct) {
                // 如果编码已存在，返回空或错误信息
                return '';
            }
            
            // 如果编码不存在，直接使用指定编码
            return $prefix . $code;
        }
            
     
      
    }
} 