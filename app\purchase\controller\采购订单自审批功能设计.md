# 采购订单自审批功能设计方案

## 需求背景
移除采购订单的原有复杂审批流程，改为创建人自己审批的简单模式。

## 当前审批流程分析

### 现有问题
1. 使用复杂的多级审批流程系统
2. 需要配置审批流程、审批人等
3. 涉及多个数据库表：FlowCate、Flow、FlowRecord、FlowStep
4. 前端使用oaCheck组件处理复杂的审批逻辑

### 现有审批状态
- `check_status`: 0=未审核，1=待审核，2=已通过，3=已拒绝
- `status`: 1=草稿，2=待审核，3=已审核，4=部分入库，5=全部入库，6=已取消

## 自审批功能设计

### 设计原则
1. 简化审批流程，只保留基本的审批状态管理
2. 创建人可以直接审批自己创建的订单
3. 移除复杂的审批流程配置和多级审批
4. 保持数据库字段兼容性，避免大规模数据迁移

### 功能设计

#### 1. 审批状态简化
- 保留 `check_status` 字段，简化状态：
  - 0: 待审批（订单创建后的初始状态）
  - 2: 审批通过
  - 3: 审批拒绝

#### 2. 自审批逻辑
- 创建人可以直接审批自己的订单
- 审批操作包括：通过、拒绝
- 审批时需要填写审批意见

#### 3. 界面设计
- 移除复杂的oaCheck组件
- 在详情页面添加审核操作区域
- 提供审核通过、审核拒绝、反审核按钮
- 根据当前审核状态动态显示可用操作
- 显示当前审核状态和审核信息
- 保留操作记录标签页

## 实现方案

### 1. 控制器修改
在 `app/purchase/controller/Order.php` 中：

#### 新增方法：
- `selfApprove()`: 自审批方法（通过/拒绝/反审核）
- 修改 `detail()` 方法，传递是否为创建人的标识

#### 审批操作类型：
- `approve`: 审核通过（check_status=2, status=3）
- `reject`: 审核拒绝（check_status=3, status=1）
- `unapprove`: 反审核（check_status=0, status=1）

#### 修改现有方法：
- 简化 `approve()` 方法，移除复杂的流程调用
- 保留基本的状态更新和日志记录

### 2. 前端页面修改
在 `app/purchase/view/order/detail.html` 中：

#### 移除：
- oaCheck组件的初始化和调用
- 复杂的审批流程显示
- 审批信息标签页
- 审批按钮和相关JavaScript代码

#### 保留：
- 操作记录标签页
- 基本的订单信息显示

#### 新增：
- 审核操作区域（所有用户可见）
- 审核通过、审核拒绝、反审核按钮
- 审核意见输入框
- 当前审核状态显示
- 根据状态动态显示可用操作
- 详细的操作日志记录

### 3. 数据库字段使用
保持现有字段，简化使用：
- `check_status`: 审批状态
- `check_last_uid`: 审批人ID（即创建人ID）
- `check_time`: 审批时间
- 其他审批流程相关字段保持不变但不使用

### 4. 权限控制
- 所有有权限的用户都可以进行审批操作（移除创建人限制）
- 基于订单状态控制可执行的操作
- 详细记录操作日志，包括操作人、操作时间、操作意见

## 实现步骤

1. ✅ 修改控制器，添加自审批方法（支持审核、拒绝、反审核）
2. ✅ 修改详情页面模板，移除复杂审批组件
3. ✅ 添加审核操作区域和相关按钮
4. ✅ 实现审核状态的动态显示和操作控制
5. ✅ 修复操作记录显示问题
6. ✅ 移除创建人限制，允许所有用户审核
7. ✅ 完善操作日志记录，包含详细的操作信息
8. ✅ 修复API返回格式，统一使用JSON格式
9. ✅ 修复操作记录日志记录问题，使用正确的日志表
10. ✅ 在列表页面添加审核和反审核按钮
11. ✅ 修复反审核业务逻辑，禁止有入库记录的订单反审核
12. 测试完整的审核流程

## 问题修复记录

### API返回格式问题
- **问题**：selfApprove接口返回格式不统一，导致前端解析错误
- **解决**：统一使用 `json()` 方法返回标准JSON格式
- **影响**：确保前端能正确处理API响应

### 操作记录显示问题
- **问题**：审批操作没有显示在操作记录中
- **原因**：使用了错误的日志表（oa_log vs oa_edit_log）
- **解决**：使用 EditLog 模型的 add_log 方法记录到正确的表
- **影响**：操作记录能正确显示审批相关信息

### 列表页面审核功能
- **新增**：在采购订单列表页面添加审核和反审核按钮
- **功能**：
  - 待审核/已拒绝状态：显示"审核"按钮，可快速审核通过
  - 已审核状态：显示"反审核"按钮，可将订单回退到待审核状态
  - 支持输入审核意见
  - 操作后自动刷新列表
- **优势**：提高审核效率，无需进入详情页面即可完成审核操作

### 反审核业务逻辑限制
- **问题**：有出入库记录的订单不应允许反审核
- **解决方案**：
  - 在反审核操作前检查是否存在入库记录（purchase_receipt表）
  - 检查是否存在库存变动记录（inventory_log表）
  - 列表页面根据入库记录状态控制反审核按钮显示
- **业务规则**：
  - 已有入库记录的订单不允许反审核
  - 已有库存变动的订单不允许反审核
  - 前端按钮根据后端数据智能显示/隐藏

## 注意事项

1. 保持数据库字段兼容性
2. 确保审批状态的一致性
3. 保留操作日志记录
4. 考虑后续可能的扩展需求
5. **数据库表名修复**：确保使用正确的表名 `oa_inventory_transaction` 而不是 `inventory_log` 或 `oa_inventory_log`
6. **库存变动检查**：通过入库单ID列表检查相关的库存变动记录
7. **重要发现**：系统实际使用的是 `oa_inventory_transaction` 表记录库存流水，不是 `inventory_log` 表

### 正确的库存变动检查代码
```php
// 1. 检查入库记录
$receiptCount = Db::name('purchase_receipt')->where('order_id', $order_id)->count();

// 2. 检查库存变动记录（使用正确的表名）
if ($receiptCount > 0) {
    $receiptIds = Db::name('purchase_receipt')->where('order_id', $order_id)->column('id');
    $inventoryTransactionCount = Db::name('inventory_transaction')
        ->where('ref_type', 'purchase_receipt')
        ->where('ref_id', 'in', $receiptIds)
        ->count();
}
```

## 测试计划

1. 创建新的采购订单
2. 测试自审批通过功能
3. 测试自审批拒绝功能
4. 验证审批状态显示
5. 检查操作日志记录
6. 测试权限控制（非创建人不能审批）
