# 发货管理真实可用库存计算逻辑

## 设计理念

在发货管理的订单商品列表中，显示的库存数量应该是**真正可用于当前订单的库存数量**，而不是简单的系统库存数量。这个数量需要考虑库存分配、锁定状态、优先级等多个因素。

## 库存计算逻辑

### 1. 基础库存数据
```php
// 从实时库存表获取基础数据
$inventoryInfo = Db::name('inventory_realtime')
    ->where('product_id', $productId)
    ->field('SUM(quantity) as total_qty, SUM(available_quantity) as available_qty, SUM(locked_quantity) as locked_qty')
    ->find();

$totalQty = $inventoryInfo['total_qty'] ?: 0; // 总库存
$systemAvailableQty = $inventoryInfo['available_qty'] ?: 0; // 系统可用库存
$systemLockedQty = $inventoryInfo['locked_qty'] ?: 0; // 系统锁定库存
```

### 2. 当前订单锁定库存
```php
// 查询当前订单明细已锁定的库存
$currentOrderLocked = Db::name('inventory_lock')
    ->where('product_id', $productId)
    ->where('ref_type', 'customer_order_detail')
    ->where('ref_id', $orderDetailId)
    ->where('status', 1) // 锁定中状态
    ->sum('quantity') ?: 0;
```

### 3. 分配需求状态
```php
// 查询当前订单的分配需求
$allocationRequest = Db::name('inventory_allocation_request')
    ->where('product_id', $productId)
    ->where('ref_type', 'customer_order_detail')
    ->where('ref_id', $orderDetailId)
    ->where('status', 'in', [1, 2]) // 待分配、部分分配
    ->find();
```

### 4. 真实可用库存计算
```php
// 计算真正可用于当前订单的库存
// 可用库存 = 系统可用库存 + 当前订单已锁定库存
$realAvailableQty = $systemAvailableQty + $currentOrderLocked;

// 如果有分配需求且已部分分配，则可用库存还要加上已分配数量
if ($allocationRequest && $allocationRequest['allocated_quantity'] > 0) {
    $realAvailableQty += $allocationRequest['allocated_quantity'];
}

// 但不能超过订单需求数量
$orderNeedQty = $orderDetail['quantity']; // 订单需求数量
$maxAvailableQty = min($realAvailableQty, $orderNeedQty);
```

## 计算原理说明

### 1. 为什么要加上当前订单已锁定库存？
- **场景**：当前订单已经锁定了部分库存，这部分库存对当前订单来说是可用的
- **逻辑**：已锁定的库存可以用于当前订单的发货，所以应该计入可用库存
- **示例**：系统可用库存10，当前订单已锁定5，则当前订单可用库存 = 10 + 5 = 15

### 2. 为什么要加上已分配数量？
- **场景**：通过分配系统已经为当前订单分配了库存，但可能还未锁定
- **逻辑**：已分配的库存是专门为当前订单预留的，应该计入可用库存
- **示例**：系统可用库存10，已分配给当前订单3，则当前订单可用库存 = 10 + 3 = 13

### 3. 为什么要限制不超过订单需求数量？
- **场景**：避免显示超过订单实际需要的库存数量
- **逻辑**：即使库存充足，也只显示订单需要的数量，避免混淆
- **示例**：订单需要5个，计算出可用库存20个，最终显示5个

## 返回数据结构

```php
$item['inventory_qty'] = $totalQty; // 总库存（所有仓库汇总）
$item['system_available_qty'] = $systemAvailableQty; // 系统可用库存
$item['system_locked_qty'] = $systemLockedQty; // 系统锁定库存
$item['current_order_locked'] = $currentOrderLocked; // 当前订单锁定库存
$item['available_qty'] = $maxAvailableQty; // 真正可用于当前订单的库存 ⭐
$item['allocation_status'] = $allocationRequest ? $allocationRequest['status'] : 0; // 分配状态
```

## 业务场景示例

### 场景1：正常有库存
```
总库存：100
系统可用库存：80
系统锁定库存：20
当前订单锁定：0
订单需求：10
→ 可用库存：min(80 + 0, 10) = 10 ✅
```

### 场景2：当前订单已锁定部分库存
```
总库存：100
系统可用库存：70
系统锁定库存：30
当前订单锁定：15
订单需求：20
→ 可用库存：min(70 + 15, 20) = 20 ✅
```

### 场景3：通过分配系统已分配
```
总库存：100
系统可用库存：60
已分配给当前订单：8
当前订单锁定：0
订单需求：10
→ 可用库存：min(60 + 0 + 8, 10) = 10 ✅
```

### 场景4：库存不足
```
总库存：100
系统可用库存：5
当前订单锁定：3
订单需求：15
→ 可用库存：min(5 + 3, 15) = 8 ⚠️ 库存不足
```

### 场景5：无库存但有分配需求
```
总库存：0
系统可用库存：0
分配需求状态：待分配
订单需求：10
→ 可用库存：0 ❌ 需要等待入库分配
```

## 状态说明

### 分配状态 (allocation_status)
- **0**：无分配需求
- **1**：待分配 - 已创建分配需求，等待库存入库
- **2**：部分分配 - 已分配部分库存，等待剩余库存
- **3**：完全分配 - 已完全分配所需库存
- **4**：已取消 - 分配需求已取消

### 库存状态判断
```php
if ($available_qty >= $order_need_qty) {
    // 库存充足，可以发货
    $status = 'sufficient';
} elseif ($available_qty > 0) {
    // 库存不足，可以部分发货
    $status = 'insufficient';
} else {
    // 无库存，需要等待
    $status = 'out_of_stock';
}
```

## 前端显示建议

### 1. 库存数量显示
```html
<!-- 根据库存状态显示不同颜色 -->
<span class="stock-qty {{ $status_class }}">{{ $available_qty }}</span>

<!-- 显示详细信息 -->
<div class="stock-detail">
    <div>可用：{{ $available_qty }}</div>
    <div>总库存：{{ $inventory_qty }}</div>
    <div>已锁定：{{ $current_order_locked }}</div>
</div>
```

### 2. 状态提示
```html
<!-- 库存充足 -->
<span class="badge badge-success">库存充足</span>

<!-- 库存不足 -->
<span class="badge badge-warning">库存不足</span>

<!-- 无库存 -->
<span class="badge badge-danger">无库存</span>

<!-- 等待分配 -->
<span class="badge badge-info">等待分配</span>
```

## 优势特点

### 1. 业务准确性
- 显示真正可用于当前订单的库存数量
- 考虑了锁定、分配等业务因素
- 避免了误导性的库存信息

### 2. 用户体验
- 用户看到的就是实际可发货的数量
- 减少因库存信息不准确导致的操作错误
- 提供清晰的库存状态提示

### 3. 系统一致性
- 与库存分配系统保持一致
- 与锁定机制保持同步
- 支持复杂的业务场景

## 总结

通过这种计算逻辑，发货管理页面显示的库存数量真正反映了当前订单可以使用的库存数量，考虑了库存分配、锁定状态、优先级等多个因素，为用户提供了准确、可靠的库存信息，提升了系统的业务准确性和用户体验。
