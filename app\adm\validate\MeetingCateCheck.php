<?php


namespace app\adm\validate;

use think\Validate;

class MeetingCateCheck extends Validate
{
    protected $rule = [
        'title' => 'require|unique:meeting_cate',
        'id' => 'require',
    ];

    protected $message = [
        'title.require' => '名称不能为空',
        'title.unique' => '同样的名称已经存在',
        'id.require' => '缺少更新条件',
    ];

    protected $scene = [
        'add' => ['title'],
        'edit' => ['id', 'title'],
    ];
}
