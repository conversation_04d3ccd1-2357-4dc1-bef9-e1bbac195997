<?php
/**
 * 修复生产订单领料状态
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

use think\facade\Db;

echo "开始修复生产订单领料状态...\n";

try {
    // 1. 查找所有有领料单的生产订单
    $materialRequests = Db::name('production_material_request')
        ->field('production_order_id, request_no')
        ->group('production_order_id')
        ->select()
        ->toArray();
    
    $fixedCount = 0;
    $totalCount = count($materialRequests);
    
    echo "找到 {$totalCount} 个有领料单的生产订单\n";
    
    foreach ($materialRequests as $request) {
        $productionOrderId = $request['production_order_id'];
        
        // 2. 检查该生产订单的所有领料单是否都已出库完成
        $allMaterialRequests = Db::name('production_material_request')
            ->where('production_order_id', $productionOrderId)
            ->select()
            ->toArray();
        
        $allCompleted = true;
        $hasAnyOutbound = false;
        
        foreach ($allMaterialRequests as $materialRequest) {
            // 检查该领料单对应的出库单是否都已完成
            $outboundCount = Db::name('outbound')
                ->where('ref_type', 'production_material_request')
                ->where('ref_no', $materialRequest['request_no'])
                ->where('status', 4) // 已出库
                ->count();
            
            $totalOutboundCount = Db::name('outbound')
                ->where('ref_type', 'production_material_request')
                ->where('ref_no', $materialRequest['request_no'])
                ->count();
            
            if ($totalOutboundCount > 0) {
                $hasAnyOutbound = true;
            }
            
            if ($outboundCount < $totalOutboundCount) {
                $allCompleted = false;
            }
        }
        
        // 3. 确定新的领料状态
        $newFeedingFlag = 0; // 默认未投料
        if ($hasAnyOutbound) {
            $newFeedingFlag = $allCompleted ? 2 : 1; // 2=投料完成, 1=部分投料
        }
        
        // 4. 获取当前状态
        $currentOrder = Db::name('produce_order')
            ->where('id', $productionOrderId)
            ->field('id, order_no, feeding_flag')
            ->find();
        
        if (!$currentOrder) {
            echo "生产订单 {$productionOrderId} 不存在，跳过\n";
            continue;
        }
        
        // 5. 如果状态需要更新
        if ($currentOrder['feeding_flag'] != $newFeedingFlag) {
            Db::name('produce_order')
                ->where('id', $productionOrderId)
                ->update([
                    'feeding_flag' => $newFeedingFlag,
                    'update_time' => time()
                ]);
            
            $statusText = [
                0 => '未投料',
                1 => '部分投料', 
                2 => '投料完成'
            ];
            
            echo "✅ 更新生产订单 {$currentOrder['order_no']} (ID:{$productionOrderId}) 领料状态: {$statusText[$currentOrder['feeding_flag']]} → {$statusText[$newFeedingFlag]}\n";
            
            $fixedCount++;
        } else {
            echo "生产订单 {$currentOrder['order_no']} (ID:{$productionOrderId}) 状态正确，无需更新\n";
        }
    }
    
    echo "\n修复完成！\n";
    echo "总计检查: {$totalCount} 个生产订单\n";
    echo "修复数量: {$fixedCount} 个\n";
    
} catch (\Exception $e) {
    echo "修复过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
