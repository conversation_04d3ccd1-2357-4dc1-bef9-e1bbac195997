# 订单查看页面金额税额合计分别显示

## 问题描述
在订单查看页面（/customer/order/view）中，订单明细表格的合计行只显示了一个总金额，但应该分别显示：
1. 金额合计（不含税金额的总和）
2. 税额合计（税额的总和）

## 当前问题
- 合计行只有一个"合计(含税)"显示
- 金额列和税额列都没有显示各自的合计数值
- 用户无法清楚看到不含税金额总计和税额总计

## 解决方案
修改订单查看页面的合计行，使其分别显示：
1. 在金额列显示所有明细的金额合计（不含税）
2. 在税额列显示所有明细的税额合计

### 需要修改的文件
1. `app/customer/view/order/view.html` - 修改合计行的显示逻辑
2. `app/customer/controller/Order.php` - 在view方法中计算并传递金额合计和税额合计

### 实现步骤
1. 在控制器中计算订单明细的金额合计和税额合计
2. 将计算结果传递给视图
3. 修改视图中的合计行，分别显示金额合计和税额合计

## 技术细节
- 金额合计：所有明细的amount字段求和
- 税额合计：所有明细的tax_amount字段求和
- 显示位置：表格的tfoot部分，对应金额列和税额列

## 修复完成情况

### ✅ 已完成的修改

1. **控制器数据准备** - `app/customer/controller/Order.php`
   - 控制器的view方法已经正确计算了金额合计和税额合计
   - 在第995-996行将 `totalAmount` 和 `totalTax` 传递给视图

2. **视图文件修改** - `app/customer/view/order/view.html`
   - 修改了第195-203行的合计行显示逻辑
   - 金额合计显示在金额列（第7列）
   - 税额合计显示在税额列（第9列）

### 修改详情

**修改后的合计行代码：**
```html
<tfoot>
    <tr>
        <td colspan="6" style="text-align: right;"><strong>合计：</strong></td>
        <td style="text-align: right;"><strong>{$totalAmount|default='0.00'}</strong></td>
        <td>&nbsp;</td>
        <td style="text-align: right;"><strong>{$totalTax|default='0.00'}</strong></td>
        <td colspan="5">&nbsp;</td>
    </tr>
</tfoot>
```

### 表格列结构对应关系
1. 序号 (1)
2. 材料编码 (2)
3. 产品名称 (3)
4. 数量 (4)
5. 每套/件 (5)
6. 单价 (6)
7. **金额** (7) ← 显示金额合计
8. 税率 (8)
9. **税额** (9) ← 显示税额合计
10. 库存 (10)
11. 备注 (11)
12. BOM (12)
13. 来源 (13)
14. 附件 (14)

## 测试建议
1. 访问订单查看页面，确认合计行正确显示金额合计和税额合计
2. 验证数值计算是否正确（金额合计 = 所有明细金额之和，税额合计 = 所有明细税额之和）
3. 检查显示格式是否美观，数值对齐是否正确
