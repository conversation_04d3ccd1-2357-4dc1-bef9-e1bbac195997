# 生产领料列表出库状态显示修复

## 🔍 问题描述

生产领料模块列表页面（`http://tc.xinqiyu.cn:8830/Produce/MaterialRequest/index`）的"出库状态"列显示为空，无法正确显示领料单的出库情况。

## 🎯 问题原因

1. **后端数据缺失**：控制器查询时没有包含出库状态的逻辑
2. **前端列定义不完整**：模板中缺少出库状态列的定义和显示模板

## 🛠️ 解决方案

### 1. 后端修复 - 添加出库状态查询逻辑

#### 修改控制器查询
在 `app\Produce\controller\MaterialRequest.php` 的 `index()` 方法中添加出库状态查询：

```php
// 为每个领料单添加出库状态
$listData = $list->toArray();
foreach ($listData['data'] as &$item) {
    $item['outbound_status'] = $this->getOutboundStatus($item['request_no']);
    $item['outbound_status_text'] = $this->getOutboundStatusText($item['outbound_status']);
}
```

#### 添加出库状态查询方法
```php
/**
 * 获取领料单的出库状态
 * @param string $requestNo 领料单号
 * @return int 出库状态：0=未出库,1=部分出库,2=全部出库
 */
private function getOutboundStatus($requestNo)
{
    try {
        // 查询该领料单对应的所有出库单
        $outbounds = Db::name('outbound')
            ->where('ref_type', 'production_material_request')
            ->where('ref_no', $requestNo)
            ->field('id, status, total_quantity')
            ->select()
            ->toArray();
        
        if (empty($outbounds)) {
            return 0; // 未出库
        }
        
        // 检查是否有已完成出库的单据
        $hasCompleted = false;
        $hasPartial = false;
        
        foreach ($outbounds as $outbound) {
            if ($outbound['status'] == 4) { // 全部出库
                $hasCompleted = true;
            } elseif ($outbound['status'] == 3) { // 部分出库
                $hasPartial = true;
            }
        }
        
        if ($hasCompleted) {
            return 2; // 全部出库
        } elseif ($hasPartial) {
            return 1; // 部分出库
        } else {
            return 0; // 未出库
        }
        
    } catch (\Exception $e) {
        return 0; // 默认未出库
    }
}

/**
 * 获取出库状态文本
 * @param int $status 出库状态
 * @return string 状态文本
 */
private function getOutboundStatusText($status)
{
    switch ($status) {
        case 0:
            return '未出库';
        case 1:
            return '部分出库';
        case 2:
            return '全部出库';
        default:
            return '未知';
    }
}
```

### 2. 前端修复 - 添加出库状态列显示

#### 添加表格列定义
在 `app\Produce\view\material_request\index.html` 中添加出库状态列：

```javascript
{field: 'outbound_status', title: '出库状态', width: 100, align: 'center', templet: '#outboundStatusTpl'},
```

#### 添加出库状态显示模板
```html
<!-- 出库状态模板 -->
<script type="text/html" id="outboundStatusTpl">
    {{# if (d.outbound_status == 0) { }}
        <span class="layui-badge layui-bg-gray">未出库</span>
    {{# } else if (d.outbound_status == 1) { }}
        <span class="layui-badge layui-bg-orange">部分出库</span>
    {{# } else if (d.outbound_status == 2) { }}
        <span class="layui-badge layui-bg-green">全部出库</span>
    {{# } else { }}
        <span class="layui-badge">未知</span>
    {{# } }}
</script>
```

## 📋 出库状态说明

### 状态定义
- **未出库** (0)：领料单还没有生成出库单或出库单未执行
- **部分出库** (1)：有出库单但只完成了部分出库
- **全部出库** (2)：所有相关出库单都已完成出库

### 状态判断逻辑
1. **查询关联出库单**：通过 `ref_type='production_material_request'` 和 `ref_no=领料单号` 查找
2. **检查出库单状态**：
   - `status=4`：全部出库
   - `status=3`：部分出库
   - 其他状态：未完成出库
3. **综合判断**：
   - 有任何一个出库单完成全部出库 → 全部出库
   - 有部分出库但无全部出库 → 部分出库
   - 无出库单或都未完成 → 未出库

### 视觉效果
- **未出库**：灰色标签
- **部分出库**：橙色标签
- **全部出库**：绿色标签

## 🎯 修复效果

### 修复前
- ❌ 出库状态列显示为空
- ❌ 无法了解领料单的出库进度
- ❌ 业务流程跟踪不完整

### 修复后
- ✅ 出库状态正确显示
- ✅ 清楚了解每个领料单的出库情况
- ✅ 完整的业务流程跟踪
- ✅ 便于管理和监控

## 🔧 测试验证

1. **访问列表页面**：`http://tc.xinqiyu.cn:8830/Produce/MaterialRequest/index`
2. **检查出库状态列**：应该显示正确的状态标签
3. **验证状态准确性**：对比出库单的实际状态
4. **测试不同状态**：创建不同出库状态的领料单进行验证

修复完成后，生产领料模块将能够正确显示每个领料单的出库状态，提供完整的业务流程跟踪。
