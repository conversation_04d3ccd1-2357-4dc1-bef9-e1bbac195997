<?php
namespace app\Produce\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\Log;
use think\facade\Request;
use think\facade\View;

/**
 * 生产计划排产控制器
 */
class ProductionPlan extends BaseController
{
    /**
     * 获取当前用户姓名
     */
    private function getCurrentUserName()
    {
        if ($this->uid > 0) {
            $admin = get_admin($this->uid);
            return $admin['name'] ?? '系统用户';
        }
        return '系统用户';
    }
    /**
     * 排产主页面
     */
    public function index()
    {
        return View::fetch();
    }

    /**
     * 获取待排产订单列表
     */
    public function getUnscheduledOrders()
    {
        try {
            $page = Request::param('page', 1);
            $limit = Request::param('limit', 20);
            $status = Request::param('status', '');
            $customer = Request::param('customer', '');
            $keyword = Request::param('keyword', '');

            $where = [];
            // 显示未排产的订单，或者已逾期但未完成的订单
            $where[] = ['plan_status', 'in', [0, 1, 2]]; // 未排产、已排产、生产中
            $where[] = ['status', 'in', [0, 1, 2]]; // 待排产、已排产、生产中状态

            // 添加逾期订单的特殊处理
            $currentTime = time();
            $where[] = ['delivery_date', '>=', $currentTime - 30 * 24 * 3600]; // 显示30天内的订单（包括逾期）

            if (!empty($status)) {
                $where[] = ['status', '=', $status];
            }
            if (!empty($customer)) {
                $where[] = ['customer_order_no', 'like', '%' . $customer . '%'];
            }
            if (!empty($keyword)) {
                $where[] = ['order_no|product_name', 'like', '%' . $keyword . '%'];
            }

            $orders = Db::name('produce_order')
                ->where($where)
                ->field([
                    'id', 'order_no', 'product_id', 'product_name', 'quantity',
                    'delivery_date', 'priority', 'status', 'customer_order_no',
                    'create_time', 'estimated_days'
                ])
                ->order('priority desc, delivery_date asc, create_time asc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page
                ]);

            // 计算紧急程度和优先级评分
            $currentTime = time();
            foreach ($orders->items() as &$order) {
                $order['urgency_score'] = $this->calculateUrgencyScore($order);
                $order['priority_score'] = $this->calculatePriorityScore($order);
                $order['delivery_date_formatted'] = date('Y-m-d', $order['delivery_date']);
                $order['estimated_days'] = $order['estimated_days'] ?: 1;

                // 判断是否逾期
                $order['is_overdue'] = $order['delivery_date'] < $currentTime;
                $order['overdue_days'] = $order['is_overdue'] ? ceil(($currentTime - $order['delivery_date']) / 86400) : 0;
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $orders->items(),
                'count' => $orders->total()
            ]);

        } catch (\Exception $e) {
            Log::error('获取待排产订单失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取已排产计划数据
     */
    public function getScheduledPlans()
    {
        try {
            $startDate = Request::param('start_date', date('Y-m-d'));
            $endDate = Request::param('end_date', date('Y-m-d', strtotime('+30 days')));
            $status = Request::param('status', '');

            $where = [];
            $where[] = ['pp.plan_start_date', '<=', $endDate];
            $where[] = ['pp.plan_end_date', '>=', $startDate];

            if (!empty($status)) {
                $where[] = ['pp.status', '=', $status];
            }

            $plans = Db::name('production_plan')
                ->alias('pp')
                ->leftJoin('produce_order po', 'pp.order_id = po.id')
                ->where($where)
                ->field([
                    'pp.*',
                    'po.order_no', 'po.product_name', 'po.quantity',
                    'po.customer_order_no', 'po.delivery_date'
                ])
                ->order('pp.plan_start_date asc, pp.priority desc')
                ->select();

            // 格式化数据用于甘特图显示
            $ganttData = [];
            foreach ($plans as $plan) {
                // 实时计算订单进度
                $actualProgress = $this->calculateOrderProgress($plan['order_id'], $plan['quantity']);

                // 如果实际进度与数据库中的进度不同，更新数据库
                if (abs($actualProgress - $plan['progress']) > 0.1) {
                    Db::name('production_plan')
                        ->where('id', $plan['id'])
                        ->update(['progress' => $actualProgress, 'update_time' => time()]);
                }

                $ganttData[] = [
                    'id' => $plan['id'],
                    'order_id' => $plan['order_id'],
                    'order_no' => $plan['order_no'],
                    'product_name' => $plan['product_name'],
                    'quantity' => $plan['quantity'],
                    'customer_order_no' => $plan['customer_order_no'],
                    'start_date' => $plan['plan_start_date'],
                    'end_date' => $plan['plan_end_date'],
                    'progress' => $actualProgress, // 使用实时计算的进度
                    'status' => $plan['status'],
                    'priority' => $plan['priority'],
                    'notes' => $plan['notes'],
                    'delivery_date' => date('Y-m-d', $plan['delivery_date']),
                    'is_delayed' => $plan['status'] == 3 || ($plan['plan_end_date'] < date('Y-m-d') && $plan['status'] != 2)
                ];
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $ganttData
            ]);

        } catch (\Exception $e) {
            Log::error('获取排产计划失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存/更新排产计划
     */
    public function savePlan()
    {
        try {
            $orderId = Request::param('order_id');
            $startDate = Request::param('start_date');
            $endDate = Request::param('end_date');
            $priority = Request::param('priority', 1);
            $notes = Request::param('notes', '');

            if (empty($orderId) || empty($startDate) || empty($endDate)) {
                return json(['code' => 1, 'msg' => '参数不完整']);
            }

            // 验证日期
            if (strtotime($startDate) > strtotime($endDate)) {
                return json(['code' => 1, 'msg' => '开始日期不能大于结束日期']);
            }

            // 计算生产天数
            $planDays = (strtotime($endDate) - strtotime($startDate)) / 86400 + 1;

            Db::startTrans();
            try {
                // 检查是否已存在排产计划
                $existingPlan = Db::name('production_plan')->where('order_id', $orderId)->find();

                $planData = [
                    'order_id' => $orderId,
                    'plan_start_date' => $startDate,
                    'plan_end_date' => $endDate,
                    'plan_days' => $planDays,
                    'priority' => $priority,
                    'notes' => $notes,
                    'update_time' => time(),
                    'update_uid' => $this->uid,
                    'update_name' => $this->getCurrentUserName()
                ];

                if ($existingPlan) {
                    // 更新现有计划
                    Db::name('production_plan')->where('id', $existingPlan['id'])->update($planData);
                    $planId = $existingPlan['id'];
                } else {
                    // 创建新计划
                    $planData['create_time'] = time();
                    $planData['create_uid'] = $this->uid;
                    $planData['create_name'] = $this->getCurrentUserName();
                    $planId = Db::name('production_plan')->insertGetId($planData);
                }

                // 更新生产订单的排产状态
                Db::name('produce_order')->where('id', $orderId)->update([
                    'plan_id' => $planId,
                    'plan_status' => 1,
                    'status' => 1, // 更新工单状态为"已排产"
                    'scheduled_date' => $startDate,
                    'estimated_days' => $planDays,
                    'update_time' => time()
                ]);

                // 记录操作日志
                $this->logPlanAction($planId, $orderId, $existingPlan ? 'update' : 'create', $existingPlan, $planData);

                Db::commit();

                return json(['code' => 0, 'msg' => '排产成功']);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('保存排产计划失败', [
                'order_id' => $orderId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '排产失败：' . $e->getMessage()]);
        }
    }

    /**
     * 计算紧急程度评分
     */
    private function calculateUrgencyScore($order)
    {
        $deliveryDate = $order['delivery_date'];
        $currentTime = time();
        $daysToDelivery = ($deliveryDate - $currentTime) / 86400;

        if ($daysToDelivery <= 0) {
            return 100; // 已过期
        } elseif ($daysToDelivery <= 3) {
            return 90; // 3天内
        } elseif ($daysToDelivery <= 7) {
            return 70; // 一周内
        } elseif ($daysToDelivery <= 15) {
            return 50; // 半月内
        } else {
            return 30; // 半月以上
        }
    }

    /**
     * 计算优先级评分
     */
    private function calculatePriorityScore($order)
    {
        $urgencyScore = $this->calculateUrgencyScore($order);
        $priorityScore = $order['priority'] * 20; // 优先级1-5，转换为20-100分
        $quantityScore = min($order['quantity'] / 100, 10); // 数量影响，最高10分

        return $urgencyScore * 0.4 + $priorityScore * 0.3 + $quantityScore * 0.3;
    }

    /**
     * 自动排产算法
     */
    public function autoSchedule()
    {
        try {
            $startDate = Request::param('start_date', date('Y-m-d'));
            $maxDailyOrders = Request::param('max_daily_orders', 5); // 每日最大订单数

            // 获取待排产订单
            $orders = Db::name('produce_order')
                ->where('plan_status', 0)
                ->where('status', 'in', [0, 1])
                ->field([
                    'id', 'order_no', 'product_name', 'quantity',
                    'delivery_date', 'priority', 'estimated_days'
                ])
                ->select()
                ->toArray();

            if (empty($orders)) {
                return json(['code' => 1, 'msg' => '没有待排产订单']);
            }

            // 计算每个订单的优先级评分
            foreach ($orders as &$order) {
                $order['priority_score'] = $this->calculatePriorityScore($order);
                $order['estimated_days'] = $order['estimated_days'] ?: 1;
            }

            // 按优先级评分排序
            usort($orders, function($a, $b) {
                return $b['priority_score'] <=> $a['priority_score'];
            });

            Db::startTrans();
            try {
                $currentDate = $startDate;
                $scheduledCount = 0;

                foreach ($orders as $order) {
                    // 查找可用的排产日期
                    $availableDate = $this->findAvailableDate($currentDate, $order['estimated_days'], $maxDailyOrders);

                    if ($availableDate) {
                        $endDate = date('Y-m-d', strtotime($availableDate . ' +' . ($order['estimated_days'] - 1) . ' days'));

                        // 创建排产计划
                        $planId = Db::name('production_plan')->insertGetId([
                            'order_id' => $order['id'],
                            'plan_start_date' => $availableDate,
                            'plan_end_date' => $endDate,
                            'plan_days' => $order['estimated_days'],
                            'priority' => $order['priority'],
                            'auto_scheduled' => 1,
                            'create_time' => time(),
                            'create_uid' => $this->uid,
                            'create_name' => $this->getCurrentUserName()
                        ]);

                        // 更新订单状态
                        Db::name('produce_order')->where('id', $order['id'])->update([
                            'plan_id' => $planId,
                            'plan_status' => 1,
                            'status' => 1, // 更新工单状态为"已排产"
                            'scheduled_date' => $availableDate,
                            'estimated_days' => $order['estimated_days'],
                            'update_time' => time()
                        ]);

                        // 记录日志
                        $this->logPlanAction($planId, $order['id'], 'auto_schedule', null, [
                            'start_date' => $availableDate,
                            'end_date' => $endDate,
                            'priority_score' => $order['priority_score']
                        ]);

                        $scheduledCount++;
                    }
                }

                Db::commit();

                return json([
                    'code' => 0,
                    'msg' => "自动排产完成，共排产 {$scheduledCount} 个订单"
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('自动排产失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '自动排产失败：' . $e->getMessage()]);
        }
    }

    /**
     * 查找可用的排产日期
     */
    private function findAvailableDate($startDate, $requiredDays, $maxDailyOrders)
    {
        $currentDate = $startDate;
        $maxSearchDays = 60; // 最多搜索60天
        $searchedDays = 0;

        while ($searchedDays < $maxSearchDays) {
            $canSchedule = true;

            // 检查连续的天数是否都可用
            for ($i = 0; $i < $requiredDays; $i++) {
                $checkDate = date('Y-m-d', strtotime($currentDate . ' +' . $i . ' days'));

                // 跳过周末（可选）
                $dayOfWeek = date('w', strtotime($checkDate));
                if ($dayOfWeek == 0 || $dayOfWeek == 6) {
                    $canSchedule = false;
                    break;
                }

                // 检查当天的订单数量
                $dailyOrderCount = Db::name('production_plan')
                    ->where('plan_start_date', '<=', $checkDate)
                    ->where('plan_end_date', '>=', $checkDate)
                    ->count();

                if ($dailyOrderCount >= $maxDailyOrders) {
                    $canSchedule = false;
                    break;
                }
            }

            if ($canSchedule) {
                return $currentDate;
            }

            // 移动到下一天
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
            $searchedDays++;
        }

        return null; // 找不到可用日期
    }

    /**
     * 更新订单进度
     */
    public function updateProgress()
    {
        try {
            $planId = Request::param('plan_id');
            $progress = Request::param('progress', 0);
            $actualStartDate = Request::param('actual_start_date', '');
            $notes = Request::param('notes', '');

            if (empty($planId)) {
                return json(['code' => 1, 'msg' => '计划ID不能为空']);
            }

            $progress = max(0, min(100, floatval($progress)));

            $updateData = [
                'progress' => $progress,
                'update_time' => time(),
                'update_uid' => $this->uid,
                'update_name' => $this->getCurrentUserName()
            ];

            if (!empty($actualStartDate)) {
                $updateData['actual_start_date'] = $actualStartDate;
            }

            if (!empty($notes)) {
                $updateData['notes'] = $notes;
            }

            // 根据进度自动更新状态
            if ($progress == 0) {
                $updateData['status'] = 0; // 待开始
            } elseif ($progress > 0 && $progress < 100) {
                $updateData['status'] = 1; // 生产中
                if (empty($updateData['actual_start_date'])) {
                    $updateData['actual_start_date'] = date('Y-m-d');
                }
            } elseif ($progress == 100) {
                $updateData['status'] = 2; // 已完成
                $updateData['actual_end_date'] = date('Y-m-d');
            }

            Db::name('production_plan')->where('id', $planId)->update($updateData);

            // 同步更新生产订单状态
            $plan = Db::name('production_plan')->where('id', $planId)->find();
            if ($plan) {
                $orderStatus = 1; // 已排产
                if ($progress > 0) {
                    $orderStatus = 2; // 生产中
                }
                if ($progress == 100) {
                    $orderStatus = 3; // 已完成
                }

                Db::name('produce_order')->where('id', $plan['order_id'])->update([
                    'status' => $orderStatus,
                    'progress' => $progress,
                    'update_time' => time()
                ]);
            }

            return json(['code' => 0, 'msg' => '更新成功']);

        } catch (\Exception $e) {
            Log::error('更新进度失败', [
                'plan_id' => $planId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取每日统计数据
     */
    public function getDailyStats()
    {
        try {
            $startDate = Request::param('start_date', date('Y-m-d', strtotime('-7 days')));
            $endDate = Request::param('end_date', date('Y-m-d'));

            $stats = [];
            $currentDate = $startDate;

            while (strtotime($currentDate) <= strtotime($endDate)) {
                // 获取当日排产统计
                $dailyPlans = Db::name('production_plan')
                    ->alias('pp')
                    ->leftJoin('produce_order po', 'pp.order_id = po.id')
                    ->where('pp.plan_start_date', '<=', $currentDate)
                    ->where('pp.plan_end_date', '>=', $currentDate)
                    ->field([
                        'pp.status',
                        'po.quantity',
                        'pp.progress'
                    ])
                    ->select();

                $totalOrders = count($dailyPlans);
                $completedOrders = 0;
                $inProgressOrders = 0;
                $pendingOrders = 0;
                $totalQuantity = 0;
                $completedQuantity = 0;

                foreach ($dailyPlans as $plan) {
                    $totalQuantity += $plan['quantity'];

                    switch ($plan['status']) {
                        case 0:
                            $pendingOrders++;
                            break;
                        case 1:
                            $inProgressOrders++;
                            $completedQuantity += ($plan['quantity'] * $plan['progress'] / 100);
                            break;
                        case 2:
                            $completedOrders++;
                            $completedQuantity += $plan['quantity'];
                            break;
                    }
                }

                $stats[] = [
                    'date' => $currentDate,
                    'total_orders' => $totalOrders,
                    'completed_orders' => $completedOrders,
                    'in_progress_orders' => $inProgressOrders,
                    'pending_orders' => $pendingOrders,
                    'total_quantity' => $totalQuantity,
                    'completed_quantity' => round($completedQuantity),
                    'completion_rate' => $totalOrders > 0 ? round($completedOrders / $totalOrders * 100, 2) : 0,
                    'quantity_rate' => $totalQuantity > 0 ? round($completedQuantity / $totalQuantity * 100, 2) : 0
                ];

                $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('获取每日统计失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除排产计划
     */
    public function deletePlan()
    {
        try {
            $planId = Request::param('plan_id');

            if (empty($planId)) {
                return json(['code' => 1, 'msg' => '计划ID不能为空']);
            }

            $plan = Db::name('production_plan')->where('id', $planId)->find();
            if (!$plan) {
                return json(['code' => 1, 'msg' => '排产计划不存在']);
            }

            Db::startTrans();
            try {
                // 删除排产计划
                Db::name('production_plan')->where('id', $planId)->delete();

                // 更新生产订单状态
                Db::name('produce_order')->where('id', $plan['order_id'])->update([
                    'plan_id' => 0,
                    'plan_status' => 0,
                    'scheduled_date' => null,
                    'update_time' => time()
                ]);

                // 记录日志
                $this->logPlanAction($planId, $plan['order_id'], 'delete', $plan, null);

                Db::commit();

                return json(['code' => 0, 'msg' => '删除成功']);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('删除排产计划失败', [
                'plan_id' => $planId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量排产
     */
    public function batchSchedule()
    {
        try {
            $orderIds = Request::param('order_ids', []);
            $startDate = Request::param('start_date', date('Y-m-d'));
            $maxDailyOrders = Request::param('max_daily_orders', 5);

            if (empty($orderIds) || !is_array($orderIds)) {
                return json(['code' => 1, 'msg' => '请选择要排产的订单']);
            }

            $orders = Db::name('produce_order')
                ->where('id', 'in', $orderIds)
                ->where('plan_status', 0)
                ->field([
                    'id', 'order_no', 'product_name', 'quantity',
                    'delivery_date', 'priority', 'estimated_days'
                ])
                ->select()
                ->toArray();

            if (empty($orders)) {
                return json(['code' => 1, 'msg' => '没有可排产的订单']);
            }

            // 按优先级和交期排序
            foreach ($orders as &$order) {
                $order['priority_score'] = $this->calculatePriorityScore($order);
                $order['estimated_days'] = $order['estimated_days'] ?: 1;
            }

            usort($orders, function($a, $b) {
                return $b['priority_score'] <=> $a['priority_score'];
            });

            Db::startTrans();
            try {
                $scheduledCount = 0;
                $currentDate = $startDate;

                foreach ($orders as $order) {
                    $availableDate = $this->findAvailableDate($currentDate, $order['estimated_days'], $maxDailyOrders);

                    if ($availableDate) {
                        $endDate = date('Y-m-d', strtotime($availableDate . ' +' . ($order['estimated_days'] - 1) . ' days'));

                        $planId = Db::name('production_plan')->insertGetId([
                            'order_id' => $order['id'],
                            'plan_start_date' => $availableDate,
                            'plan_end_date' => $endDate,
                            'plan_days' => $order['estimated_days'],
                            'priority' => $order['priority'],
                            'auto_scheduled' => 0,
                            'create_time' => time(),
                            'create_uid' => $this->uid,
                            'create_name' => $this->getCurrentUserName()
                        ]);

                        Db::name('produce_order')->where('id', $order['id'])->update([
                            'plan_id' => $planId,
                            'plan_status' => 1,
                            'status' => 1, // 更新工单状态为"已排产"
                            'scheduled_date' => $availableDate,
                            'estimated_days' => $order['estimated_days'],
                            'update_time' => time()
                        ]);

                        $this->logPlanAction($planId, $order['id'], 'batch_schedule', null, [
                            'start_date' => $availableDate,
                            'end_date' => $endDate
                        ]);

                        $scheduledCount++;
                    }
                }

                Db::commit();

                return json([
                    'code' => 0,
                    'msg' => "批量排产完成，共排产 {$scheduledCount} 个订单"
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('批量排产失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '批量排产失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取订单工序报工详情（用于悬停提示）
     */
    public function getOrderProcessDetails()
    {
        try {
            $orderId = Request::param('order_id');
            $date = Request::param('date', date('Y-m-d')); // 默认今天

            if (empty($orderId)) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }

            // 获取订单基本信息
            $order = Db::name('produce_order')
                ->where('id', $orderId)
                ->field('id, order_no, product_name, quantity')
                ->find();

            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            // 获取订单的所有工序
            $processes = Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->order('step_no asc')
                ->select()
                ->toArray();

            $processDetails = [];
            $totalReported = 0;
            $totalQualified = 0;

            foreach ($processes as $process) {
                // 获取该工序在指定日期的报工数据
                $dailyReports = Db::name('production_work_report')
                    ->where('order_id', $orderId)
                    ->where('process_id', $process['id'])
                    ->where('report_date', $date)
                    ->where('status', 1)
                    ->field('SUM(quantity) as daily_quantity, SUM(qualified_qty) as daily_qualified, SUM(work_time) as daily_work_time, COUNT(*) as report_count')
                    ->find();

                // 获取该工序的累计报工数据
                $totalReports = Db::name('production_work_report')
                    ->where('order_id', $orderId)
                    ->where('process_id', $process['id'])
                    ->where('status', 1)
                    ->field('SUM(quantity) as total_quantity, SUM(qualified_qty) as total_qualified')
                    ->find();

                $dailyQuantity = $dailyReports['daily_quantity'] ?: 0;
                $dailyQualified = $dailyReports['daily_qualified'] ?: 0;
                $dailyWorkTime = $dailyReports['daily_work_time'] ?: 0;
                $reportCount = $dailyReports['report_count'] ?: 0;

                $totalQuantity = $totalReports['total_quantity'] ?: 0;
                $totalQualifiedQty = $totalReports['total_qualified'] ?: 0;

                // 计算完成率
                $completionRate = $order['quantity'] > 0 ? round(($totalQualifiedQty / $order['quantity']) * 100, 1) : 0;

                $processDetails[] = [
                    'process_name' => $process['process_name'],
                    'step_no' => $process['step_no'],
                    'daily_quantity' => $dailyQuantity,
                    'daily_qualified' => $dailyQualified,
                    'daily_work_time' => round($dailyWorkTime / 60, 1), // 转换为小时
                    'report_count' => $reportCount,
                    'total_quantity' => $totalQuantity,
                    'total_qualified' => $totalQualifiedQty,
                    'completion_rate' => $completionRate,
                    'status' => $this->getProcessStatus($totalQualifiedQty, $order['quantity'])
                ];

                $totalReported += $dailyQuantity;
                $totalQualified += $dailyQualified;
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'order' => $order,
                    'date' => $date,
                    'processes' => $processDetails,
                    'summary' => [
                        'total_daily_reported' => $totalReported,
                        'total_daily_qualified' => $totalQualified,
                        'process_count' => count($processes)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取工序状态
     */
    private function getProcessStatus($qualifiedQty, $orderQty)
    {
        if ($qualifiedQty == 0) {
            return '未开始';
        } elseif ($qualifiedQty >= $orderQty) {
            return '已完成';
        } else {
            return '进行中';
        }
    }

    /**
     * 计算订单实际进度
     * @param int $orderId 订单ID
     * @param int $orderQuantity 订单数量
     * @return float 进度百分比
     */
    private function calculateOrderProgress($orderId, $orderQuantity)
    {
        if ($orderQuantity <= 0) {
            return 0;
        }

        // 获取订单的所有工序
        $processes = Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();

        if (empty($processes)) {
            return 0;
        }

        // 计算每个工序的权重（平均分配）
        $totalProcesses = count($processes);
        $weightPerProcess = 100 / $totalProcesses;

        $totalProgress = 0;

        foreach ($processes as $process) {
            // 获取该工序的报工数据
            $reportData = Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('process_id', $process['id'])
                ->where('status', 1)
                ->field('SUM(qualified_qty) as total_qualified')
                ->find();

            $qualifiedQty = $reportData['total_qualified'] ?: 0;

            // 计算该工序的完成率
            $processProgress = min(100, ($qualifiedQty / $orderQuantity) * 100);

            // 累加到总进度
            $totalProgress += ($processProgress / 100) * $weightPerProcess;
        }

        return round($totalProgress, 2);
    }

    /**
     * 记录排产操作日志
     */
    private function logPlanAction($planId, $orderId, $action, $oldData = null, $newData = null)
    {
        Db::name('production_plan_log')->insert([
            'plan_id' => $planId,
            'order_id' => $orderId,
            'action' => $action,
            'old_data' => $oldData ? json_encode($oldData) : null,
            'new_data' => $newData ? json_encode($newData) : null,
            'operator_id' => $this->uid,
            'operator_name' => $this->getCurrentUserName(),
            'create_time' => time()
        ]);
    }
}
