<?php
/**
 * BOM修复测试文件
 * 用于测试BOM兼容函数是否正常工作
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

echo "=== BOM修复测试 ===\n\n";

try {
    // 1. 测试BOM主表兼容性
    echo "1. 测试BOM主表兼容性:\n";
    try {
        $count = Db::name('oa_material_bom')->count();
        echo "   ✅ oa_material_bom 表存在，记录数: {$count}\n";
        $newTable = true;
    } catch (\Exception $e) {
        try {
            $count = Db::name('bom_master')->count();
            echo "   ⚠️  使用旧表 bom_master，记录数: {$count}\n";
            $newTable = false;
        } catch (\Exception $e2) {
            echo "   ❌ BOM表都不存在\n";
            $newTable = null;
        }
    }
    
    // 2. 测试BOM明细表兼容性
    echo "\n2. 测试BOM明细表兼容性:\n";
    try {
        $count = Db::name('oa_material_bom_detail')->count();
        echo "   ✅ oa_material_bom_detail 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        try {
            $count = Db::name('bom_item')->count();
            echo "   ⚠️  使用旧表 bom_item，记录数: {$count}\n";
        } catch (\Exception $e2) {
            echo "   ❌ BOM明细表都不存在\n";
        }
    }
    
    // 3. 测试BOM兼容函数
    echo "\n3. 测试BOM兼容函数:\n";
    if (function_exists('has_product_bom')) {
        echo "   ✅ has_product_bom() 函数已定义\n";
        
        // 测试函数调用
        try {
            $result = has_product_bom(1);
            echo "   ✅ has_product_bom(1) 调用成功，结果: " . ($result ? '有BOM' : '无BOM') . "\n";
        } catch (\Exception $e) {
            echo "   ❌ has_product_bom(1) 调用失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ has_product_bom() 函数未定义\n";
    }
    
    if (function_exists('get_product_bom')) {
        echo "   ✅ get_product_bom() 函数已定义\n";
        
        // 测试函数调用
        try {
            $result = get_product_bom(1);
            echo "   ✅ get_product_bom(1) 调用成功，结果: " . ($result ? 'BOM ID: ' . $result['id'] : '无BOM') . "\n";
        } catch (\Exception $e) {
            echo "   ❌ get_product_bom(1) 调用失败: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ get_product_bom() 函数未定义\n";
    }
    
    if (function_exists('get_bom_items')) {
        echo "   ✅ get_bom_items() 函数已定义\n";
    } else {
        echo "   ❌ get_bom_items() 函数未定义\n";
    }
    
    if (function_exists('get_bom_items_with_product')) {
        echo "   ✅ get_bom_items_with_product() 函数已定义\n";
    } else {
        echo "   ❌ get_bom_items_with_product() 函数未定义\n";
    }
    
    // 4. 测试产品表兼容性
    echo "\n4. 测试产品表兼容性:\n";
    try {
        $count = Db::name('product')->count();
        echo "   ✅ product 表存在，记录数: {$count}\n";
        
        // 获取一个产品ID用于测试
        $product = Db::name('product')->where('id', '>', 0)->find();
        if ($product) {
            echo "   ✅ 测试产品: ID={$product['id']}, 名称={$product['title']}\n";
            
            // 测试该产品的BOM
            if (function_exists('has_product_bom')) {
                $hasBom = has_product_bom($product['id']);
                echo "   📋 产品 {$product['id']} BOM状态: " . ($hasBom ? '有BOM' : '无BOM') . "\n";
            }
        }
    } catch (\Exception $e) {
        echo "   ❌ product 表不存在或查询失败: " . $e->getMessage() . "\n";
    }
    
    // 5. 测试客户订单产品列表API
    echo "\n5. 测试客户订单产品列表API兼容性:\n";
    echo "   📝 请手动访问: http://tc.xinqiyu.cn:8830/customer/Order/get_product_datalist.html?page=1&limit=15\n";
    echo "   📝 检查是否还有 'Undefined array key \"data\"' 错误\n";
    
    echo "\n=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
