<?php
declare(strict_types = 1);

namespace app\api\controller;

use app\api\BaseController;
use app\warehouse\model\PendingOutbound as PendingOutboundModel;
use app\warehouse\model\Outbound as OutboundModel;
use app\warehouse\model\OutboundDetail as OutboundDetailModel;
use think\facade\Db;

/**
 * 待出库清单API控制器
 */
class PendingOutbound extends BaseController
{
    /**
     * 获取待出库清单数据
     * 
     * @return \think\Response
     */
    public function getList()
    {
        $param = get_params();
        
        // 记录前端传递的参数
        \think\facade\Log::info('前端传递的参数', [
            'param' => is_array($param) ? json_encode($param) : $param
        ]);
        
        $where = [['delete_time', '=', 0]];
        
        // 状态筛选 - 如果未指定，默认显示未处理数据
        if (isset($param['status']) && $param['status'] !== '') {
            $where[] = ['status', '=', $param['status']];
        } else {
            // 默认显示未处理数据
            $where[] = ['status', '=', 0]; // 0 = 待处理
        }
        
        // 优先级筛选
        if (isset($param['priority']) && $param['priority'] !== '') {
            $where[] = ['priority', '=', $param['priority']];
        }
        
        // 关键词搜索（产品名称、编码、备注）
        if (isset($param['keywords']) && $param['keywords'] !== '') {
            $where[] = ['product_name|product_code|notes', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 预计出库日期范围
        if (isset($param['date_range']) && $param['date_range'] !== '') {
            $dates = explode(' - ', $param['date_range']);
            if (count($dates) == 2) {
                $where[] = ['expected_date', 'between', [strtotime($dates[0] . ' 00:00:00'), strtotime($dates[1] . ' 23:59:59')]];
            }
        }
        
        // 来源类型筛选
        if (isset($param['source_type']) && $param['source_type'] !== '') {
            $where[] = ['source_type', '=', $param['source_type']];
        }
        
        // 构建查询对象
        $query = PendingOutboundModel::alias('po')->where($where);
        
        // 获取所有已审核通过的投料单ID
        $approvedFeedingIds = \think\facade\Db::name('production_feeding')
            ->where('check_status', 1)
            ->where('delete_time', 0)
            ->column('id');
        
        // 记录已审核的投料单ID
        \think\facade\Log::info('已审核的投料单ID', [
            'approved_feeding_ids_count' => count($approvedFeedingIds),
            'approved_feeding_ids' => is_array($approvedFeedingIds) ? implode(',', $approvedFeedingIds) : $approvedFeedingIds
        ]);
        
        // 无论用户是否指定 source_type 参数，都需要过滤未审核的投料单
        // 使用复杂条件：要么不是生产领料类型，要么是生产领料类型且投料单已审核
        if (!empty($approvedFeedingIds)) {
            $query->where(function ($q) use ($approvedFeedingIds) {
                $q->where('po.source_type', '<>', 2)
                  ->whereOr(function ($q2) use ($approvedFeedingIds) {
                      $q2->where('po.source_type', '=', 2)
                         ->whereIn('po.source_detail_id', $approvedFeedingIds);
                  });
            });
        } else {
            // 如果没有已审核的投料单，则只显示非生产领料类型的记录
            $query->where('po.source_type', '<>', 2);
        }
        
        // 记录最终的SQL
        \think\facade\Log::info('最终查询SQL', [
            'sql' => $query->buildSql() ? (string)$query->buildSql() : ''
        ]);
        
        // 分页查询
        $list = $query->field('po.id, po.source_type, po.source_id, po.source_detail_id, po.product_id, po.product_name, po.product_code, po.specs, 
                    po.warehouse_id, po.warehouse_name, po.location_id, po.location_name, 
                    po.quantity, po.unit, po.status, po.priority, po.expected_date, 
                    po.notes, po.outbound_id, po.created_by, po.create_time')
            ->order('po.priority desc, po.create_time desc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
           
        // 记录查询结果
        \think\facade\Log::info('API PendingOutbound::getList - 查询结果', [
            'total' => $list->total(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage()
        ]);
        
        // 格式化数据
        $items = $list->items();
        
        // 记录返回的数据
        if (!empty($items)) {
            // 记录每条记录的关键信息
            $itemsInfo = [];
            foreach ($items as $item) {
                $itemsInfo[] = [
                    'id' => $item['id'],
                    'source_type' => $item['source_type'],
                    'source_id' => $item['source_id'],
                    'source_detail_id' => $item['source_detail_id'],
                    'product_name' => $item['product_name']
                ];
            }
            
            \think\facade\Log::info('API PendingOutbound::getList - 返回数据详情', [
                'items_count' => count($items),
                'items_info' => $itemsInfo
            ]);
        }
        
        // 批量获取来源单号
        $sourceNumbers = PendingOutboundModel::getSourceNumbers($items);
        
        // 获取所有产品ID，用于批量查询库存
        $productIds = [];
        foreach ($items as $item) {
            $productIds[] = $item['product_id'];
        }
        
        // 批量查询库存信息 - 直接使用quantity字段
        $inventoryData = [];
        if (!empty($productIds)) {
            // 使用IN查询一次性获取所有产品的库存
            $inventoryList = \think\facade\Db::name('inventory')
                ->field('product_id, warehouse_id, SUM(quantity) as total_quantity')
                ->where('product_id', 'in', $productIds)
                ->group('product_id, warehouse_id')
                ->select()
                ->toArray();
            
            // 整理库存数据，格式为 [product_id => [warehouse_id => quantity, ...], ...]
            foreach ($inventoryList as $inv) {
                if (!isset($inventoryData[$inv['product_id']])) {
                    $inventoryData[$inv['product_id']] = [];
                }
                $inventoryData[$inv['product_id']][$inv['warehouse_id']] = $inv['total_quantity'];
            }
        }
        
        // 处理列表项
        foreach ($items as &$item) {
            $item['source_type_text'] = PendingOutboundModel::getSourceTypeText($item['source_type']);
            $item['status_text'] = PendingOutboundModel::getStatusText($item['status']);
            $item['priority_text'] = PendingOutboundModel::getPriorityText($item['priority']);
            
            // 获取来源单号
            $item['source_no'] = PendingOutboundModel::getSourceNumber(
                $item['source_type'],
                $item['source_id'],
                $item['source_detail_id']
            );
            
            // 记录来源单号获取结果
            \think\facade\Log::info('获取来源单号结果', [
                'id' => $item['id'],
                'source_detail_id' => $item['source_detail_id'],
                'source_no' => is_array($item['source_no']) ? json_encode($item['source_no']) : $item['source_no']
            ]);
            
            // 确保 source_no 是字符串
            if (!is_string($item['source_no'])) {
                \think\facade\Log::error('来源单号类型错误', [
                    'id' => $item['id'],
                    'source_type' => $item['source_type'],
                    'source_id' => $item['source_id'],
                    'source_detail_id' => $item['source_detail_id'],
                    'source_no_type' => gettype($item['source_no']),
                    'source_no' => $item['source_no']
                ]);
                $item['source_no'] = is_array($item['source_no']) ? json_encode($item['source_no']) : (string)$item['source_no'];
            }
            
            // 添加库存信息 - 直接使用quantity字段作为可用库存
            $item['current_stock'] = 0; // 默认库存为0
            $item['suggested_stock'] = 0; // 建议仓库的库存
            
            // 计算总库存 - 直接使用quantity字段
            if (isset($inventoryData[$item['product_id']])) {
                $totalStock = array_sum($inventoryData[$item['product_id']]);
                
                // 如果有建议仓库，查找该仓库的库存
                if ($item['warehouse_id'] > 0 && isset($inventoryData[$item['product_id']][$item['warehouse_id']])) {
                    $item['suggested_stock'] = $inventoryData[$item['product_id']][$item['warehouse_id']];
                }
                
                // 设置可用库存 - 直接使用总库存
                $item['current_stock'] = $totalStock;
            } else {
                // 如果没有查询到库存记录，使用辅助函数获取
                $item['current_stock'] = get_inventory_stock($item['product_id']);
            }
            
            // 判断库存是否足够
            $item['stock_sufficient'] = $item['current_stock'] >= $item['quantity'];
            
            // 添加库存差额字段
            $item['stock_gap'] = $item['current_stock'] >= $item['quantity'] ? 0 : $item['quantity'] - $item['current_stock'];
            
            // 记录原始数据类型，帮助调试
            \think\facade\Log::debug('API PendingOutbound::getList - 处理列表项', [
                'id' => $item['id'],
                'source_type' => $item['source_type'],
                'source_id' => $item['source_id'],
                'source_detail_id' => $item['source_detail_id'],
                'source_no' => $item['source_no'],
                'product_id' => $item['product_id'],
                'current_stock' => $item['current_stock'],
                'suggested_stock' => $item['suggested_stock'],
                'stock_sufficient' => $item['stock_sufficient'],
                'stock_gap' => $item['stock_gap']
            ]);
            
            // 安全处理日期
            try {
                $item['create_time'] = !empty($item['create_time']) ? 
                    (is_numeric($item['create_time']) ? 
                        date('Y-m-d H:i', intval($item['create_time'])) : 
                        date('Y-m-d H:i', strtotime($item['create_time']))
                    ) : '';
                    
                $item['expected_date'] = !empty($item['expected_date']) ? 
                    (is_numeric($item['expected_date']) ? 
                        date('Y-m-d', intval($item['expected_date'])) : 
                        date('Y-m-d', strtotime($item['expected_date']))
                    ) : '';
            } catch (\Exception $e) {
                \think\facade\Log::error('日期格式化失败', [
                    'id' => $item['id'],
                    'create_time' => $item['create_time'],
                    'expected_date' => $item['expected_date'],
                    'error' => $e->getMessage()
                ]);
            }
            
            // 获取关联的出库单号
            if (!empty($item['outbound_id'])) {
                $outbound = OutboundModel::where('id', $item['outbound_id'])->field('outbound_no')->find();
                $item['outbound_no'] = $outbound ? $outbound['outbound_no'] : '';
            } else {
                $item['outbound_no'] = '';
            }
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $list->total(),
            'data' => $items
        ]);
    }
    
    /**
     * 添加待出库项
     * 
     * @return \think\Response
     */
    public function add()
    {
        $data = get_params();
        
        // 数据验证
        $validate = [
            'product_id' => 'require',
            'quantity' => 'require|float|gt:0',
            'source_type' => 'require|in:1,2',
        ];
        
        $message = [
            'product_id.require' => '产品不能为空',
            'quantity.require' => '出库数量不能为空',
            'quantity.float' => '出库数量必须是数字',
            'quantity.gt' => '出库数量必须大于0',
            'source_type.require' => '来源类型不能为空',
            'source_type.in' => '来源类型无效',
        ];
        
        $this->validate($data, $validate, $message);
        
        // 获取产品信息
        $product = Db::name('product')->where('id', $data['product_id'])->field('id, title, material_code, specs, unit')->find();
        if (!$product) {
            return json(['code' => 1, 'msg' => '产品不存在']);
        }
        
        // 准备数据
        $insertData = [
            'source_type' => $data['source_type'],
            'source_id' => $data['source_id'] ?? 0,
            'source_detail_id' => $data['source_detail_id'] ?? 0,
            'product_id' => $product['id'],
            'product_name' => $product['title'],
            'product_code' => $product['material_code'] ?? '',
            'specs' => $product['specs'] ?? '',
            // 仓库和库位为建议性质，不再强制要求
            'warehouse_id' => $data['warehouse_id'] ?? 0,
            'warehouse_name' => isset($data['warehouse_id']) && $data['warehouse_id'] > 0 ? Db::name('warehouse')->where('id', $data['warehouse_id'])->value('name') : '',
            'location_id' => $data['location_id'] ?? 0,
            'location_name' => isset($data['location_id']) && $data['location_id'] > 0 ? Db::name('warehouse_location')->where('id', $data['location_id'])->value('name') : '',
            'quantity' => $data['quantity'],
            'unit' => $product['unit'] ?? $data['unit'] ?? '',
            'status' => PendingOutboundModel::STATUS_PENDING,
            'priority' => $data['priority'] ?? PendingOutboundModel::PRIORITY_MEDIUM,
            'expected_date' => isset($data['expected_date']) ? strtotime($data['expected_date']) : null,
            'notes' => $data['notes'] ?? '',
            'created_by' => get_login_admin_id(),
            'create_time' => time(),
            'update_time' => time(),
        ];
        
        // 插入数据
        $result = Db::name('warehouse_pending_outbound')->insert($insertData);
        if ($result) {
            return json(['code' => 0, 'msg' => '添加成功']);
        } else {
            return json(['code' => 1, 'msg' => '添加失败']);
        }
    }
    
    /**
     * 编辑待出库项
     * 
     * @return \think\Response
     */
    public function edit()
    {
        $data = get_params();
        $id = isset($data['id']) ? intval($data['id']) : 0;
        
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 检查记录是否存在
        $item = PendingOutboundModel::where('id', $id)->find();
        if (!$item) {
            return json(['code' => 1, 'msg' => '待出库项不存在']);
        }
        
        // 数据验证
        $validate = [
            'id' => 'require',
            'quantity' => 'require|float|gt:0',
            'priority' => 'require|in:1,2,3',
        ];
        
        $message = [
            'id.require' => '记录ID不能为空',
            'quantity.require' => '出库数量不能为空',
            'quantity.float' => '出库数量必须是数字',
            'quantity.gt' => '出库数量必须大于0',
            'priority.require' => '优先级不能为空',
            'priority.in' => '优先级无效',
        ];
        
        $this->validate($data, $validate, $message);
        
        // 不允许修改已出库或已取消的记录
        if ($item['status'] == PendingOutboundModel::STATUS_COMPLETED || $item['status'] == PendingOutboundModel::STATUS_CANCELLED) {
            return json(['code' => 1, 'msg' => '已完成或已取消的记录不能修改']);
        }
        
        // 处理库位信息（作为建议性质）
        if (isset($data['warehouse_id'])) {
            if ($data['warehouse_id'] > 0) {
                $data['warehouse_name'] = Db::name('warehouse')->where('id', $data['warehouse_id'])->value('name');
            } else {
                $data['warehouse_name'] = '';
            }
        }
        if (isset($data['location_id'])) {
            if ($data['location_id'] > 0) {
                $data['location_name'] = Db::name('warehouse_location')->where('id', $data['location_id'])->value('name');
            } else {
                $data['location_name'] = '';
            }
        }
        
        // 处理日期
        if (isset($data['expected_date'])) {
            $data['expected_date'] = strtotime($data['expected_date']);
        }
        
        // 更新数据
        $data['update_time'] = time();
        $result = PendingOutboundModel::where('id', $id)->update($data);
        
        if ($result !== false) {
            return json(['code' => 0, 'msg' => '更新成功']);
        } else {
            return json(['code' => 1, 'msg' => '更新失败']);
        }
    }
    
    /**
     * 删除待出库项
     * 
     * @return \think\Response
     */
    public function delete()
    {
        $data = get_params();
        $id = isset($data['id']) ? intval($data['id']) : 0;
        
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 检查记录是否存在
        $item = PendingOutboundModel::where('id', $id)->find();
        if (!$item) {
            return json(['code' => 1, 'msg' => '待出库项不存在']);
        }
        
        // 不允许删除已完成的记录
        if ($item['status'] == PendingOutboundModel::STATUS_COMPLETED) {
            return json(['code' => 1, 'msg' => '已完成的记录不能删除']);
        }
        
        // 软删除
        $result = PendingOutboundModel::destroy($id);
        if ($result) {
            return json(['code' => 0, 'msg' => '删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '删除失败']);
        }
    }
    
    /**
     * 批量删除待出库项
     * 
     * @return \think\Response
     */
    public function batchDelete()
    {
        $data = get_params();
        $ids = isset($data['ids']) ? $data['ids'] : [];
        
        if (empty($ids)) {
            return json(['code' => 1, 'msg' => '请选择要删除的记录']);
        }
        
        // 检查是否有已完成的记录
        $completedCount = PendingOutboundModel::where('id', 'in', $ids)
            ->where('status', PendingOutboundModel::STATUS_COMPLETED)
            ->count();
            
        if ($completedCount > 0) {
            return json(['code' => 1, 'msg' => '选中的记录中包含已完成的记录，不能删除']);
        }
        
        // 软删除
        $result = PendingOutboundModel::destroy($ids);
        if ($result) {
            return json(['code' => 0, 'msg' => '批量删除成功']);
        } else {
            return json(['code' => 1, 'msg' => '批量删除失败']);
        }
    }
    
    /**
     * 创建出库单
     * 
     * @return \think\Response
     */
    public function createOutbound()
    {
        $data = get_params();
        
        $ids = isset($data['ids']) ? $data['ids'] : [];
        $outboundType = isset($data['outbound_type']) ? intval($data['outbound_type']) : 0;
        $outboundDate = isset($data['outbound_date']) ? $data['outbound_date'] : '';
        $customerId = isset($data['customer_id']) ? intval($data['customer_id']) : 0;
        $notes = isset($data['notes']) ? $data['notes'] : '';
        
        if (empty($ids)) {
            return json(['code' => 1, 'msg' => '请选择待出库项']);
        }
        
        if (empty($outboundType)) {
            return json(['code' => 1, 'msg' => '请选择出库类型']);
        }
        
        if (empty($outboundDate)) {
            return json(['code' => 1, 'msg' => '请选择出库日期']);
        }
        
        // 检查选中的待出库项是否都是待处理状态
        $pendingItems = PendingOutboundModel::where('id', 'in', $ids)
            ->where('status', PendingOutboundModel::STATUS_PENDING)
            ->select();
            
        if ($pendingItems->isEmpty()) {
            return json(['code' => 1, 'msg' => '没有可处理的待出库项']);
        }
        
        if (count($pendingItems) != count($ids)) {
            return json(['code' => 1, 'msg' => '选中的待出库项中包含已处理或已取消的记录']);
        }
        
        // 检查库存是否满足出库需求
        foreach ($pendingItems as $item) {
            // 获取当前产品的可用库存
            $availableStock = get_inventory_stock($item->product_id);
            
            // 如果库存不足，返回错误
            if ($availableStock < $item->quantity) {
                return json([
                    'code' => 1, 
                    'msg' => "产品 {$item->product_name} 库存不足，需要: {$item->quantity}，可用: {$availableStock}"
                ]);
            }
        }
        
        // 获取登录用户ID
        $adminId = 0;
        try {
            $adminId = get_login_admin_id();
        } catch (\Exception $e) {
            \think\facade\Log::error('获取登录用户ID失败', ['error' => $e->getMessage()]);
        }
        
        if ($adminId <= 0) {
            return json(['code' => 1, 'msg' => '获取用户信息失败，请重新登录']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 创建出库单
            $outbound = new OutboundModel();
            $outbound->outbound_type = $outboundType;
            
            // 确保outbound_date是时间戳 outbound_date
            if (is_numeric($outboundDate)) {
                $outbound->outbound_date = intval($outboundDate);
            } else {
                $timestamp = strtotime($outboundDate);
                if ($timestamp !== false) {
                    $outbound->outbound_date = $timestamp;
                } else {
                    \think\facade\Log::error('API PendingOutbound::createOutbound - 出库日期格式错误', [
                        'outbound_date' => $outboundDate
                    ]);
                    throw new \Exception('出库日期格式错误');
                }
            }
            $outbound->outbound_date= $outboundDate;
            $outbound->customer_id = $customerId > 0 ? $customerId : null;
            $outbound->notes = $notes;
            $outbound->status = OutboundModel::STATUS_DRAFT;
            $outbound->created_by = $adminId;
            
            // 生成出库单号
            $outbound->outbound_no = OutboundModel::generateOutboundNo($outboundType);
            
            // 保存出库单
            $outbound->save();
            
            // 创建出库单明细 - 智能分配库存
            foreach ($pendingItems as $item) {
                // 获取产品在各个仓库的库存分布
                $warehouseInventories = $this->getProductWarehouseInventories($item->product_id);
                
                // 如果有推荐仓库，优先从推荐仓库出库
                $remainingQuantity = $item->quantity;
                $allocatedDetails = [];
                
                if ($item->warehouse_id > 0) {
                    // 优先从推荐仓库分配
                    foreach ($warehouseInventories as $inventory) {
                        if ($inventory['warehouse_id'] == $item->warehouse_id && $inventory['quantity'] > 0) {
                            $allocQuantity = min($remainingQuantity, $inventory['quantity']);
                            if ($allocQuantity > 0) {
                                $allocatedDetails[] = [
                                    'warehouse_id' => $inventory['warehouse_id'],
                                    'warehouse_name' => $inventory['warehouse_name'],
                                    'quantity' => $allocQuantity
                                ];
                                $remainingQuantity -= $allocQuantity;
                            }
                            break;
                        }
                    }
                }
                
                // 如果推荐仓库库存不足，从其他仓库补充
                if ($remainingQuantity > 0) {
                    foreach ($warehouseInventories as $inventory) {
                        // 跳过已经分配过的推荐仓库
                        if ($item->warehouse_id > 0 && $inventory['warehouse_id'] == $item->warehouse_id) {
                            continue;
                        }
                        
                        if ($inventory['quantity'] > 0) {
                            $allocQuantity = min($remainingQuantity, $inventory['quantity']);
                            if ($allocQuantity > 0) {
                                $allocatedDetails[] = [
                                    'warehouse_id' => $inventory['warehouse_id'],
                                    'warehouse_name' => $inventory['warehouse_name'],
                                    'quantity' => $allocQuantity
                                ];
                                $remainingQuantity -= $allocQuantity;
                                
                                if ($remainingQuantity <= 0) {
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // 如果仍有未分配的数量，可能是库存不足或其他问题
                if ($remainingQuantity > 0) {
                    throw new \Exception("产品 [{$item->product_name}] 库存分配失败，剩余未分配数量: {$remainingQuantity}");
                }
                
                // 创建出库单明细记录
                foreach ($allocatedDetails as $detail) {
                    $outboundDetail = new OutboundDetailModel();
                    $outboundDetail->outbound_id = $outbound->id;
                    $outboundDetail->product_id = $item->product_id;
                    $outboundDetail->product_name = $item->product_name;
                    $outboundDetail->product_code = $item->product_code;
                    $outboundDetail->specs = $item->specs;
                    $outboundDetail->warehouse_id = $detail['warehouse_id'];
                    $outboundDetail->warehouse_name = $detail['warehouse_name'];
                    $outboundDetail->location_id = 0; // 不再使用库位
                    $outboundDetail->location_name = '';
                    $outboundDetail->quantity = $detail['quantity'];
                    $outboundDetail->unit = $item->unit;
                    $outboundDetail->batch_no = $item->batch_no;
                    
                    // 添加源数据关联，便于追溯
                    $outboundDetail->source_type = $item->source_type;
                    $outboundDetail->source_id = $item->source_id;
                    $outboundDetail->source_detail_id = $item->source_detail_id;
                    $outboundDetail->pending_id = $item->id; // 直接关联到待出库项
                    
                    $outboundDetail->save();
                }
                
                // 更新待出库项状态为处理中
                PendingOutboundModel::updateStatus($item->id, PendingOutboundModel::STATUS_PROCESSING, $outbound->id);
            }
            
            // 添加出库单操作日志
            $outbound->addLog('创建出库单', '从待出库清单创建出库单');
            
            // 提交事务
            Db::commit();
            
            // 检查源单据的待出库项是否全部完成
            $sourceInfo = [];
            if (count($pendingItems) > 0) {
                $firstItem = $pendingItems[0];
                $sourceInfo = PendingOutboundModel::checkSourceCompletion(
                    $firstItem->source_type,
                    $firstItem->source_id,
                    $firstItem->source_detail_id
                );
            }
            
            return json([
                'code' => 0, 
                'msg' => '出库单创建成功', 
                'data' => [
                    'outbound_id' => $outbound->id,
                    'outbound_no' => $outbound->outbound_no,
                    'source_completion' => $sourceInfo
                ]
            ]);
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json(['code' => 1, 'msg' => '出库单创建失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取产品在各个仓库的库存分布
     * 
     * @param int $productId 产品ID
     * @return array 仓库库存列表
     */
    private function getProductWarehouseInventories($productId)
    {
        // 查询产品在各个仓库的库存
        $inventories = Db::name('inventory')
            ->alias('i')
            ->join('warehouse w', 'i.warehouse_id = w.id')
            ->where('i.product_id', $productId)
            ->where('i.status', 1)
            ->field('i.warehouse_id, w.name as warehouse_name, SUM(i.quantity) as quantity')
            ->group('i.warehouse_id')
            ->order('i.quantity DESC') // 优先从库存多的仓库出库
            ->select()
            ->toArray();
            
        return $inventories;
    }
    
    /**
     * 获取库位列表（AJAX）
     * 
     * @return \think\Response
     */
    public function getLocations()
    {
        $data = get_params();
        $warehouseId = isset($data['warehouse_id']) ? intval($data['warehouse_id']) : 0;
        
        if ($warehouseId <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $locations = Db::name('warehouse_location')
            ->field('id, name')
            ->where('warehouse_id', $warehouseId)
            ->where('status', 1)
            ->select();
            
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $locations]);
    }
} 