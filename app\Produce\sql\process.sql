-- 工序管理相关表结构

-- 工序表
CREATE TABLE IF NOT EXISTS `oa_produce_process` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工序ID',
  `code` varchar(50) NOT NULL DEFAULT '' COMMENT '工序编号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工序名称',
  `group_id` int(11) NOT NULL DEFAULT '0' COMMENT '工作组ID',
  `report_user` varchar(255) DEFAULT '' COMMENT '报工用户',
  `standard_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '标准单价',
  `efficiency` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '标准效率(件/小时)',
  `pricing_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计价方式:1按件计价,2按时计价',
  `quantity_ratio` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '数量单比',
  `description` text COMMENT '工序描述',
  `serial_number` varchar(100) DEFAULT '' COMMENT '工序序号',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `group_id` (`group_id`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工序表';

-- 工作组表
CREATE TABLE IF NOT EXISTS `oa_process_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工作组ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工作组名称',
  `description` text COMMENT '工作组描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作组表';

-- 插入默认工作组数据
INSERT INTO `oa_process_group` (`name`, `description`, `sort`, `status`, `admin_id`, `create_time`, `update_time`) VALUES
('机加工组', '负责机械加工相关工序', 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('装配组', '负责产品装配相关工序', 2, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('检验组', '负责产品检验相关工序', 3, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('包装组', '负责产品包装相关工序', 4, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入示例工序数据
INSERT INTO `oa_produce_process` (`code`, `name`, `group_id`, `standard_price`, `efficiency`, `pricing_method`, `quantity_ratio`, `description`, `admin_id`, `create_time`, `update_time`) VALUES
('GX001', '车削加工', 1, 5.00, 10.00, 1, 1.00, '使用车床进行零件车削加工', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX002', '铣削加工', 1, 8.00, 8.00, 1, 1.00, '使用铣床进行零件铣削加工', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX003', '钻孔加工', 1, 3.00, 15.00, 1, 1.00, '使用钻床进行零件钻孔加工', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX004', '总装配', 2, 12.00, 5.00, 1, 1.00, '产品总装配工序', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX005', '分装配', 2, 8.00, 8.00, 1, 1.00, '产品分装配工序', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX006', '首件检验', 3, 15.00, 3.00, 1, 1.00, '产品首件检验工序', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX007', '终检', 3, 10.00, 6.00, 1, 1.00, '产品终检工序', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GX008', '包装', 4, 2.00, 20.00, 1, 1.00, '产品包装工序', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());