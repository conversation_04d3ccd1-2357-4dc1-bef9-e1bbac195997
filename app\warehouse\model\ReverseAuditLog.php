<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 反审操作日志模型
 */
class ReverseAuditLog extends Model
{
    // 设置表名
    protected $name = 'reverse_audit_log';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false; // 日志记录不需要更新时间
    
    // 业务类型常量
    const REF_TYPE_CUSTOMER_ORDER = 'customer_order';      // 客户订单
    const REF_TYPE_PRODUCTION_ORDER = 'production_order';  // 生产订单
    const REF_TYPE_PURCHASE_ORDER = 'purchase_order';      // 采购订单
    const REF_TYPE_TRANSFER = 'transfer';                  // 调拨单
    const REF_TYPE_QUALITY_CHECK = 'quality_check';       // 质检
    const REF_TYPE_SYSTEM_CLEANUP = 'system_cleanup';     // 系统清理
    
    // 设置字段信息
    protected $schema = [
        'id'               => 'int',
        'ref_type'         => 'string',
        'ref_id'           => 'int',
        'ref_no'           => 'string',
        'released_locks'   => 'string',
        'released_quantity'=> 'float',
        'reason'           => 'string',
        'operator_id'      => 'int',
        'operator_name'    => 'string',
        'create_time'      => 'int',
    ];
    
    // JSON字段
    protected $json = ['released_locks'];
    
    /**
     * 关联操作人信息
     */
    public function operator()
    {
        return $this->belongsTo('app\admin\model\Admin', 'operator_id');
    }
    
    /**
     * 获取业务类型文本
     */
    public function getRefTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::REF_TYPE_CUSTOMER_ORDER => '客户订单',
            self::REF_TYPE_PRODUCTION_ORDER => '生产订单',
            self::REF_TYPE_PURCHASE_ORDER => '采购订单',
            self::REF_TYPE_TRANSFER => '调拨单',
            self::REF_TYPE_QUALITY_CHECK => '质检',
            self::REF_TYPE_SYSTEM_CLEANUP => '系统清理'
        ];
        
        return $typeMap[$data['ref_type']] ?? '未知类型';
    }
    
    /**
     * 获取释放的锁定记录数量
     */
    public function getReleasedCountAttr($value, $data)
    {
        $releasedLocks = $data['released_locks'];
        if (is_string($releasedLocks)) {
            $releasedLocks = json_decode($releasedLocks, true);
        }
        
        return is_array($releasedLocks) ? count($releasedLocks) : 0;
    }
    
    /**
     * 创建反审日志
     */
    public static function createLog($data)
    {
        $log = new self();
        $log->save([
            'ref_type' => $data['ref_type'],
            'ref_id' => $data['ref_id'],
            'ref_no' => $data['ref_no'] ?? '',
            'released_locks' => $data['released_locks'] ?? [],
            'released_quantity' => $data['released_quantity'] ?? 0,
            'reason' => $data['reason'] ?? '',
            'operator_id' => $data['operator_id'],
            'operator_name' => $data['operator_name'] ?? ''
        ]);
        
        return $log;
    }
    
    /**
     * 获取业务相关的反审日志
     */
    public static function getByBusiness($refType, $refId)
    {
        return self::where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->order('create_time DESC')
            ->select();
    }
    
    /**
     * 获取操作人的反审日志
     */
    public static function getByOperator($operatorId, $limit = 100)
    {
        return self::where('operator_id', $operatorId)
            ->order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 获取反审统计
     */
    public static function getReverseAuditStats($startTime = null, $endTime = null)
    {
        $where = [];
        
        if ($startTime) {
            $where[] = ['create_time', '>=', $startTime];
        }
        
        if ($endTime) {
            $where[] = ['create_time', '<=', $endTime];
        }
        
        return self::where($where)
            ->field([
                'ref_type',
                'COUNT(*) as count',
                'SUM(released_quantity) as total_released_quantity'
            ])
            ->group('ref_type')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取最近的反审记录
     */
    public static function getRecent($limit = 50)
    {
        return self::order('create_time DESC')
            ->limit($limit)
            ->select();
    }
    
    /**
     * 搜索器：按业务类型搜索
     */
    public function searchRefTypeAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('ref_type', $value);
        }
    }
    
    /**
     * 搜索器：按操作人搜索
     */
    public function searchOperatorIdAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('operator_id', $value);
        }
    }
    
    /**
     * 搜索器：按业务单号搜索
     */
    public function searchRefNoAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('ref_no', 'like', '%' . $value . '%');
        }
    }
    
    /**
     * 搜索器：按时间范围搜索
     */
    public function searchCreateTimeAttr($query, $value)
    {
        if (is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }
    
    /**
     * 搜索器：按原因搜索
     */
    public function searchReasonAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('reason', 'like', '%' . $value . '%');
        }
    }
}
