{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部流水</li>
                <li>入库记录</li>
                <li>出库记录</li>
                <li>调拨记录</li>
                <li>盘点记录</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="warehouse_id">
                <option value="">选择仓库</option>
                {volist name="warehouses" id="warehouse"}
                <option value="{$warehouse.id}">{$warehouse.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="transaction_type">
                <option value="">流水类型</option>
                <option value="in">入库</option>
                <option value="out">出库</option>
                <option value="transfer_out">调拨出库</option>
                <option value="transfer_in">调拨入库</option>
                <option value="adjust">盘点调整</option>
                <option value="lock">锁定</option>
                <option value="unlock">解锁</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="ref_type">
                <option value="">关联类型</option>
                <option value="purchase_receipt">采购入库单</option>
                <option value="customer_order">客户订单</option>
                <option value="purchase_receipt_reverse">采购入库反审核</option>
                <option value="receipt">入库单</option>
                <option value="outbound">出库单</option>
                <option value="transfer">调拨单</option>
                <option value="check">盘点单</option>
                <option value="inventory_check">库存盘点</option>
                <option value="inventory_transfer">库存调拨</option>
                <option value="inventory_freeze">库存冻结</option>
                <option value="inventory_unfreeze">库存解冻</option>
                <option value="inventory_scrap">库存报废</option>
                <option value="inventory_move">库存移动</option>
                <option value="order">销售订单</option>
                <option value="purchase">采购订单</option>
                <option value="production">生产订单</option>
                <option value="manual">手动操作</option>
                <option value="system">系统操作</option>
                <option value="lock">库存锁定</option>
                <option value="other">其他</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="create_time" placeholder="选择时间范围" readonly name="time_range">
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，产品名称/编码/流水号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_inventory_transaction" lay-filter="table_inventory_transaction"></table>
</div>

<!-- 工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="statistics"><i class="layui-icon layui-icon-chart"></i>流水统计</button>
        <button class="layui-btn layui-btn-warm layui-btn-sm" lay-event="export"><i class="layui-icon layui-icon-export"></i>导出Excel</button>
    </div>
</script>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus;
        
        //tab切换
        element.on('tab(tab)', function(data){
            var typeMap = {
                0: "",           // 全部流水
                1: "in",         // 入库记录
                2: "out",        // 出库记录
                3: "transfer",   // 调拨记录（包含transfer_in和transfer_out）
                4: "adjust"      // 盘点记录
            };
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();

            // 根据tab类型设置搜索条件
            var searchWhere = {};
            if (data.index == 3) {
                // 调拨记录需要特殊处理，包含调入和调出
                searchWhere = {transaction_type: 'transfer_in,transfer_out'};
            } else if (typeMap[data.index]) {
                searchWhere = {transaction_type: typeMap[data.index]};
            }

            layui.pageTable.reload({where: searchWhere, page: {curr: 1}});
            return false;
        });
        
        //日期范围
        var create_time = new laydatePlus({'target':'create_time'});
        
        layui.pageTable = table.render({
            elem: "#table_inventory_transaction"
            ,title: "库存流水列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/warehouse/InventoryTransaction/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,request: {
                pageName: 'page',
                limitName: 'limit'
            }
            ,response: {
                statusName: 'code',
                statusCode: 0,
                msgName: 'msg',
                countName: 'count',
                dataName: 'data'
            }
            ,cols: [[ //表头
                {
                    field: 'id',
                    title: 'ID号',
                    align: 'center',
                    width: 80
                },{ 
                    field: 'transaction_no', 
                    title: '流水号', 
                    align: 'center', 
                    width: 180,
                    templet: function (d) {
                        return '<span style="color:#1E9FFF;cursor:pointer;" lay-event="view">' + d.transaction_no + '</span>';
                    }
                },{ 
                    field: 'product_name', 
                    title: '产品信息', 
                    align: 'left', 
                    width: 200,
                    templet: function (d) {
                        var html = '<div style="line-height:18px;">';
                        html += '<div style="font-weight:bold;">' + (d.product_name || '') + '</div>';
                        html += '<div style="color:#999;font-size:12px;">' + (d.material_code || '') + '</div>';
                        html += '</div>';
                        return html;
                    }
                },{ 
                    field: 'warehouse_name', 
                    title: '仓库', 
                    align: 'center', 
                    width: 120
                },{ 
                    field: 'transaction_type', 
                    title: '流水类型', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        var typeMap = {
                            'in': '入库',
                            'out': '出库',
                            'transfer_in': '调拨入库',
                            'transfer_out': '调拨出库',
                            'adjust': '盘点调整',
                            'lock': '锁定',
                            'unlock': '解锁'
                        };
                        var typeClass = {
                            'in': 'layui-bg-green',
                            'out': 'layui-bg-red',
                            'transfer_in': 'layui-bg-blue',
                            'transfer_out': 'layui-bg-orange',
                            'adjust': 'layui-bg-cyan',
                            'lock': 'layui-bg-orange',
                            'unlock': 'layui-bg-green'
                        };
                        var text = typeMap[d.transaction_type] || d.transaction_type;
                        var className = typeClass[d.transaction_type] || 'layui-bg-gray';
                        return '<span class="layui-badge ' + className + '">' + text + '</span>';
                    }
                },{ 
                    field: 'quantity', 
                    title: '数量变动', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        var sign = '';
                        // 根据交易类型确定符号
                        if (d.transaction_type === 'in' ||
                            d.transaction_type === 'transfer_in' ||
                            d.transaction_type === 'unlock') {
                            sign = '+';
                        } else if (d.transaction_type === 'out' ||
                                   d.transaction_type === 'transfer_out' ||
                                   d.transaction_type === 'lock') {
                            sign = '-';
                        } else if (d.transaction_type === 'adjust') {
                            // 盘点调整根据数量正负判断
                            sign = d.quantity >= 0 ? '+' : '-';
                        }
                        var color = sign === '+' ? 'color:#5FB878' : (sign === '-' ? 'color:#FF5722' : 'color:#333');
                        return '<span style="' + color + ';font-weight:bold;">' + sign + Math.abs(d.quantity) + ' ' + (d.unit || '') + '</span>';
                    }
                },{ 
                    field: 'before_quantity', 
                    title: '变动前', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        return d.before_quantity + ' ' + (d.unit || '');
                    }
                },{ 
                    field: 'after_quantity', 
                    title: '变动后', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        return d.after_quantity + ' ' + (d.unit || '');
                    }
                },{ 
                    field: 'ref_type', 
                    title: '关联类型', 
                    align: 'center', 
                    width: 100,
                    templet: function (d) {
                        var refTypeMap = {
                            'purchase_receipt': '采购入库单',
                            'customer_order': '客户订单',
                            'purchase_receipt_reverse': '采购入库反审核',
                            'receipt': '入库单',
                            'outbound': '出库单',
                            'transfer': '调拨单',
                            'check': '盘点单',
                            'inventory_check': '库存盘点',
                            'inventory_transfer': '库存调拨',
                            'inventory_freeze': '库存冻结',
                            'inventory_unfreeze': '库存解冻',
                            'inventory_scrap': '库存报废',
                            'inventory_move': '库存移动',
                            'order': '销售订单',
                            'purchase': '采购订单',
                            'production': '生产订单',
                            'manual': '手动操作',
                            'system': '系统操作'
                        };
                        return refTypeMap[d.ref_type] || d.ref_type;
                    }
                },{ 
                    field: 'ref_no', 
                    title: '关联单号', 
                    align: 'center', 
                    width: 150
                },{ 
                    field: 'creator_name', 
                    title: '操作人', 
                    align: 'center', 
                    width: 100
                },{ 
                    field: 'create_time', 
                    title: '创建时间', 
                    align: 'center', 
                    width: 160,
                    templet: function (d) {
                        return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm');
                    }
                },{ 
                    field: 'notes', 
                    title: '备注', 
                    align: 'left', 
                    width: 200,
                    templet: function (d) {
                        return d.notes ? '<span title="' + d.notes + '">' + (d.notes.length > 20 ? d.notes.substr(0, 20) + '...' : d.notes) + '</span>' : '';
                    }
                },{
                    title: '操作',
                    align: 'center',
                    width: 120,
                    templet: function (d) {
                        return '<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="view">查看详情</span>';
                    }
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_inventory_transaction)', function(obj){
            if (obj.event === 'statistics'){
                tool.side("/warehouse/InventoryTransaction/statistics");
                return;
            }
            if (obj.event === 'export'){
                var searchData = $("#barsearchform").serialize();
                layer.confirm('确定要导出当前筛选条件下的库存流水数据吗？', { icon: 3, title: '提示' }, function (index) {
                    window.open("/warehouse/InventoryTransaction/export?" + searchData);
                    layer.close(index);
                });
                return;
            }
        });	
            
        table.on('tool(table_inventory_transaction)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/warehouse/InventoryTransaction/view?id="+data.id);
                return;
            }
        });
    }
</script>
{/block}
