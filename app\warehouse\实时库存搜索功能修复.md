# 实时库存搜索功能修复

## 🔍 问题描述

实时库存页面（`http://tc.xinqiyu.cn:8830/warehouse/InventoryRealtimeController/index`）的搜索功能报错：

```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'keywords' in 'where clause'
```

## 🎯 问题原因分析

### 1. 模型搜索器缺失
控制器中使用了 `withSearch(['product_id', 'warehouse_id', 'keywords'], $filter)`，但 `InventoryRealtime` 模型中没有定义 `searchKeywords` 方法，导致框架直接将 'keywords' 当作数据库字段使用。

### 2. 显示所有产品模式下缺少搜索逻辑
在 `show_all=true` 模式下，查询逻辑没有处理关键词搜索和其他筛选条件。

## 🛠️ 解决方案

### 1. 添加模型搜索器

在 `app\warehouse\model\InventoryRealtime.php` 中添加 `searchKeywords` 方法：

```php
/**
 * 关键词搜索器
 * @param \think\db\Query $query
 * @param string $value
 * @return void
 */
public function searchKeywords($query, $value)
{
    if (!empty($value)) {
        $keywords = trim($value);
        
        // 查询匹配的产品ID
        $productIds = Db::name('product')
            ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
            ->column('id');
        
        if (!empty($productIds)) {
            // 如果找到匹配的产品，按产品ID搜索
            $query->whereIn('product_id', $productIds);
        } else {
            // 如果没有找到匹配的产品，返回空结果
            $query->where('product_id', 0);
        }
    }
}
```

### 2. 修复显示所有产品模式的搜索

在 `app\warehouse\controller\InventoryRealtimeController.php` 中修改查询逻辑：

```php
if ($showAll) {
    // 显示所有产品，包括零库存
    $query = \think\facade\Db::name('product')
        ->alias('p')
        ->leftJoin('inventory_realtime ir', 'p.id = ir.product_id')
        ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
        ->field('...')
        ->where('p.delete_time', 0);
    
    // 添加关键词搜索
    if (!empty($filter['keywords'])) {
        $keywords = trim($filter['keywords']);
        $query->where('p.title|p.material_code|p.specs', 'like', '%' . $keywords . '%');
    }
    
    // 添加产品ID筛选
    if (!empty($filter['product_id'])) {
        $query->where('p.id', $filter['product_id']);
    }
    
    // 添加仓库ID筛选
    if (!empty($filter['warehouse_id'])) {
        $query->where('ir.warehouse_id', $filter['warehouse_id']);
    }
    
    $list = $query->order('p.id desc')->paginate([...]);
}
```

## 📋 搜索功能说明

### 支持的搜索字段

1. **产品名称**：通过产品表的 `title` 字段匹配
2. **物料编码**：通过产品表的 `material_code` 字段匹配
3. **产品规格**：通过产品表的 `specs` 字段匹配

### 两种显示模式

#### 1. 默认模式（只显示有库存的产品）
- 使用模型的 `withSearch` 方法
- 通过 `searchKeywords` 搜索器处理关键词
- 支持产品ID、仓库ID、关键词搜索

#### 2. 显示所有产品模式（包括零库存）
- 使用原生SQL查询
- 直接在查询中处理搜索条件
- 支持产品ID、仓库ID、关键词搜索

## 🔧 技术实现细节

### 搜索器模式（默认模式）

```php
// 控制器中使用
$list = $this->model
    ->with(['product', 'warehouse'])
    ->withSearch(['product_id', 'warehouse_id', 'keywords'], $filter)
    ->order('id desc')
    ->paginate([...]);

// 模型中的搜索器
public function searchKeywords($query, $value)
{
    // 先查询匹配的产品ID
    $productIds = Db::name('product')
        ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
        ->column('id');
    
    // 根据产品ID筛选库存记录
    if (!empty($productIds)) {
        $query->whereIn('product_id', $productIds);
    } else {
        $query->where('product_id', 0); // 返回空结果
    }
}
```

### 原生查询模式（显示所有产品）

```php
// 直接在查询中处理搜索条件
if (!empty($filter['keywords'])) {
    $keywords = trim($filter['keywords']);
    $query->where('p.title|p.material_code|p.specs', 'like', '%' . $keywords . '%');
}
```

## 🎨 用户体验

### 搜索示例

#### 1. 按产品名称搜索
- 输入：`螺丝`
- 匹配：所有产品名称包含"螺丝"的库存记录

#### 2. 按物料编码搜索
- 输入：`M001`
- 匹配：所有物料编码包含"M001"的库存记录

#### 3. 按产品规格搜索
- 输入：`M8`
- 匹配：所有规格包含"M8"的库存记录

### 筛选组合

用户可以同时使用：
- **关键词搜索**：产品名称/编码/规格
- **产品筛选**：选择特定产品
- **仓库筛选**：选择特定仓库
- **显示模式**：有库存 vs 所有产品

## 📊 性能优化

### 1. 分步查询
```php
// 先查询匹配的产品ID，避免复杂的JOIN
$productIds = Db::name('product')
    ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
    ->column('id');

// 再根据产品ID查询库存
$query->whereIn('product_id', $productIds);
```

### 2. 索引建议
确保以下字段有适当的索引：
- `product.title`
- `product.material_code`
- `product.specs`
- `inventory_realtime.product_id`
- `inventory_realtime.warehouse_id`

## 🧪 测试验证

### 测试用例

#### 1. 关键词搜索测试
- **输入**：产品名称的部分关键词
- **预期**：返回相关产品的库存记录

#### 2. 组合筛选测试
- **输入**：关键词 + 仓库筛选
- **预期**：返回指定仓库中匹配关键词的库存记录

#### 3. 显示模式切换测试
- **操作**：切换"显示所有产品"开关
- **预期**：两种模式下搜索功能都正常工作

#### 4. 无匹配结果测试
- **输入**：不存在的关键词
- **预期**：返回空列表，不报错

### 验证步骤

1. 访问 `http://tc.xinqiyu.cn:8830/warehouse/InventoryRealtimeController/index`
2. 在关键词搜索框中输入测试关键词
3. 点击搜索按钮
4. 验证搜索结果的准确性
5. 测试"显示所有产品"模式下的搜索功能

## 🎯 修复效果

### 修复前
- ❌ 关键词搜索功能报SQL错误
- ❌ 显示所有产品模式下无法搜索
- ❌ 用户无法通过产品信息快速定位库存

### 修复后
- ✅ 关键词搜索功能正常工作
- ✅ 两种显示模式都支持完整的搜索功能
- ✅ 支持产品名称、编码、规格的模糊搜索
- ✅ 搜索结果准确，性能良好

## 🚀 后续优化建议

1. **搜索高亮**：在搜索结果中高亮显示匹配的关键词
2. **搜索建议**：提供自动完成和搜索建议功能
3. **高级搜索**：支持多条件组合和精确匹配
4. **搜索历史**：记录用户的搜索历史

修复完成后，实时库存页面的搜索功能将完全可用，用户可以快速定位和查看所需的库存信息。
