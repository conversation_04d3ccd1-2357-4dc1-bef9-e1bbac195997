{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>{if condition="!empty($info.id)"}编辑工艺路线{else /}新增工艺路线{/if}</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" id="processTemplateForm">
                <input type="hidden" name="id" value="{$info.id|default=''}">
                
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工艺名称 <span class="text-danger">*</span></label>
                            <div class="layui-input-block">
                                <input type="text" name="name" value="{$info.name|default=''}" placeholder="请输入工艺名称" class="layui-input" lay-verify="required" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <input type="text" name="remark" value="{$info.remark|default=''}" placeholder="请输入备注" class="layui-input" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">工艺步骤</label>
                    <div class="layui-input-block">
                        <div id="processSteps">
                            <!-- 工艺步骤将在这里动态生成 -->
                        </div>
                        <button type="button" class="layui-btn layui-btn-normal" id="addStep">
                            <i class="layui-icon layui-icon-add-1"></i> 添加步骤
                        </button>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="save">保存</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['form', 'tool'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;
        var stepIndex = 0;
        var steps = [];
        var processList = []; // 工序列表
        
        // 初始化步骤数据
        {if condition="!empty($info.step_list)"}
        try {
            var stepData = {$info.step_list|json_encode|raw};
            if (Array.isArray(stepData)) {
                steps = stepData;
            }
        } catch(e) {
            console.log('解析步骤数据失败:', e);
        }
        {/if}
        
        // 加载工序列表
        function loadProcessList() {
            console.log('开始加载工序列表...');
            tool.post('/Produce/processTemplate/getProcessList', {}, function(res) {
                console.log('工序列表响应:', res);
                if (res.code === 0) {
                    processList = res.data || [];
                    console.log('工序列表加载成功，数量:', processList.length);
                    renderSteps(); // 重新渲染步骤
                } else {
                    console.log('加载工序列表失败:', res.msg);
                    renderSteps(); // 即使失败也要渲染步骤
                }
            });
        }
        
        // 生成工序选项HTML
        function generateProcessOptions(selectedName) {
            var html = '<option value="">请选择</option>';
            processList.forEach(function(process) {
                var selected = process.name === selectedName ? 'selected' : '';
                html += '<option value="' + process.name + '" ' + selected + '>' + process.name + '</option>';
            });
            return html;
        }
        
        // 渲染步骤列表
        function renderSteps() {
            var html = '';
            steps.forEach(function(step, index) {
                html += '<div class="layui-card step-item" data-index="' + index + '" style="margin-bottom: 15px;">';
                html += '<div class="layui-card-header">';
                html += '<span>步骤 ' + (index + 1) + '</span>';
                html += '<div class="layui-btn-group" style="float: right;">';
                html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeStep(' + index + ')">';
                html += '<i class="layui-icon layui-icon-delete"></i> 删除';
                html += '</button>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-card-body">';
                html += '<div class="layui-row">';
                html += '<div class="layui-col-md4">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">工序名称</label>';
                html += '<div class="layui-input-block">';
                html += '<select name="steps[' + index + '][name]" lay-search="">';
                html += generateProcessOptions(step.name || '');
                html += '</select>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md2">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">工序类型</label>';
                html += '<div class="layui-input-block">';
                html += '<select name="steps[' + index + '][type]">';
                html += '<option value="数据记录"' + (step.type === '数据记录' ? ' selected' : '') + '>数据记录</option>';
                html += '<option value="状态记录"' + (step.type === '状态记录' ? ' selected' : '') + '>状态记录</option>';
                html += '</select>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md1">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">完成时间</label>';
                html += '<div class="layui-input-block">';
                html += '<input type="number" name="steps[' + index + '][completion_time]" value="' + (step.completion_time || 0) + '" placeholder="0" class="layui-input" autocomplete="off">';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md1">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">时间单位</label>';
                html += '<div class="layui-input-block">';
                html += '<select name="steps[' + index + '][time_unit]">';
                html += '<option value="天"' + (step.time_unit === '天' ? ' selected' : '') + '>天</option>';
                html += '<option value="小时"' + (step.time_unit === '小时' ? ' selected' : '') + '>小时</option>';
                html += '<option value="分钟"' + (step.time_unit === '分钟' ? ' selected' : '') + '>分钟</option>';
                html += '</select>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md2">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">加工方式</label>';
                html += '<div class="layui-input-block">';
                html += '<select name="steps[' + index + '][processing_type]">';
                html += '<option value="自制"' + (step.processing_type === '自制' ? ' selected' : '') + '>自制</option>';
                html += '<option value="外协"' + (step.processing_type === '外协' ? ' selected' : '') + '>外协</option>';
                html += '</select>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md2">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">检验方式</label>';
                html += '<div class="layui-input-block">';
                html += '<select name="steps[' + index + '][inspection_method]">';
                html += '<option value="免检"' + (step.inspection_method === '免检' ? ' selected' : '') + '>免检</option>';
                html += '<option value="抽检"' + (step.inspection_method === '抽检' ? ' selected' : '') + '>抽检</option>';
                html += '<option value="全检"' + (step.inspection_method === '全检' ? ' selected' : '') + '>全检</option>';
                html += '</select>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="layui-col-md1">';
                html += '<div class="layui-form-item">';
                html += '<label class="layui-form-label">工序描述</label>';
                html += '<div class="layui-input-block">';
                html += '<input type="text" name="steps[' + index + '][description]" value="' + (step.description || '') + '" placeholder="请输入描述" class="layui-input" autocomplete="off">';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
            });
            
            document.getElementById('processSteps').innerHTML = html;
            form.render();
        }
        
        // 添加步骤
        document.getElementById('addStep').addEventListener('click', function() {
            steps.push({
                name: '',
                type: '数据记录',
                processing_type: '自制',
                inspection_method: '免检',
                completion_time: 0,
                time_unit: '天',
                description: ''
            });
            renderSteps();
        });
        
        // 删除步骤
        window.removeStep = function(index) {
            steps.splice(index, 1);
            renderSteps();
        }
        
        // 如果没有步骤数据，添加一个默认步骤
        if (steps.length === 0) {
            steps.push({
                name: '',
                type: '数据记录',
                processing_type: '自制',
                inspection_method: '免检',
                completion_time: 0,
                time_unit: '天',
                description: ''
            });
        }
        
        // 页面初始化时立即加载工序列表
        console.log('页面初始化，开始加载工序列表');
        loadProcessList();
        
        // 表单提交
        form.on('submit(save)', function(data) {
            var formData = data.field;
            
            // 处理步骤数据
            var processedSteps = [];
            steps.forEach(function(step, index) {
                processedSteps.push({
                    step: index + 1,
                    name: formData['steps[' + index + '][name]'] || '',
                    type: formData['steps[' + index + '][type]'] || '数据记录',
                    processing_type: formData['steps[' + index + '][processing_type]'] || '自制',
                    inspection_method: formData['steps[' + index + '][inspection_method]'] || '免检',
                    completion_time: parseInt(formData['steps[' + index + '][completion_time]']) || 0,
                    time_unit: formData['steps[' + index + '][time_unit]'] || '天',
                    description: formData['steps[' + index + '][description]'] || ''
                });
            });
            
            var submitData = {
                id: formData.id,
                name: formData.name,
                remark: formData.remark,
                steps: processedSteps
            };
            
            tool.post('/Produce/processTemplate/save', submitData, function(res) {
                if (res.code === 0) {
                    layer.msg('保存成功', {icon: 1});
                    setTimeout(function() {
                        parent.layui.pageTable.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }, 1000);
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            });
            
            return false;
        });
    }
</script>
{/block}

{block name="style"}
<style>
.step-item {
    border: 1px solid #e6e6e6;
}
.step-item .layui-card-header {
    background-color: #f8f8f8;
    border-bottom: 1px solid #e6e6e6;
    padding: 10px 15px;
}
.step-item .layui-card-body {
    padding: 15px;
}
</style>
{/block}