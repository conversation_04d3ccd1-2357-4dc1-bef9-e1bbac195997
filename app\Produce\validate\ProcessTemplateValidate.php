<?php
declare (strict_types = 1);

namespace app\Produce\validate;

use think\Validate;

class ProcessTemplateValidate extends Validate
{
    protected $rule = [
        'name' => 'require|max:100',
        'remark' => 'max:500',
        'steps' => 'require|array'
    ];

    protected $message = [
        'name.require' => '工艺名称不能为空',
        'name.max' => '工艺名称不能超过100个字符',
        'remark.max' => '备注不能超过500个字符',
        'steps.require' => '工艺步骤不能为空',
        'steps.array' => '工艺步骤格式错误'
    ];

    protected $scene = [
        'add' => ['name', 'remark', 'steps'],
        'edit' => ['name', 'remark', 'steps']
    ];

    /**
     * 验证工艺步骤数据
     * @param array $steps 工艺步骤数组
     * @return bool|string
     */
    public function checkSteps($steps)
    {
        if (empty($steps) || !is_array($steps)) {
            return '工艺步骤不能为空';
        }

        foreach ($steps as $index => $step) {
            // 验证必填字段
            if (empty($step['name'])) {
                return '第' . ($index + 1) . '步工序名称不能为空';
            }

            if (empty($step['type'])) {
                return '第' . ($index + 1) . '步工序类型不能为空';
            }

            if (empty($step['processing_type'])) {
                return '第' . ($index + 1) . '步加工类型不能为空';
            }

            if (empty($step['inspection_method'])) {
                return '第' . ($index + 1) . '步检验方式不能为空';
            }

            // 验证数据类型
            if (!is_numeric($step['completion_time']) || $step['completion_time'] < 0) {
                return '第' . ($index + 1) . '步完成时间必须为非负数';
            }

            // 验证枚举值
            $validTypes = ['数据记录', '状态记录'];
            if (!in_array($step['type'], $validTypes)) {
                return '第' . ($index + 1) . '步工序类型无效';
            }

            $validProcessingTypes = ['自制', '外协'];
            if (!in_array($step['processing_type'], $validProcessingTypes)) {
                return '第' . ($index + 1) . '步加工类型无效';
            }

            $validInspectionMethods = ['免检', '抽检', '全检'];
            if (!in_array($step['inspection_method'], $validInspectionMethods)) {
                return '第' . ($index + 1) . '步检验方式无效';
            }

            $validTimeUnits = ['天', '小时'];
            if (!empty($step['time_unit']) && !in_array($step['time_unit'], $validTimeUnits)) {
                return '第' . ($index + 1) . '步时间单位无效';
            }
        }

        return true;
    }
}