{extend name="../../base/view/common/base" /}

{block name="body"}
<style>
/* 父子商品关系样式 */
.child-product {
    background-color: #f8f8f8 !important; 
    border-left: 3px solid #1E9FFF !important;
}
.child-product-name {
    padding-left: 30px !important; 
    position: relative;
}
.child-product-name:before {
    content: "└─";
    position: absolute;
    left: 8px;
    color: #1E9FFF;
    font-weight: bold;
    font-size: 16px;
}
/* 父商品标记 */
.parent-product-marker {
    display: inline-block;
    background-color: #1E9FFF;
    color: white;
    font-size: 12px;
    padding: 0 5px;
    border-radius: 3px;
    margin-right: 5px;
    vertical-align: middle;
}
/* 子商品标记 */
.child-product-marker {
    display: inline-block;
    background-color: #FF9800;
    color: white;
    font-size: 12px;
    padding: 0 5px;
    border-radius: 3px;
    margin-right: 5px;
    vertical-align: middle;
}
/* 确保删除按钮始终显示 */
.layui-btn-danger[lay-event="del"] {
    display: inline-block !important;
    visibility: visible !important;
}
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">新增销售订单</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="orderForm">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_no" value="{$order_no|default='系统自动生成'}" readonly class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_date" id="orderDate" placeholder="请选择订单日期" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户</label>
                            <div class="layui-input-block">
                                <select name="customer_id" lay-verify="required" lay-filter="customer" lay-search>
                                    <option value="">请选择客户</option>
                                    {volist name=":get_customer_list()" id="vo"}
                                    <option value="{$vo.id}">{$vo.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单类型</label>
                            <div class="layui-input-block">
                                <select name="order_type" lay-verify="required">
                                    <option value="">请选择订单类型</option>
                                    <option value="1">现金单</option>
                                    <option value="2">账期单</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
               
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">预计交期</label>
                            <div class="layui-input-block">
                                <input type="text" name="delivery_date" id="deliveryDate" placeholder="请选择交货日期" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">税率</label>
                            <div class="layui-input-block">
                                    <select name="tax_rate" lay-verify="required" lay-filter="tax_rate">

                                    <option value="0">不含税</option>
                                    <option value="1">1%</option>
                                    <option value="10">10%</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">管销费</label>
                            <div class="layui-input-block">
                                <input type="text" name="glf" id="glf" placeholder="管销费"  class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">运费</label>
                            <div class="layui-input-block">
                                <input type="text" name="yunfei" id="yunfei" placeholder="运费"  class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注说明</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入备注说明" class="layui-textarea"></textarea>
                    </div>
                </div>
                
                <fieldset class="layui-elem-field">
                    <legend>商品明细</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <button type="button" class="layui-btn layui-btn-normal" id="btnAddProduct">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加商品
                                </button>
                                <button type="button" class="layui-btn layui-btn-danger" id="btnRemoveProduct">
                                    <i class="layui-icon layui-icon-delete"></i> 删除选中
                                </button>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="productTable" lay-filter="productTable">
                            <thead>
                                <tr>
                                    <th lay-data="{type:'checkbox', fixed:'left'}"></th>
                                    <th lay-data="{field:'material_code', width:180}">商品编码</th>
                                    <th lay-data="{field:'product_name', width:180}">商品名称</th>
                                    <th lay-data="{field:'product_specs', width:120}">规格型号</th>
                                    <th lay-data="{field:'unit', width:80}">单位</th>
                                    <th lay-data="{field:'quantity', width:100, edit:'text'}">数量</th>
                                    <th lay-data="{field:'multiplier', width:80, edit:'text'}">每套/件                                    </th>
                                    <th lay-data="{field:'price', width:120, edit:'text'}">单价</th>
                                    <th lay-data="{field:'tax_rate', width:100, edit:'text'}">税率(%)</th>
                                    <th lay-data="{field:'amount', width:120}">金额</th>
                                    <th lay-data="{field:'tax_amount', width:120}">税额</th>
                                    <th lay-data="{field:'total_amount', width:120}">价税合计</th>
                                    <th lay-data="{field:'notes', width:180, edit:'text'}">备注</th>
                                    <th lay-data="{field:'attachment', title:'附件', width:100, templet:'#attachmentTpl'}">附件</th>
                                    <th lay-data="{width:160, toolbar:'#productBar', fixed:'right'}">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加的商品行 -->
                            </tbody>
                        </table>
                        
                        <script type="text/html" id="productBar">
                            {{# if(!d.parent_product_id){ }}
                            <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="addChild">添加子商品</a>
                            {{# } }}
                            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
                        </script>

                        <script type="text/html" id="attachmentTpl">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="upload">
                                {{# if(d.attachments && d.attachments.length > 0){ }}
                                <i class="layui-icon layui-icon-file"></i> 附件({{d.attachments.length}})
                                {{# } else if(d.attachment_id){ }}
                                <i class="layui-icon layui-icon-file"></i> 查看
                                {{# }else{ }}
                                <i class="layui-icon layui-icon-upload"></i> 上传
                                {{# } }}
                            </button>
                        </script>
                    </div>
                </fieldset>
                
                <div class="layui-form-item text-right">
                    <div class="layui-input-block">
                        <span>订单总金额: </span>
                        <span id="totalAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">总税额: </span>
                        <span id="totalTaxAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">价税合计: </span>
                        <span id="totalAmountWithTax" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="hidden" name="__token__" value="{:token()}">
                        <button type="button" class="layui-btn" lay-submit lay-filter="saveOrder">保存订单</button>
                        <!-- <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="submitOrder">保存并提交</button> -->
                        <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 附件上传弹窗 -->
<div id="fileUploadDialog" style="display: none; padding: 20px;">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">附件</label>
            <div class="layui-input-block">
                <div class="layui-upload-drag" id="fileUploadArea">
                    <div id="uploadBox">
                        <input type="hidden" data-type="file" id="fileAttachmentId" value="">
                        <div class="layui-upload-drag-desc">
                            <i class="layui-icon layui-icon-upload"></i>
                            <p>点击上传，或将文件拖拽到此处</p>
                        </div>
                    </div>
                </div>
                <div id="uploadedFileInfo" style="margin-top: 10px;">
                    <div class="layui-card">
                        <div class="layui-card-header">已上传附件</div>
                        <div class="layui-card-body" id="uploadedFileList" style="min-height: 120px; padding: 10px;">
                            <div class="layui-empty-content" id="noFileInfo" style="padding: 20px; text-align: center; color: #999;">
                                <i class="layui-icon layui-icon-file" style="font-size: 30px;"></i>
                                <p>暂无已上传附件</p>
                            </div>
                            <!-- 已上传文件将在这里显示 -->
                            <div id="fileItemTemplate" style="display: none;">
                                <div class="layui-file-item" style="padding: 10px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between;">
                                    <div class="file-preview" style="width: 60px; height: 60px; margin-right: 10px; text-align: center; overflow: hidden; border-radius: 3px; background-color: #f9f9f9; display: flex; align-items: center; justify-content: center;">
                                        <!-- 图片预览/文件图标将在这里显示 -->
                                    </div>
                                    <div class="file-info" style="flex: 1;">
                                        <div class="file-name" style="font-weight: bold;"></div>
                                        <div class="file-meta" style="color: #999; font-size: 12px;">
                                            <span class="file-size"></span>
                                            <span class="file-type"></span>
                                        </div>
                                    </div>
                                    <div class="file-actions">
                                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal btn-preview-file">
                                            <i class="layui-icon layui-icon-read"></i> 预览
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-xs layui-btn-primary btn-download-file" style="display: none;">
                                            <i class="layui-icon layui-icon-download-circle"></i> 下载
                                        </button>
                                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger btn-delete-file">
                                            <i class="layui-icon layui-icon-delete"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div id="multiFileList" class="layui-row" style="margin-top: 10px;">
                                <!-- 这里将动态添加附件项 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 20px; text-align: right;">
            <button type="button" class="layui-btn" id="btnConfirmUpload">确定</button>
            <button type="button" class="layui-btn layui-btn-primary" id="btnCancelUpload">取消</button>
        </div>
    </div>
</div>

<!-- 附件预览弹窗 -->
<div id="filePreviewDialog" style="display: none; padding: 20px;">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">文件名</label>
            <div class="layui-input-block">
                <input type="text" id="previewFileName" class="layui-input" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">文件类型</label>
            <div class="layui-input-block">
                <input type="text" id="previewFileType" class="layui-input" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">上传时间</label>
            <div class="layui-input-block">
                <input type="text" id="previewFileTime" class="layui-input" readonly>
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 20px; text-align: center;">
            <a class="layui-btn layui-btn-normal" id="btnDownloadFile" href="javascript:;">
                <i class="layui-icon layui-icon-download-circle"></i> 下载文件
            </a>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'uploadPlus'];
    
    // 定义全局计算总金额函数，供子页面调用
    window.calculateTotal = function(){
        var table = layui.table;
        var data = table.cache.productTable || [];
        var total = 0;
        var totalTax = 0;
        var totalWithTax = 0;
        
        for(var i = 0; i < data.length; i++){
            if(data[i].amount){
                total += parseFloat(data[i].amount);
            }
            if(data[i].tax_amount){
                totalTax += parseFloat(data[i].tax_amount);
            }
            if(data[i].total_amount){
                totalWithTax += parseFloat(data[i].total_amount);
            }
        }
        
        document.getElementById('totalAmount').innerText = '￥' + total.toFixed(2);
        document.getElementById('totalTaxAmount').innerText = '￥' + totalTax.toFixed(2);
        document.getElementById('totalAmountWithTax').innerText = '￥' + totalWithTax.toFixed(2);
        
        return {
            total: total,
            tax: totalTax,
            withTax: totalWithTax
        };
    };
    
    // 定义全局的styleChildProducts函数，供子页面调用
    window.styleChildProducts = function() {
        // 确保layui对象可用
        if (!layui || !layui.table) {
            console.error("layui对象不可用");
            return;
        }
        
        var table = layui.table;
        var tableData = table.cache.productTable || [];
        
        // 遍历表格数据，为子商品添加样式
        tableData.forEach(function(item, index) {
            if (item.is_child) {
                var tr = $('#productTable').next().find('tr[data-index="' + index + '"]');
                
                // 添加子商品样式
                tr.addClass('child-product');
                
                // 为商品名称添加缩进和标识
                var productNameTd = tr.find('td[data-field="product_name"]').find('.layui-table-cell');
                if (!productNameTd.find('.child-prefix').length) {
                    // 添加子商品标记和缩进
                    var newHtml = '<span class="child-product-marker">子品</span>' + productNameTd.html();
                    productNameTd.html(newHtml);
                    productNameTd.addClass('child-product-name');
                }
                
                // 确保操作列按钮正确显示
                var operationTd = tr.find('td[data-content="操作"]');
                if(operationTd.length > 0) {
                    operationTd.find('.layui-btn').show();
                    operationTd.find('.layui-btn-danger').css('display', 'inline-block !important');
                }
            } else {
                // 为父商品添加标记
                var tr = $('#productTable').next().find('tr[data-index="' + index + '"]');
                var productNameTd = tr.find('td[data-field="product_name"]').find('.layui-table-cell');
                
                if (!productNameTd.find('.parent-product-marker').length) {
                    // 添加父商品标记
                    var newHtml = '<span class="parent-product-marker">主品</span>' + productNameTd.html();
                    productNameTd.html(newHtml);
                }
                
                // 确保操作列按钮正确显示
                var operationTd = tr.find('td[data-content="操作"]');
                if(operationTd.length > 0) {
                    operationTd.find('.layui-btn').show();
                    operationTd.find('.layui-btn-danger').css('display', 'inline-block !important');
                }
            }
        });
        
        // 调试信息
        console.log("样式应用完成，检查操作列按钮");
        $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
            console.log("第", i, "行操作列:", $(el).html());
            // 强制显示删除按钮
            $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
            $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
        });
    };
    
    // 定义全局的addChildToParent函数，供子页面调用
    window.addChildToParent = function(childProducts) {
        // 确保layui对象可用
        if (!layui || !layui.table) {
            console.error("layui对象不可用");
            return;
        }
        
        var table = layui.table;
        var tableData = table.cache.productTable || [];
        var parentData = window.currentParentData; // 使用全局变量存储父商品数据
        
        if (!parentData) {
            layer.msg('未找到父商品数据', {icon: 2});
            return;
        }
        
        // 添加调试信息
        console.log("添加子商品 - 父商品:", parentData.product_name, "product_id:", parentData.product_id);
        
        if (!parentData.product_id) {
            console.error("错误：父商品没有product_id");
            layer.msg('无法添加子商品：父商品标识符缺失', {icon: 2});
            return;
        }
        
        var parentIndex = -1;
        
        // 使用product_id查找父商品在当前表格数据中的索引（更可靠）
        for (var i = 0; i < tableData.length; i++) {
            if (tableData[i].product_id === parentData.product_id) {
                parentIndex = i;
                break;
            }
        }
        
        if (parentIndex === -1) {
            console.error("未找到父商品，父商品ID:", parentData.product_id);
            layer.msg('未找到父商品', {icon: 2});
            return;
        }
        
        // 插入子商品到父商品后面
        for (var i = 0; i < childProducts.length; i++) {
            var childProduct = childProducts[i];
            
            // 获取全局税率
            var taxRate = parseFloat($('select[name="tax_rate"]').val() || 0);
            
            // 确保父项数据中有倍数字段，如果没有则默认为1
            if(typeof parentData.multiplier === 'undefined') {
                parentData.multiplier = 1;
                console.log("父项没有倍数字段，设置默认值:", parentData.multiplier);
            }
            
            // 父项数量和倍数
            var parentQuantity = parseFloat(parentData.quantity) || 1;
            var parentMultiplier = parseFloat(parentData.multiplier) || 1;
            
            console.log("子项数量计算 - 父项数量:", parentQuantity, "父项倍数:", parentMultiplier);
            
            // 设置父子关系和缩进标识 - 使用product_id作为父子关系标识
            var newProduct = {
                product_id: childProduct.id,
                parent_product_id: parentData.product_id, // 使用product_id作为父商品标识
                product_name: childProduct.title,
                material_code: childProduct.material_code,
                product_specs: childProduct.specs,
                unit: childProduct.unit,
                // 子商品数量 = 父商品数量 × 子商品倍数
                multiplier: 1, // 子商品默认倍数为1
                quantity: parseFloat(parentData.quantity || 1), // 初始数量等于父项数量
                price: childProduct.purchase_price,
                tax_rate: taxRate,
                amount: childProduct.purchase_price,
                tax_amount: (childProduct.purchase_price * taxRate / 100).toFixed(2),
                total_amount: (parseFloat(childProduct.purchase_price) + parseFloat((childProduct.purchase_price * taxRate / 100).toFixed(2))).toFixed(2),
                is_child: true, // 标记为子商品
                attachments: [], // 附件数组，存储多个附件信息
                attachment_id: '', // 保留兼容旧代码
                delivery_date: parentData.delivery_date || '', // 继承父商品的交期
                notes: ''
            };
            
            console.log("添加子商品:", newProduct.product_name, "product_id:", newProduct.product_id, "parent_product_id:", newProduct.parent_product_id);
            
            // 插入到父商品后面
            tableData.splice(parentIndex + 1, 0, newProduct);
            parentIndex++; // 更新索引，确保下一个子商品插入位置正确
        }
        
        // 重新加载表格
        table.reload('productTable', {
            data: tableData,
            done: function() {
                console.log("表格重载完成");
                // 重新应用样式
                styleChildProducts();
                
                // 确保删除按钮显示
                setTimeout(function() {
                    $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
                        $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
                        $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
                    });
                }, 200);
            }
        });
        
        // 重新计算总金额
        window.calculateTotal();
    };
    
    function gouguInit() {
        var form = layui.form;
        var table = layui.table;
        var laydate = layui.laydate;
        var util = layui.util;
        var tool = layui.tool;
        var uploadPlus = layui.uploadPlus;
        var $ = layui.jquery;
        var layer = layui.layer;
        
        // 添加自动保存定时器变量
        var autoSaveTimer = null;
        var currentOrderId = null; // 添加当前订单ID变量
        
        // 自动保存函数
        function autoSave() {
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            var formData = form.val('orderForm');
            
            // 如果没有商品明细，不执行自动保存
            if(tableData.length === 0) {
                return;
            }
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据
            for(var i = 0; i < tableData.length; i++){
                tableData[i].tax_rate = globalTaxRate;
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                
                if (!tableData[i].attachment_id) {
                    tableData[i].attachment_id = '';
                }
                
                if (!tableData[i].attachments) {
                    tableData[i].attachments = [];
                }
                
                if (!Array.isArray(tableData[i].attachments)) {
                    tableData[i].attachments = [];
                }
                
                var attachmentIds = [];
                if (tableData[i].attachments.length > 0) {
                    tableData[i].attachments.forEach(function(attachment) {
                        if (attachment && attachment.id) {
                            attachmentIds.push(attachment.id);
                        }
                    });
                }
                
                tableData[i].attachment_ids = attachmentIds;
            }
            
            formData.details = tableData;
            
            // 根据是否有订单ID决定使用新增还是编辑接口
            var saveUrl = currentOrderId ? '/customer/order/edit?id=' + currentOrderId : '/customer/order/add';
            
            // 发送自动保存请求
            tool.post(saveUrl, formData, function(res){
                if(res.code === 0){
                    // 如果是新增，保存返回的订单ID
                    if (!currentOrderId && res.data && res.data.order_id) {
                        currentOrderId = res.data.order_id;
                    }
                    
                    // 使用更温和的提示方式
                    layer.msg('自动保存成功', {
                        icon: 1,
                        time: 1000,
                        offset: 'rt', // 右上角显示
                        shade: 0 // 不显示遮罩
                    });
                    
                    // 获取新的令牌
                    tool.get('/customer/order/get_token', {}, function(tokenRes) {
                        if(tokenRes.code === 0 && tokenRes.data) {
                            // 更新表单中的令牌值
                            $('input[name="__token__"]').val(tokenRes.data);
                        }
                    });
                }
            });
        }

        // 修改手动保存的处理
        form.on('submit(saveOrder)', function(data){
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            if(tableData.length === 0){
                layer.msg('请添加商品明细', {icon: 2});
                return false;
            }
            
            var formData = data.field;
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据，将每行的税率设置为全局税率
            for(var i = 0; i < tableData.length; i++){
                tableData[i].tax_rate = globalTaxRate;
                // 重新计算税额和价税合计
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                
                // 确保附件ID存在且是有效值
                if (!tableData[i].attachment_id) {
                    tableData[i].attachment_id = '';
                }
                
                // 确保附件数组存在
                if (!tableData[i].attachments) {
                    tableData[i].attachments = [];
                }
                
                // 确保attachments是数组
                if (!Array.isArray(tableData[i].attachments)) {
                    tableData[i].attachments = [];
                }
                
                // 收集所有附件ID用于后端处理
                var attachmentIds = [];
                if (tableData[i].attachments.length > 0) {
                    tableData[i].attachments.forEach(function(attachment) {
                        if (attachment && attachment.id) {
                            attachmentIds.push(attachment.id);
                        }
                    });
                }
                
                // 添加附件ID数组
                tableData[i].attachment_ids = attachmentIds;
            }
            
            formData.details = tableData;
            
            // 根据是否有订单ID决定使用新增还是编辑接口
            var saveUrl = currentOrderId ? '/customer/order/edit?id=' + currentOrderId : '/customer/order/add';
            
            console.log("保存订单数据:", formData, "使用接口:", saveUrl);
            
            tool.post(saveUrl, formData, function(res){
                if(res.code === 0){
                    // 如果是新增，保存返回的订单ID
                    if (!currentOrderId && res.data && res.data.order_id) {
                        currentOrderId = res.data.order_id;
                    }
                    
                    layer.msg('保存成功', {icon: 1}, function(){
                        // 检查是否在弹窗中
                        if (typeof refreshParent === 'function') {
                            // 在弹窗中，刷新父页面并关闭当前弹窗
                            refreshParent();
                        } else {
                            // 不在弹窗中，使用原来的跳转逻辑
                            window.location.href = '/customer/order/view?id=' + (currentOrderId || res.data.order_id);
                        }
                    });
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            });
            return false;
        });
        
        // 在页面加载完成后启动自动保存定时器
        $(function() {
            // 每5分钟自动保存一次
            autoSaveTimer = setInterval(autoSave, 5 * 60 * 1000);
            
            // 在页面卸载时清除定时器
            $(window).on('unload', function() {
                if(autoSaveTimer) {
                    clearInterval(autoSaveTimer);
                }
            });
        });
        
        // 验证关键对象是否存在
        console.log("工具对象检查 - layer:", (typeof layer !== 'undefined'), "tool:", (typeof tool !== 'undefined'));
        
        // 如果tool对象存在，检查photoView方法
        if (typeof tool !== 'undefined') {
            console.log("tool.photoView:", (typeof tool.photoView === 'function' ? '存在' : '不存在'));
        }
        
        // 当前日期
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = (currentDate.getMonth() + 1 < 10 ? '0' : '') + (currentDate.getMonth() + 1);
        var day = (currentDate.getDate() < 10 ? '0' : '') + currentDate.getDate();
        var today = year + '-' + month + '-' + day;
        
        // 渲染日期选择器
        laydate.render({
            elem: '#orderDate',
            value: today
        });
        
        laydate.render({
            elem: '#deliveryDate',
            min: today
        });
        
        // 初始化表格
        table.init('productTable', {
            limit: 1000,
            toolbar: true,
            defaultToolbar: ['filter'],
            done: function(res, curr, count){
                calculateTotal();
                // 处理子商品的样式
                styleChildProducts();
            }
        });
        
        // 监听单元格编辑
        table.on('edit(productTable)', function(obj){
            var value = obj.value;
            var field = obj.field;
            var data = obj.data;
            
            // 数量、单价或税率变更时，计算金额
            if(field === 'quantity' || field === 'price'){
                // 将输入转为数值并验证
                var val = parseFloat(value);
                if(isNaN(val) || val < 0){
                    layer.msg('请输入有效的数字', {icon: 5});
                    // 恢复原值
                    $(this).val(data[field]);
                    return;
                }
                
                if(field === 'quantity'){
                    data.quantity = val;
                    
                    // 如果是父项，更新所有子项的数量
                    if(!data.is_child) {
                        updateChildrenQuantities(data);
                    }
                }else if(field === 'price'){
                    data.price = val;
                }
                
                // 获取当前选择的税率（如果行中未设置税率）
                if (!data.tax_rate) {
                    data.tax_rate = parseFloat($('select[name="tax_rate"]').val() || 0);
                }
                
                // 计算金额
                data.amount = (data.quantity * data.price).toFixed(2);
                data.tax_amount = (data.amount * data.tax_rate / 100).toFixed(2);
                data.total_amount = (parseFloat(data.amount) + parseFloat(data.tax_amount)).toFixed(2);
                
                // 更新表格这一行的数据
                obj.update({
                    quantity: data.quantity,
                    price: data.price,
                    tax_rate: data.tax_rate,
                    amount: data.amount,
                    tax_amount: data.tax_amount,
                    total_amount: data.total_amount
                });
                
                // 重新计算总金额
                calculateTotal();
            } else if(field === 'multiplier') {
                // 处理倍数变更
                var val = parseFloat(value);
                if(isNaN(val) || val <= 0){
                    layer.msg('倍数必须是大于0的数字', {icon: 5});
                    // 恢复默认值
                    $(this).val(data.multiplier || 1);
                    return;
                }
                
                // 更新倍数
                data.multiplier = val;
                
                if(data.is_child) {
                    // 如果是子项，查找其父项
                    var parentItem = null;
                    for(var i = 0; i < table.cache.productTable.length; i++) {
                        if(!table.cache.productTable[i].is_child && 
                           table.cache.productTable[i].product_id === data.parent_product_id) {
                            parentItem = table.cache.productTable[i];
                            break;
                        }
                    }
                    
                    if(parentItem) {
                        // 更新自身数量 = 父项数量 × 子项倍数
                        var parentQuantity = parseFloat(parentItem.quantity) || 0;
                        data.quantity = parentQuantity * data.multiplier;
                        
                        // 重新计算金额
                        data.amount = (data.quantity * data.price).toFixed(2);
                        data.tax_amount = (data.amount * data.tax_rate / 100).toFixed(2);
                        data.total_amount = (parseFloat(data.amount) + parseFloat(data.tax_amount)).toFixed(2);
                        
                        console.log("子项倍数变更 - 子项:", data.product_name, 
                                  "父项:", parentItem.product_name,
                                  "父项数量:", parentQuantity,
                                  "子项倍数:", data.multiplier,
                                  "新子项数量:", data.quantity);
                        
                        // 更新表格这一行的数据
                        obj.update({
                            multiplier: data.multiplier,
                            quantity: data.quantity,
                            amount: data.amount,
                            tax_amount: data.tax_amount,
                            total_amount: data.total_amount
                        });
                    }
                } else {
                    // 如果是父项，更新所有子项的数量
                    updateChildrenQuantities(data);
                    
                    // 更新表格这一行的数据
                    obj.update({
                        multiplier: data.multiplier
                    });
                }
                
                // 重新计算总金额
                calculateTotal();
            }
        });
        
        // 监听工具条
        table.on('tool(productTable)', function(obj){
            var data = obj.data;
            if(obj.event === 'del'){
                // 添加调试信息，打印商品数据以便检查
                console.log("删除商品数据:", data);
                
                // 判断是否为子商品，通过parent_product_id属性而不是parent_id
                if(data.parent_product_id !== undefined && data.parent_product_id !== null && data.parent_product_id !== '') {
                    // 如果存在parent_product_id，说明是子商品，只删除自身
                    console.log("当前为子商品，只删除自身。parent_product_id:", data.parent_product_id);
                    deleteProductSingle(obj);
                } else {
                    // 如果不存在parent_product_id，说明是父商品，删除商品及其子商品
                    console.log("当前为父商品，删除商品及其子商品");
                    deleteProductWithChildren(obj);
                }
                calculateTotal();
            } else if(obj.event === 'addChild') {
                // 添加子商品
                addChildProduct(data);
            } else if(obj.event === 'upload') {
                // 处理附件上传
                var data = obj.data;
                
                // 检查product_id
                if (!data.product_id) {
                    console.error("错误：要上传附件的商品没有product_id");
                    layer.msg('无法上传附件：商品标识符缺失', {icon: 2});
                    return;
                }
                
                // 存储当前行索引（兼容旧代码）和product_id（更可靠）
                currentEditIndex = data.LAY_INDEX || data.LAY_INDEX_INIT || -1;
                
                // 获取当前商品的附件信息
                var attachmentId = data.attachment_id || '';
                console.log("商品附件信息:", data); // 调试信息
                
                // 存储当前商品ID，确保上传回调时能找到正确的行
                window.currentProductIndex = currentEditIndex;
                window.currentProductId = data.product_id;
                
                // 尝试使用localStorage保存更多信息
                try {
                    localStorage.setItem('currentEditIndex', currentEditIndex);
                    localStorage.setItem('currentProductId', data.product_id);
                    localStorage.setItem('currentProductName', data.product_name || '');
                } catch (e) {
                    console.error("保存到本地存储失败:", e);
                }
                
                // 重置上传组件
                resetUploadComponent();
                
                // 打开弹窗
                var dialogTitle = '附件管理' + (data.product_name ? ' - ' + data.product_name : '');
                
                // 清除之前可能存在的商品信息元素，防止重复
                $('#fileUploadDialog .layui-form .product-info-item').remove();
                
                // 显示当前编辑的商品信息
                var productInfoHtml = '';
                if (data.product_name) {
                    productInfoHtml = '<div class="layui-form-item product-info-item">' +
                        '<label class="layui-form-label">当前商品</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" readonly class="layui-input layui-bg-gray" value="' + data.product_name + '" title="' + data.product_name + '">' +
                        '<input type="hidden" id="currentProductId" value="' + (data.product_id || '') + '">' +
                        '</div></div>';
                        
                        // 插入到上传对话框中
                        $('#fileUploadDialog .layui-form').prepend(productInfoHtml);
                }
                
                // 打开弹窗前先显示或隐藏已有的附件信息
                if (data.attachments && data.attachments.length > 0) {
                    console.log("发现已上传的多个附件:", data.attachments.length);
                    
                    // 清空并隐藏"暂无附件"
                    $('#uploadedFileList .layui-file-item').remove();
                    $('#noFileInfo').hide();
                    
                    // 显示多个附件
                    initFileList(data.attachments);
                } else if (attachmentId) {
                    console.log("发现已上传附件:", attachmentId);
                    // 显示已有附件信息
                    var fileInfo = {
                        id: attachmentId,
                        name: data.attachment_name || '未知文件',
                        size: data.attachment_size || 0,
                        fileext: data.attachment_ext || '',
                        url: data.attachment_url || ''
                    };
                    
                    // 确保URL以 / 或 http 开头
                    if (fileInfo.url && !fileInfo.url.startsWith('/') && !fileInfo.url.startsWith('http')) {
                        fileInfo.url = '/' + fileInfo.url;
                    }
                    
                    console.log("准备显示附件信息:", fileInfo);
                    
                    // 直接调用initFileList而不是showUploadedFileInfo
                    initFileList(fileInfo);
                    $('#noFileInfo').hide();
                    
                    // 添加调试信息
                setTimeout(function() {
                        var items = $('#uploadedFileList .layui-file-item').length;
                        console.log("附件列表项数量:", items);
                        if (items === 0) {
                            console.error("未能正确添加附件项到列表");
                        }
                    }, 100);
                } else {
                    console.log("无已上传附件");
                    // 清空并显示"暂无附件"
                    $('#uploadedFileList .layui-file-item').remove();
                    showEmptyFileInfo();
                }
                
                // 打开弹窗
                layer.open({
                    type: 1,
                    title: dialogTitle,
                    content: $('#fileUploadDialog'),
                    area: ['700px', '550px'],
                    shade: 0.3,
                    closeBtn: 1,
                    shadeClose: true,
                    end: function() {
                        // 弹窗关闭时确保关闭所有loading
                        layer.closeAll('loading');
                    }
                });
            }
        });
        
        // 监听关闭按钮，确保关闭加载层
        $('#fileUploadDialog').on('click', '.layui-layer-close', function() {
            layer.closeAll('loading');
        });
        
        // 确认上传按钮
        $('#btnConfirmUpload').on('click', function() {
            layer.closeAll();
            layer.closeAll('loading'); // 确保关闭所有加载层
        });
        
        // 取消上传按钮
        $('#btnCancelUpload').on('click', function() {
            layer.closeAll();
            layer.closeAll('loading'); // 确保关闭所有加载层
        });
        
        // 重置上传组件
        function resetUploadComponent() {
            console.log("重置上传组件");
            // 移除旧的上传组件
            $('#fileUploadArea').html('<div id="uploadBox"><input type="hidden" data-type="file" id="fileAttachmentId" value=""><div class="layui-upload-drag-desc"><i class="layui-icon layui-icon-upload"></i><p>点击上传，或将文件拖拽到此处</p></div></div>');
            
            // 重新初始化上传组件
            attachment = new uploadPlus({
                "target": 'fileUploadArea',
                "targetBox": 'uploadBox',
                "url": '/api/index/upload',
                "attachment": {
                    "type": 1,
                    "uidDelete": true,
                    "ajaxSave": function(res) {
                        console.log("上传响应:", res);
                        layer.closeAll('loading');
                        
                        if (res.code == 0) {
                            console.log("上传成功，文件ID:", res.data.id);
                            
                            // 尝试从多个地方获取当前编辑的行索引和商品ID
                            var editIndex = currentEditIndex;
                            console.log("初始editIndex值:", editIndex);
                            var productId = '';
                            
                            // 1. 优先从隐藏字段获取
                            productId = $('#currentProductId').val();
                            console.log("从隐藏字段获取产品ID:", productId);
                            
                            // 2. 如果没有，尝试从全局变量获取
                            if (!productId) {
                                productId = window.currentProductId;
                                console.log("从全局变量获取产品ID:", productId);
                            }
                            
                            // 3. 最后尝试从本地存储获取
                            if (!productId) {
                                try {
                                    productId = localStorage.getItem('currentProductId');
                                    console.log("从本地存储获取产品ID:", productId);
                                } catch (e) {
                                    console.error("从本地存储读取失败:", e);
                                }
                            }
                            
                            console.log("最终确定的索引:", editIndex, "产品ID:", productId);
                            
                            // 确保文件路径正确
                            if (res.data.url && !res.data.url.startsWith('/') && !res.data.url.startsWith('http')) {
                                res.data.url = '/' + res.data.url;
                            }
                            if (res.data.filepath && !res.data.filepath.startsWith('/') && !res.data.filepath.startsWith('http')) {
                                res.data.filepath = '/' + res.data.filepath;
                            }
                            
                            // 确保有url属性，如果没有则使用filepath
                            if (!res.data.url && res.data.filepath) {
                                res.data.url = res.data.filepath;
                            }
                            
                            // 创建新的附件对象
                            var newAttachment = {
                                id: res.data.id,
                                name: res.data.name,
                                url: res.data.url || res.data.filepath,
                                fileext: res.data.fileext,
                                size: res.data.size
                            };
                            
                            var tableData = table.cache.productTable || [];
                            console.log("表格数据:", tableData);
                            
                            // 检查索引是否有效并在表格数据范围内
                            if (editIndex !== undefined && editIndex !== null && editIndex >= 0 && editIndex < tableData.length) {
                                console.log("找到有效行索引:", editIndex);
                                
                                // 更新指定行的附件信息
                                if (tableData[editIndex]) {
                                    // 确保attachments数组存在
                                    if (!tableData[editIndex].attachments) {
                                        tableData[editIndex].attachments = [];
                                    }
                                    
                                    // 添加新附件到attachments数组，而不是覆盖
                                    tableData[editIndex].attachments.push(newAttachment);
                                    console.log("已添加新附件到attachments数组，现有附件数量:", tableData[editIndex].attachments.length);
                                    
                                    // 同时更新主附件字段（为了向后兼容）
                                    // 这里选择保留最新上传的附件作为主附件
                                    tableData[editIndex].attachment_id = res.data.id;
                                    tableData[editIndex].attachment_name = res.data.name;
                                    tableData[editIndex].attachment_url = res.data.url || res.data.filepath;
                                    tableData[editIndex].attachment_ext = res.data.fileext;
                                    tableData[editIndex].attachment_size = res.data.size;
                                    
                                    console.log("已更新表格第" + editIndex + "行的附件信息:", tableData[editIndex]);
                                }
                            } else {
                                console.log("索引无效或超出范围，尝试通过产品ID查找或找到一个空行");
                                
                                // 如果有产品ID，尝试找到对应的行
                            var foundIndex = -1;
                            if (productId) {
                                for (var i = 0; i < tableData.length; i++) {
                                    if (tableData[i] && tableData[i].product_id == productId) {
                                        foundIndex = i;
                                        console.log("通过产品ID找到行:", foundIndex);
                                        break;
                                    }
                                }
                            }
                            
                                // 如果找到了产品ID对应的行
                            if (foundIndex >= 0) {
                                // 确保attachments数组存在
                                if (!tableData[foundIndex].attachments) {
                                    tableData[foundIndex].attachments = [];
                                }
                                
                                // 添加新附件到attachments数组
                                tableData[foundIndex].attachments.push(newAttachment);
                                console.log("已添加新附件到attachments数组，现有附件数量:", tableData[foundIndex].attachments.length);
                                
                                // 同时更新主附件字段（为了向后兼容）
                                tableData[foundIndex].attachment_id = res.data.id;
                                tableData[foundIndex].attachment_name = res.data.name;
                                tableData[foundIndex].attachment_url = res.data.url || res.data.filepath;
                                tableData[foundIndex].attachment_ext = res.data.fileext;
                                tableData[foundIndex].attachment_size = res.data.size;
                                
                                    console.log("已更新通过产品ID找到的行:", foundIndex, tableData[foundIndex]);
                                } else {
                                    // 尝试找到第一个空行
                                    for (var i = 0; i < tableData.length; i++) {
                                        if (!tableData[i] || !tableData[i].product_id) {
                                            tableData[i] = tableData[i] || {};
                                            
                                            // 确保attachments数组存在
                                            if (!tableData[i].attachments) {
                                                tableData[i].attachments = [];
                                            }
                                            
                                            // 添加新附件到attachments数组
                                            tableData[i].attachments.push(newAttachment);
                                            console.log("已添加新附件到空行的attachments数组，现有附件数量:", tableData[i].attachments.length);
                                            
                                            // 同时更新主附件字段（为了向后兼容）
                                            tableData[i].attachment_id = res.data.id;
                                            tableData[i].attachment_name = res.data.name;
                                            tableData[i].attachment_url = res.data.url || res.data.filepath;
                                            tableData[i].attachment_ext = res.data.fileext;
                                            tableData[i].attachment_size = res.data.size;
                                            
                                            console.log("已更新找到的空行:", i, tableData[i]);
                                            foundIndex = i;
                                            break;
                                        }
                                    }
                                }
                                
                                // 如果没有找到合适的行，添加到最后
                                if (foundIndex < 0) {
                                    console.log("未找到合适的行，将附件添加到新行");
                                    var newRow = {
                                        attachment_id: res.data.id,
                                        attachment_name: res.data.name,
                                        attachment_url: res.data.url || res.data.filepath,
                                        attachment_ext: res.data.fileext,
                                        attachment_size: res.data.size,
                                        attachments: [newAttachment] // 初始化attachments数组并添加新附件
                                    };
                                    tableData.push(newRow);
                                }
                            }
                            
                            // 更新表格
                                table.reload('productTable', {
                                    data: tableData
                                });
                                
                            console.log("表格已重新加载");
                            
                            // 确定当前行索引，用于显示所有附件
                            var currentRowIndex = editIndex >= 0 ? editIndex : foundIndex;
                            
                            // 显示文件信息到上传弹窗中
                            if (currentRowIndex >= 0 && tableData[currentRowIndex] && tableData[currentRowIndex].attachments) {
                                // 显示所有附件，而不仅仅是新上传的附件
                                console.log("显示所有附件:", tableData[currentRowIndex].attachments);
                                initFileList(tableData[currentRowIndex].attachments);
                            } else {
                                // 如果找不到完整的attachments数组，至少显示新上传的附件
                                var displayFileInfo = {
                                    id: res.data.id,
                                    name: res.data.name,
                                    url: res.data.url || res.data.filepath,
                                    fileext: res.data.fileext,
                                    size: res.data.size
                                };
                                
                                console.log("显示上传文件:", displayFileInfo);
                                initFileList(displayFileInfo);
                            }
                            
                            // 重置上传组件
                            $('#attachment-upload').val('');
                                layer.msg('上传成功');
                        } else {
                            console.error("上传失败或无效数据:", res);
                            layer.msg(res.msg || '上传失败', {icon: 2});
                        }
                    }
                }
            });
            
            // 将attachment赋值给全局的upload_instance
            upload_instance = attachment;
        }
        
        // 监听税率变更
        form.on('select(tax_rate)', function(data){
            var taxRate = parseFloat(data.value);
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            
            // 更新所有商品的税率和税额
            for(var i = 0; i < tableData.length; i++){
                var item = tableData[i];
                item.tax_rate = taxRate;
                item.tax_amount = (item.amount * taxRate / 100).toFixed(2);
                item.total_amount = (parseFloat(item.amount) + parseFloat(item.tax_amount)).toFixed(2);
            }
            
            // 重新加载表格
            table.reload('productTable', {
                data: tableData
        });
        
        // 重新计算总金额
            calculateTotal();
        });
        
        // 定义当前编辑的商品索引
        var currentEditIndex = -1;
        var currentFileInfo = null;
        var attachment = null;
        var upload_instance = null; // 添加上传实例全局变量
        
        // 初始页面加载时初始化上传组件
        $(function() {
            // 初始化上传组件
            resetUploadComponent();
            
            // 添加调试信息，检查关键元素
            console.log("DOM加载完成，检查关键元素:");
            console.log("- fileUploadDialog存在:", $('#fileUploadDialog').length > 0);
            console.log("- uploadedFileList存在:", $('#uploadedFileList').length > 0);
            console.log("- fileItemTemplate存在:", $('#fileItemTemplate').length > 0);
            
            // 在上传弹窗打开后检查上传组件状态
            $(document).on('click', '[lay-event="upload"]', function() {
                setTimeout(function() {
                    console.log("上传按钮点击后检查元素:");
                    console.log("- 弹窗是否已打开:", $('.layui-layer:visible').length > 0);
                    console.log("- 上传区域是否存在:", $('#fileUploadArea:visible').length > 0);
                    console.log("- 附件列表是否存在:", $('#uploadedFileList:visible').length > 0);
                    console.log("- 已添加的附件项数量:", $('#uploadedFileList .layui-file-item').length);
                    
                    // 检查附件列表样式
                    var listStyle = $('#uploadedFileList').attr('style') || '';
                    console.log("- 附件列表样式:", listStyle);
                }, 500);
            });
            
            // 为文档中已存在的附件列表中的图片和查看大图按钮添加全局事件处理
            $(document).on('click', '.preview-image', function() {
                var imageUrl = $(this).data('url') || $(this).attr('src');
                console.log("图片点击(全局处理):", imageUrl);
                viewImage(imageUrl);
            });
            
            // 为查看大图按钮添加全局事件处理
            $(document).on('click', '[data-action="view-image"]', function() {
                var imageUrl = $(this).data('url');
                console.log("查看大图按钮点击(全局处理):", imageUrl);
                viewImage(imageUrl);
            });
            
            // 特别针对截图中红框标出的查看大图按钮
            // 注意：这个按钮可能是由后端生成的，我们需要为所有查看大图按钮添加事件处理
            $(document).on('click', '.查看大图, [data-event="view-image"], .查看大图按钮, [title="查看大图"]', function(e) {
                e.preventDefault();
                console.log("查看大图按钮点击（特殊处理）:", this);
                
                // 尝试获取图片URL
                var imageUrl = $(this).data('url') || $(this).data('src') || $(this).attr('href');
                if (!imageUrl) {
                    // 如果按钮本身没有URL，尝试从附近元素获取
                    var container = $(this).closest('.layui-file-item, .file-item, .attachment-item');
                    if (container.length > 0) {
                        // 查找图片元素
                        var img = container.find('img');
                        if (img.length > 0) {
                            imageUrl = img.attr('src') || img.data('src') || img.data('url');
                        }
                        
                        // 如果找不到图片，尝试从其他可能的元素获取
                        if (!imageUrl) {
                            imageUrl = container.find('[data-href], [href], [data-url], [data-src]').first().attr('data-href') || 
                                      container.find('[data-href], [href], [data-url], [data-src]').first().attr('href') || 
                                      container.find('[data-href], [href], [data-url], [data-src]').first().data('url') || 
                                      container.find('[data-href], [href], [data-url], [data-src]').first().data('src');
                        }
                    }
                }
                
                console.log("找到的图片URL:", imageUrl);
                
                if (imageUrl) {
                    viewImage(imageUrl);
                } else {
                    layer.msg('无法找到图片URL', {icon: 2});
                }
            });
            
            // 针对已上传文件列表中的查看大图按钮
            $(document).on('click', '#fileUploadDialog .layui-btn:contains("查看大图")', function() {
                console.log("已上传文件列表查看大图按钮点击");
                
                // 尝试获取图片URL
                var container = $(this).closest('tr, .layui-file-item');
                var filename = container.find('.filename, .file-name').text();
                console.log("文件名:", filename);
                
                // 尝试查找图片URL
                var imageUrl = $(this).data('url') || $(this).attr('href');
                if (!imageUrl) {
                    var img = container.find('img');
                    if (img.length > 0) {
                        imageUrl = img.attr('src');
                    } else {
                        // 如果找不到图片，尝试从下载按钮获取
                        var downloadBtn = container.find('.btn-download-file, [download], [title*="下载"]');
                        if (downloadBtn.length > 0) {
                            imageUrl = downloadBtn.attr('href') || downloadBtn.data('href');
                        }
                    }
                }
                
                console.log("找到的图片URL:", imageUrl);
                
                if (imageUrl) {
                    viewImage(imageUrl);
                } else {
                    layer.msg('无法找到图片URL', {icon: 2});
                }
            });
        });
        
        // 显示已上传附件信息
        function showUploadedFileInfo(fileInfo) {
            console.log("显示上传文件信息:", fileInfo);
            if (!fileInfo) {
                console.error("文件信息为空");
                return;
            }
            
            // 确保fileInfoArea存在，如果不存在则可能是另一种UI结构
            if ($('#fileInfoArea').length === 0) {
                // 使用initFileList来显示文件
                initFileList(fileInfo);
                return;
            }
            
            // 清除之前的文件信息显示
            $('#fileInfoArea').html('');
            
            var fileExt = (fileInfo.fileext || fileInfo.attachment_ext || '').toLowerCase();
            var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
            
            // 确保文件路径完整
            var filepath = fileInfo.filepath || fileInfo.url || fileInfo.attachment_url || '';
            if (filepath && filepath.indexOf('http') !== 0 && !filepath.startsWith('/')) {
                filepath = '/' + filepath;
            }
            
            var fileHtml = '<div class="file-info-box">';
            
            // 图片预览或文件图标
            if (isImage) {
                fileHtml += '<div class="image-preview"><img src="' + filepath + '" onerror="this.onerror=null;this.src=\'/static/images/image-error.png\';$(this).next().show();" alt="' + (fileInfo.name || fileInfo.attachment_name || '') + '"/>';
                fileHtml += '<div class="image-load-error" style="display:none;">图片加载失败</div></div>';
            } else {
                fileHtml += '<div class="file-icon"><i class="layui-icon layui-icon-file"></i></div>';
            }
            
            // 文件信息
            fileHtml += '<div class="file-details">';
            fileHtml += '<div class="file-name">' + (fileInfo.name || fileInfo.attachment_name || '未知文件') + '</div>';
            
            // 文件大小格式化
            var sizeStr = '';
            var fileSize = fileInfo.filesize || fileInfo.attachment_size || fileInfo.size || 0;
            if (fileSize) {
                var size = parseInt(fileSize);
                if (size < 1024) {
                    sizeStr = size + ' B';
                } else if (size < 1024 * 1024) {
                    sizeStr = (size / 1024).toFixed(2) + ' KB';
                } else {
                    sizeStr = (size / 1024 / 1024).toFixed(2) + ' MB';
                }
            }
            
            fileHtml += '<div class="file-meta">' + (fileExt ? fileExt.toUpperCase() + ' 文件' : '') + (sizeStr ? ' · ' + sizeStr : '') + '</div>';
            
            // 操作按钮
            fileHtml += '<div class="file-actions">';
            fileHtml += '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="window.open(\'' + filepath + '\')" title="查看文件"><i class="layui-icon layui-icon-search"></i>查看</button>';
            fileHtml += '<button type="button" class="layui-btn layui-btn-xs" onclick="downloadFile(\'' + filepath + '\', \'' + (fileInfo.name || fileInfo.attachment_name || '') + '\')" title="下载文件"><i class="layui-icon layui-icon-download-circle"></i>下载</button>';
            fileHtml += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteAttachment(' + (fileInfo.id || fileInfo.attachment_id || 0) + ')" title="删除文件"><i class="layui-icon layui-icon-delete"></i>删除</button>';
            fileHtml += '</div></div></div>';
            
            $('#fileInfoArea').html(fileHtml);
            $('#fileInfoArea').show();
        }
        
        // 下载文件函数
        function downloadFile(url, filename) {
            console.log("下载文件:", url, filename);
            if (!url) {
                layer.msg('文件链接无效', {icon: 2});
                return;
            }
            
            // 确保URL完整
            if (url.indexOf('http') !== 0 && !url.startsWith('/')) {
                url = '/' + url;
            }
            
            // 创建一个临时的a标签来触发下载
            var a = document.createElement('a');
            a.href = url;
            a.download = filename || '下载文件';
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
        
        // 获取文件图标的类名
        function getFileIconClass(fileExt) {
            var iconMap = {
                // 文档类型
                'doc': 'layui-icon-file-b',
                'docx': 'layui-icon-file-b',
                'pdf': 'layui-icon-file-pdf',
                'txt': 'layui-icon-file-text',
                'rtf': 'layui-icon-file-text',
                
                // 表格类型
                'xls': 'layui-icon-file-excel',
                'xlsx': 'layui-icon-file-excel',
                'csv': 'layui-icon-file-excel',
                
                // 演示文稿
                'ppt': 'layui-icon-file-ppt',
                'pptx': 'layui-icon-file-ppt',
                
                // 压缩文件
                'zip': 'layui-icon-file-zip',
                'rar': 'layui-icon-file-zip',
                '7z': 'layui-icon-file-zip',
                'tar': 'layui-icon-file-zip',
                'gz': 'layui-icon-file-zip',
                
                // 音频
                'mp3': 'layui-icon-file-audio',
                'wav': 'layui-icon-file-audio',
                'wma': 'layui-icon-file-audio',
                'flac': 'layui-icon-file-audio',
                'midi': 'layui-icon-file-audio',
                
                // 视频
                'mp4': 'layui-icon-file-video',
                'avi': 'layui-icon-file-video',
                'mov': 'layui-icon-file-video',
                'wmv': 'layui-icon-file-video',
                'flv': 'layui-icon-file-video',
                'mpg': 'layui-icon-file-video',
                'mpeg': 'layui-icon-file-video',
                
                // 代码
                'html': 'layui-icon-file-code',
                'css': 'layui-icon-file-code',
                'js': 'layui-icon-file-code',
                'php': 'layui-icon-file-code',
                'java': 'layui-icon-file-code',
                'py': 'layui-icon-file-code',
                
                // 默认
                'default': 'layui-icon-file'
            };
            
            return iconMap[fileExt] || iconMap['default'];
        }
        
        // 显示无文件信息
        function showEmptyFileInfo() {
            $('#uploadedFileList .layui-file-item').remove();
            $('#noFileInfo').show();
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
        }
        
        // 添加一个通用的查看图片函数
        function viewImage(imageUrl) {
            console.log("查看大图:", imageUrl);
            if (!imageUrl) {
                layer.msg('图片路径无效', {icon: 2});
                return;
            }
            
            try {
                    // 使用layer.photos
                    layer.photos({
                        photos: {
                            "data": [{"src": imageUrl}]
                        },
                        anim: 5,
                        shade: 0.8,
                        closeBtn: 1,
                        shadeClose: true
                    });
              
            } catch (e) {
                console.error("图片预览错误:", e, imageUrl);
                // 如果上述方法失败，直接在新窗口打开
                window.open(imageUrl, '_blank');
            }
        }

        // 初始化文件列表 - 重新实现使用更直接的方式
        function initFileList(attachments) {
            console.log("初始化文件列表:", attachments);
            
            // 先清除现有的文件项
            $('#uploadedFileList .layui-file-item').remove();
            
            // 处理输入参数，支持单个对象或数组
            var fileItems = [];
            if (Array.isArray(attachments)) {
                fileItems = attachments;
            } else if (attachments && typeof attachments === 'object') {
                fileItems = [attachments];
            } else if (arguments.length >= 3) {
                // 兼容旧的调用方式 initFileList(id, name, size, ext, url)
                fileItems = [{
                    id: arguments[0],
                    name: arguments[1],
                    size: arguments[2],
                    fileext: arguments[3],
                    url: arguments[4]
                }];
            }
            
            if (fileItems.length > 0) {
                // 隐藏"暂无附件"提示
                $('#noFileInfo').hide();
                
                // 使用字符串拼接HTML，而不是DOM操作
                var fileListHtml = '';
                
                // 遍历所有文件项添加到列表
                fileItems.forEach(function(fileInfo) {
                    if (!fileInfo || (!fileInfo.id && !fileInfo.attachment_id)) return;
                    
                    // 确保获取正确的ID和其他属性
                    var attachmentId = fileInfo.id || fileInfo.attachment_id;
                    var attachmentName = fileInfo.name || fileInfo.attachment_name || '未知文件';
                    var attachmentSize = fileInfo.size || fileInfo.attachment_size || 0;
                    var attachmentExt = fileInfo.fileext || fileInfo.attachment_ext || '';
                    var attachmentUrl = fileInfo.url || fileInfo.attachment_url || fileInfo.filepath || '';
                    
                    // 确保URL以 / 或 http 开头
                    if (attachmentUrl && !attachmentUrl.startsWith('/') && !attachmentUrl.startsWith('http')) {
                        attachmentUrl = '/' + attachmentUrl;
                    }
                    
                    console.log("处理文件项: ID=", attachmentId, "名称=", attachmentName, "类型=", attachmentExt, "URL=", attachmentUrl);
                    
                    // 判断文件类型
                    var fileExt = (attachmentExt || '').toLowerCase();
                    var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
                    
                    // 构建预览内容
                    var previewContent = '';
                    if (isImage && attachmentUrl) {
                        console.log("显示图片预览:", attachmentUrl);
                        // 添加明确的点击提示和样式
                        previewContent = '<img src="' + attachmentUrl + '" alt="缩略图" data-url="' + attachmentUrl + '" class="preview-image" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; cursor: zoom-in; transition: transform 0.2s;" title="点击查看大图">';
                    } else {
                        // 非图片类型，显示文件图标
                        var iconClass = getFileIconClass(fileExt);
                        previewContent = '<i class="layui-icon ' + iconClass + '" style="font-size: 30px; color: #999;"></i>';
                    }
                    
                    // 定义预览按钮文本和样式
                    var previewBtnText = isImage ? 
                        '<i class="layui-icon layui-icon-picture"></i> 查看大图' : 
                        '<i class="layui-icon layui-icon-file"></i> 文件信息';
                    
                    // 构建文件项HTML
                    var itemHtml = 
                        '<div class="layui-file-item" id="file-' + attachmentId + '" style="padding: 10px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; background-color: #fafafa;">' +
                            '<div class="file-preview" style="width: 60px; height: 60px; margin-right: 10px; text-align: center; overflow: hidden; border-radius: 3px; background-color: #f9f9f9; display: flex; align-items: center; justify-content: center;">' +
                                previewContent +
                            '</div>' +
                            '<div class="file-info" style="flex: 1;">' +
                                '<div class="file-name" style="font-weight: bold;">' + attachmentName + '</div>' +
                                '<div class="file-meta" style="color: #999; font-size: 12px;">' +
                                    '<span class="file-size">' + formatFileSize(attachmentSize) + '</span> · ' +
                                    '<span class="file-type">' + attachmentExt.toUpperCase() + '</span>' +
                                '</div>' +
                            '</div>' +
                            '<div class="file-actions">' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal btn-preview-file" data-id="' + attachmentId + '" data-url="' + attachmentUrl + '"' + (isImage ? ' data-action="view-image"' : '') + '>' +
                                    previewBtnText +
                                '</button>' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary btn-download-file" data-id="' + attachmentId + '" data-href="' + attachmentUrl + '">' +
                                    '<i class="layui-icon layui-icon-download-circle"></i> 下载' +
                                '</button>' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger btn-delete-file" data-id="' + attachmentId + '">' +
                                    '<i class="layui-icon layui-icon-delete"></i> 删除' +
                                '</button>' +
                            '</div>' +
                        '</div>';
                    
                    fileListHtml += itemHtml;
                });
                
                // 一次性添加所有文件项到DOM
                $('#uploadedFileList').append(fileListHtml);
                
                // 确认DOM更新，检查是否正确添加了文件项
                var addedItems = $('#uploadedFileList .layui-file-item').length;
                console.log("已添加文件项数量:", addedItems);
                
                // 为新添加的元素绑定事件
                // 1. 为图片缩略图添加点击事件
                $('#uploadedFileList .preview-image').on('click', function() {
                    var imageUrl = $(this).data('url') || $(this).attr('src');
                    console.log("图片点击:", imageUrl);
                    viewImage(imageUrl);
                });
                
                // 2. 为预览按钮添加点击事件
                $('#uploadedFileList .btn-preview-file').on('click', function() {
                    var fileId = $(this).data('id');
                    var fileUrl = $(this).data('url') || $(this).parent().find('.btn-download-file').data('href');
                    var fileName = $(this).parent().parent().find('.file-name').text();
                    var fileExt = $(this).parent().parent().find('.file-type').text().toLowerCase();
                    var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt.toLowerCase()) !== -1;
                    
                    console.log("预览按钮点击:", fileUrl, isImage);
                    
                    if (isImage && fileUrl) {
                        // 使用统一的查看图片函数
                        viewImage(fileUrl);
                    } else {
                        // 查找文件信息以预览
                        var tableData = table.cache.productTable || [];
                        var fileInfo = null;
                        
                        for (var i = 0; i < tableData.length; i++) {
                            if (tableData[i] && tableData[i].attachment_id == fileId) {
                                fileInfo = {
                                    id: fileId,
                                    name: tableData[i].attachment_name,
                                    filepath: tableData[i].attachment_url,
                                    fileext: tableData[i].attachment_ext,
                                    create_time: Math.floor(Date.now() / 1000)
                                };
                                break;
                            }
                        }
                        
                        if (fileInfo) {
                            previewFile(fileInfo);
                        } else {
                            layer.msg('无法获取文件信息');
                        }
                    }
                });
                
                $('#uploadedFileList .btn-download-file').on('click', function() {
                    var fileHref = $(this).data('href');
                    if (fileHref) {
                        // 创建临时a标签并模拟点击，实现下载
                        var a = document.createElement('a');
                        a.href = fileHref;
                        a.target = '_blank';
                        a.download = ''; // 空值表示使用服务器提供的文件名
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    } else {
                        layer.msg('文件链接无效');
                    }
                });
                
                $('#uploadedFileList .btn-delete-file').on('click', function() {
                    var fileId = $(this).data('id');
                    var fileName = $(this).parent().parent().find('.file-name').text();
                    
                    layer.confirm('确定要删除附件"' + fileName + '"吗？<br><small>删除后无法恢复</small>', {
                        title: '删除确认',
                        btn: ['确定删除','取消'],
                        icon: 3
                    }, function(index) {
                        // 从表格中删除附件信息
                        var tableData = table.cache.productTable || [];
                        var fileDeleted = false;
                        
                        // 先尝试从当前编辑行的attachments数组中删除
                        if (currentEditIndex >= 0 && tableData[currentEditIndex]) {
                            var row = tableData[currentEditIndex];
                            // 检查当前行是否有attachments数组
                            if (row.attachments && row.attachments.length > 0) {
                                // 查找并删除attachments数组中的文件
                                for (var j = 0; j < row.attachments.length; j++) {
                                    if (row.attachments[j] && row.attachments[j].id == fileId) {
                                        console.log("从attachments数组中删除附件", j);
                                        row.attachments.splice(j, 1);
                                        fileDeleted = true;
                                        
                                        // 如果删除后attachments数组不为空，设置第一个附件为主附件
                                        if (row.attachments.length > 0) {
                                            row.attachment_id = row.attachments[0].id;
                                            row.attachment_name = row.attachments[0].name;
                                            row.attachment_url = row.attachments[0].url;
                                            row.attachment_ext = row.attachments[0].fileext;
                                            row.attachment_size = row.attachments[0].size;
                                        } else {
                                            // 如果删除后attachments为空，清空主附件字段
                                            row.attachment_id = '';
                                            row.attachment_name = '';
                                            row.attachment_url = '';
                                            row.attachment_ext = '';
                                            row.attachment_size = 0;
                                        }
                                        break;
                                    }
                                }
                            }
                            
                            // 如果在attachments中没找到，检查主附件字段
                            if (!fileDeleted && row.attachment_id == fileId) {
                                console.log("从主附件字段删除");
                                row.attachment_id = '';
                                row.attachment_name = '';
                                row.attachment_url = '';
                                row.attachment_ext = '';
                                row.attachment_size = 0;
                                // 确保attachments数组为空
                                row.attachments = [];
                                fileDeleted = true;
                            }
                        }
                        
                        // 如果当前行没有找到，遍历所有行查找
                        if (!fileDeleted) {
                            for (var i = 0; i < tableData.length; i++) {
                                var row = tableData[i];
                                if (row) {
                                    // 检查attachments数组
                                    if (row.attachments && row.attachments.length > 0) {
                                        for (var j = 0; j < row.attachments.length; j++) {
                                            if (row.attachments[j] && row.attachments[j].id == fileId) {
                                                console.log("从行", i, "的attachments数组中删除附件", j);
                                                row.attachments.splice(j, 1);
                                                fileDeleted = true;
                                                
                                                // 如果删除后attachments数组不为空，设置第一个附件为主附件
                                                if (row.attachments.length > 0) {
                                                    row.attachment_id = row.attachments[0].id;
                                                    row.attachment_name = row.attachments[0].name;
                                                    row.attachment_url = row.attachments[0].url;
                                                    row.attachment_ext = row.attachments[0].fileext;
                                                    row.attachment_size = row.attachments[0].size;
                                                } else {
                                                    // 如果删除后attachments为空，清空主附件字段
                                                    row.attachment_id = '';
                                                    row.attachment_name = '';
                                                    row.attachment_url = '';
                                                    row.attachment_ext = '';
                                                    row.attachment_size = 0;
                                                }
                                                break;
                                            }
                                        }
                                        
                                        if (fileDeleted) break;
                                    }
                                    
                                    // 检查主附件字段
                                    if (!fileDeleted && row.attachment_id == fileId) {
                                        console.log("从行", i, "的主附件字段删除");
                                        row.attachment_id = '';
                                        row.attachment_name = '';
                                        row.attachment_url = '';
                                        row.attachment_ext = '';
                                        row.attachment_size = 0;
                                        // 确保attachments数组为空
                                        row.attachments = [];
                                        fileDeleted = true;
                                        break;
                                    }
                                }
                            }
                        }
                        
                        if (fileDeleted) {
                            // 更新表格
                            table.reload('productTable', {
                                data: tableData
                            });
                            
                            // 从界面上移除文件项
                            $('#file-' + fileId).fadeOut('fast', function() {
                                $(this).remove();
                                
                                // 如果没有文件了，显示空信息
                                if ($('#uploadedFileList .layui-file-item').length === 0) {
                                    showEmptyFileInfo();
                                }
                            });
                            
                            layer.msg('附件"' + fileName + '"已删除', {icon: 1});
                        } else {
                            console.error("未找到要删除的附件:", fileId);
                            layer.msg('删除失败，未找到附件', {icon: 2});
                        }
                        
                        layer.close(index);
                    });
                });
                
                // 图片加载错误处理
                $('#uploadedFileList img.preview-image').on('error', function() {
                    var imgUrl = $(this).attr('src');
                    console.error("图片加载失败:", imgUrl);
                    $(this).attr('src', '/static/images/image-error.png');
                });
            } else {
                showEmptyFileInfo();
            }
        }
        
        // 查看文件
        function previewFile(fileInfo) {
            console.log("预览文件:", fileInfo);
            
            if (!fileInfo || !fileInfo.id) {
                layer.msg('文件信息不完整');
                return;
            }
            
            $('#previewFileName').val(fileInfo.name || '');
            $('#previewFileType').val(fileInfo.fileext || '');
            $('#previewFileTime').val(fileInfo.create_time ? layui.util.toDateString(fileInfo.create_time * 1000, 'yyyy-MM-dd HH:mm:ss') : '');
            
            // 如果有文件路径，则设置下载链接
            if (fileInfo.filepath) {
                $('#btnDownloadFile').attr('href', fileInfo.filepath).show();
            } else {
                $('#btnDownloadFile').hide();
            }
            
            layer.open({
                type: 1,
                title: '附件预览',
                content: $('#filePreviewDialog'),
                area: ['500px', '350px'],
                shade: 0.3,
                closeBtn: 1,
                shadeClose: true
            });
        }
        
        // 监听预览按钮点击事件
        $(document).on('click', '.btn-preview-file', function() {
            var fileId = $(this).data('id');
            if (fileId) {
                console.log("预览按钮点击, 文件ID:", fileId);
                
                // 从表格数据中查找文件信息
                var tableData = table.cache.productTable || [];
                var fileInfo = null;
                
                // 先从当前编辑的行获取
                if (currentEditIndex >= 0 && tableData[currentEditIndex] && tableData[currentEditIndex].attachment_id == fileId) {
                    console.log("从当前行获取文件信息");
                    fileInfo = {
                        id: fileId,
                        name: tableData[currentEditIndex].attachment_name,
                        filepath: tableData[currentEditIndex].attachment_url,
                        fileext: tableData[currentEditIndex].attachment_ext,
                        create_time: Math.floor(Date.now() / 1000)
                    };
                } else {
                    // 如果当前行没有，则遍历所有行
                    console.log("在所有行中查找文件信息");
                    for (var i = 0; i < tableData.length; i++) {
                        if (tableData[i] && tableData[i].attachment_id == fileId) {
                            fileInfo = {
                                id: fileId,
                                name: tableData[i].attachment_name,
                                filepath: tableData[i].attachment_url,
                                fileext: tableData[i].attachment_ext,
                                create_time: Math.floor(Date.now() / 1000)
                            };
                            break;
                        }
                    }
                }
                
                if (fileInfo) {
                    console.log("找到文件信息:", fileInfo);
                    
                    // 判断文件类型，图片用photos查看，其他打开预览窗
                    var fileExt = (fileInfo.fileext || '').toLowerCase();
                    var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
                    
                    if (isImage && fileInfo.filepath) {
                        // 记录图片路径用于调试
                        console.log("正在预览图片，路径:", fileInfo.filepath);
                        try {
                            // 尝试使用tool.photoView (如果存在)
                            if (typeof tool !== 'undefined' && typeof tool.photoView === 'function') {
                                tool.photoView(fileInfo.filepath);
                            } else {
                                // 简化Layer.photos配置
                                layer.photos({
                                    photos: {
                                        "data": [{"src": fileInfo.filepath}]
                                    },
                                    anim: 5
                                });
                            }
                        } catch (e) {
                            console.error("图片预览错误:", e);
                            // 如果预览失败，直接在新窗口打开
                            window.open(fileInfo.filepath, '_blank');
                        }
                    } else {
                        // 非图片类型使用普通预览窗
                        previewFile(fileInfo);
                    }
                } else {
                    console.log("未找到文件信息，尝试从API获取");
                    // 如果表格中没有找到，尝试从API获取
                    getFileInfo(fileId, function(fileInfo) {
                        if (fileInfo) {
                            var fileExt = (fileInfo.fileext || '').toLowerCase();
                            var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
                            
                            if (isImage && fileInfo.filepath) {
                                layer.photos({
                                    photos: {
                                        "title": fileInfo.name,
                                        "id": fileInfo.id,
                                        "start": 0,
                                        "data": [{
                                            "src": fileInfo.filepath,
                                            "thumb": fileInfo.filepath
                                        }]
                                    },
                                    anim: 5
                                });
                            } else {
                                previewFile(fileInfo);
                            }
                        } else {
                            layer.msg('无法获取文件信息');
                        }
                    });
                }
            } else {
                layer.msg('无效的文件ID');
            }
        });
        
        // 监听下载按钮点击事件
        $(document).on('click', '.btn-download-file', function() {
            var fileHref = $(this).attr('data-href');
            if (fileHref) {
                console.log("下载文件:", fileHref);
                // 创建临时a标签并模拟点击，实现下载
                var a = document.createElement('a');
                a.href = fileHref;
                a.target = '_blank';
                a.download = ''; // 空值表示使用服务器提供的文件名
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } else {
                layer.msg('文件链接无效');
            }
        });
        
        // 文件上传回调函数
        function uploadCallBack(res) {
            console.log("uploadCallBack被调用:", res);
            
            // 确保upload_instance已初始化
            if (!upload_instance && attachment) {
                upload_instance = attachment;
            }
            
            // 重置上传组件
            if (upload_instance) {
                upload_instance.reset();
            } else {
                console.warn("upload_instance未定义，无法重置上传组件");
            }
            
            // 如果上传成功
            if (res.code === 0) {
                // 获取上传文件的信息
                var fileInfo = res.data.file;
                var fileUrl = fileInfo.url;
                
                // 确保URL以/开头
                if (fileUrl && fileUrl.indexOf('/') !== 0) {
                    fileUrl = '/' + fileUrl;
                }
                
                // 获取当前编辑的产品索引
                var index = null;
                
                // 尝试从多个可能的来源获取索引
                if (typeof currentEditIndex !== 'undefined' && currentEditIndex !== null && !isNaN(parseInt(currentEditIndex))) {
                    // 如果当前有活跃的编辑索引，则使用它
                    index = parseInt(currentEditIndex);
                } else if (typeof window.currentProductIndex !== 'undefined' && window.currentProductIndex !== null && !isNaN(parseInt(window.currentProductIndex))) {
                    // 如果有全局索引变量，则使用它
                    index = parseInt(window.currentProductIndex);
                } else {
                    // 从localStorage获取之前保存的索引
                    try {
                        var storedIndex = localStorage.getItem('currentEditIndex');
                        if (storedIndex !== null) {
                            var parsedIndex = parseInt(storedIndex);
                            if (!isNaN(parsedIndex)) {
                                index = parsedIndex;
                                console.log("从localStorage恢复索引:", index);
                            }
                        }
                    } catch (e) {
                        console.error("从localStorage读取索引时出错:", e);
                    }
                }
                
                var tableData = table.cache.productTable || [];
                
                // 检查索引的有效性
                if (index !== null && index >= 0 && index < tableData.length) {
                    // 创建新的附件对象
                    var newAttachment = {
                        fileId: fileInfo.id,
                        id: fileInfo.id,
                        name: fileInfo.name,
                        url: fileUrl,
                        fileext: fileInfo.ext,
                        size: fileInfo.size
                    };
                    
                    // 初始化attachments数组（如果不存在）
                    if (!tableData[index].attachments) {
                        tableData[index].attachments = [];
                    }
                    
                    // 添加新附件到数组
                    tableData[index].attachments.push(newAttachment);
                    
                    // 同时更新主附件字段（向后兼容）
                    // 注意这里我们会覆盖主附件字段，但attachments数组会保留所有附件
                    tableData[index].attachment_id = fileInfo.id;
                    tableData[index].attachment_name = fileInfo.name;
                    tableData[index].attachment_url = fileUrl;
                    tableData[index].attachment_ext = fileInfo.ext;
                    tableData[index].attachment_size = fileInfo.size;
                                
                                // 重新加载表格数据
                                table.reload('productTable', {
                                    data: tableData
                                });
                                
                                // 在上传对话框中显示所有附件
                    initFileList(tableData[index].attachments);
                                
                                layer.msg('上传成功');
                    console.log("附件上传成功，更新了索引 " + index + " 的产品行，现有附件数量: " + tableData[index].attachments.length);
                            } else {
                    // 如果找不到有效的索引，尝试找到一个空行
                    var found = false;
                    for (var i = 0; i < tableData.length; i++) {
                        if (!tableData[i].product_id) {
                            // 创建新的附件对象
                            var newAttachment = {
                                fileId: fileInfo.id,
                                id: fileInfo.id,
                                name: fileInfo.name,
                                url: fileUrl,
                                fileext: fileInfo.ext,
                                size: fileInfo.size
                            };
                            
                            // 初始化attachments数组（如果不存在）
                            if (!tableData[i].attachments) {
                                tableData[i].attachments = [];
                            }
                            
                            // 添加新附件到数组
                            tableData[i].attachments.push(newAttachment);
                            
                            // 同时更新主附件字段（向后兼容）
                            tableData[i].attachment_id = fileInfo.id;
                            tableData[i].attachment_name = fileInfo.name;
                            tableData[i].attachment_url = fileUrl;
                            tableData[i].attachment_ext = fileInfo.ext;
                            tableData[i].attachment_size = fileInfo.size;
                            
                            // 重新加载表格数据
                            table.reload('productTable', {
                                data: tableData
                            });
                            
                            // 在上传对话框中显示所有附件
                            initFileList(tableData[i].attachments);
                            
                            found = true;
                            layer.msg('上传成功');
                            console.log("附件上传成功，更新了索引 " + i + " 的产品行，现有附件数量: " + tableData[i].attachments.length);
                            break;
                        }
                    }
                    
                    if (!found) {
                        layer.msg('请先选择要编辑的产品行');
                    }
                            }
                        } else {
                layer.msg('上传失败: ' + res.msg);
            }
        }
        
        // 获取文件信息
        function getFileInfo(fileId, callback) {
            tool.get('/api/index/get_file_info', { file_id: fileId }, function(res) {
                if (res.code == 0) {
                    callback(res.data);
                } else {
                    layer.msg(res.msg || '获取文件信息失败');
                }
            });
        }
        
        // 删除按钮点击事件
        $(document).on('click', '.btn-delete-file', function() {
            var fileId = $(this).data('id');
            var fileName = $(this).closest('.layui-file-item').find('.file-name').text();
            var fileExt = $(this).closest('.layui-file-item').find('.file-type').text().toLowerCase();
            var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt.toLowerCase()) !== -1;
            
            if (fileId) {
                layer.confirm('确定要删除附件"' + fileName + '"吗？<br><small>删除后无法恢复</small>', {
                    title: '删除确认',
                    btn: ['确定删除','取消'],
                    icon: 3
                }, function(index) {
                    console.log("删除附件:", fileId, "当前编辑索引:", currentEditIndex);
                    
                    // 从表格中删除附件信息
                    var tableData = table.cache.productTable || [];
                    var fileDeleted = false;
                    
                    // 先尝试从当前编辑行删除
                    if (currentEditIndex >= 0 && tableData[currentEditIndex]) {
                        // 检查当前行是否有这个附件
                        var row = tableData[currentEditIndex];
                        if (row.attachments && row.attachments.length > 0) {
                            for (var j = 0; j < row.attachments.length; j++) {
                                if (row.attachments[j] && row.attachments[j].id == fileId) {
                                    console.log("从当前行attachments数组中删除附件", j);
                                    // 从数组中移除这个附件
                                    row.attachments.splice(j, 1);
                                    fileDeleted = true;
                                    break;
                                }
                            }
                            
                            // 如果删除后仍有其他附件，将第一个附件作为主附件
                            if (row.attachments.length > 0) {
                                row.attachment_id = row.attachments[0].id;
                                row.attachment_name = row.attachments[0].name;
                                row.attachment_url = row.attachments[0].url;
                                row.attachment_ext = row.attachments[0].fileext;
                                row.attachment_size = row.attachments[0].size;
                            } else {
                                // 否则清空主附件信息
                                row.attachment_id = '';
                                row.attachment_name = '';
                                row.attachment_url = '';
                                row.attachment_ext = '';
                                row.attachment_size = 0;
                            }
                        }
                        // 兼容旧代码，检查主附件ID
                        else if (row.attachment_id == fileId) {
                            console.log("从当前行删除主附件");
                            row.attachment_id = '';
                            row.attachment_name = '';
                            row.attachment_url = '';
                            row.attachment_ext = '';
                            row.attachment_size = 0;
                            // 确保attachments数组为空
                            row.attachments = [];
                            fileDeleted = true;
                        }
                    }
                    
                    // 如果当前行没有删除成功，则遍历所有行查找
                    if (!fileDeleted) {
                        for (var i = 0; i < tableData.length; i++) {
                            var row = tableData[i];
                            if (row) {
                                if (row.attachments && row.attachments.length > 0) {
                                    for (var j = 0; j < row.attachments.length; j++) {
                                        if (row.attachments[j] && row.attachments[j].id == fileId) {
                                            console.log("从行", i, "的attachments数组中删除附件", j);
                                            // 从数组中移除这个附件
                                            row.attachments.splice(j, 1);
                                            fileDeleted = true;
                                            break;
                                        }
                                    }
                                    
                                    if (fileDeleted) {
                                        // 如果删除后仍有其他附件，将第一个附件作为主附件
                                        if (row.attachments.length > 0) {
                                            row.attachment_id = row.attachments[0].id;
                                            row.attachment_name = row.attachments[0].name;
                                            row.attachment_url = row.attachments[0].url;
                                            row.attachment_ext = row.attachments[0].fileext;
                                            row.attachment_size = row.attachments[0].size;
                                        } else {
                                            // 否则清空主附件信息
                                            row.attachment_id = '';
                                            row.attachment_name = '';
                                            row.attachment_url = '';
                                            row.attachment_ext = '';
                                            row.attachment_size = 0;
                                        }
                                        break;
                                    }
                                }
                                // 兼容旧代码，检查主附件ID
                                else if (row.attachment_id == fileId) {
                                    console.log("从行", i, "删除主附件");
                                    row.attachment_id = '';
                                    row.attachment_name = '';
                                    row.attachment_url = '';
                                    row.attachment_ext = '';
                                    row.attachment_size = 0;
                                    // 确保attachments数组为空
                                    row.attachments = [];
                                    fileDeleted = true;
                                    break;
                                }
                            }
                        }
                    }
                    
                    // 如果删除成功，则更新表格
                    if (fileDeleted) {
                        // 直接更新表格缓存
                        table.cache.productTable = tableData;
                        
                        // 更新表格显示
                        table.reload('productTable', {
                            data: tableData
                        });
                        
                        // 从界面上移除文件项
                        $('#file-' + fileId).fadeOut('fast', function() {
                            $(this).remove();
                            
                            // 如果没有文件了，显示空信息
                            if ($('#uploadedFileList .layui-file-item').length === 0) {
                                showEmptyFileInfo();
                            }
                        });
                        
                        layer.msg('附件"' + fileName + '"已删除', {icon: 1});
                    } else {
                        console.error("未找到要删除的附件:", fileId);
                        layer.msg('删除失败，未找到附件', {icon: 2});
                    }
                    
                    layer.close(index);
                });
            }
        });
        
        // 添加商品
        $('#btnAddProduct').on('click', function(){
            var taxRate = parseFloat($('select[name="tax_rate"]').val() || 0);

            
            tool.box('selectProduct', {
                title: '选择商品',
                width: '100%',
                success: function(layero, index){
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var table = iframeWindow.layui.table;
                    
                    // 监听选择事件
                    iframeWindow.layui.table.on('tool(productTable)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'select'){
                            var table = layui.table;
                            var tableData = table.cache.productTable || [];
                            
                            // 检查是否已添加该商品
                            var exists = tableData.some(function(item){
                                return item.product_id === data.id;
                            });
                            
                            if(exists){
                                layer.msg('该商品已添加', {icon: 2});
                                return;
                            }
                            
                            // 使用当前选择的税率
                            var price = data.price || 0;
                            var amount = (parseFloat(price) * 1).toFixed(2);
                            var tax_amount = (amount * taxRate / 100).toFixed(2);
                            var total_amount = (parseFloat(amount) + parseFloat(tax_amount)).toFixed(2);
                            
                            // 添加新商品，增加附件字段
                            tableData.push({
                                product_id: data.id,
                                product_name: data.title,
                                product_specs: data.specs,
                                unit: data.unit,
                                quantity: 1,
                                multiplier: 1, // 添加倍数字段，默认为1
                                price: price,
                                tax_rate: taxRate,
                                amount: amount,
                                tax_amount: tax_amount,
                                total_amount: total_amount,
                                delivery_date: '',
                                notes: '',
                                // 将单个附件字段改为附件数组
                                attachments: [], // 附件数组，存储多个附件信息
                                attachment_id: '', // 保留兼容旧代码
                                attachment_name: '', // 保留兼容旧代码
                                attachment_url: '', // 保留兼容旧代码
                                attachment_ext: '', // 保留兼容旧代码
                                attachment_size: 0 // 保留兼容旧代码
                            });
                            
                            table.reload('productTable', {
                                data: tableData
                            });
                            
                            // 计算总金额
                            calculateTotal();
                            
                            layer.close(index);
                        }
                    });
                }
            },"95%", "85%");
        });
        
        // 删除选中商品
        $('#btnRemoveProduct').on('click', function(){
            var table = layui.table;
            var checkStatus = table.checkStatus('productTable');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请选择要删除的商品', {icon: 2});
                return;
            }
            layer.confirm('确定要删除选中的商品吗？', function(index){
                var tableData = table.cache.productTable || [];
                var newData = tableData.filter(function(item){
                    return !data.some(function(selected){
                        return selected.LAY_TABLE_INDEX === item.LAY_TABLE_INDEX;
                    });
                });
                table.reload('productTable', {
                    data: newData
                });
                layer.close(index);
            });
        });
        
        // 取消按钮
        $('#btnCancel').on('click', function(){
            // 检查是否在弹窗中
            if (typeof refreshParent === 'function') {
                // 在弹窗中，关闭当前弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            } else {
                // 不在弹窗中，使用原来的关闭逻辑
                tool.close();
            }
        });
        
        // 保存订单
        form.on('submit(saveOrder)', function(data){
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            if(tableData.length === 0){
                layer.msg('请添加商品明细', {icon: 2});
                return false;
            }
            
            var formData = data.field;
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据，将每行的税率设置为全局税率
            for(var i = 0; i < tableData.length; i++){
                tableData[i].tax_rate = globalTaxRate;
                // 重新计算税额和价税合计
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                
                // 确保附件ID存在且是有效值
                if (!tableData[i].attachment_id) {
                    tableData[i].attachment_id = '';
                }
                
                // 确保附件数组存在
                if (!tableData[i].attachments) {
                    tableData[i].attachments = [];
                }
                
                // 确保attachments是数组
                if (!Array.isArray(tableData[i].attachments)) {
                    tableData[i].attachments = [];
                }
                
                // 收集所有附件ID用于后端处理
                var attachmentIds = [];
                if (tableData[i].attachments.length > 0) {
                    tableData[i].attachments.forEach(function(attachment) {
                        if (attachment && attachment.id) {
                            attachmentIds.push(attachment.id);
                        }
                    });
                }
                
                // 添加附件ID数组
                tableData[i].attachment_ids = attachmentIds;
            }
            
            formData.details = tableData;
            
            // 根据是否有订单ID决定使用新增还是编辑接口
            var saveUrl = currentOrderId ? '/customer/order/edit?id=' + currentOrderId : '/customer/order/add';
            
            console.log("保存订单数据:", formData, "使用接口:", saveUrl);
            
            tool.post(saveUrl, formData, function(res){
                if(res.code === 0){
                    // 如果是新增，保存返回的订单ID
                    if (!currentOrderId && res.data && res.data.order_id) {
                        currentOrderId = res.data.order_id;
                    }
                    
                    layer.msg('保存成功', {icon: 1}, function(){
                        // 检查是否在弹窗中
                        if (typeof refreshParent === 'function') {
                            // 在弹窗中，刷新父页面并关闭当前弹窗
                            refreshParent();
                        } else {
                            // 不在弹窗中，使用原来的跳转逻辑
                            window.location.href = '/customer/order/view?id=' + (currentOrderId || res.data.order_id);
                        }
                    });
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            });
            return false;
        });
        
        // 保存并提交
        form.on('submit(submitOrder)', function(data){
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            if(tableData.length === 0){
                layer.msg('请添加商品明细', {icon: 2});
                return false;
            }
            
            var formData = data.field;
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据，将每行的税率设置为全局税率
            for(var i = 0; i < tableData.length; i++){
                tableData[i].tax_rate = globalTaxRate;
                // 重新计算税额和价税合计
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
            }
            
            formData.items = tableData;
            formData.status = 1; // 设置为已提交状态
            
            tool.post('/customer/order/add', formData, function(res){
                if(res.code === 0){
                    layer.msg('提交成功', {icon: 1});
                    tool.close();
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            });
            return false;
        });
        
        // 选择和上传文件的按钮点击处理
        $('#btnUpload').on('click', function() {
            // 存储当前编辑的索引到localStorage
            if (typeof currentEditIndex !== 'undefined' && currentEditIndex >= 0) {
                localStorage.setItem('currentEditIndex', currentEditIndex);
                console.log("上传前已保存currentEditIndex到localStorage:", currentEditIndex);
            } else if (typeof window.currentProductIndex !== 'undefined' && window.currentProductIndex >= 0) {
                localStorage.setItem('currentEditIndex', window.currentProductIndex);
                console.log("上传前已保存window.currentProductIndex到localStorage:", window.currentProductIndex);
            }
        });
        
        // 删除商品及其子商品
        function deleteProductWithChildren(obj) {
            var data = obj.data;
            var tableData = table.cache.productTable || [];
            
            // 添加调试信息
            console.log("删除父商品及其子商品 - 父商品:", data.product_name, "product_id:", data.product_id);
            
            if (!data.product_id) {
                console.error("错误：要删除的父商品没有product_id");
                layer.msg('无法删除商品：缺少商品标识符', {icon: 2});
                return;
            }
            
            // 创建新数组，排除要删除的商品及其子商品（使用product_id和parent_product_id）
            var newData = tableData.filter(function(item) {
                var keepItem = item.product_id !== data.product_id && 
                               item.parent_product_id !== data.product_id;
                               
                // 添加调试信息
                if (!keepItem) {
                    console.log("将删除商品:", item.product_name, 
                                "product_id:", item.product_id, 
                                "parent_product_id:", item.parent_product_id);
                }
                
                return keepItem;
            });
            
            // 添加调试信息
            console.log("删除操作前表格数据长度:", tableData.length);
            console.log("删除操作后表格数据长度:", newData.length);
            console.log("删除操作移除了", tableData.length - newData.length, "个商品");
            
            // 重载表格数据
            table.reload('productTable', {
                data: newData
            });
            
            layer.msg('商品及其子商品已删除', {icon: 1});
        }
        
        // 只删除单个商品（不删除子商品）
        function deleteProductSingle(obj) {
            var data = obj.data;
            var tableData = table.cache.productTable || [];
            
            // 添加调试信息：删除前表格数据
            console.log("删除前表格数据长度:", tableData.length);
            console.log("删除前表格数据:", JSON.stringify(tableData));
            console.log("要删除的商品:", data.product_name, "product_id:", data.product_id, "parent_product_id:", data.parent_product_id);
            
            if (!data.product_id) {
                console.error("错误：要删除的商品没有product_id");
                layer.msg('无法删除商品：缺少商品标识符', {icon: 2});
                return;
            }
            
            // 创建新数组，使用复合条件来精确识别要删除的商品
            var newData = tableData.filter(function(item) {
                // 如果当前商品与要删除的商品有不同的product_id，则保留
                if (item.product_id !== data.product_id) {
                    return true;
                }
                
                // 如果product_id相同，但是我们要删除的是子商品（有parent_product_id）
                // 则只删除parent_product_id也相同的项（即同一父商品下的特定子商品）
                if (data.parent_product_id) {
                    var shouldKeep = item.parent_product_id !== data.parent_product_id;
                    console.log("相同product_id的子商品:", item.product_name, 
                                "parent_product_id:", item.parent_product_id,
                                "要删除的parent_product_id:", data.parent_product_id,
                                "是否保留:", shouldKeep);
                    return shouldKeep;
                }
                
                // 如果要删除的不是子商品，而是普通商品，则删除product_id相同的项
                return false;
            });
            
            // 添加调试信息：删除后的新数据
            console.log("删除后新数据长度:", newData.length);
            console.log("删除操作移除了", tableData.length - newData.length, "个商品");
            
            // 强制添加额外的安全检查，确保只删除选中商品
            if (tableData.length - newData.length !== 1) {
                console.warn("警告：应该只删除1个商品，但实际删除了", tableData.length - newData.length, "个商品");
                
                // 如果没有删除任何商品，可能是匹配条件有问题
                if (tableData.length === newData.length) {
                    console.error("错误：未能删除任何商品，可能是匹配条件不正确");
                    layer.msg('删除失败：未找到匹配的商品', {icon: 2});
                    return;
                }
            }
            
            // 重载表格数据
            table.reload('productTable', {
                data: newData
            });
            
            layer.msg('商品已删除', {icon: 1});
        }
        
        // 添加子商品
        function addChildProduct(parentData) {
            // 使用product_id而非LAY_TABLE_INDEX
            var parentId = parentData.product_id;
            
            if (!parentId) {
                console.error("错误：父商品没有product_id");
                layer.msg('无法添加子商品：父商品标识符缺失', {icon: 2});
                return;
            }
            
            // 保存父商品数据到全局变量，以便全局的addChildToParent函数可以访问
            window.currentParentData = parentData;
            
            // 打开商品选择弹窗，传递product_id而非索引
            tool.side('selectChildProduct?parent_product_id=' + parentId, {
                title: '选择子商品',
                width: '100%',
                success: function(layero, index){
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var table = iframeWindow.layui.table;
                    
                    // 设置父商品数据，供子页面使用
                    iframeWindow.parentProductData = parentData;
                }
            });
        }
        
        // 更新子项数量的辅助函数
        function updateChildrenQuantities(parentData) {
            if(!parentData || !parentData.product_id) return;
            
            var tableData = table.cache.productTable || [];
            var hasUpdates = false;
            
            // 父项数量
            var parentQuantity = parseFloat(parentData.quantity) || 0;
            
            console.log("更新子项数量 - 父项:", parentData.product_name, 
                      "父项ID:", parentData.product_id, 
                      "父项数量:", parentQuantity);
            
            // 记录子项更新前后的数据，用于调试
            var updateLog = [];
            
            // 遍历所有行，查找并更新子项
            for(var i = 0; i < tableData.length; i++) {
                var item = tableData[i];
                
                // 找到对应的子项
                if(item.is_child && item.parent_product_id === parentData.product_id) {
                    var oldQuantity = item.quantity;
                    // 使用子项自己的倍数
                    var childMultiplier = parseFloat(item.multiplier) || 1;
                    
                    // 计算新数量 = 父项数量 × 子项倍数
                    var newQuantity = parentQuantity * childMultiplier;
                    
                    // 更新数量和金额
                    item.quantity = newQuantity;
                    
                    // 重新计算金额
                    item.amount = (item.quantity * item.price).toFixed(2);
                    item.tax_amount = (item.amount * item.tax_rate / 100).toFixed(2);
                    item.total_amount = (parseFloat(item.amount) + parseFloat(item.tax_amount)).toFixed(2);
                    
                    updateLog.push({
                        name: item.product_name,
                        oldQuantity: oldQuantity,
                        newQuantity: newQuantity,
                        multiplier: childMultiplier
                    });
                    
                    hasUpdates = true;
                }
            }
            
            // 输出更新日志
            if(updateLog.length > 0) {
                console.log("子项数量更新明细:", updateLog);
            }
            
            // 如果有更新，重新加载表格
            if(hasUpdates) {
                console.log("有子项更新，重新加载表格");
                
                // 强制刷新表格 - 重建整个表格数据
                table.reload('productTable', {
                    data: tableData,
                    done: function() {
                        console.log("表格重载完成，子项数量已更新");
                        // 重新应用样式
                        styleChildProducts();
                        
                        // 确保删除按钮显示
                        setTimeout(function() {
                            $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
                                $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
                                $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
                            });
                        }, 200);
                    }
                });
                
                // 重新计算总金额
                calculateTotal();
            } else {
                console.log("没有找到需要更新的子项");
            }
        }
    }
</script>
{/block} 