{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar border-x" id="searchForm">
        <div class="layui-input-inline" style="width:180px;">
            <input type="text" name="order_no" placeholder="输入订单编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:180px;">
            <select name="customer_id">
                <option value="">选择客户</option>
                {volist name=":get_customer_list()" id="vo"}
                <option value="{$vo.id}">{$vo.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search">
                <i class="layui-icon layui-icon-search mr-1"></i>搜索
            </button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    
    <table class="layui-hide" id="orderTable" lay-filter="orderTable"></table>
    
    <div style="text-align: center; padding: 20px;">
        <button type="button" class="layui-btn" id="confirmBtn">确认选择</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus', 'form'];
    function gouguInit() {
        var table = layui.tablePlus, 
            form = layui.form, 
            tool = layui.tool,
            $ = layui.jquery;
        
        var selected = []; // 存储选中的订单
        
        // 初始化表格
        var tableIns = table.render({
            elem: '#orderTable',
            url: '/customer/delivery/selectOrder',
            page: true,
            limit: 20,
            cellMinWidth: 80,
            height: 'full-200',
            cols: [[
                {type: 'radio', fixed: 'left'},
                {field: 'id', title: 'ID', width: 80, sort: true},
                {field: 'order_no', title: '订单编号', width: 160},
                {field: 'customer_name', title: '客户名称', width: 200, templet: function(d) {
                    return d.customer ? d.customer.name : '';
                }},
                {field: 'order_date', title: '订单日期', width: 120},
                {field: 'delivery_date', title: '交货日期', width: 120},
                {field: 'total_amount', title: '订单金额', width: 120, templet: function(d) {
                    return '￥' + (d.total_amount || 0);
                }},
                {field: 'status_text', title: '状态', width: 100, align: 'center', templet: function(d) {
                    var statusMap = {
                        1: '<span class="layui-badge layui-bg-blue">已审核</span>',
                        2: '<span class="layui-badge layui-bg-orange">部分发货</span>',
                        3: '<span class="layui-badge layui-bg-green">待补货</span>'
                    };
                    return statusMap[d.status] || '<span class="layui-badge layui-bg-gray">未知</span>';
                }},
                {field: 'has_shippable', title: '可发货', width: 100, align: 'center', templet: function(d) {
                    return d.has_shippable ? '<span class="layui-badge layui-bg-green">是</span>' : '<span class="layui-badge layui-bg-gray">否</span>';
                }}
            ]]
        });
        
        // 监听单选框选择
        table.on('radio(orderTable)', function(obj) {
            selected = [obj.data]; // 单选，只保存一个
        });
        
        // 搜索功能
        form.on('submit(table-search)', function(data) {
            tableIns.reload({
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });
        
        // 重置搜索
        form.on('submit(table-reset)', function() {
            $('#searchForm')[0].reset();
            tableIns.reload({
                where: {},
                page: {curr: 1}
            });
            return false;
        });
        
        // 确认选择
        $('#confirmBtn').on('click', function() {
            if (selected.length === 0) {
                layer.msg('请选择一个订单', {icon: 2});
                return;
            }
            
            var order = selected[0];
            if (!order.has_shippable) {
                layer.msg('该订单没有可发货的商品', {icon: 2});
                return;
            }
            
            // 调用父窗口回调函数
            if (window.parent && window.parent._layui_layer_callback) {
                window.parent._layui_layer_callback(selected);
                
                // 关闭当前窗口
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            } else {
                console.error('父窗口回调函数未定义');
                layer.msg('操作失败：无法与父窗口通信', {icon: 2});
            }
        });
        
        // 取消按钮
        $('#cancelBtn').on('click', function() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        });
        
        // 双击行选择
        table.on('rowDouble(orderTable)', function(obj) {
            if (!obj.data.has_shippable) {
                layer.msg('该订单没有可发货的商品', {icon: 2});
                return;
            }
            
            selected = [obj.data];
            $('#confirmBtn').click();
        });
    }
</script>
{/block}
