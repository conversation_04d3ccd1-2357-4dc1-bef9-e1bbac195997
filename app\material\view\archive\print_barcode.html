<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>打印二维码</title>
    <style>
        @media print {
            @page {
                margin: 0;
                size: auto;
            }
            body {
                margin: 0;
                padding: 0;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .print-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .barcode-label {
            width: 400px;
            height: 250px;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px auto;
            display: flex;
            background: white;
            page-break-inside: avoid;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        .label-left {
            flex: 1;
            padding-right: 15px;
        }

        .label-right {
            width: 140px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .label-info {
            font-size: 13px;
            line-height: 1.8;
            margin-bottom: 4px;
            color: #333;
        }

        .label-info strong {
            display: inline-block;
            width: 70px;
            font-weight: bold;
            color: #000;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        
        .page-info {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        .print-buttons {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-buttons no-print">
            <button class="btn" onclick="printLabel()">打印</button>
            <button class="btn btn-secondary" onclick="window.close()">关闭</button>
        </div>

        <div class="barcode-label">
            <div class="label-left">
                <div class="label-info"><strong>物料名称:</strong> {$material.title}</div>
                <div class="label-info"><strong>物料编号:</strong> {$material.material_code}</div>
                <div class="label-info"><strong>物料规格:</strong> {$material.specs|default='-'}</div>
                <div class="label-info"><strong>单位:</strong> {$material.unit|default='-'}</div>
                <div class="label-info"><strong>打印时间:</strong> {$printTime}</div>
                <div class="label-info"><strong>备注:</strong></div>
            </div>
            <div class="label-right">
                <img src="{$qrImageUrl}" alt="二维码" class="qr-code" />
                <div class="page-info">1-1</div>
            </div>
        </div>
    </div>

    <script>
        function printLabel() {
            // 获取标签内容
            var labelContent = document.querySelector('.barcode-label');

            // 创建新窗口用于打印
            var printWindow = window.open('', '_blank', 'width=800,height=600');

            // 构建打印页面内容
            var printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>打印标签</title>
                    <style>
                        @page {
                            margin: 0;
                            size: auto;
                        }
                        body {
                            margin: 0;
                            padding: 20px;
                            font-family: "Microsoft YaHei", Arial, sans-serif;
                            width: 100vw;
                            height: 100vh;
                            box-sizing: border-box;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .barcode-label {
                            width: 800px;
                            height: 600px;
                            border: none;
                            padding: 40px;
                            display: flex;
                            background: white;
                            box-sizing: border-box;
                        }
                        .label-left {
                            flex: 1;
                            padding-right: 30px;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                        }
                        .label-right {
                            width: 250px;
                            text-align: center;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                        }
                        .label-info {
                            font-size: 24px;
                            line-height: 1.8;
                            margin-bottom: 15px;
                            color: #333;
                        }
                        .label-info strong {
                            display: inline-block;
                            width: 120px;
                            font-weight: bold;
                            color: #000;
                        }
                        .qr-code {
                            width: 180px;
                            height: 180px;
                            border: none;
                            margin-bottom: 15px;
                        }
                        .page-info {
                            text-align: center;
                            font-size: 18px;
                            color: #666;
                            margin-top: 15px;
                        }
                    </style>
                </head>
                <body>
                    ${labelContent.outerHTML}
                </body>
                </html>
            `;

            // 写入内容到新窗口
            printWindow.document.write(printContent);
            printWindow.document.close();

            // 等待内容加载完成后打印
            printWindow.onload = function() {
                setTimeout(function() {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };
        }
    </script>
</body>
</html>
