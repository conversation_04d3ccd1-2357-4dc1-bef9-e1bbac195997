{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>领料单详情 - {$request.request_no}</h3>
        </div>
        <div class="layui-card-body">
                    <!-- 基本信息 -->
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">基本信息</div>
                                <div class="layui-card-body">
                                    <div class="layui-row">
                                        <div class="layui-col-md3">
                                            <strong>领料单号：</strong>{$request.request_no}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>生产订单号：</strong>{$request.production_order_no}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>产品名称：</strong>{$request.product_name}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>生产数量：</strong>{$request.production_quantity}
                                        </div>
                                    </div>
                                    <div class="layui-row" style="margin-top: 10px;">
                                        <div class="layui-col-md3">
                                            <strong>领料类型：</strong>
                                            {switch name="request.request_type"}
                                                {case value="1"}正常领料{/case}
                                                {case value="2"}补料{/case}
                                                {case value="3"}超领{/case}
                                                {default /}未知
                                            {/switch}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>物料种类：</strong>{$request.total_items}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>领料总量：</strong>{$request.total_quantity}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>超领标识：</strong>
                                            {if condition="$request.has_excess == 1"}
                                                <span class="layui-badge layui-bg-red">有超领({$request.excess_items}项)</span>
                                            {else /}
                                                <span class="layui-badge layui-bg-green">正常</span>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="layui-row" style="margin-top: 10px;">
                                        <div class="layui-col-md3">
                                            <strong>状态：</strong>
                                            {switch name="request.status"}
                                                {case value="0"}<span class="layui-badge layui-bg-orange">待审核</span>{/case}
                                                {case value="1"}<span class="layui-badge layui-bg-blue">已审核</span>{/case}
                                                {case value="2"}<span class="layui-badge layui-bg-green">已出库</span>{/case}
                                                {case value="3"}<span class="layui-badge layui-bg-cyan">已完成</span>{/case}
                                                {case value="4"}<span class="layui-badge layui-bg-gray">已取消</span>{/case}
                                                {default /}<span class="layui-badge">未知</span>
                                            {/switch}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>出库仓库：</strong>{$request.warehouse_name}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>创建人：</strong>{$request.created_name}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>创建时间：</strong>{$request.create_time|date='Y-m-d H:i:s'}
                                        </div>
                                    </div>
                                    {if condition="$request.approved_name"}
                                    <div class="layui-row" style="margin-top: 10px;">
                                        <div class="layui-col-md3">
                                            <strong>审核人：</strong>{$request.approved_name}
                                        </div>
                                        <div class="layui-col-md3">
                                            <strong>审核时间：</strong>{$request.approve_time|date='Y-m-d H:i:s'}
                                        </div>
                                        <div class="layui-col-md6">
                                            <strong>审核备注：</strong>{$request.approve_notes|default='无'}
                                        </div>
                                    </div>
                                    {/if}
                                    {if condition="$request.notes"}
                                    <div class="layui-row" style="margin-top: 10px;">
                                        <div class="layui-col-md12">
                                            <strong>备注说明：</strong>{$request.notes}
                                        </div>
                                    </div>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 物料明细 -->
                    <div class="layui-row" style="margin-top: 15px;">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">物料明细</div>
                                <div class="layui-card-body">
                                    <table class="layui-table">
                                        <thead>
                                            <tr>
                                                <th>序号</th>
                                                <th>物料编码</th>
                                                <th>物料名称</th>
                                                <th>规格</th>
                                                <th>单位</th>
                                                <th>BOM用量</th>
                                                <th>标准需求</th>
                                                <th>申请数量</th>
                                                <th>超领数量</th>
                                                <th>可用库存</th>
                                                <th>实际出库</th>
                                                <th>超领状态</th>
                                                <th>备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {volist name="details" id="detail" key="k"}
                                            <tr>
                                                <td>{$k}</td>
                                                <td>{$detail.material_code}</td>
                                                <td>{$detail.material_name}</td>
                                                <td>{$detail.material_specs}</td>
                                                <td>{$detail.unit}</td>
                                                <td class="layui-text-right">{$detail.bom_quantity}</td>
                                                <td class="layui-text-right">{$detail.standard_quantity}</td>
                                                <td class="layui-text-right">{$detail.request_quantity}</td>
                                                <td class="layui-text-right">
                                                    {if condition="$detail.excess_quantity > 0"}
                                                        <span style="color: red; font-weight: bold;">+{$detail.excess_quantity}</span>
                                                    {else /}
                                                        0.0000
                                                    {/if}
                                                </td>
                                                <td class="layui-text-right">{$detail.available_stock}</td>
                                                <td class="layui-text-right">{$detail.actual_quantity}</td>
                                                <td class="layui-text-center">
                                                    {if condition="$detail.is_excess == 1"}
                                                        <span class="layui-badge layui-bg-red">超领</span>
                                                    {else /}
                                                        <span class="layui-badge layui-bg-green">正常</span>
                                                    {/if}
                                                </td>
                                                <td>{$detail.notes}</td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

               
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool, layer = layui.layer;

        // 审核领料单
        window.approveRequest = function(id) {
        layer.prompt({
            title: '审核领料单',
            formType: 2,
            area: ['400px', '200px'],
            value: '审核通过'
        }, function(value, index) {
            tool.post('/Produce/MaterialRequest/approve', {
                id: id,
                approve_notes: value
            }, function(res) {
                if (res.code === 0) {
                    layer.msg('审核成功');
                    setTimeout(function() {
                        parent.location.reload();
                    }, 1500);
                } else {
                    layer.msg(res.msg);
                }
            });
            layer.close(index);
        });
        };

        // 拒绝领料单
        window.rejectRequest = function(id) {
        layer.prompt({
            title: '拒绝原因',
            formType: 2,
            area: ['400px', '200px'],
            value: ''
        }, function(value, index) {
            if (!value.trim()) {
                layer.msg('请填写拒绝原因');
                return;
            }
            
            tool.post('/Produce/MaterialRequest/reject', {
                id: id,
                reject_notes: value
            }, function(res) {
                if (res.code === 0) {
                    layer.msg('已拒绝');
                    setTimeout(function() {
                        parent.location.reload();
                    }, 1500);
                } else {
                    layer.msg(res.msg);
                }
            });
            layer.close(index);
        });
        };

        // 执行出库
        window.executeOutbound = function(id) {
        layer.confirm('确定要执行出库操作吗？', {icon: 3, title: '提示'}, function(index) {
            // TODO: 实现出库逻辑
            layer.msg('出库功能待实现');
            layer.close(index);
        });
        };
    }
</script>
{/block}
