{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb" lay-separator=">">
            <a href="/Produce/process/index">工序管理</a>
            <a><cite>工序详情</cite></a>
        </span>
        <div class="layui-card-header-auto">
            <a class="layui-btn layui-btn-sm" href="/Produce/process/add?id={$detail.id}">
                <i class="layui-icon layui-icon-edit"></i> 编辑工序
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <table class="layui-table" lay-skin="nob">
                    <colgroup>
                        <col width="120">
                        <col>
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-bg-gray">工序编号</td>
                            <td>
                                <span class="layui-badge layui-bg-gray">{$detail.code|default='系统自动生成'}</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">工序名称</td>
                            <td><strong>{$detail.name}</strong></td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">工作组</td>
                            <td>{$detail.group_name|default='未分配'}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">报工用户</td>
                            <td>
                                {if condition="$detail.report_user_names != '未指定'"}
                                    {$detail.report_user_names}
                                {else /}
                                    <span class="text-muted">未指定</span>
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">计价方式</td>
                            <td>
                                {if condition="$detail.pricing_method == 1"}
                                <span class="layui-badge layui-bg-blue">按件计价</span>
                                {else /}
                                <span class="layui-badge layui-bg-orange">按时计价</span>
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">标准单价</td>
                            <td><span class="text-success">¥{$detail.standard_price|number_format=2}</span></td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">标准效率</td>
                            <td><span class="text-info">{$detail.efficiency|number_format=2}/小时</span></td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">数量单比</td>
                            <td>{$detail.quantity_ratio|number_format=2}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">工序序号</td>
                            <td>{$detail.serial_number|default='未设置'}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">创建时间</td>
                            <td>{$detail.create_time}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">更新时间</td>
                            <td>{$detail.update_time}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">工序统计</div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-xs6">
                                <div class="text-center">
                                    <div class="text-muted">本月使用次数</div>
                                    <div class="text-main" style="font-size: 24px; font-weight: bold;">0</div>
                                </div>
                            </div>
                            <div class="layui-col-xs6">
                                <div class="text-center">
                                    <div class="text-muted">累计使用次数</div>
                                    <div class="text-main" style="font-size: 24px; font-weight: bold;">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {if condition="!empty($detail.description)"}
        <div class="layui-card">
            <div class="layui-card-header">工序描述</div>
            <div class="layui-card-body">
                <div class="layui-text">
                    {$detail.description|nl2br}
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = [];
    function gouguInit() {
        // 页面初始化完成
    }
</script>
{/block}