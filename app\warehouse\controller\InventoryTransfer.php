<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\model\InventoryTransfer as InventoryTransferModel;
use app\warehouse\service\InventoryTransferService;
use think\response\Json;
use think\facade\Request;
use think\facade\View;

/**
 * 仓库间调拨控制器
 */
class InventoryTransfer extends BaseController
{
    /**
     * 模型对象
     * @var InventoryTransferModel
     */
    protected $model = null;
    
    /**
     * 服务对象
     * @var InventoryTransferService
     */
    protected $service = null;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new InventoryTransferModel();
        $this->service = new InventoryTransferService();
    }
    
    /**
     * 调拨单列表
     */
    public function index()
    {
        if (Request::isAjax()) {
            // 搜索条件
            $filter = Request::get();
            
            $list = $this->model
                ->with(['fromWarehouse', 'toWarehouse', 'creator'])
                ->withSearch(['from_warehouse_id', 'to_warehouse_id', 'status', 'time_range', 'keywords'], $filter)
                ->order('id desc')
                ->paginate([
                    'list_rows' => $filter['limit'] ?? 10,
                    'query' => $filter
                ]);
            
            return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
        }

        return View::fetch('inventory_transfer/index');
    }
    
    /**
     * 创建调拨单
     */
    public function add()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证主表数据
            $validate = [
                'from_warehouse_id' => 'require|integer|gt:0',
                'to_warehouse_id' => 'require|integer|gt:0',
                'details' => 'require|array'
            ];
            
            $this->validate($params, $validate);
            
            // 验证明细数据
            if (empty($params['details'])) {
                return json(['code' => 1, 'msg' => '调拨明细不能为空']);
            }
            
            foreach ($params['details'] as $detail) {
                if (empty($detail['product_id']) || empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return json(['code' => 1, 'msg' => '调拨明细数据不完整']);
                }

                // 验证库存是否充足
                $inventory = \think\facade\Db::name('oa_inventory_realtime')
                    ->where([
                        ['product_id', '=', $detail['product_id']],
                        ['warehouse_id', '=', $params['from_warehouse_id']]
                    ])
                    ->find();

                $availableQuantity = $inventory ? $inventory['available_quantity'] : 0;

                if ($detail['quantity'] > $availableQuantity) {
                    // 获取产品名称
                    $productName = \think\facade\Db::name('oa_product')
                        ->where('id', $detail['product_id'])
                        ->value('title');
                    if (!$productName) {
                        $productName = \think\facade\Db::name('product')
                            ->where('id', $detail['product_id'])
                            ->value('title');
                    }

                    return json([
                        'code' => 1,
                        'msg' => "产品 \"{$productName}\" 调拨数量({$detail['quantity']})超过可用库存({$availableQuantity})"
                    ]);
                }
            }
            
            try {
                $transferData = [
                    'from_warehouse_id' => $params['from_warehouse_id'],
                    'to_warehouse_id' => $params['to_warehouse_id'],
                    'notes' => $params['notes'] ?? '',
                    'created_by' => $this->uid
                ];
                
                $transfer = $this->service->createTransfer($transferData, $params['details']);
                
                return json(['code' => 0, 'msg' => '创建成功', 'data' => $transfer]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('inventory_transfer/add');
    }

    /**
     * 调拨单详情
     */
    public function detail()
    {
        $id = Request::param('id');
        
        try {
            $detail = $this->service->getTransferDetail($id);
            
            if (Request::isAjax()) {
                return json(['code' => 0, 'msg' => '查询成功', 'data' => $detail]);
            }

            View::assign('detail', $detail);
            return View::fetch('inventory_transfer/detail');
        } catch (\Exception $e) {
            if (Request::isAjax()) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 审核调拨单
     */
    public function approve()
    {
        if (Request::isPost()) {
            $id = Request::param('id');

            try {
                $this->service->approveTransfer($id, $this->uid);
                return json(['code' => 0, 'msg' => '审核成功']);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('inventory_transfer/approve');
    }

    /**
     * 执行调拨
     */
    public function execute()
    {
        if (Request::isPost()) {
            $id = Request::param('id');

            try {
                $this->service->executeTransfer($id, $this->uid);
                return json(['code' => 0, 'msg' => '执行成功']);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('inventory_transfer/execute');
    }

    /**
     * 取消调拨单
     */
    public function cancel()
    {
        if (Request::isPost()) {
            $id = Request::param('id');

            try {
                $this->service->cancelTransfer($id, $this->uid);
                return json(['code' => 0, 'msg' => '取消成功']);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('inventory_transfer/cancel');
    }

    /**
     * 批量审核
     */
    public function batchApprove()
    {
        if (Request::isPost()) {
            $ids = Request::param('ids');
            
            if (empty($ids)) {
                return json(['code' => 1, 'msg' => '请选择要审核的调拨单']);
            }
            
            $ids = is_array($ids) ? $ids : explode(',', $ids);
            
            try {
                $results = $this->service->batchApprove($ids, $this->uid);
                
                $successCount = 0;
                $failCount = 0;
                $messages = [];
                
                foreach ($results as $id => $result) {
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failCount++;
                        $messages[] = "调拨单{$id}: {$result['message']}";
                    }
                }
                
                $msg = "成功审核{$successCount}个调拨单";
                if ($failCount > 0) {
                    $msg .= "，失败{$failCount}个：" . implode('；', $messages);
                }
                
                return json(['code' => 0, 'msg' => $msg, 'data' => $results]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return View::fetch('inventory_transfer/index');
    }

    /**
     * 批量执行
     */
    public function batchExecute()
    {
        if (Request::isPost()) {
            $ids = Request::param('ids');
            
            if (empty($ids)) {
                return json(['code' => 1, 'msg' => '请选择要执行的调拨单']);
            }
            
            $ids = is_array($ids) ? $ids : explode(',', $ids);
            
            try {
                $results = $this->service->batchExecute($ids, $this->uid);
                
                $successCount = 0;
                $failCount = 0;
                $messages = [];
                
                foreach ($results as $id => $result) {
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failCount++;
                        $messages[] = "调拨单{$id}: {$result['message']}";
                    }
                }
                
                $msg = "成功执行{$successCount}个调拨单";
                if ($failCount > 0) {
                    $msg .= "，失败{$failCount}个：" . implode('；', $messages);
                }
                
                return json(['code' => 0, 'msg' => $msg, 'data' => $results]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return View::fetch('inventory_transfer/batch_execute');
    }
    
    /**
     * 调拨统计
     */
    public function statistics()
    {
        if (Request::isAjax()) {
            $params = Request::get();

            try {
                $statistics = $this->service->getTransferStatistics($params);
                return json(['code' => 0, 'msg' => '查询成功', 'data' => $statistics]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        return View::fetch('inventory_transfer/statistics');
    }

    /**
     * 导出调拨单
     */
    public function export()
    {
        $filter = Request::get();

        $list = $this->model
            ->with(['fromWarehouse', 'toWarehouse', 'creator', 'details.product'])
            ->withSearch(['from_warehouse_id', 'to_warehouse_id', 'status', 'time_range', 'keywords'], $filter)
            ->order('id desc')
            ->select();

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                '调拨单号' => $item->transfer_no,
                '源仓库' => $item->fromWarehouse->name ?? '',
                '目标仓库' => $item->toWarehouse->name ?? '',
                '状态' => $item->status_text,
                '总金额' => $item->total_amount,
                '创建人' => $item->creator->nickname ?? '',
                '创建时间' => date('Y-m-d H:i:s', $item->create_time),
                '备注' => $item->notes
            ];
        }

        // 这里可以使用 PhpSpreadsheet 或其他库来生成 Excel 文件
        // 简化处理，返回 CSV 格式
        $filename = '调拨单_' . date('YmdHis') . '.csv';

        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $output = fopen('php://output', 'w');

        // 写入 BOM 头，解决中文乱码
        fwrite($output, "\xEF\xBB\xBF");

        // 写入表头
        if (!empty($data)) {
            fputcsv($output, array_keys($data[0]));

            // 写入数据
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }

        fclose($output);
        exit;
    }

    /**
     * 获取产品列表（AJAX搜索）
     */
    public function getProducts()
    {
        if (Request::isAjax()) {
            $keyword = Request::param('keyword', '', 'trim');
            $warehouseId = Request::param('warehouse_id', 0);

            $where = [
                ['status', '=', 1],
                ['delete_time', '=', 0]
            ];

            if (!empty($keyword)) {
                $where[] = ['title|material_code', 'like', '%' . $keyword . '%'];
            }

            // 先尝试 product 表
            try {
                $products = \think\facade\Db::name('product')
                    ->where($where)
                    ->field('id, title, material_code, specs, unit, purchase_price as cost_price')
                    ->limit(20)
                    ->order('id desc')
                    ->select();
            } catch (\Exception $e) {
                // 如果失败，尝试 oa_product 表
                try {
                    $products = \think\facade\Db::name('oa_product')
                        ->where($where)
                        ->field('id, title, material_code, specs, unit, purchase_price as cost_price')
                        ->limit(20)
                        ->order('id desc')
                        ->select();
                } catch (\Exception $e2) {
                    return json(['code' => 1, 'msg' => '产品表不存在或无数据: ' . $e2->getMessage()]);
                }
            }

            // 如果指定了仓库ID，获取库存信息
            if ($warehouseId > 0) {
                foreach ($products as &$product) {
                    try {
                        $inventory = \think\facade\Db::name('oa_inventory_realtime')
                            ->where([
                                ['product_id', '=', $product['id']],
                                ['warehouse_id', '=', $warehouseId]
                            ])
                            ->value('available_quantity');

                        $product['available_quantity'] = $inventory ?: 0;
                    } catch (\Exception $e) {
                        $product['available_quantity'] = 0;
                    }
                }
            }

            return json(['code' => 0, 'data' => $products]);
        }

        return json(['code' => 1, 'msg' => '请求方式错误']);
    }

    /**
     * 获取仓库列表
     */
    public function getWarehouses()
    {
        if (Request::isAjax()) {
            // 先尝试 warehouse 表
            try {
                $warehouses = \think\facade\Db::name('warehouse')
                    ->where('status', 1)
                    ->field('id, name')
                    ->order('id asc')
                    ->select();

                return json(['code' => 0, 'data' => $warehouses]);
            } catch (\Exception $e) {
                // 如果失败，尝试 oa_warehouse 表
                try {
                    $warehouses = \think\facade\Db::name('oa_warehouse')
                        ->where('status', 1)
                        ->field('id, name')
                        ->order('id asc')
                        ->select();

                    return json(['code' => 0, 'data' => $warehouses]);
                } catch (\Exception $e2) {
                    return json(['code' => 1, 'msg' => '仓库表不存在或无数据: ' . $e2->getMessage()]);
                }
            }
        }

        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
}
