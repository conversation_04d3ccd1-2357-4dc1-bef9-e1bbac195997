<?php
namespace app\material\model;

use think\Model;

class Archive extends Model
{
    protected $table = 'oa_product';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'title'                 => 'string',
        'cate_id'              => 'int',
        'thumb'                => 'int',
        'material_code'         => 'string',
        'barcode'              => 'string',
        'unit'                 => 'string',
        'specs'                => 'string',
        'brand'                => 'string',
        'producer'             => 'string',
        'base_price'           => 'decimal',
        'purchase_price'       => 'decimal',
        'sale_price'           => 'decimal',
        'purchase_cycle'       => 'int',
        'description'          => 'text',
        'file_ids'             => 'string',
        'stock'                => 'int',
        'status'               => 'tinyint',
        'admin_id'             => 'int',
        'create_time'          => 'int',
        'update_time'          => 'int',
        'update_id'            => 'int',
        'delete_time'          => 'int',
        'source_type'          => 'int',
        'code'                 => 'string',
        'type'                 => 'int',
        // 新增字段
        'default_warehouse'    => 'int',
        'min_order_qty'        => 'decimal',
        'min_package_qty'      => 'decimal',
        'material_level'       => 'string',
        'material_source'      => 'string',
        'category'             => 'string',
        'model'                => 'string',
        'color'                => 'string',
        'remark'               => 'text',
        'material_images'      => 'text',
        'material_drawing'     => 'string',
        'quality_management'   => 'tinyint',
        'quality_exempt'       => 'tinyint',
        'quality_settings'     => 'text',
        'reference_cost'       => 'decimal',
        'sales_price'          => 'decimal',
        'min_sales_price'      => 'decimal',
        'max_sales_price'      => 'decimal',
        'attachments'          => 'text',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = false;
    
    // JSON字段处理
    protected $json = ['quality_settings', 'material_images', 'attachments'];
    
    // 关联物料分类
    public function category()
    {
        return $this->belongsTo(Category::class, 'cate_id', 'id');
    }

    // 关联默认仓库
    public function warehouse()
    {
        return $this->belongsTo(\app\warehouse\model\Warehouse::class, 'default_warehouse', 'id');
    }

    // 关联基本单位
    public function unitInfo()
    {
        return $this->belongsTo(\app\common\model\Unit::class, 'unit', 'name');
    }
    
    // 获取器 - 格式化创建时间
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化更新时间
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }
    
    // 获取器 - 格式化质量设置
    public function getQualitySettingsAttr($value)
    {
        if (is_string($value) && !empty($value)) {
            return json_decode($value, true) ?: [];
        }
        return [];
    }
    
    // 修改器 - 质量设置
    public function setQualitySettingsAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value);
        }
        return $value;
    }

    // 获取器 - 格式化物料图片
    public function getMaterialImagesAttr($value)
    {
        if (is_string($value) && !empty($value)) {
            return json_decode($value, true) ?: [];
        }
        return [];
    }

    // 修改器 - 物料图片
    public function setMaterialImagesAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value);
        }
        return $value;
    }

    // 获取器 - 格式化附件
    public function getAttachmentsAttr($value)
    {
        if (is_string($value) && !empty($value)) {
            return json_decode($value, true) ?: [];
        }
        return [];
    }

    // 修改器 - 附件
    public function setAttachmentsAttr($value)
    {
        if (is_array($value)) {
            return json_encode($value);
        }
        return $value;
    }

}