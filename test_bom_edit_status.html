<!DOCTYPE html>
<html>
<head>
    <title>BOM编辑状态测试</title>
    <style>
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .status-demo {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .status-draft {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .status-approved {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .form-item {
            margin: 10px 0;
        }
        .form-label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .form-input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        .form-input:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
        .btn {
            padding: 5px 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-left: 125px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BOM编辑状态测试</h1>
        
        <h2>功能说明</h2>
        <p>根据BOM的status字段值，决定是否允许修改关联的产成品：</p>
        <ul>
            <li><strong>status = 0 (草稿状态)</strong>：允许修改产品信息</li>
            <li><strong>status = 1 (已审核状态)</strong>：不允许修改产品信息</li>
        </ul>
        
        <div class="status-demo status-draft">
            <h3>草稿状态 BOM (status = 0)</h3>
            <div class="form-item">
                <span class="form-label">BOM编号：</span>
                <input type="text" class="form-input" value="BOM202508020001" />
            </div>
            <div class="form-item">
                <span class="form-label">BOM名称：</span>
                <input type="text" class="form-input" value="S-18音响BOM-复制" />
            </div>
            <div class="form-item">
                <span class="form-label">产品名称：</span>
                <input type="text" class="form-input" value="S-18音响SS6" />
                <button class="btn" onclick="selectProduct()">选择产品</button>
            </div>
            <div class="help-text">草稿状态可修改产品</div>
        </div>
        
        <div class="status-demo status-approved">
            <h3>已审核状态 BOM (status = 1)</h3>
            <div class="form-item">
                <span class="form-label">BOM编号：</span>
                <input type="text" class="form-input" value="BOM202508010001" />
            </div>
            <div class="form-item">
                <span class="form-label">BOM名称：</span>
                <input type="text" class="form-input" value="S-18音响BOM" />
            </div>
            <div class="form-item">
                <span class="form-label">产品名称：</span>
                <input type="text" class="form-input" value="S-18音响SS6" disabled />
                <button class="btn" disabled>选择产品</button>
            </div>
            <div class="help-text">已审核的BOM不可修改产品</div>
        </div>
        
        <h2>实现要点</h2>
        <ol>
            <li><strong>控制器修改</strong>：在edit方法中添加status判断逻辑</li>
            <li><strong>模板修改</strong>：根据BOM status显示不同的产品选择界面</li>
            <li><strong>前端交互</strong>：添加selectProduct函数处理产品选择</li>
            <li><strong>数据验证</strong>：确保只有草稿状态的BOM才能修改产品信息</li>
        </ol>
        
        <h2>数据库字段</h2>
        <p><strong>oa_material_bom表的status字段：</strong></p>
        <ul>
            <li>0 = 草稿</li>
            <li>1 = 已审核</li>
            <li>2 = 已停用</li>
        </ul>
        
        <h2>复制功能</h2>
        <p>复制BOM时，新创建的BOM的status会自动设置为0（草稿状态），这样用户就可以修改复制后的BOM的产品信息。</p>
    </div>
    
    <script>
        function selectProduct() {
            alert('模拟产品选择功能\n实际实现中会打开产品选择弹窗');
        }
    </script>
</body>
</html>
