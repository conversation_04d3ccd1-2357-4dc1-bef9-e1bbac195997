{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部发货指令</li>
                <li>待处理</li>
                <li>处理中</li>
                <li>已完成</li>
                <li>已取消</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:180px;">
            <input type="text" name="delivery_no" placeholder="输入发货单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:180px;">
            <input type="text" name="order_no" placeholder="输入订单编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:180px;">
            <select name="customer_id">
                <option value="">选择客户</option>
                {volist name=":get_customer_list()" id="vo"}
                <option value="{$vo.id}">{$vo.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_delivery" lay-filter="table_delivery"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 创建发货指令</span>
    </button>
  </div>
</script>

<script type="text/html" id="actionBar">
  <div class="layui-btn-group">
    <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</button>
    {{# if(d.status == 0 && (d.outbound_status == 'none' || d.outbound_status == 0 || d.outbound_status == 1 || d.outbound_status == 2)){ }}
    <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="cancel">撤销</button>
    {{# } }}
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus', 'element'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool;
        
        // tab切换
        element.on('tab(tab)', function(data) {
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:data.index == 0 ? '' : (data.index-1)}, page:{curr:1}});
            return false;
        });
        
        // 表格初始化
        layui.pageTable = table.render({
            elem: "#table_delivery"
            ,title: "发货指令列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/customer/delivery/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {field: 'id', title: 'ID', width: 60, align: 'center'},
                {field: 'delivery_no', title: '发货单号', width: 160, align: 'center'},
                {field: 'order_no', title: '订单编号', width: 160, align: 'center'},
                {field: 'customer_name', title: '客户名称', width: 180},
                {field: 'outbound_status_text', title: '出库状态', width: 100, align: 'center', templet: function(d) {
                    return '<span class="' + d.outbound_status_class + '">' + d.outbound_status_text + '</span>';
                }},
                {field: 'outbound_no', title: '出库单号', width: 160, align: 'center', templet: function(d) {
                    if (d.outbound_no) {
                        return '<a href="/warehouse/outbound/detail.html?id=' + d.outbound_id + '" target="_blank" style="color: #1E9FFF;">' + d.outbound_no + '</a>';
                    }
                    return '<span class="layui-text-muted">-</span>';
                }},
                {field: 'outbound_operator', title: '出库人', width: 100, align: 'center', templet: function(d) {
                    return d.outbound_operator || '<span class="layui-text-muted">-</span>';
                }},
                {field: 'outbound_time', title: '出库时间', width: 160, align: 'center', templet: function(d) {
                    return d.outbound_time || '<span class="layui-text-muted">-</span>';
                }},
                {field: 'create_time', title: '创建时间', width: 160, align: 'center'},
                {field: 'right', title: '操作', width: 120, align: 'center', toolbar: '#actionBar', fixed: 'right'}
            ]]
        });
        
        // 表格工具条
        table.on('toolbar(table_delivery)', function(obj){
            if(obj.event === 'add'){
                tool.box('/customer/delivery/add', "选择发货订单", "95%", "85%");
            }
        });
        
        // 行工具条
        table.on('tool(table_delivery)', function(obj){
            var data = obj.data;
            
            if(obj.event === 'view'){
                tool.side('/customer/delivery/view/id/' + data.id, {
                    title: '发货指令详情',
                    area: ['80%', '90%']
                });
            } else if(obj.event === 'cancel'){
                layer.prompt({
                    formType: 2,
                    title: '请输入撤销原因',
                    area: ['400px', '200px']
                }, function(value, index){
                    if(value.length < 5){
                        layer.msg('撤销原因至少需要5个字符', {icon: 2});
                        return;
                    }
                    
                    tool.post('/customer/delivery/cancel', {id: data.id, reason: value}, function(res){
                        if(res.code == 0){
                            layer.msg(res.msg, {icon: 1});
                            layui.pageTable.reload();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    
                    layer.close(index);
                });
            }
        });
        
        // 表单搜索
        form.on('submit(table-search)', function(data){
            layui.pageTable.reload({
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });
    }
</script>
{/block} 