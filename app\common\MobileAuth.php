<?php

declare (strict_types = 1);

namespace app\common;

use think\facade\Session;
use think\facade\Db;
use think\facade\Cache;
use think\facade\Request;
use app\common\Workwechat;

/**
 * 移动端页面权限检查类
 */
class MobileAuth
{
    /**
     * 检查用户是否已登录
     * @return bool
     */
    public static function isLogin()
    {
        $session_admin = get_config('app.session_admin');
        if (!Session::has($session_admin)) {
            
            return false;
        }
        return true;
    }
    
    /**
     * 获取当前登录用户ID
     * @return int
     */
    public static function getUserId()
    {
        $session_admin = get_config('app.session_admin');
        if (!Session::has($session_admin)) {
            return 0;
        }
        return Session::get($session_admin);
    }
    
    /**
     * 获取用户所属的角色组
     * @param int $userId
     * @return array
     */
    public static function getUserGroups($userId = 0)
    {
         $user=Db::name('admin')->where('id',$userId)->find();
        $groups=db::name('admin_group')->where('id', $user['position_id'])->value('rules');
        return $groups;
        // if ($userId == 0) {
        //     $userId = self::getUserId();
        // }
        
        // if ($userId <= 0) {
        //     return [];
        // }
       
        
        // // 从缓存中获取用户角色组
        // $cacheKey = 'mobile_user_groups_' . $userId;
        // if (Cache::has($cacheKey)) {
        //     return Cache::get($cacheKey);
        // }
       
      
        // // 为避免表不存在的错误，我们简化处理方式
        // // 如果用户ID是1，将其视为拥有第一个角色组（管理员组）
        // if ($userId == 1) {
        //     $groups = [1]; // 管理员组ID通常为1
        // } else {
        //     try {
        //         // 尝试从数据库查询用户角色组，如果表存在
        //         $groups = Db::name('auth_group_access')
        //             ->alias('a')
        //             ->join('auth_group g', 'a.group_id = g.id')
        //             ->where('a.uid', $userId)
        //             ->where('g.status', 1)
        //             ->column('g.id');
        //     } catch (\Exception $e) {
        //         // 如果查询出错（如表不存在），返回空数组
        //         $groups = [];
        //     }
        // }
        
        // // 缓存用户角色组，有效期1小时
        // Cache::set($cacheKey, $groups, 3600);
     
    }
    
    /**
     * 检查用户是否有访问特定页面的权限
     * @param string $module 模块名
     * @param string $controller 控制器名
     * @param string $action 方法名
     * @param int $userId 用户ID
     * @return bool
     */
    public static function checkPagePermission($module = '', $controller = '', $action = '', $userId = 0)
    {
        // 超级管理员始终具有所有权限
        if (self::isSuperAdmin($userId)) {
            return true;
        }
        
        // 未指定模块、控制器、方法时，使用当前请求的路由参数
        if (empty($module)) {
            $module = strtolower(app('http')->getName());
        }
        if (empty($controller)) {
            $controller = strtolower(Request::controller());
        }
        if (empty($action)) {
            $action = strtolower(Request::action());
        }
        
        // 添加调试信息，检查路由参数是否正确
        if (empty($module) || empty($controller) || empty($action)) {
            // 如果获取不到路由参数，记录日志并默认放行
            trace("无法获取完整路由: module={$module}, controller={$controller}, action={$action}", 'notice');
            return true; // 临时允许访问，避免权限验证失败
        }
        
        // 白名单路由，不需要权限验证
        $whiteList = self::getWhiteList();
        $routeKey = "{$module}/{$controller}/{$action}";


      
       // print_r($whiteList);
        if (in_array($routeKey, $whiteList)) {
            return true;
           
        }

        

        // 获取用户角色组
        $groupIds = self::getUserGroups($userId);
        if (empty($groupIds)) {
            return false;
        }
       
       
        // // 获取角色组权限规则
        $rules = self::getGroupPermissionRules($groupIds);
        print_r($groupIds);
        exit;
        
        if (empty($rules)) {
            return false;
        }
       
       
       
        // 检查是否有权限访问当前路由
        foreach ($rules as $rule) {
            if ($rule == '*') {
                return true;
            }
            
            // 处理通配符
            if (strpos($rule, '*') !== false) {
                $pattern = '/^' . str_replace(['/', '*'], ['\/', '.*'], $rule) . '$/i';
                if (preg_match($pattern, $routeKey)) {
                    return true;
                }
            } else if ($rule == $routeKey) {
                return true;
            }
        }
        
        // 记录访问被拒绝的信息
        trace("权限验证失败: {$routeKey}, 用户ID: {$userId}", 'notice');
        
        return false;
    }
     
    /**
     * 检查用户是否是超级管理员
     * @param int $userId
     * @return bool
     */
    public static function isSuperAdmin($userId = 0)
    {
        if ($userId == 0) {
            $userId = self::getUserId();
        }
        
        if ($userId <= 0) {
            return false;
        }
        
        // 从缓存中获取
        $cacheKey = 'mobile_is_super_admin_' . $userId;
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        // 查询数据库
        $admin = Db::name('admin')->where('id', $userId)->find();
        
        // 判断是否是超级管理员（ID为1的用户为超级管理员）
        $isSuperAdmin = ($admin && $userId == 1);
        
        // 或者通过角色判断是否是超级管理员
        if (!$isSuperAdmin && $admin) {
            // 检查用户是否属于超级管理员角色组
            $groupIds = self::getUserGroups($userId);
            if (!empty($groupIds)) {
                // 检查是否有超级管理员角色组（ID为1的角色组通常是超级管理员）
               // $isSuperAdmin = in_array(1, $groupIds);
            }
        }
        
        // 缓存结果，有效期1小时
        Cache::set($cacheKey, $isSuperAdmin, 3600);
        
        return $isSuperAdmin;
    }
    
    /**
     * 获取权限白名单
     * @return array
     */
    public static function getWhiteList()
    {
        return [
            'qiye/qiye/index',
            'qiye/index/index',
            'qiye/index/login',
            'qiye/qiye/login',
            'qiye/approve/apply',
            'qiye/approve/mylist',
            'qiye/approve/checklist',
            'qiye/approve/copylist',
            'qiye/inventor/index',
            'qiye/inventor/search',
            'home/leaves/add',
            'home/trips/add',
            'home/outs/add',
            'home/overtimes/add',
            'finance/expense/add',
            'home/index/edit_password',//no
        ];
    }
    
    /**
     * 获取角色组的权限规则
     * @param array $groupIds
     * @return array
     */
    public static function getGroupPermissionRules($groupIds)
    {
        if (empty($groupIds)) {
            return [];
        }
        
        // 为避免表不存在的错误，简化处理方式
        try {
            // 查询数据库
            $rules = Db::name('admin_group')
                ->whereIn('id', $groupIds)
                ->where('status', 1)
                ->column('rules');
            
            $rulesList = [];
            foreach ($rules as $rule) {
                if (!empty($rule)) {
                    $ruleArr = explode(',', $rule);
                    $rulesList = array_merge($rulesList, $ruleArr);
                }
            }
            
            // 去重
            $rulesList = array_unique($rulesList);
            
            // 获取对应的权限规则
            $permissionRules = [];
            if (!empty($rulesList)) {
                try {
                    $permissions = Db::name('auth_rule')
                        ->whereIn('id', $rulesList)
                        ->where('status', 1)
                        ->column('name');
                    
                    foreach ($permissions as $permission) {
                        if (!empty($permission)) {
                            $permissionRules[] = strtolower($permission);
                        }
                    }
                } catch (\Exception $e) {
                    // 如果auth_rule表不存在，为管理员ID为1的添加通配符权限
                    if (in_array(1, $groupIds)) {
                        $permissionRules[] = '*'; // 超级管理员拥有所有权限
                    }
                }
            }
        } catch (\Exception $e) {
            // 如果查询出错（如表不存在），为管理员添加通配符权限
            if (in_array(1, $groupIds)) {
                $permissionRules = ['*']; // 超级管理员拥有所有权限
            } else {
                $permissionRules = [];
            }
        }
        
        // // 为ID为1的用户组添加所有权限
        // if (in_array(1, $groupIds) && !in_array('*', $permissionRules)) {
        //     $permissionRules[] = '*';
        // }
        
        // // 缓存结果，有效期10分钟
        // Cache::set($cacheKey, $permissionRules, 600);
        
        return $permissionRules;
    }
    
    /**
     * 清除用户权限缓存
     * @param int $userId
     */
    public static function clearUserCache($userId)
    {
        Cache::delete('mobile_user_groups_' . $userId);
        Cache::delete('mobile_is_super_admin_' . $userId);
        
        // 获取用户角色组，用于清除组权限缓存
        try {
            $groups = Db::name('auth_group_access')
                ->where('uid', $userId)
                ->column('group_id');
                
            if (!empty($groups)) {
                Cache::delete('mobile_group_rules_' . implode('_', $groups));
            }
        } catch (\Exception $e) {
            // 如果表不存在，则忽略错误
        }
    }
    
    /**
     * 判断是否是移动端客户端
     * @return bool
     */
    public static function isMobileClient()
    {
        // 使用辅助函数判断是否是移动端
        if (function_exists('is_mobile_device')) {
            return is_mobile_device();
        }

        // 自身逻辑判断
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
            $mobileAgents = [
                'mobile', 'android', 'iphone', 'ipad', 'ipod', 'opera mini', 'opera mobi', 'ucweb', 'windows phone'
            ];
            
            foreach ($mobileAgents as $agent) {
                if (strpos($userAgent, $agent) !== false) {
                    return true;
                }
            }
        }
        
        // 特定参数判断，例如URL中包含mobile=1的参数
        if (Request::param('mobile') == 1) {
            return true;
        }
        
        // 特定路径判断
        $path = Request::pathinfo();
        if (strpos($path, 'qiye') === 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 重定向到登录页面
     * @return \think\response\Redirect
     */
    public static function redirectToLogin()
    {
        try {
            // 尝试使用企业微信登录
            $client = new Workwechat();
            return $client->Login('getLoginUrl', '');
        } catch (\Exception $e) {
            // 记录错误信息
            trace("企业微信登录失败: " . $e->getMessage(), 'error');
            // 使用普通重定向
            return redirect('/qiye/login/index')->with('msg', '请先登录');
        }
    }
    
    /**
     * 重定向到无权限页面
     * @return \think\response\Redirect
     */
    public static function redirectToNoAccess()
    {
        return redirect('/qiye/error/noaccess');
    }
} 