# 物料领料自动锁定库存错误修复

## 🔍 问题描述

在实现自动锁定库存功能后，出现以下错误：
```
[2025-08-12T09:29:25+08:00][error] 自动锁定库存失败
[2025-08-12T09:29:25+08:00][error] 创建领料单失败
```

从界面显示可以看出：
- **可用库存**：100
- **需求数量**：100  
- **系统提示**：当前可用库存：0，需求数量：100

## 🎯 问题原因分析

### 1. 数据库表名问题
- 使用了 `Db::name()` 方法，但可能表前缀配置有问题
- 应该使用完整的表名 `oa_` 前缀

### 2. 查询逻辑问题
- 查询条件可能不正确
- 数据类型转换问题

### 3. 事务处理缺失
- 没有使用事务保证数据一致性
- 可能导致部分操作成功，部分失败

## 🛠️ 解决方案

### 1. 修正数据库表名

```php
// 修改前：使用 Db::name()
$availableStocks = Db::name('inventory_realtime')
$lockedStocks = Db::name('inventory_lock')

// 修改后：使用完整表名
$availableStocks = Db::table('oa_inventory_realtime')
$lockedStocks = Db::table('oa_inventory_lock')
```

### 2. 添加详细的调试日志

```php
// 添加调试日志
Log::info('开始自动锁定库存', [
    'material_id' => $materialId,
    'production_order_id' => $productionOrderId,
    'request_quantity' => $requestQuantity
]);

// 记录查询结果
Log::info('库存查询结果', [
    'material_id' => $materialId,
    'available_stocks' => $availableStocks,
    'sql' => Db::getLastSql()
]);
```

### 3. 添加事务处理

```php
// 开启事务
Db::startTrans();
try {
    // 创建库存锁定记录
    $lockId = Db::table('oa_inventory_lock')->insertGetId($lockData);

    // 更新实时库存
    $updateResult = Db::table('oa_inventory_realtime')
        ->where('id', $stock['id'])
        ->dec('available_quantity', $lockQuantity)
        ->inc('locked_quantity', $lockQuantity)
        ->update();
    
    if (!$updateResult) {
        throw new \Exception("更新库存失败，仓库ID：{$stock['warehouse_id']}");
    }
    
    // 提交事务
    Db::commit();
    
} catch (\Exception $e) {
    // 回滚事务
    Db::rollback();
    throw $e;
}
```

### 4. 改进错误处理

```php
// 查询该物料的总库存情况
$totalStock = Db::table('oa_inventory_realtime')
    ->where('product_id', intval($materialId))
    ->sum('available_quantity');
    
return [
    'success' => false,
    'available_quantity' => floatval($totalStock),
    'message' => '没有可用库存'
];
```

## 📋 修复的关键点

### 1. 表名修正
- `inventory_realtime` → `oa_inventory_realtime`
- `inventory_lock` → `oa_inventory_lock`
- `warehouse` → `oa_warehouse`

### 2. 查询优化
- 使用 `leftJoin` 替代 `join` 避免关联失败
- 添加 `intval()` 确保数据类型正确
- 使用 `floatval()` 处理数量字段

### 3. 事务安全
- 所有库存操作都在事务中执行
- 失败时自动回滚，保证数据一致性
- 添加更新结果检查

### 4. 调试信息
- 记录查询参数和结果
- 记录SQL语句便于调试
- 详细的错误信息

## 🧪 测试验证

### 测试步骤
1. 创建一个需要物料的生产订单
2. 确保物料有足够的可用库存但没有锁定库存
3. 尝试创建领料单
4. 检查是否自动锁定成功

### 预期结果
- ✅ 自动查询到可用库存
- ✅ 成功创建锁定记录
- ✅ 正确更新库存状态
- ✅ 成功创建领料单

## 🎯 修复效果

### 修复前
- ❌ 查询不到可用库存（显示0）
- ❌ 自动锁定功能失败
- ❌ 创建领料单失败

### 修复后
- ✅ 正确查询到可用库存
- ✅ 自动锁定功能正常工作
- ✅ 事务保证数据一致性
- ✅ 详细的日志便于调试

现在可以重新测试物料领料功能，应该能够正常工作了！
