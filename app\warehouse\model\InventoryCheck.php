<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存盘点模型
 */
class InventoryCheck extends Model
{
    // 设置表名
    protected $name = 'inventory_check';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 设置字段信息
    protected $schema = [
        'id'               => 'int',
        'check_no'         => 'string',
        'warehouse_id'     => 'int',
        'check_date'       => 'int',
        'status'           => 'tinyint',
        'total_products'   => 'int',
        'checked_products' => 'int',
        'notes'            => 'string',
        'created_by'       => 'int',
        'approved_by'      => 'int',
        'approved_time'    => 'int',
        'create_time'      => 'int',
        'update_time'      => 'int',
    ];
    
    // 设置状态常量
    const STATUS_PENDING = 0;    // 待盘点
    const STATUS_CHECKING = 1;   // 盘点中
    const STATUS_COMPLETED = 2;  // 已完成
    const STATUS_CANCELLED = 3;  // 已取消
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : $this->status;
        $statusArray = [
            self::STATUS_PENDING => '待盘点',
            self::STATUS_CHECKING => '盘点中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return isset($statusArray[$status]) ? $statusArray[$status] : '未知';
    }
    
    /**
     * 关联盘点明细
     */
    public function details()
    {
        return $this->hasMany(InventoryCheckDetail::class, 'check_id', 'id');
    }
    
    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'warehouse_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\admin\model\Admin', 'created_by', 'id');
    }
    
    /**
     * 关联审核人
     */
    public function approver()
    {
        return $this->belongsTo('app\admin\model\Admin', 'approved_by', 'id');
    }
    
    /**
     * 生成盘点单号
     */
    public static function generateCheckNo()
    {
        $prefix = 'PD';
        $date = date('Ymd');
        
        // 查询当天最大序号
        $maxNo = self::where('check_no', 'like', $prefix . $date . '%')
            ->max('check_no');
        
        if ($maxNo) {
            $sequence = intval(substr($maxNo, -4)) + 1;
        } else {
            $sequence = 1;
        }
        
        return $prefix . $date . sprintf('%04d', $sequence);
    }
    
    /**
     * 搜索器：关键词搜索
     */
    public function searchKeywordsAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('check_no|notes', 'like', '%' . $value . '%');
        }
    }
    
    /**
     * 搜索器：仓库ID
     */
    public function searchWarehouseIdAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：状态
     */
    public function searchStatusAttr($query, $value)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }
    
    /**
     * 搜索器：时间范围
     */
    public function searchTimeRangeAttr($query, $value)
    {
        if (!empty($value) && is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }
}
