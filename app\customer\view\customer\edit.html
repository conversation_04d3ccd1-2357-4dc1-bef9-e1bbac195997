{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-page">
	<h3 class="pb-3">编辑客户信息</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">客户名称<font>*</font></td>
			<td colspan="3"><input type="text" name="name" lay-verify="required" value="{$detail.name}" lay-reqText="请输入客户名称" autocomplete="off" placeholder="请输入客户名称" class="layui-input"></td>
			<td class="layui-td-gray">客户来源<font>*</font></td>
			<td>
				<select name="source_id" lay-verify="required" lay-reqText="请选择客户来源">
				<option value="">请选择客户来源</option>
				{volist name=":get_base_data('customer_source')" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.source_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">联系地址<font>*</font></td>
			<td colspan="3"><input type="text" name="address" value="{$detail.address}" autocomplete="off" lay-verify="required" lay-reqText="请输入客户联系地址" placeholder="请输入客户联系地址" class="layui-input"></td>
			<td class="layui-td-gray">所属行业<font>*</font></td>
			<td>
			  <select name="industry_id" lay-verify="required" lay-reqText="请选择所属行业">
				<option value="">请选择所属行业</option>
				{volist name=":get_base_data('Industry')" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.industry_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">客户等级<font>*</font></td>
			<td>
			  <select name="grade_id" lay-verify="required" lay-reqText="请选择客户等级">
				<option value="">请选择客户等级</option>
				{volist name=":get_base_data('customer_grade')" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.grade_id"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
			<td class="layui-td-gray">客户状态<font>*</font></td>
			<td>
			  <select name="customer_status" lay-verify="required" lay-reqText="请选择客户状态">
				<option value="">请选择客户当前状态</option>
				{volist name=":get_base_type_data('basic_customer',1)" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.customer_status"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
			<td class="layui-td-gray">客户意向<font>*</font></td>
			<td>
			  <select name="intent_status" lay-verify="required" lay-reqText="请选择客户意向">
				<option value="">请选择客户意向</option>
				{volist name=":get_base_type_data('basic_customer',2)" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$detail.intent_status"} selected{/eq}>{$v.title}</option>
				{/volist}
			  </select>
			</td>
		  </tr>
		  {eq name="$sea" value="0"}
		  <tr>
				<td class="layui-td-gray">归属员工</td>
				<td>
					<input type="text" name="belong_name" value="{$detail.belong_name|default=''}" autocomplete="off" readonly placeholder="请选择客户归属人" class="layui-input">
					<input type="hidden" name="belong_uid" value="{$detail.belong_uid|default=0}">
					<input type="hidden" name="belong_did" value="{$detail.belong_did|default=0}">
				</td>
				<td class="layui-td-gray">归属部门</td>
				<td><input type="text" name="belong_department" value="{$detail.belong_department|default=''}" autocomplete="off" readonly class="layui-input"></td>
				<td class="layui-td-gray">共享员工</td>
				 <td colspan="3">
					<input type="text" name="share_names" value="{$detail.share_names|default=''}" autocomplete="off" readonly placeholder="请选择共享人员" class="layui-input picker-admin" data-type="2">
					<input type="hidden" name="share_ids" value="{$detail.share_ids}">
				</td>
		  </tr>
		  {/eq}
		<tr>
			<td class="layui-td-gray" style="vertical-align:top">客户介绍<font>*</font></td>
			<td colspan="5"><textarea name="content" placeholder="请输入客户介绍信息" lay-verify="required" lay-reqText="请输入客户介绍信息" class="layui-textarea">{$detail.content|default=""}</textarea></td>
		</tr>
		<tr>
			<td class="layui-td-gray" style="vertical-align:top">经营业务</td>
			<td colspan="5"><textarea name="market" placeholder="请输入客户主要经营业务" class="layui-textarea">{$detail.market|default=""}</textarea></td>
		</tr>
		<tr>
			<td class="layui-td-gray" style="vertical-align:top">备注信息</td>
			<td colspan="5"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea">{$detail.remark|default=""}</textarea></td>
		</tr>
    </table>
    <div class="pt-4">
	   <input type="hidden" name="id" value="{$detail.id}">
	   <input type="hidden" name="scene" value="edit"/>
      <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
      <button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->
{block name="copyright"}{/block}
<!-- 脚本 -->
{block name="script"}
<script>
	var moduleInit = ['tool','oaPicker'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool,oaPicker = layui.oaPicker;
		//选择归属人人弹窗
		$('body').on('click','[name="belong_name"]',function () {
			var ids=$('[name="belong_uid"]').val(),names=$('[name="belong_name"]').val();
			oaPicker.employeeInit({
				ids:ids,
				names:names,
				type:1,
				callback:function(data){
					let select_id=[],select_name=[],select_did=[],select_dname=[];
					for(var a=0; a<data.length;a++){
						select_id.push(data[a].id);
						select_name.push(data[a].name);
						select_did.push(data[a].did);
						select_dname.push(data[a].department);
					}
					$('[name="belong_uid"]').val(select_id.join(','));
					$('[name="belong_name"]').val(select_name.join(','));
					$('[name="belong_did"]').val(select_did.join(','));
					$('[name="belong_department"]').val(select_dname.join(','));
				}
			});
		});	
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			let clickbtn = $(this);
			tool.post("/customer/customer/add", data.field, callback,clickbtn);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->