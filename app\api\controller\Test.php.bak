<?php


declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use app\api\model\EditLog;
use think\facade\Db;
use app\Produce\model\ProductionFeeding;
use think\facade\Session;

class Test extends BaseController
{
	public function index()
	{

		// 获取订单详情
		$action_id = 22;
		$orderDetails = Db::name('customer_order_detail')->where(['order_id' => $action_id])->select();

        
		foreach ($orderDetails as $orderDetail) {
			$productId = $orderDetail['product_id'];
			$quantity = $orderDetail['quantity'];
			$orderDetail_id=$orderDetail['id'];
		
			// 检查产品是否有BOM表
			$bomExists = Db::name('bom_master')->where(['product_id' => $productId, 'check_status' => 2])->find();
           
			if ($bomExists) {
				// 产品有BOM表，查询所有BOM物料
				$bomDetails = Db::name('bom_item')->where(['bom_id' => $bomExists['id']])->select();
				
				foreach ($bomDetails as $bomDetail) {
					$materialId = $bomDetail['material_id'];
					$requiredQuantity = $bomDetail['qty'] * $quantity; // BOM用量 * 销售数量
					
					// 获取当前库存
					$inventory = Db::name('inventory')->where(['product_id' => $materialId])->find();
					$currentStock = $inventory ? $inventory['quantity'] : 0;
                    
					// 获取在途数量
					$inTransit = Db::name('purchase_order_detail')
						->alias('pod')
						->join('purchase_order po', 'po.id = pod.order_id')
						->where([
							'pod.product_id' => $materialId,
							['po.status', 'in', [1, 2]]
						])
						->sum('pod.quantity');
                    
                       
					// 计算可用库存
					$availableStock = $currentStock + $inTransit;
					
					// 如果库存不足，插入需求数据
					if ($availableStock < $requiredQuantity) {
						$needQuantity = $requiredQuantity - $availableStock;
						// 向物料需求表插入数据
						Db::name('material_requirement')->insert([
							'order_id' => $action_id,
							'product_id' => $materialId,
							'quantity' => $needQuantity,
							'source_id' => $orderDetail_id,
							'source_type' => 'customer_order',
							'status' => 0, // 未处理
							'gap'=>0,
							'create_time' => time(),
							'create_by' => $this->uid
						]);
					}
				}
			} else {
				// 产品没有BOM表，直接检查产品库存
				$inventory = Db::name('inventory')->where(['product_id' => $productId])->find();
				$currentStock = $inventory ? $inventory['quantity'] : 0;
				
				// 获取在途数量
				$inTransit = Db::name('purchase_order_detail')
					->alias('pod')
					->join('purchase_order po', 'po.id = pod.order_id')
					->where([
						'pod.product_id' => $productId,
						['po.status', 'in', [1, 2]] // 采购中或已审核状态
					])
					->sum('pod.quantity');
				
				// 计算可用库存
				$availableStock = $currentStock + $inTransit;
				
				// 如果库存不足，插入需求数据
				if ($availableStock < $quantity) {
					$needQuantity = $quantity - $availableStock;
					// 向物料需求表插入数据
					Db::name('material_requirement')->insert([
						'order_id' => $action_id,
						'product_id' => $productId,
						'quantity' => $needQuantity,
						'source_id' => $orderDetail_id,
						'source_type' => 'customer_order',
						'status' => 0, // 未处理
						'gap'=>0,
						'create_time' => time(),
						'create_by' => $this->uid
					]);
				}
			}
		}
		//end


		return 'hello';
	}
	//销售订单及物料需求表
	public function getmrppurchase_suggestion()
	{
		// 获取分页参数
		$page = request()->param('page', 1);
		$limit = 5;//request()->param('limit', 15);
		
		// 先获取已审核订单的ID
		$checkedOrderIds = Db::name('customer_order')
			->where('check_status', 2)
			->column('id');
		trace('已审核订单ID列表：' . json_encode($checkedOrderIds), 'debug');


	

		// 获取这些订单对应的订单明细ID
		$checkedDetailIds = Db::name('customer_order_detail')
			->whereIn('order_id', $checkedOrderIds)
			->column('id');
		trace('已审核订单的明细ID列表：' . json_encode($checkedDetailIds), 'debug');



	

		// 获取物料需求列表
		$result = Db::name('material_requirement')
			->alias('mr')
			->join('product p', 'p.id = mr.product_id')
			->leftJoin('customer_order_detail cod', 'cod.id = mr.source_id')
			->leftJoin('customer_order co', 'co.id = cod.order_id')
			->whereIn('mr.source_id', $checkedDetailIds)  // 只查询已审核订单明细的需求
			->where(function($query) {
				$query->where('mr.gap', '<', Db::raw('mr.quantity'))
					->whereOr('mr.gap', 'is', null)
					->whereOr('mr.gap', '=', 0);
			})
			->field([
				'mr.*',
				'p.title as product_name',
				'p.material_code as product_code',
				'co.order_no as business_no',
				'mr.gap - mr.quantity as gap',
				'co.check_status',
				'mr.source_type'
			])
			->order('mr.create_time desc')
			->paginate([
				'list_rows' => $limit,
				'page' => $page
			]);
		
		// 获取分页后的数据列表
		$list = $result->items();
		
		// 转换为数组 - items()已经返回数组，无需再次转换
		// $list = $list->toArray();

		// 添加更详细的调试信息
		trace('物料需求查询SQL：' . Db::getLastSql(), 'debug');
		trace('查询结果数量：' . count($list), 'debug');
		trace('分页参数：page=' . $page . ', limit=' . $limit, 'debug');
		trace('总记录数：' . $result->total(), 'debug');
		
		if (!empty($list)) {
			trace('第一条记录信息：' . json_encode($list[0], JSON_UNESCAPED_UNICODE), 'debug');
		}
		
		// 检查是否有已审核的订单
		$checkedOrders = Db::name('customer_order')
			->where('check_status', 2)
			->count();
		trace('已审核订单数量：' . $checkedOrders, 'debug');

		foreach ($list as &$item) {
			$item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
			$item['status_text'] = $item['status'] == 0 ? '未处理' : '已处理';
		 
			// 根据不同的source_type获取对应的业务单号
			if ($item['source_type'] && $item['source_id']) {
				switch ($item['source_type']) {
					case 'customer_order':
						// 订单号已在初始查询中获取
						$item['types'] = '销售订单';
						if (empty($item['business_no'])) {
							$item['business_no'] = '未知';
						}

						break;
					// 可以根据需要添加其他业务类型的处理
				}
			}
		}
		unset($item);

		// 包含分页信息的完整返回
		return json([
			'code' => 200,
			'msg' => 'success',
			'count' => $result->total(), // 总记录数
			'data' => $list
		]);

	}


	  /**
     * 获取BOM详细信息
     */
    public function getBomInfo()
    {
        $product_id = request()->param('id/d', 0);
        
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询最新有效的BOM信息
        $bomInfo = Db::name('bom_master')
            ->where('product_id', $product_id)
            ->where('check_status', 2) // 已审核状态
            ->where('delete_time', 0)
            ->order('create_time desc')
            ->find();

            
            
        if (!$bomInfo) {
            return json(['code' => 1, 'msg' => '该产品没有有效的BOM']);
        }
        
        // 获取产品信息
        $productInfo = Db::name('product')
            ->where('id', $product_id)
            ->field('id, title, material_code, specs, unit')
            ->find();
            
        // 获取BOM子项
        $bomItems = Db::name('bom_item')
            ->alias('bi')
            ->join('product p', 'p.id = bi.material_id', 'left')
            ->where('bi.bom_id', $bomInfo['id'])
            ->where('bi.is_needed', 1) // 只获取需要的子项
            ->field('bi.*, p.title as material_name, p.material_code, p.specs as product_specs, p.unit as uom_name')
            ->select()
            ->toArray();
            
        // 获取每个物料的库存
        foreach ($bomItems as &$item) {
            // 获取库存数量
            $item['stock'] = $this->getProductStock($item['material_id']);
        }
        
        $bomInfo['items'] = $bomItems;
        $bomInfo['product'] = $productInfo;
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomInfo]);
    }

	
    /**
     * 获取产品当前库存
     * @param int $product_id 产品ID
     * @return float 库存数量
     */
    private function getProductStock($product_id)
    {
        // 获取可用库存
        $stock = Db::name('inventory')
            ->where('product_id', $product_id)
            ->where('status', 1) // 可用状态
            ->sum('quantity');
            
        return floatval($stock);
    }

}
