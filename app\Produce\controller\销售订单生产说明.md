# 销售订单生产功能说明

## 功能概述

销售订单生产功能允许基于已有的销售订单创建生产工单，确保生产与销售需求的精确对接。

## 使用步骤

### 1. 选择生产类型
在创建生产订单页面，首先选择**生产类型**：
- **库存生产**：为库存补货而生产
- **销售订单生产**：为特定销售订单而生产

### 2. 选择销售订单
当选择"销售订单生产"后，会显示**销售订单**选择框：
- 系统会列出所有已审核通过的销售订单
- 显示格式：`订单号 - 客户名称`
- 只有状态为"已审核"且"审核通过"的订单才会显示

### 3. 添加产品
选择销售订单后，点击"添加产品"按钮：
- 系统会自动加载该销售订单的产品明细
- 显示剩余需要生产的数量
- 只能选择还有剩余生产需求的产品

### 4. 工序设置
添加产品后，系统会：
- 自动加载该产品的标准工艺
- 显示所有工序步骤
- 允许对工序进行微调（编辑、删除等）

## 注意事项

### 销售订单要求
销售订单必须满足以下条件才能用于生产：
1. **订单状态** > 0（已提交）
2. **审核状态** = 2（审核通过）
3. **有剩余生产需求**（订单数量 > 已生产数量）

### 产品工艺
- 每个产品都有固定的工艺模板
- 不能随意更换工艺，只能微调
- 系统会自动加载产品的标准工艺

### 数据关联
- 生产订单会关联销售订单ID和订单号
- 生产明细会关联销售订单明细ID
- 便于后续的生产进度跟踪和交付管理

## 常见问题

### Q: 为什么看不到销售订单选择？
**A:** 请确保：
1. 已选择"销售订单生产"类型
2. 系统中有已审核通过的销售订单
3. 页面已正确加载

### Q: 为什么销售订单列表为空？
**A:** 可能的原因：
1. 没有创建销售订单
2. 销售订单未审核或审核未通过
3. 所有销售订单都已完成生产

### Q: 为什么某些产品不能选择？
**A:** 产品不能选择的原因：
1. 该产品的生产需求已满足
2. 产品状态异常
3. 产品没有配置工艺模板

## 数据流程

```
销售订单创建 → 订单审核 → 选择订单生产 → 创建生产工单 → 工序执行 → 生产完成 → 订单交付
```

## 技术实现

### 前端逻辑
- 生产类型切换时显示/隐藏销售订单选择
- 选择销售订单后加载对应的产品明细
- 自动加载产品工艺并允许微调

### 后端处理
- `getAvailableCustomerOrders()` - 获取可用销售订单
- `getCustomerOrderDetails()` - 获取订单产品明细
- `createCustomOrderProcesses()` - 创建订单工序实例

### 数据关联
- `customer_order_id` - 关联销售订单
- `customer_order_detail_id` - 关联销售订单明细
- `process_template_id` - 关联产品工艺模板

这样的设计确保了生产与销售的紧密结合，提高了生产计划的准确性和可追溯性。
