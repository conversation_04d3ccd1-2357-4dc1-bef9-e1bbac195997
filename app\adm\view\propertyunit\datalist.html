{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm addNew" type="button">+ 添加资产单位</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, form = layui.form;
		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,title:'资产单位列表'
			,url: "/adm/propertyunit/datalist"
			,page: true
			,cellMinWidth: 80
			,cols: [[
				{field:'id',width:80, title: 'ID号', align:'center'}
				,{field:'title',title: '名称',width:150}
				,{field:'desc',title: '描述'}
				,{field:'sort',width:100,title: '排序', align:'center'}
				,{field:'status', title: '状态',width:100,align:'center',templet: function(d){
					var html1='<span class="green">正常</span>';
					var html2='<span class="yellow">禁用</span>';
					if(d.status==1){
						return html1;
					}
					else{
						return html2;
					}
				}}
				,{width:132,fixed:'right', title: '操作', align:'center',templet: function(d){
					var html='';
					var btn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
					var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
					var btn2='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="open">启用</a>';
					if(d.status==1){
						html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
					}
					else{
						html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
					}
					return html;
				}}
			]]
		});
		
		table.on('tool(test)',function (obj) {
			if(obj.event === 'edit'){					
				tool.side("/adm/propertyunit/add?id="+obj.data.id);
			}
			if(obj.event === 'disable'){
				layer.confirm('确定要禁用该资产单位吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/adm/propertyunit/check", { id: obj.data.id,status: 0,title: obj.data.title}, callback);
					layer.close(index);
				});
			}
			if(obj.event === 'open'){
				layer.confirm('确定要启用该资产单位吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/adm/propertyunit/check", { id: obj.data.id,status: 1,title: obj.data.title}, callback);
					layer.close(index);
				});
			}
		});
		
		$('body').on('click','.addNew',function(){
			tool.side("/adm/propertyunit/add");	
		});
	}
	</script>
{/block}
<!-- /脚本 -->