# 物料档案系统手工操作指南

## 1. 数据库操作步骤

### 1.1 备份数据库
在执行任何数据库更改之前，请先备份数据库：
```sql
-- 使用mysqldump备份数据库
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 1.2 执行数据库更新脚本
按顺序执行 `database_updates.sql` 文件中的SQL语句：

1. **添加字段到 oa_product 表**
```sql
-- 执行第1部分：为 oa_product 表添加缺失的字段
ALTER TABLE `oa_product` 
ADD COLUMN `default_warehouse` int(11) DEFAULT '0' COMMENT '默认仓库ID' AFTER `stock`,
-- ... 其他字段
```

2. **创建供应商价格表**
```sql
-- 执行第2部分：创建供应商价格表
CREATE TABLE IF NOT EXISTS `material_supplier_price` (
  -- 表结构定义
);
```

3. **创建外协价格表**
```sql
-- 执行第3部分：创建外协价格表
CREATE TABLE IF NOT EXISTS `material_outsource_price` (
  -- 表结构定义
);
```

4. **创建单位管理表**
```sql
-- 执行第4部分：创建单位管理表
CREATE TABLE IF NOT EXISTS `oa_unit` (
  -- 表结构定义
);
```

5. **创建物料编号序列表**
```sql
-- 执行第5部分：创建物料编号序列表
CREATE TABLE IF NOT EXISTS `material_code_sequence` (
  -- 表结构定义
);
```

6. **插入默认单位数据**
```sql
-- 执行第6部分：插入默认单位数据
INSERT INTO `oa_unit` ...
```

7. **插入默认配置**
```sql
-- 执行第7部分：插入默认的编号序列配置
INSERT INTO `material_code_sequence` ...
```

8. **添加索引**
```sql
-- 执行第8部分：添加索引优化
ALTER TABLE `oa_product` ADD INDEX `idx_material_code` (`material_code`);
-- ... 其他索引
```

### 1.3 验证数据库更改
执行以下SQL验证更改是否成功：

```sql
-- 检查 oa_product 表结构
DESCRIBE oa_product;

-- 检查新建的表是否存在
SHOW TABLES LIKE 'material_%';
SHOW TABLES LIKE 'oa_unit';

-- 检查索引是否创建成功
SHOW INDEX FROM oa_product;

-- 检查默认数据是否插入
SELECT * FROM material_code_sequence;
SELECT * FROM oa_unit WHERE status = 1;
```

## 2. 代码部署步骤

### 2.1 文件更新确认
确认以下文件已经更新：
- `app/material/model/Archive.php` - 物料档案模型文件
- `app/material/controller/Archive.php` - 物料档案控制器文件
- `app/material/validate/Archive.php` - 物料档案验证器文件
- `app/common/model/Unit.php` - 单位管理模型文件
- `app/material/view/archive/add.html` - 物料档案添加页面模板

### 2.2 清理缓存
```bash
# 删除缓存目录
rm -rf runtime/cache/*
rm -rf runtime/temp/*

# 或者在Windows系统中
rmdir /s runtime\cache
rmdir /s runtime\temp
```

### 2.3 权限检查
确保以下目录有写入权限：
- `runtime/` 目录及其子目录
- `public/uploads/` 目录（如果存在）

## 3. 功能测试步骤

### 3.1 基础功能测试

1. **访问物料档案添加页面**
   - URL: `http://your-domain/material/archive/add`
   - 检查页面是否正常加载
   - 检查所有表单字段是否显示正确

2. **测试自动编号功能**
   - 勾选"系统自动编号"复选框
   - 检查物料编号输入框是否变为只读状态
   - 提交表单，检查是否自动生成编号

3. **测试数据保存**
   - 填写完整的表单数据
   - 提交表单，检查是否保存成功
   - 查看数据库中的数据是否正确

### 3.2 高级功能测试

1. **测试JSON字段**
   - 添加质检设置项目
   - 上传物料图片
   - 添加附件
   - 检查数据库中JSON字段是否正确存储

2. **测试关联数据**
   - 添加供应商价格信息
   - 添加外协价格信息
   - 检查关联表中的数据是否正确

3. **测试编辑功能**
   - 编辑已存在的物料档案
   - 检查数据是否正确回显
   - 修改数据后保存，检查更新是否成功

## 4. 错误排查

### 4.1 常见数据库错误

1. **字段不存在错误**
   ```
   错误信息：Column 'xxx' doesn't exist
   解决方案：检查 database_updates.sql 是否完全执行
   ```

2. **表不存在错误**
   ```
   错误信息：Table 'xxx' doesn't exist
   解决方案：检查关联表是否创建成功
   ```

### 4.2 常见PHP错误

1. **类不存在错误**
   ```
   错误信息：Class 'xxx' not found
   解决方案：清理缓存，检查命名空间
   ```

2. **方法不存在错误**
   ```
   错误信息：Call to undefined method
   解决方案：检查方法名是否正确，文件是否更新
   ```

### 4.3 前端错误

1. **表单提交失败**
   - 检查网络请求是否正常
   - 查看浏览器控制台错误信息
   - 检查后端日志文件

2. **数据回显异常**
   - 检查数据库中的数据格式
   - 验证JSON字段的序列化是否正确

## 5. 性能优化建议

### 5.1 数据库优化
- 定期分析表结构，添加必要的索引
- 监控慢查询，优化SQL语句
- 考虑对大量数据进行分表处理

### 5.2 代码优化
- 使用缓存减少数据库查询
- 优化文件上传处理逻辑
- 实现数据的批量操作功能

## 6. 监控和维护

### 6.1 日志监控
定期检查以下日志文件：
- `runtime/log/` 目录下的应用日志
- 数据库错误日志
- Web服务器访问日志

### 6.2 数据备份
建议设置定期数据备份：
- 每日增量备份
- 每周全量备份
- 重要操作前手动备份

## 7. 联系支持

如果在操作过程中遇到问题，请：
1. 记录详细的错误信息
2. 保存相关的日志文件
3. 提供操作步骤和环境信息
4. 联系技术支持团队

---

**注意：在生产环境中执行任何操作前，请务必在测试环境中先进行验证！**
