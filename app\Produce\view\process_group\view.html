{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb" lay-separator=">">
            <a href="/Produce/processGroup/index">工作组管理</a>
            <a><cite>工作组详情</cite></a>
        </span>
        <div class="layui-card-header-auto">
            <a class="layui-btn layui-btn-sm" href="/Produce/processGroup/add?id={$detail.id}">
                <i class="layui-icon layui-icon-edit"></i> 编辑工作组
            </a>
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <table class="layui-table" lay-skin="nob">
                    <colgroup>
                        <col width="120">
                        <col>
                    </colgroup>
                    <tbody>
                        <tr>
                            <td class="layui-bg-gray">工作组名称</td>
                            <td><strong>{$detail.name}</strong></td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">排序</td>
                            <td>{$detail.sort}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">状态</td>
                            <td>
                                {if condition="$detail.status == 1"}
                                <span class="layui-badge layui-bg-green">启用</span>
                                {else /}
                                <span class="layui-badge layui-bg-gray">禁用</span>
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">创建时间</td>
                            <td>{$detail.create_time}</td>
                        </tr>
                        <tr>
                            <td class="layui-bg-gray">更新时间</td>
                            <td>{$detail.update_time}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">工作组统计</div>
                    <div class="layui-card-body">
                        <div class="text-center">
                            <div class="text-muted">工序数量</div>
                            <div class="text-main" style="font-size: 32px; font-weight: bold; color: #1E9FFF;">
                                {$processes|count}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {if condition="!empty($detail.description)"}
        <div class="layui-card">
            <div class="layui-card-header">工作组描述</div>
            <div class="layui-card-body">
                <div class="layui-text">
                    {$detail.description|nl2br}
                </div>
            </div>
        </div>
        {/if}
        
        {if condition="!empty($processes)"}
        <div class="layui-card">
            <div class="layui-card-header">关联工序列表</div>
            <div class="layui-card-body">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>工序编号</th>
                            <th>工序名称</th>
                            <th>标准单价</th>
                            <th>标准效率</th>
                            <th>计价方式</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="processes" id="process"}
                        <tr>
                            <td><span class="layui-badge layui-bg-gray">{$process.code}</span></td>
                            <td><a href="/Produce/process/view?id={$process.id}" class="layui-text">{$process.name}</a></td>
                            <td>¥{$process.standard_price|number_format=2}</td>
                            <td>{$process.efficiency|number_format=2}/小时</td>
                            <td>
                                {if condition="$process.pricing_method == 1"}
                                <span class="layui-badge layui-bg-blue">按件计价</span>
                                {else /}
                                <span class="layui-badge layui-bg-orange">按时计价</span>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>
        {else /}
        <div class="layui-card">
            <div class="layui-card-header">关联工序列表</div>
            <div class="layui-card-body">
                <div class="text-center text-muted" style="padding: 40px 0;">
                    <i class="layui-icon layui-icon-face-cry" style="font-size: 48px;"></i>
                    <div style="margin-top: 10px;">该工作组下暂无工序</div>
                </div>
            </div>
        </div>
        {/if}
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = [];
    function gouguInit() {
        // 页面初始化完成
    }
</script>
{/block}