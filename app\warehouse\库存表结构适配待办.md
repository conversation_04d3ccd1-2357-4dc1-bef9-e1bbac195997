# 库存表结构适配待办事项

## 问题描述
原有的`oa_inventory`表已被删除，库存功能已重新改写。需要根据新的库存表结构适配出库单执行功能。

## 当前临时处理
为了避免系统报错，已临时注释掉相关的库存查询和更新代码：

### 1. execute()方法中的库存查询
```php
// 位置：app/warehouse/controller/Outbound.php 第2231-2237行
foreach ($details as &$detail) {
    // TODO: 需要根据新的库存表结构重新实现库存查询
    // 临时设置默认值，避免报错
    $detail['current_stock'] = 999; // 临时设置一个大数值
    $detail['remaining_quantity'] = $detail['quantity'] - $detail['actual_quantity'];
}
```

### 2. doExecute()方法中的库存检查
```php
// 位置：app/warehouse/controller/Outbound.php 第2287-2298行
// TODO: 需要根据新的库存表结构重新实现库存检查
// 临时跳过库存检查，避免报错
/*
$inventory = Db::name('inventory')
    ->where('product_id', $detailInfo['product_id'])
    ->where('warehouse_id', $outbound->warehouse_id)
    ->find();

if (!$inventory || $inventory['available_quantity'] < $actualQuantity) {
    throw new \Exception("产品【{$detailInfo['product_name']}】库存不足");
}
*/
```

### 3. doExecute()方法中的库存更新
```php
// 位置：app/warehouse/controller/Outbound.php 第2308-2338行
// TODO: 需要根据新的库存表结构重新实现库存更新
// 临时注释掉库存更新，避免报错
/*
// 更新库存
Db::name('inventory')->update([
    'quantity' => Db::raw('quantity - ' . $actualQuantity),
    'available_quantity' => Db::raw('available_quantity - ' . $actualQuantity),
    'update_time' => time()
]);

// 记录库存变动日志
Db::name('inventory_log')->insert([...]);
*/
```

## 需要适配的功能

### 1. 库存查询功能
**位置**：`execute()`方法
**需求**：查询产品在指定仓库的当前可用库存
**原逻辑**：
```php
$inventory = Db::name('inventory')
    ->where('product_id', $detail['product_id'])
    ->where('warehouse_id', $outbound->warehouse_id)
    ->field('quantity, available_quantity')
    ->find();
$detail['current_stock'] = $inventory ? $inventory['available_quantity'] : 0;
```

**需要提供**：
- 新的库存表名
- 新的字段名（产品ID、仓库ID、可用数量等）
- 查询逻辑

### 2. 库存充足性检查
**位置**：`doExecute()`方法
**需求**：检查产品库存是否足够出库
**原逻辑**：
```php
if (!$inventory || $inventory['available_quantity'] < $actualQuantity) {
    throw new \Exception("产品库存不足");
}
```

**需要提供**：
- 如何检查库存是否充足
- 库存不足时的错误提示

### 3. 库存扣减功能
**位置**：`doExecute()`方法
**需求**：执行出库时扣减库存数量
**原逻辑**：
```php
Db::name('inventory')->update([
    'quantity' => Db::raw('quantity - ' . $actualQuantity),
    'available_quantity' => Db::raw('available_quantity - ' . $actualQuantity),
    'update_time' => time()
]);
```

**需要提供**：
- 新的库存更新逻辑
- 需要更新的字段名

### 4. 库存变动日志
**位置**：`doExecute()`方法
**需求**：记录库存变动历史
**原逻辑**：
```php
Db::name('inventory_log')->insert([
    'product_id' => $detailInfo['product_id'],
    'warehouse_id' => $outbound->warehouse_id,
    'change_type' => 'outbound',
    'change_quantity' => -$actualQuantity,
    'before_quantity' => $inventory['quantity'],
    'after_quantity' => $inventory['quantity'] - $actualQuantity,
    // ... 其他字段
]);
```

**需要提供**：
- 是否需要记录库存变动日志
- 新的日志表结构和字段

## 其他需要适配的地方

### 1. MaterialRequest控制器
**文件**：`app/Produce/controller/MaterialRequest.php`
**问题**：生产领料时也使用了`inventory_lock`表进行库存锁定
**位置**：`getLockedStockByMaterial()`方法

### 2. 其他出库相关功能
**可能涉及的文件**：
- 销售出库
- 调拨出库
- 其他使用库存的模块

## 建议的适配步骤

### 1. 了解新库存表结构
请提供：
- 新的库存表名和字段结构
- 库存查询的标准方法
- 库存更新的标准方法

### 2. 创建库存服务类
建议创建一个统一的库存服务类：
```php
class InventoryService
{
    // 查询库存
    public function getStock($productId, $warehouseId) {}
    
    // 检查库存充足性
    public function checkStock($productId, $warehouseId, $quantity) {}
    
    // 扣减库存
    public function reduceStock($productId, $warehouseId, $quantity, $notes = '') {}
    
    // 增加库存
    public function addStock($productId, $warehouseId, $quantity, $notes = '') {}
}
```

### 3. 逐步替换
- 先适配出库单执行功能
- 再适配其他库存相关功能
- 确保数据一致性

## 当前状态
- ✅ 临时修复了SQL错误，页面可以正常访问
- ✅ 出库单列表和基本功能正常
- ⚠️ 执行出库页面可以打开，但库存显示为临时值(999)
- ❌ 执行出库操作会跳过库存检查和更新
- ❌ 不会记录库存变动日志

## 下一步
请提供新的库存表结构信息，我将根据新结构重新实现这些功能。

需要的信息：
1. 新的库存表名和字段结构
2. 库存查询的标准SQL或方法
3. 库存更新的标准逻辑
4. 是否需要库存变动日志，如果需要，日志表的结构
5. 是否有库存锁定功能，如果有，相关表结构
