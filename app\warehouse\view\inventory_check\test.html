{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">库存盘点测试页面</div>
        <div class="layui-card-body">
            <table class="layui-hide" id="test_table" lay-filter="test_table"></table>
        </div>
    </div>
</div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        console.log('开始初始化测试页面...');
        
        // 检查必要的模块是否加载
        if (!layui.tablePlus) {
            console.error('tablePlus模块未加载');
            layer.msg('页面初始化失败：tablePlus模块未加载', {icon: 2});
            return;
        }
        
        var table = layui.tablePlus;
        console.log('模块加载成功');
        
        // 渲染表格
        table.render({
            elem: "#test_table"
            ,title: "测试表格"
            ,url: "/warehouse/inventory_check/test"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,parseData: function(res){
                console.log('返回的数据:', res);
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.count,
                    "data": res.data
                };
            }
            ,done: function(res, curr, count){
                console.log('表格渲染完成:', res);
                if (res.code !== 0) {
                    layer.msg(res.msg || '数据加载失败', {icon: 2});
                }
            }
            ,cols: [[
                {
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'check_no',
                    title: '盘点单号',
                    align: 'center',
                    width: 150
                },{
                    field: 'warehouse_name',
                    title: '仓库',
                    align: 'center',
                    width: 120
                },{
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    width: 100,
                    templet: function (d) {
                        var statusText = {
                            0: '待盘点',
                            1: '盘点中',
                            2: '已完成',
                            3: '已取消'
                        };
                        return statusText[d.status] || '未知';
                    }
                },{
                    field: 'total_products',
                    title: '产品总数',
                    align: 'center',
                    width: 100
                },{
                    field: 'checked_products',
                    title: '已盘点',
                    align: 'center',
                    width: 100
                },{
                    field: 'creator_name',
                    title: '创建人',
                    align: 'center',
                    width: 100
                },{
                    field: 'notes',
                    title: '备注',
                    minWidth: 120
                }
            ]]
        });
        
        console.log('测试页面初始化完成');
    }
</script>
{/block}
