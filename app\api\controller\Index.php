<?php


declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use app\api\model\EditLog;
use think\facade\Db;
use app\Produce\model\ProductionFeeding;

class Index extends BaseController
{
	public function index()
	{
		return 'hello';
	}
	//上传文件
	public function upload()
	{
		if (request()->isPost()) {
			$param = get_params();
			$sourse = 'file';
			if(isset($param['sourse'])){
				$sourse = $param['sourse'];
			}
			if($sourse == 'file' || $sourse == 'tinymce'){
				if(request()->file('file')){
					$file = request()->file('file');
				}
				else{
					return to_assign(1, '没有选择上传文件');
				}
			}
			else{
				if (request()->file('editormd-image-file')) {
					$file = request()->file('editormd-image-file');
				} else {
					return to_assign(1, '没有选择上传文件');
				}
			}
			// 获取上传文件的hash散列值
			$sha1 = $file->hash('sha1');
			$md5 = $file->hash('md5');
			$rule = [
				'image' => 'jpg,png,jpeg,gif',
				'doc' => 'txt,doc,docx,ppt,pptx,xls,xlsx,pdf',
				'file' => 'zip,gz,7z,rar,tar',
				'video' => 'mpg,mp4,mpeg,avi,wmv,mov,flv,m4v',
				'audio' => 'mp3,wav,wma,flac,midi',
			];
			$fileExt = $rule['image'] . ',' . $rule['doc'] . ',' . $rule['file'] . ',' . $rule['video'] . ',' . $rule['audio'];
			//1M=1024*1024=1048576字节
			$file_size = get_system_config('system','upload_max_filesize');
			if(!isset($file_size)){
				$file_size=50;
			}
			$fileSize = $file_size * 1024 * 1024;
			if (isset($param['type']) && $param['type']) {
				$fileExt = $rule[$param['type']];
			}
			if (isset($param['size']) && $param['size']) {
				$fileSize = $param['size'];
			}
			$validate = \think\facade\Validate::rule([
				'image' => 'require|fileSize:' . $fileSize . '|fileExt:' . $fileExt,
			]);
			$file_check['image'] = $file;
			if (!$validate->check($file_check)) {
				return to_assign(1, $validate->getError());
			}
			// 日期前綴
			$dataPath = date('Ym');
			$use = 'thumb';
			$filename = \think\facade\Filesystem::disk('public')->putFile($dataPath, $file, function () use ($md5) {
				return set_salt(5).'_'.$md5;
			});
			if ($filename) {
				//写入到附件表
				$data = [];
				$path = get_config('filesystem.disks.public.url');
				$data['filepath'] = $path . '/' . $filename;
				$data['name'] = $file->getOriginalName();
				$data['mimetype'] = $file->getOriginalMime();
				$data['fileext'] = $file->extension();
				$data['filesize'] = $file->getSize();
				$data['filename'] = $filename;
				$data['sha1'] = $sha1;
				$data['md5'] = $md5;
				$data['module'] = \think\facade\App::initialize()->http->getName();
				$data['action'] = app('request')->action();
				$data['uploadip'] = app('request')->ip();
				$data['create_time'] = time();
				$data['user_id'] = $this->uid;
				if ($data['module'] = 'admin') {
					//通过后台上传的文件直接审核通过
					$data['status'] = 1;
					$data['admin_id'] = $data['user_id'];
					$data['audit_time'] = time();
				}
				$data['use'] = request()->has('use') ? request()->param('use') : $use; //附件用处
				$res['id'] = Db::name('file')->insertGetId($data);
				$res['filepath'] = $data['filepath'];
				$res['name'] = $data['name'];
				$res['uid'] = $this->uid;
				$res['filename'] = $data['filename'];
				$res['filesize'] = $data['filesize'];
				$res['fileext'] = $data['fileext'];
				add_log('upload', $data['user_id'], $data,'文件');
				if($sourse == 'editormd'){
					//editormd编辑器上传返回
					return json(['success'=>1,'message'=>'上传成功','url'=>$data['filepath']]);
				}
				else if($sourse == 'tinymce'){
					//tinymce编辑器上传返回
					return json(['success'=>1,'message'=>'上传成功','location'=>$data['filepath']]);
				}
				else{
					//普通上传返回
					return to_assign(0, '上传成功', $res);
				}            
			 }
			 else {
				return to_assign(1, '上传失败，请重试');
			 }
		}
		else{
			return to_assign(1, '非法请求');
		} 
	}
	
	//执行分块上传的控制器方法
	public function chunkUpload() {
		if ($this->request->isPost()) {
			//执行分块上传流程
			$data = $this->request->post();
			//判断是否是分块上传
			if ($data['type'] === 'chunk') {
				$file = request()->file('file');
				
				$rule = [
					'image' => 'jpg,png,jpeg,gif',
					'doc' => 'txt,doc,docx,ppt,pptx,xls,xlsx,pdf',
					'file' => 'zip,gz,7z,rar,tar',
					'video' => 'mpg,mp4,mpeg,avi,wmv,mov,flv,m4v',
					'audio' => 'mp3,wav,wma,flac,midi',
				];
				$fileExt = $rule['image'] . ',' . $rule['doc'] . ',' . $rule['file'] . ',' . $rule['video'] . ',' . $rule['audio'];
				//1M=1024*1024=1048576字节
				$file_size = get_system_config('system','upload_max_filesize');
				if(!isset($file_size)){
					$file_size=50;
				}
				$fileSize = $file_size * 1024 * 1024;
				$validate = \think\facade\Validate::rule([
					'image' => 'require|fileSize:' . $fileSize . '|fileExt:' . $fileExt,
				]);
				$file_check['image'] = $file;
				if (!$validate->check($file_check)) {
					return to_assign(1, $validate->getError());
				}
				
				//获取对应的上传配置
				$fs = \think\facade\Filesystem::disk('public');
				$ext = $file->extension();
				$chunkPath = $data['file_id'].'/'.$file->md5().($ext ? '.'.$ext : '');
				//存储分片文件到指定路径
				$savename = $fs->putFileAs( 'chunk', $file,$chunkPath);
				if (!$savename) {
					return json([
						'code' => 1,
						'msg' => '上传失败',
						'data' => [],
					]);
				}
				if (!$data['is_end']) {
					$filepath = '';
				} else {
					//合并块文件
					$fileUrl = '';
					$chunkSaveDir = \think\facade\Filesystem::getDiskConfig('public');
					$smallChunkDir = $chunkSaveDir['root'].'/chunk/'.$data['file_id'];
					//获取已存储的属于源文件的所有分块文件 进行合并
					if ($handle = opendir($smallChunkDir)) {
						$chunkList = [];
						$modifyTime = [];
						while (false !== ($file = readdir($handle))) {
							if ($file != "." && $file != "..") {
								$temp['path'] = $smallChunkDir.'/'.$file;
								$temp['modify'] = filemtime($smallChunkDir.'/'.$file);
								$chunkList[] = $temp;
								$modifyTime[] = $temp['modify'];
							}
						}
						//对分块文件进行排序
						array_multisort($modifyTime,SORT_ASC,$chunkList);
						$saveDir = \think\facade\Filesystem::getDiskConfig('public');
						$saveName = md5($data['file_id'].$data['file_name']).'.'.$data['file_extension'];
						$newPath = $saveDir['root'].'/'.date('Ym').'/'.$saveName;
						if (!file_exists($saveDir['root'].'/'.date('Ym'))) {
							mkdir($saveDir['root'].'/'.date('Ym'),0777,true);
						}
						$newFileHandle = fopen($newPath,'a+b');
						foreach ($chunkList as $item) {
							fwrite($newFileHandle,file_get_contents($item['path']));
							unlink($item['path']);
						}
						rmdir($smallChunkDir);
						//将合并后的文件存储到指定路径
						$fileUrl = $saveDir['url'].'/'.date('Ym').'/'.$saveName;
						fclose($newFileHandle);
						closedir($handle);
					} else {
						return json([
							'code' => 1,
							'msg' => '目录：'.$chunkSaveDir['root'].'/chunk/'.$data['file_id'].'不存在',
							'data' => [],
						]);
					}
					$filepath = $fileUrl;
				}
				$res=[];
				//合并流程结束
				if ($filepath!='') {
					$fileinfo = [];
					$fileinfo['filepath'] = $filepath;
					$fileinfo['name'] = $data['file_name'];
					$fileinfo['fileext'] = $data['file_extension'];
					$fileinfo['filesize'] = $data['file_size'];
					$fileinfo['filename'] = date('Ym').'/'.$saveName;
					$fileinfo['sha1'] = $data['file_id'];
					$fileinfo['md5'] = $data['file_id'];
					$fileinfo['module'] = \think\facade\App::initialize()->http->getName();
					$fileinfo['action'] = app('request')->action();
					$fileinfo['uploadip'] = app('request')->ip();
					$fileinfo['create_time'] = time();
					$fileinfo['user_id'] = get_login_admin('id') ? get_login_admin('id') : 0;
					if ($fileinfo['module'] = 'admin') {
						//通过后台上传的文件直接审核通过
						$fileinfo['status'] = 1;
						$fileinfo['admin_id'] = $fileinfo['user_id'];
						$fileinfo['audit_time'] = time();
					}
					$fileinfo['use'] = 'big';
					$res['id'] = Db::name('file')->insertGetId($fileinfo);
					$res['filepath'] = $fileinfo['filepath'];
					$res['name'] = $fileinfo['name'];
					$res['filename'] = $fileinfo['filename'];
					$res['filesize'] = $fileinfo['filesize'];
					$res['fileext'] = $fileinfo['fileext'];
					add_log('upload', $fileinfo['user_id'], $fileinfo);
				} 
				return to_assign(0, '上传成功', $res);
			}
		}
		else{
			return to_assign(1, '非法请求', $res);
		}
	}

	//取消上传，删除临时文件
	public function clearChunk() {
		if ($this->request->isPost()) {
			$param = get_params();
			$saveDir = \think\facade\Filesystem::getDiskConfig('public');
			$smallChunkDir = $saveDir['root'].'/chunk/'.$param['file_id'];
			if(!is_dir($smallChunkDir)){
				return to_assign(0, '上传的临时文件已删除');
			}
			//获取已存储的属于源文件的所有分块文件
			if ($handle = opendir($smallChunkDir)) {
				while (false !== ($file = readdir($handle))) {
					if ($file != "." && $file != "..") {
						$temp['path'] = $smallChunkDir.'/'.$file;
						unlink($temp['path']);
					}
				}
				rmdir($smallChunkDir);
				closedir($handle);
				return to_assign(0, '已取消上传');
			}
		}
	}
 
	// 获取文件信息
	public function get_file_info()
	{
		$param = get_params();
		if (empty($param['file_id'])) {
			return to_assign(1, '文件ID不能为空');
		}
		
		$file = Db::name('File')->where('id', $param['file_id'])->find();
		if (empty($file)) {
			return to_assign(1, '文件不存在');
		}
		
		// 确保文件路径格式正确
		if (!empty($file['filepath']) && 
		    (strpos($file['filepath'], '/') !== 0) && 
		    (strpos($file['filepath'], 'http') !== 0)) {
			$file['filepath'] = '/' . $file['filepath'];
		}
		
		return to_assign(0, '获取成功', $file);
	}
 
    //附件重命名
    public function file_edit()
    {
        $param = get_params();
		if (Db::name('File')->where('id',$param['id'])->update(['name'=>$param['title']]) !== false) {
			add_log('edit', $param['id'], $param,'文件名称');
			return to_assign(0, "操作成功");
		} else {
			return to_assign(1, "操作失败");
		}
    }

    //获取编辑记录
    public function load_log()
    {
        $param = get_params();
		$log = new EditLog();
		$list = $log->datalist($param);
        return to_assign(0, '', $list);
    }


    //清空缓存
    public function cache_clear()
    {
        \think\facade\Cache::clear();
        return to_assign(0, '系统缓存已清空');
    }

    // 测试邮件发送
    public function email_test()
    {
        $sender = get_params('email');
        //检查是否邮箱格式
        if (!is_email($sender)) {
            return to_assign(1, '测试邮箱码格式有误');
        }
        $email_config = \think\facade\Db::name('config')->where('name', 'email')->find();
        $config = unserialize($email_config['content']);
        $content = $config['template'];
        //所有项目必须填写
        if (empty($config['smtp']) || empty($config['smtp_port']) || empty($config['smtp_user']) || empty($config['smtp_pwd'])) {
            return to_assign(1, '请完善邮件配置信息');
        }

        $send = send_email($sender, '测试邮件', $content);
        if ($send) {
            return to_assign(0, '邮件发送成功');
        } else {
            return to_assign(1, '邮件发送失败');
        }
    }
	
	//获取未读消息
	public function get_msg()
	{
		$msg_map[] = ['to_uid', '=', $this->uid];
		$msg_map[] = ['read_time', '=', 0];
		$msg_map[] = ['delete_time', '=', 0];
		$msg_count = Db::name('Msg')->where($msg_map)->count();
		return to_assign(0, 'ok', $msg_count);
	}

    //获取部门
    public function get_department()
    {
        $department = get_department();
        return to_assign(0, '', $department);
    }

    //获取部门树形节点列表，用于tree前端组件
    public function get_department_tree()
    {
        $department = get_department();
        $list = get_tree($department);
        $data['trees'] = $list;
        return json($data);
    }
	
    //获取下属部门树形节点列表，用于tree前端组件
    public function get_department_tree_sub()
    {
		if($this->uid==1){
			$department = get_department();
		}
		else{
			$dids = get_leader_departments($this->uid);
			$department = Db::name('Department')->order('sort desc,id asc')->where([['status','=',1],['id','in',$dids]])->select()->toArray();
		}
        $list = get_tree($department,$department[0]['pid']);
        $data['trees'] = $list;
        return json($data);
    }
	
    //获取部门树形节点列表，用于X-select前端组件
    public function get_department_select()
    {
		$keyword = get_params('keyword');
		$selected = [];
		if(!empty($keyword)){
			$selected = explode(",",$keyword);
		}		
        $department = get_department();
        $list = get_select_tree($department, 0,0,$selected);
		return to_assign(0, '',$list);
    }

    //获取所有员工，did>0时时获取部门员工,用于picker签单组件
    public function get_employee($did = 0)
    {
		$where=[];
		$whereOr=[];
		if (!empty($did)) {
			$admin_array = Db::name('DepartmentAdmin')->where('department_id',$did)->column('admin_id');
			$map1=[
				['a.id','in',$admin_array],
			];
			$map2=[
				['a.did', '=', $did],
			];
			$whereOr =[$map1,$map2];
		}		
		$where[] = ['a.id', '>', 1];
		$where[] = ['a.status', '=', 1];
		$employee = Db::name('admin')
			->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,d.title as department')
            ->alias('a')
			->join('Position p', 'p.id = a.position_id','left')
			->join('Department d', 'a.did = d.id','left')
			->where($where)
			->where(function ($query) use($whereOr) {
				if (!empty($whereOr)){
					$query->whereOr($whereOr);
				}
			})
			->group('a.id')
			->order('a.id desc')
			->select();
        return to_assign(0, '', $employee);
    }
	
   //获取所有下属员工，did>0时时获取部门员工,用于picker签单组件
    public function get_employee_sub($did = 0)
    {
		$where=[];
		$whereOr=[];
		if (!empty($did)) {
			$admin_array = Db::name('DepartmentAdmin')->where('department_id',$did)->column('admin_id');
			$map1=[
				['a.id','in',$admin_array],
			];
			$map2=[
				['a.did', '=', $did],
			];
			$whereOr =[$map1,$map2];
		}
		else{
			if($this->uid>1){
				$dids = get_leader_departments($this->uid);
				$where[] = ['a.did', 'in', $dids];
			}
		}
		$where[] = ['a.id', '>', 1];
		$where[] = ['a.status', '=', 1];
		$employee = Db::name('admin')
			->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,d.title as department')
            ->alias('a')
			->join('Position p', 'p.id = a.position_id','left')
			->join('Department d', 'a.did = d.id','left')
			->where($where)
			->where(function ($query) use($whereOr) {
				if (!empty($whereOr)){
					$query->whereOr($whereOr);
				}
			})
			->group('a.id')
			->order('a.id desc')
			->select();
        return to_assign(0, '', $employee);
    }
	
    //获取所有员工
    public function get_personnel()
    {       
		$param = get_params();
		$where[] = ['a.status', '=', 1];
		$where[] = ['a.id', '>', 1];
		if (!empty($param['keywords'])) {
			$where[] = ['a.name', 'like', '%' . $param['keywords'] . '%'];
		}
		if(!empty($param['ids'])){
			//排除某些员工
			$where[] = ['a.id', 'notin', $param['ids']];
		}
		$rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $list = Db::name('admin')
            ->field('a.id,a.did,a.position_id,a.mobile,a.name,a.nickname,a.sex,a.status,a.thumb,a.username,d.title as department')
            ->alias('a')
            ->join('Department d', 'a.did = d.id')
            ->where($where)
			->order('a.id desc')
			->paginate(['list_rows'=> $rows]);
		return table_assign(0, '', $list);
    }	
	
    //获取所有员工，用于X-select前端组件,did>0时时获取部门员工
    public function get_employee_select($did=0)
    {
		$keyword = get_params('keyword');
		$selected = [];
		if(!empty($keyword)){
			$selected = explode(",",$keyword);
		}
		if($did == 0){
			$employee = Db::name('admin')->field('id as value,name')->where(['status' => 1])->select()->toArray();		
		}
		else{
			$employee = get_department_employee($did);
		}
		$list=[];
		foreach($employee as $k => $v){
			$select = '';
			if(in_array($v['id'],$selected)){
				$select = 'selected';
			}
			$list[]=[
				'value'=>$v['id'],
				'name'=>$v['name'],
				'selected'=>$select
			];
		}
        return to_assign(0, '', $list);
    }	


    //获取某部门的负责人
    public function get_department_leader($uid=0,$pid=0)
    {
        $leaders = get_department_leader($uid,$pid);
        return to_assign(0, '', $leaders);
    }

    //获取职位
    public function get_position()
    {
        $position = Db::name('Position')->field('id,title')->where([['status', '=', 1], ['id', '>', 1]])->select();
        return to_assign(0, '', $position);
    }
	
	//获取消息模板
    public function get_template()
    {
		$param = get_params();
		if (!empty($param['keywords'])) {
			$where[] = ['title', 'like', '%' . $param['keywords'] . '%'];
		}
		$where[] = ['status', '=', 1];
		$rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];		
        $list = Db::name('Template')->field('id,title')->where($where)->paginate(['list_rows'=> $rows]);;
		return table_assign(0, '', $list);
    }
	
	//读取报销类型
	function get_expense_cate()
	{
		$cate = get_base_data('ExpenseCate');
		return to_assign(0, '', $cate);
	}

	//读取费用类型
	function get_cost_cate()
	{
		$cate = get_base_data('CostCate');
		return to_assign(0, '', $cate);
	}

	//读取印章类型
	function get_seal_cate()
	{
		$cate = get_base_data('SealCate');
		return to_assign(0, '', $cate);
	}

	//读取车辆类型
	function get_car_cate()
	{
		$cate = get_base_data('CarCate');
		return to_assign(0, '', $cate);
	}

	//读取企业主体
	function get_subject()
	{
		$subject = get_base_data('Subject');
		return to_assign(0, '', $subject);
	}

	//读取行业类型
	function get_industry()
	{
		$industry = get_base_data('Industry');
		return to_assign(0, '', $industry);
	}

	//读取服务类型
	function get_services()
	{
		$services = get_base_data('Services');
		return to_assign(0, '', $services);
	}
	
	//获取工作类型列表
    public function get_work_cate()
    {
        $cate = get_base_data('WorkCate');
        return to_assign(0, '', $cate);
    }
	//get_list
	public function get_list()  {
		$param=get_params();
		$name=$param['name'];
		$action_id=$param['action_id'];
		$page=$param['page'];
		$limit=$param['limit'];

		// 表名映射，处理前端传入的逻辑表名到实际数据库表名的转换
		$tableMapping = [
			'customer_delivery' => 'customer_order_delivery',  // 客户发货记录
			'customer_payment' => 'customer_order_payment',    // 客户付款记录
			// 可以根据需要添加更多映射
		];

		// 如果存在映射，使用映射后的表名
		$actualTableName = isset($tableMapping[$name]) ? $tableMapping[$name] : $name;

		// 特殊处理客户发货记录，需要关联出库状态
		if ($name === 'customer_delivery') {
			$list = $this->getCustomerDeliveryList($action_id, $page, $limit);
		} else {
			$list = Db::name($actualTableName)->where('order_id',$action_id)->where('delete_time',0)->paginate(['page'=>$page,'list_rows'=>$limit]);
		}

		return table_assign(0, '', $list);
	}

	/**
	 * 获取客户发货记录列表（包含出库状态）
	 */
	private function getCustomerDeliveryList($orderId, $page, $limit) {
		$list = Db::name('customer_order_delivery')
			->alias('d')
			->leftJoin('outbound o', 'o.ref_id = d.order_id AND o.ref_type = "customer_order" AND o.ref_no LIKE CONCAT("%/", d.delivery_no)')
			->field('d.*,
				o.id as outbound_id,
				o.outbound_no,
				o.status as outbound_status,
				o.outbound_time,
				o.outbound_date,
				CASE
					WHEN o.status IS NULL THEN "未创建出库单"
					WHEN o.status = 0 THEN "草稿"
					WHEN o.status = 1 THEN "已提交"
					WHEN o.status = 2 THEN "已审核"
					WHEN o.status = 3 THEN "部分出库"
					WHEN o.status = 4 THEN "全部出库"
					WHEN o.status = 5 THEN "已取消"
					ELSE "未知状态"
				END as warehouse_status,
				CASE
					WHEN o.status IS NULL THEN "gray"
					WHEN o.status = 0 THEN "gray"
					WHEN o.status = 1 THEN "orange"
					WHEN o.status = 2 THEN "blue"
					WHEN o.status = 3 THEN "green"
					WHEN o.status = 4 THEN "green"
					WHEN o.status = 5 THEN "red"
					ELSE "gray"
				END as warehouse_status_color')
			->where('d.order_id', $orderId)
			->where('d.delete_time', 0)
			->order('d.id', 'desc')
			->paginate(['page' => $page, 'list_rows' => $limit]);

		return $list;
	}

	//ajax  集中调用
	public function ajax_Produce_mrp()
	{
    $param = get_params();
    $order_id = $param['order_id'];
    // 使用模型关联查询
    $normalFeedings = \app\Produce\model\ProductionFeeding::with(['details.material'])
        ->where('production_order_id', $order_id)
        ->where('exceed', 0)
        ->where('delete_time', 0)
        ->select()
        ->toArray();
    
    $exceedFeedings = \app\Produce\model\ProductionFeeding::with(['details.material'])
        ->where('production_order_id', $order_id)
        ->where('exceed', 1)
        ->where('delete_time', 0)
        ->select()
        ->toArray();
    
    // 处理数据结构，将详情展平
    $normalDetails = [];
    foreach ($normalFeedings as $feeding) {
        foreach ($feeding['details'] as $detail) {
            $materialId = $detail['material_id'];
            
            // 将投料单ID和详情ID也加入，方便前端关联使用
            $detail['feeding_id'] = $feeding['id'];
            $detail['exceed_flag'] = 0;
            $detail['plan_quantity'] = $detail['required_quantity'];
            $detail['exceed_quantity'] = 0;
            $detail['exceed_actual_quantity'] = 0;
            
            $normalDetails[$materialId] = $detail;
        }
    }
    
    // 处理超量投料数据
    $result = [];
    $processedMaterials = [];
    
    // 先添加所有常规投料数据
    foreach ($normalDetails as $materialId => $detail) {
        $result[] = $detail;
        $processedMaterials[$materialId] = true;
    }
    
    // 处理超量投料数据
    foreach ($exceedFeedings as $feeding) {
        foreach ($feeding['details'] as $detail) {
            $materialId = $detail['material_id'];
            
            if (isset($normalDetails[$materialId])) {
                // 如果该物料存在于常规投料中，更新对应记录的超量数据
                foreach ($result as &$item) {
                    if ($item['material_id'] == $materialId) {
                        $item['exceed_quantity'] = $detail['required_quantity'];
                        $item['exceed_actual_quantity'] = $detail['actual_quantity'];
                        break;
                    }
                }
            } else {
                // 如果该物料仅存在于超量投料中，添加新记录
                $detail['feeding_id'] = $feeding['id'];
                $detail['exceed_flag'] = 1;
                $detail['plan_quantity'] = 0; 
                $detail['exceed_quantity'] = $detail['required_quantity'];
                $detail['exceed_actual_quantity'] = $detail['actual_quantity'];
                $result[] = $detail;
            }
        }
    }
    
    return to_assign(0, '', $result);
}

public function ajax_Produce_mrp_summary()
{
    $param = get_params();
    $order_id = $param['order_id'];

    try {
        // 1. 获取生产订单信息
        $order = \think\facade\Db::name('produce_order')->where('id', $order_id)->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '生产订单不存在', 'count' => 0, 'data' => []]);
        }

        // 2. 获取产品的一级BOM信息
        $bom = \think\facade\Db::name('material_bom')
            ->where('product_id', $order['product_id'])
            ->where('status', 1)
            ->where('delete_time', 0)
            ->order('version desc')
            ->find();

        if (!$bom) {
            return json(['code' => 1, 'msg' => '产品没有BOM信息', 'count' => 0, 'data' => []]);
        }

        // 3. 查询一级BOM明细
        $bomDetails = \think\facade\Db::name('material_bom_detail')
            ->alias('bd')
            ->leftJoin('product p', 'bd.material_id = p.id')
            ->leftJoin('inventory_realtime i', 'bd.material_id = i.product_id')
            ->where('bd.bom_id', $bom['id'])
            ->where('bd.delete_time', 0)
            ->field([
                'bd.material_id',
                'bd.quantity as bom_quantity',
                'bd.loss_rate',
                'bd.sort',
                'p.material_code',
                'p.title as material_name',
                'p.specs as material_specs',
                'p.unit as material_unit',
                'p.safety_stock',
                'COALESCE(SUM(i.quantity), 0) as current_stock',
                'COALESCE(SUM(i.available_quantity), 0) as available_stock'
            ])
            ->group('bd.id, p.id')
            ->order('bd.sort asc, bd.id asc')
            ->select();

        // 4. 处理数据，计算需求量和已领料数量
        $materialSummary = [];
        foreach ($bomDetails as $detail) {
            // 计算需求总量 = BOM用量 × 生产数量 × (1 + 损耗率)
            $requiredQuantity = $detail['bom_quantity'] * $order['quantity'] * (1 + ($detail['loss_rate'] ?: 0) / 100);

            // 查询已领料数量（从投料记录中统计）
            $fedQuantity = $this->getFedQuantity($order_id, $detail['material_id']);

            // 查询已锁定数量（从库存锁定记录中统计）
            $lockedQuantity = $this->getLockedQuantity($order_id, $detail['material_id']);

            $materialSummary[] = [
                'material_id' => $detail['material_id'],
                'material_code' => $detail['material_code'] ?: '',
                'material_name' => $detail['material_name'] ?: '',
                'material_specs' => $detail['material_specs'] ?: '',
                'unit' => $detail['material_unit'] ?: '',
                'bom_quantity' => round(floatval($detail['bom_quantity']), 4), // BOM单位用量
                'loss_rate' => round(floatval($detail['loss_rate'] ?: 0), 2), // 损耗率
                'required_quantity' => round(floatval($requiredQuantity), 4), // 需求总量
                'fed_quantity' => round(floatval($fedQuantity), 4), // 已领料数量
                'locked_quantity' => round(floatval($lockedQuantity), 4), // 已锁定数量
                'current_stock' => round(floatval($detail['current_stock']), 4), // 当前库存
                'available_stock' => round(floatval($detail['available_stock']), 4), // 可用库存
                'safety_stock' => round(floatval($detail['safety_stock'] ?: 0), 4), // 安全库存
                'shortage_quantity' => round(max(0, floatval($requiredQuantity) - floatval($fedQuantity)), 4), // 缺口数量
                'sort' => $detail['sort']
            ];
        }

        return json([
            'code' => 0,
            'msg' => '获取数据成功',
            'count' => count($materialSummary),
            'data' => $materialSummary
        ]);

    } catch (\Exception $e) {
        \think\facade\Log::error('获取BOM物料汇总数据失败', [
            'order_id' => $order_id,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return json([
            'code' => 1,
            'msg' => '获取数据失败：' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
    }
}

/**
 * 获取已领料数量
 * @param int $orderId 生产订单ID
 * @param int $materialId 物料ID
 * @return float
 */
private function getFedQuantity($orderId, $materialId)
{
    try {
        // 从生产领料单明细中统计已领料数量
        $fedQuantity = \think\facade\Db::name('production_material_request_detail')
            ->alias('d')
            ->leftJoin('production_material_request r', 'd.request_no = r.request_no')
            ->where('r.production_order_id', $orderId)
            ->where('d.material_id', $materialId)
            ->where('r.status', '>=', 1) // 只统计已审核的领料单
            ->sum('d.actual_quantity');

        return floatval($fedQuantity ?: 0);

    } catch (\Exception $e) {
        \think\facade\Log::error('获取已领料数量失败', [
            'order_id' => $orderId,
            'material_id' => $materialId,
            'error' => $e->getMessage()
        ]);
        return 0;
    }
}

/**
 * 获取已锁定数量
 * @param int $orderId 生产订单ID
 * @param int $materialId 物料ID
 * @return float
 */
private function getLockedQuantity($orderId, $materialId)
{
    try {
        // 从库存锁定记录中统计已锁定数量
        $lockedQuantity = \think\facade\Db::name('inventory_lock')
            ->where('product_id', $materialId)
            ->where('ref_type', 'production_order')
            ->where('ref_id', $orderId)
            ->where('status', 1) // 有效的锁定
            ->sum('quantity');

        return floatval($lockedQuantity);

    } catch (\Exception $e) {
        \think\facade\Log::error('获取已锁定数量失败', [
            'order_id' => $orderId,
            'material_id' => $materialId,
            'error' => $e->getMessage()
        ]);
        return 0;
    }
}



	//ajax 生产报工列表
	public function ajax_Produce_work_list()
	{
		$param = get_params();
    	$order_id = $param['order_id'];
		$produce_order=Db::name('produce_order')->where('id',$order_id)->find();
		if (empty($produce_order)) {
			return to_assign(1, '', '订单不存在');
		}

		// $process_id=$produce_order['process_id'];
		// $process=Db::name('engineering_process')->where('id',$process_id)->find();
		// if (empty($process)) {
		// 	return to_assign(1, '', '工艺不存在');
		// }

		// $stepsData = json_decode($process['steps'], true);

		
		
		$report_list=Db::name('production_work_report')->where('order_id',$order_id)->select()->toArray();
		foreach ($report_list as &$report) {
			$admin = Db::name('admin')->where('id', $report['worker_id'])->find();
			$report['admin_name'] = $admin ? $admin['name'] : '未知';
			$report['create_time']=$report['create_time']?date('Y-m-d H:i:s',$report['create_time']):'';
		}
		
		return to_assign(0, '', $report_list);
		
		
	}

	//ajax 入库记录列表（重构版本）
	public function ajax_StockList()
	{
		$param = get_params();
		$order_id = intval($param['order_id']);
		$page = intval($param['page'] ?? 1);
		$limit = intval($param['limit'] ?? 10);

		$produce_order = Db::name('produce_order')->where('id', $order_id)->find();
		if (empty($produce_order)) {
			return json(['code' => 1, 'msg' => '订单不存在', 'count' => 0, 'data' => []]);
		}

		try {
			// 从库存流水表查询生产入库记录
			$query = Db::name('inventory_transaction')
				->alias('it')
				->leftJoin('admin a', 'it.created_by = a.id')
				->leftJoin('product p', 'it.product_id = p.id')
				->leftJoin('warehouse w', 'it.warehouse_id = w.id')
				->where('it.ref_type', 'production_order')
				->where('it.ref_id', $order_id)
				->where('it.transaction_type', 'in') // 只查询入库记录
				->field([
					'it.id',
					'it.transaction_no',
					'it.product_id',
					'it.warehouse_id',
					'it.quantity',
					'it.ref_no as order_no',
					'it.notes',
					'it.create_time',
					'a.name as operator',
					'p.title as product_name',
					'p.material_code as product_code',
					'w.name as warehouse_name'
				]);

			// 获取总数
			$count = $query->count();

			// 分页查询
			$list = $query->order('it.create_time desc')
				->page($page, $limit)
				->select()
				->toArray();

			// 处理数据格式
			foreach ($list as &$item) {
				$createTime = intval($item['create_time']); // 确保是整数类型
				$item['total_quantity'] = $item['quantity']; // 兼容原字段名
				$item['status'] = '已入库'; // 库存流水记录都是已完成的
				$item['create_time'] = $createTime ? date('Y-m-d H:i:s', $createTime) : '';
				$item['input_date'] = date('Y-m-d', $createTime ?: time()); // 兼容原字段
				$item['input_no'] = $item['transaction_no']; // 兼容原字段名
			}

			return json([
				'code' => 0,
				'msg' => '获取成功',
				'count' => $count,
				'data' => $list
			]);

		} catch (\Exception $e) {
			\think\facade\Log::error('获取生产入库记录失败', [
				'order_id' => $order_id,
				'error' => $e->getMessage()
			]);

			return json([
				'code' => 1,
				'msg' => '获取入库记录失败：' . $e->getMessage(),
				'count' => 0,
				'data' => []
			]);
		}
	}

	//ajax 首检记录列表
	public function ajax_QcList()
	{
		$param = get_params();
		$order_id = $param['order_id'];
		$produce_order=Db::name('produce_order')->where('id',$order_id)->find();
		if (empty($produce_order)) {
			return to_assign(1, '', '订单不存在');
		}
		 

		$order = new \app\Produce\model\Order();
		$first_article_status = $order->getFirstArticleStatus($order_id);
		return to_assign(0, '', $first_article_status);
	}

	//ajax 获取产品库存 (简化版)
	public function ajax_getProductStockSimple()
	{
		// 1. 获取参数
		$param = get_params();
		$product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
		$warehouse_id = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
		$mode = isset($param['mode']) ? $param['mode'] : 'summary';
		
		// 2. 检查参数
		if (empty($product_id)) {
			$response = ['code' => 1, 'msg' => '产品ID不能为空', 'action' => '', 'url' => '', 'data' => []];
			return json($response);
		}
		
		// 3. 创建日志文件
		$log_file = runtime_path() . 'log/inventory_simple_' . date('Ymd') . '.log';
		
		try {
			// 4. 获取产品信息
			$productInfo = [];
			$product_sql = "SELECT id, title as product_name, material_code as product_code, specs as product_specs, unit 
					  FROM oa_product WHERE id = {$product_id} LIMIT 1";
					  
			try {
				$productResult = Db::query($product_sql);
				if (!empty($productResult)) {
					$productInfo = $productResult[0];
					error_log('[DEBUG] 查询到产品信息: ' . json_encode($productInfo, JSON_UNESCAPED_UNICODE) . "\n", 3, $log_file);
				} else {
					error_log('[DEBUG] 未找到产品信息\n', 3, $log_file);
					$productInfo = [
						'id' => $product_id,
						'product_name' => '未知产品',
						'product_code' => '',
						'product_specs' => '',
						'unit' => ''
					];
				}
			} catch (\Exception $e) {
				error_log('[ERROR] 查询产品信息失败: ' . $e->getMessage() . "\n", 3, $log_file);
				$productInfo = [
					'id' => $product_id,
					'product_name' => '未知产品',
					'product_code' => '',
					'product_specs' => '',
					'unit' => ''
				];
			}
			
			// 5. 获取仓库信息
			$warehouseInfo = [];
			if (!empty($warehouse_id)) {
				$warehouse_sql = "SELECT id, name FROM oa_warehouse WHERE id = {$warehouse_id} LIMIT 1";
				try {
					$warehouseResult = Db::query($warehouse_sql);
					if (!empty($warehouseResult)) {
						$warehouseInfo = $warehouseResult[0];
						error_log('[DEBUG] 查询到仓库信息: ' . json_encode($warehouseInfo, JSON_UNESCAPED_UNICODE) . "\n", 3, $log_file);
					} else {
						error_log('[DEBUG] 未找到仓库信息\n', 3, $log_file);
						$warehouseInfo = [
							'id' => $warehouse_id,
							'name' => '未知仓库'
						];
					}
				} catch (\Exception $e) {
					error_log('[ERROR] 查询仓库信息失败: ' . $e->getMessage() . "\n", 3, $log_file);
					$warehouseInfo = [
						'id' => $warehouse_id,
						'name' => '未知仓库'
					];
				}
			}
			
			// 6. 返回简单的结果
			if ($mode == 'summary') {
				// 汇总模式 - 直接返回产品信息和空库存
				$result = [
					'product_id' => $product_id,
					'product_name' => $productInfo['product_name'],
					'product_code' => $productInfo['product_code'],
					'product_specs' => $productInfo['product_specs'],
					'unit' => $productInfo['unit'],
					'total_quantity' => 0,
					'total_available' => 0,
					'total_allocated' => 0,
					'total_locked' => 0,
					'actual_available' => 0
				];
				
				// 尝试查询库存总量
				if (!empty($warehouse_id)) {
					$inventory_sql = "SELECT SUM(quantity) as total_quantity, 
									  SUM(available_quantity) as total_available, 
									  SUM(allocated_quantity) as total_allocated, 
									  SUM(locked_quantity) as total_locked 
									  FROM oa_inventory 
									  WHERE product_id = {$product_id} AND warehouse_id = {$warehouse_id}";
				} else {
					$inventory_sql = "SELECT SUM(quantity) as total_quantity, 
									  SUM(available_quantity) as total_available, 
									  SUM(allocated_quantity) as total_allocated, 
									  SUM(locked_quantity) as total_locked 
									  FROM oa_inventory 
									  WHERE product_id = {$product_id}";
				}
				
				try {
					$inventoryResult = Db::query($inventory_sql);
					if (!empty($inventoryResult)) {
						$inventoryData = $inventoryResult[0];
						error_log('[DEBUG] 查询到库存信息: ' . json_encode($inventoryData, JSON_UNESCAPED_UNICODE) . "\n", 3, $log_file);
						
						// 确保所有值都是数字
						$result['total_quantity'] = empty($inventoryData['total_quantity']) ? 0 : floatval($inventoryData['total_quantity']);
						$result['total_available'] = empty($inventoryData['total_available']) ? 0 : floatval($inventoryData['total_available']);
						$result['total_allocated'] = empty($inventoryData['total_allocated']) ? 0 : floatval($inventoryData['total_allocated']);
						$result['total_locked'] = empty($inventoryData['total_locked']) ? 0 : floatval($inventoryData['total_locked']);
						$result['actual_available'] = max(0, $result['total_quantity'] - $result['total_allocated'] - $result['total_locked']);
					}
				} catch (\Exception $e) {
					error_log('[ERROR] 查询库存信息失败: ' . $e->getMessage() . "\n", 3, $log_file);
				}
			} else {
				// 明细模式 - 基础结构
				$result = [
					'product_id' => $product_id,
					'product_name' => $productInfo['product_name'],
					'product_code' => $productInfo['product_code'],
					'product_specs' => $productInfo['product_specs'],
					'unit' => $productInfo['unit'],
					'total_quantity' => 0,
					'total_available' => 0,
					'total_allocated' => 0,
					'total_locked' => 0,
					'actual_available' => 0,
					'details' => []
				];
				
				// 尝试查询库存明细
				if (!empty($warehouse_id)) {
					$inventory_detail_sql = "SELECT id as inventory_id, product_id, warehouse_id, 
											quantity, available_quantity, allocated_quantity, locked_quantity, batch_no, unit 
											FROM oa_inventory 
											WHERE product_id = {$product_id} AND warehouse_id = {$warehouse_id}";
				} else {
					$inventory_detail_sql = "SELECT id as inventory_id, product_id, warehouse_id, 
											quantity, available_quantity, allocated_quantity, locked_quantity, batch_no, unit 
											FROM oa_inventory 
											WHERE product_id = {$product_id}";
				}
				
				try {
					$inventoryDetails = Db::query($inventory_detail_sql);
					error_log('[DEBUG] 查询到库存明细数量: ' . count($inventoryDetails) . "\n", 3, $log_file);
					
					if (!empty($inventoryDetails)) {
						// 处理明细
						foreach ($inventoryDetails as $detail) {
							// 获取仓库名称
							$wid = $detail['warehouse_id'];
							$warehouse_name = '未知仓库';
							if ($wid == $warehouse_id && !empty($warehouseInfo)) {
								$warehouse_name = $warehouseInfo['name'];
							} else {
								// 如果不是当前仓库，尝试查询仓库名称
								try {
									$winfo = Db::name('warehouse')->where('id', $wid)->value('name');
									if ($winfo) {
										$warehouse_name = $winfo;
									}
								} catch (\Exception $e) {
									// 忽略错误，使用默认名称
								}
							}
							
							// 确保所有值都是数字
							$quantity = empty($detail['quantity']) ? 0 : floatval($detail['quantity']);
							$available = empty($detail['available_quantity']) ? 0 : floatval($detail['available_quantity']);
							$allocated = empty($detail['allocated_quantity']) ? 0 : floatval($detail['allocated_quantity']);
							$locked = empty($detail['locked_quantity']) ? 0 : floatval($detail['locked_quantity']);
							
							// 累加总量
							$result['total_quantity'] += $quantity;
							$result['total_available'] += $available;
							$result['total_allocated'] += $allocated;
							$result['total_locked'] += $locked;
							
							// 添加明细
							$result['details'][] = [
								'inventory_id' => intval($detail['inventory_id']),
								'warehouse_id' => intval($detail['warehouse_id']),
								'warehouse_name' => $warehouse_name,
								'quantity' => $quantity,
								'available_quantity' => $available,
								'allocated_quantity' => $allocated,
								'locked_quantity' => $locked,
								'batch_no' => $detail['batch_no'] ?? ''
							];
						}
						
						// 计算实际可用
						$result['actual_available'] = max(0, $result['total_quantity'] - $result['total_allocated'] - $result['total_locked']);
					} else if (!empty($warehouse_id)) {
						// 如果没有明细但指定了仓库，添加空明细
						$result['details'][] = [
							'inventory_id' => 0,
							'warehouse_id' => $warehouse_id,
							'warehouse_name' => $warehouseInfo['name'],
							'quantity' => 0,
							'available_quantity' => 0,
							'allocated_quantity' => 0,
							'locked_quantity' => 0,
							'batch_no' => ''
						];
					}
				} catch (\Exception $e) {
					error_log('[ERROR] 查询库存明细失败: ' . $e->getMessage() . "\n", 3, $log_file);
				}
			}
			
			error_log('[DEBUG] 最终返回数据: ' . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PARTIAL_OUTPUT_ON_ERROR) . "\n", 3, $log_file);
			
			// 7. 使用直接JSON返回而不是to_assign
			$response = ['code' => 0, 'msg' => '获取成功', 'action' => '', 'url' => '', 'data' => $result];
			return json($response);
			
		} catch (\Throwable $e) {
			error_log('[ERROR] 处理异常: ' . $e->getMessage() . ' 位置:' . $e->getFile() . ':' . $e->getLine() . "\n", 3, $log_file);
			$response = ['code' => 1, 'msg' => '获取库存信息失败: ' . $e->getMessage(), 'action' => '', 'url' => '', 'data' => []];
			return json($response);
		}
	}


		//库位选择
		public function ajax_getLocation()
		{
			$param = get_params();
			$product_id = isset($param['product_id']) ? intval($param['product_id']) : 0;
			$warehouse_id = isset($param['warehouse_id']) ? intval($param['warehouse_id']) : 0;
			
			if ($warehouse_id <= 0) {
				return json(['code' => 1, 'msg' => '仓库ID不能为空']);
			}
			
			// 获取库位列表
			$where = [
				['warehouse_id', '=', $warehouse_id],
				['status', '=', 1]
			];
			
			$locations = \app\warehouse\model\Location::where($where)->select();
			
			// 如果提供了产品ID，筛选适合该产品的库位
			if ($product_id > 0) {
				// 找出已经存放了该产品的库位
				$productLocations = \app\warehouse\model\Inventory::where('product_id', $product_id)
					->where('quantity', '>', 0)
					->column('location_id');
				
				// 找出空库位
				$usedLocations = \app\warehouse\model\Inventory::where('quantity', '>', 0)
					->column('location_id');
				
				$emptyLocations = $locations->filter(function($location) use ($usedLocations) {
					return !in_array($location->id, $usedLocations);
				})->column('id');
				
				// 合并有该产品的库位和空库位
				$suitableLocations = $locations->filter(function($location) use ($productLocations, $emptyLocations) {
					return in_array($location->id, $productLocations) || in_array($location->id, $emptyLocations);
				});
				
				return json(['code' => 0, 'msg' => '获取成功', 'data' => $suitableLocations]);
			}
			
			return json(['code' => 0, 'msg' => '获取成功', 'data' => $locations]);
		}

		
	
	  /**
     * 获取新的表单令牌
     */
    public function get_token()
    {
        $token = token();
        return json(['code' => 0, 'msg' => 'success', 'data' => $token]);
    }

}
