<div>
    <table id="deliveryLogList" lay-filter="deliveryLogList"></table>
</div>
<script>	
// 修改后的 loadDeliveryLog 函数
function loadDeliveryLog(){
    // 确保从layui获取table模块
    var table = layui.table; 

    // 完整表格配置
    table.render({
        elem: '#deliveryLogList',
        url: '/api/index/get_list?name=customer_order_delivery&action_id='+purchase_id,
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'delivery_no', title: '发货单号', width: 180,templet: function(d){
                return '<a href="javascript:void(0)" onclick="viewDeliveryDetail('+d.id+')">'+d.delivery_no+'</a>'
            }},
            {field: 'expect_time', title: '预计发货时间', width: 160, sort: true},
            {field: 'contact', title: '联系人', width: 150},
			{field: 'address', title: '收货地址'},
            {field: 'status', title: '状态' , width: 150,templet: function(d){
                return ['<span class="layui-badge layui-bg-gray">待发货</span>',
                        '<span class="layui-badge layui-bg-green">已出库</span>',	
						'<span class="layui-badge layui-bg-blue">已完成</span>',
                        '<span class="layui-badge layui-bg-black">已取消</span>'][d.status]
            }},
			{field: 'create_time', title: '操作时间', width: 200},
            {fixed: 'right', title: '操作', toolbar: '#tableToolbar', width: 150}
        ]],
        page: true,
        limit: 10,
        loading: true,
        response: {
            statusCode: 0, // 对应接口的code字段
            countName: 'count', // 总条数字段
            dataName: 'data' // 数据列表字段
        },
        parseData: function(res){ // 数据格式转换
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.count,
                "data": res.data
            };
        }
    });
}
function viewDeliveryDetail(id){
			layer.open({
				type: 2,
				title: '发货详情',
				shadeClose: true,
				shade: 0.8,
				area: ['80%', '80%'],
				content: '/customer/delivery/view.html?id=' + id
			});
}
</script>	