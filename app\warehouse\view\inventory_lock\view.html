{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>库存锁定详情</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>锁定ID</strong></td>
                                <td>{$info.id}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>产品信息</strong></td>
                                <td>
                                    <div>{$info.product_name}</div>
                                    <div class="text-muted" style="font-size:12px;">编码：{$info.material_code}</div>
                                    {if $info.specs}
                                    <div class="text-muted" style="font-size:12px;">规格：{$info.specs}</div>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>仓库</strong></td>
                                <td>{$info.warehouse_name}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>锁定数量</strong></td>
                                <td>
                                    <span class="text-primary" style="font-size:16px;font-weight:bold;">
                                        {$info.quantity} {$info.unit}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>锁定状态</strong></td>
                                <td>
                                    {switch name="info.status"}
                                        {case value="1"}
                                            <span class="layui-badge layui-bg-orange">锁定中</span>
                                        {/case}
                                        {case value="2"}
                                            <span class="layui-badge layui-bg-green">已使用</span>
                                        {/case}
                                        {case value="3"}
                                            <span class="layui-badge layui-bg-gray">已释放</span>
                                        {/case}
                                        {default /}
                                            <span class="layui-badge">未知</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>关联类型</strong></td>
                                <td>{$info.ref_type_text}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>关联单号</strong></td>
                                <td>{$info.ref_no|default='无'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>创建人</strong></td>
                                <td>{$info.creator_name|default='系统'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>创建时间</strong></td>
                                <td>{$info.create_time}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>更新时间</strong></td>
                                <td>{$info.update_time}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>备注</strong></td>
                                <td>
                                    {if $info.notes}
                                        <div style="max-height:100px;overflow-y:auto;">{$info.notes}</div>
                                    {else}
                                        <span class="text-muted">无</span>
                                    {/if}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="layui-form-item" style="margin-top:20px;">
                <div class="layui-input-block">
                    {if $info.status == 1}
                        <button class="layui-btn layui-btn-warm" id="btn_unlock">释放锁定</button>
                        <button class="layui-btn layui-btn-normal" id="btn_use">使用库存</button>
                    {/if}
                    <button class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 释放锁定
        $('#btn_unlock').on('click', function() {
            layer.confirm('确定要释放该锁定记录吗？释放后库存将重新变为可用状态。', {
                icon: 3,
                title: '确认释放'
            }, function(index) {
                tool.post('/warehouse/InventoryLock/unlock', {
                    id: {$info.id}
                }, function(res) {
                    if(res.code == 0) {
                        layer.msg('释放锁定成功');
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if(parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg);
                    }
                });
                layer.close(index);
            });
        });
        
        // 使用库存
        $('#btn_use').on('click', function() {
            layer.confirm('确定要使用该锁定库存吗？使用后将从实际库存中扣除。', {
                icon: 3,
                title: '确认使用'
            }, function(index) {
                tool.post('/warehouse/InventoryLock/useLock', {
                    id: {$info.id}
                }, function(res) {
                    if(res.code == 0) {
                        layer.msg('使用锁定库存成功');
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if(parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg);
                    }
                });
                layer.close(index);
            });
        });
    }
</script>
{/block}
