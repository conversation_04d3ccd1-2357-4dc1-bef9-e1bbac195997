{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>调拨单详情</h3>
        </div>
        <div class="layui-card-body">
            <!-- 基本信息 -->
            <div class="layui-row">
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>调拨单号</strong></td>
                                <td>{$detail.transfer_no}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>源仓库</strong></td>
                                <td>{$detail.from_warehouse.name}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>目标仓库</strong></td>
                                <td>{$detail.to_warehouse.name}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>状态</strong></td>
                                <td>
                                    {switch name="detail.status"}
                                        {case value="0"}
                                            <span class="layui-badge layui-bg-orange">待审核</span>
                                        {/case}
                                        {case value="1"}
                                            <span class="layui-badge layui-bg-blue">已审核</span>
                                        {/case}
                                        {case value="2"}
                                            <span class="layui-badge layui-bg-green">已完成</span>
                                        {/case}
                                        {case value="3"}
                                            <span class="layui-badge layui-bg-gray">已取消</span>
                                        {/case}
                                        {default /}
                                            <span class="layui-badge">{$detail.status_text}</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>总金额</strong></td>
                                <td>
                                    <span style="color:#FF5722;font-size:16px;font-weight:bold;">
                                        ¥{$detail.total_amount|number_format=2}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>创建人</strong></td>
                                <td>{$detail.creator.nickname|default='系统'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>创建时间</strong></td>
                                <td>{$detail.create_time|date='Y-m-d H:i:s',###}</td>
                            </tr>
                            {if $detail.approved_by > 0}
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>审核人</strong></td>
                                <td>{$detail.approver.nickname|default='系统'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>审核时间</strong></td>
                                <td>{$detail.approved_time|date='Y-m-d H:i:s',###}</td>
                            </tr>
                            {/if}
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>备注</strong></td>
                                <td>
                                    {if $detail.notes}
                                        <div style="max-height:100px;overflow-y:auto;">{$detail.notes}</div>
                                    {else}
                                        <span class="text-muted">无</span>
                                    {/if}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 调拨明细 -->
            <div class="layui-card" style="margin-top:20px;">
                <div class="layui-card-header">
                    <h4>调拨明细</h4>
                </div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="line">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>产品信息</th>
                                <th>调拨数量</th>
                                <th>单价</th>
                                <th>金额</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="detail.details" id="item" key="k"}
                            <tr>
                                <td>{$k}</td>
                                <td>
                                    <div style="line-height:18px;">
                                        <div style="font-weight:bold;">{$item.product.title}</div>
                                        <div style="color:#999;font-size:12px;">编码：{$item.product.material_code}</div>
                                        {if $item.product.specs}
                                        <div style="color:#999;font-size:12px;">规格：{$item.product.specs}</div>
                                        {/if}
                                    </div>
                                </td>
                                <td>
                                    <span style="color:#1E9FFF;font-weight:bold;">
                                        {$item.quantity} {$item.product.unit}
                                    </span>
                                </td>
                                <td>¥{$item.price|number_format=2}</td>
                                <td>
                                    <span style="color:#FF5722;font-weight:bold;">
                                        ¥{$item.amount|number_format=2}
                                    </span>
                                </td>
                                <td>{$item.notes|default='无'}</td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="layui-form-item" style="margin-top:20px;">
                <div class="layui-input-block">
                    {if $detail.status == 0}
                        <button class="layui-btn layui-btn-normal" id="btn_approve">审核通过</button>
                        <button class="layui-btn layui-btn-danger" id="btn_cancel">取消调拨</button>
                    {/if}
                    {if $detail.status == 1}
                        <button class="layui-btn layui-btn-normal" id="btn_execute">执行调拨</button>
                    {/if}
                    <button class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 审核按钮
        $('#btn_approve').on('click', function() {
            layer.confirm('确定要审核通过这个调拨单吗？', { icon: 3, title: '提示' }, function (index) {
                $.post('/warehouse/InventoryTransferController/approve', {
                    id: '{$detail.id}'
                }, function(res) {
                    if (res.code === 0) {
                        layer.msg('审核成功', { icon: 1 });
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if (parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg, { icon: 2 });
                    }
                }, 'json');
                layer.close(index);
            });
        });
        
        // 执行按钮
        $('#btn_execute').on('click', function() {
            layer.confirm('确定要执行这个调拨单吗？执行后将实际调拨库存。', { icon: 3, title: '提示' }, function (index) {
                $.post('/warehouse/InventoryTransferController/execute', {
                    id: '{$detail.id}'
                }, function(res) {
                    if (res.code === 0) {
                        layer.msg('执行成功', { icon: 1 });
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if (parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg, { icon: 2 });
                    }
                }, 'json');
                layer.close(index);
            });
        });
        
        // 取消按钮
        $('#btn_cancel').on('click', function() {
            layer.confirm('确定要取消这个调拨单吗？', { icon: 3, title: '提示' }, function (index) {
                $.post('/warehouse/InventoryTransferController/cancel', {
                    id: '{$detail.id}'
                }, function(res) {
                    if (res.code === 0) {
                        layer.msg('取消成功', { icon: 1 });
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if (parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg, { icon: 2 });
                    }
                }, 'json');
                layer.close(index);
            });
        });
    }
</script>
{/block}
