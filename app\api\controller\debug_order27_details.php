<?php
/**
 * 调试订单27的领料明细数据
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

use think\facade\Db;

echo "<pre>";
echo "=== 调试订单27的领料明细数据 ===\n\n";

try {
    // 查询订单27的领料明细
    $exceedDetails = Db::name('production_material_request_detail')
        ->alias('pmrd')
        ->leftJoin('production_material_request pmr', 'pmrd.request_id = pmr.id')
        ->where('pmr.production_order_id', 27)
        ->where('pmr.delete_time', 0)
        ->field('pmrd.material_id, pmrd.material_name, pmrd.request_quantity, pmrd.standard_quantity, pmrd.actual_quantity, pmrd.is_excess, pmr.request_no')
        ->select()
        ->toArray();

    echo "找到 " . count($exceedDetails) . " 条领料明细记录：\n\n";

    foreach ($exceedDetails as $index => $detail) {
        echo "记录 " . ($index + 1) . "：\n";
        echo "  领料单号: " . $detail['request_no'] . "\n";
        echo "  物料ID: " . $detail['material_id'] . "\n";
        echo "  物料名称: " . $detail['material_name'] . "\n";
        echo "  申请数量: " . $detail['request_quantity'] . "\n";
        echo "  标准数量: " . $detail['standard_quantity'] . "\n";
        echo "  实际数量: " . $detail['actual_quantity'] . "\n";
        echo "  超领标记: " . $detail['is_excess'] . "\n";
        
        $actualQty = floatval($detail['actual_quantity']);
        $requestQty = floatval($detail['request_quantity']);
        $standardQty = floatval($detail['standard_quantity']);
        $isExcess = intval($detail['is_excess']);
        
        echo "  数值分析:\n";
        echo "    实际数量 > 申请数量 * 1.05: " . ($actualQty > $requestQty * 1.05 ? "是" : "否") . "\n";
        echo "    实际数量 > 标准数量 * 1.05: " . ($standardQty > 0 && $actualQty > $standardQty * 1.05 ? "是" : "否") . "\n";
        echo "    超领标记 = 1: " . ($isExcess == 1 ? "是" : "否") . "\n";
        
        if ($isExcess == 1 || $actualQty > $requestQty * 1.05 || ($standardQty > 0 && $actualQty > $standardQty * 1.05)) {
            echo "  ⚠️ 这条记录应该被识别为超领！\n";
        } else {
            echo "  ✅ 这条记录正常\n";
        }
        echo "\n";
    }

    // 查询对应的领料单信息
    echo "=== 对应的领料单信息 ===\n";
    $requests = Db::name('production_material_request')
        ->where('production_order_id', 27)
        ->where('delete_time', 0)
        ->field('id, request_no, request_type, has_excess, status')
        ->select()
        ->toArray();

    foreach ($requests as $request) {
        echo "领料单: " . $request['request_no'] . "\n";
        echo "  类型: " . $request['request_type'] . " (1=正常,2=补料,3=超领)\n";
        echo "  超领标记: " . $request['has_excess'] . "\n";
        echo "  状态: " . $request['status'] . "\n\n";
    }

} catch (\Exception $e) {
    echo "查询失败: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
