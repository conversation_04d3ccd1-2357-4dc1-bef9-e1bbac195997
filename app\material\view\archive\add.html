{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.p-4 {
    padding: 15px;
}
.p-page {
    padding: 15px;
}
.layui-td-gray {
    background-color: #f8f8f8;
    font-weight: bold;
}
.upload-item {
    position: relative;
    display: inline-block;
    margin: 5px;
}
.upload-item img {
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 工艺选择对话框样式 */
.process-item {
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.process-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #1E9FFF !important;
}

.process-item.selected {
    background: #1E9FFF !important;
    color: white !important;
    border-color: #1E9FFF !important;
    box-shadow: 0 4px 12px rgba(30, 159, 255, 0.3);
}

#selected-process-info {
    transition: all 0.3s ease;
    min-height: 40px;
    display: flex;
    align-items: center;
}

#selected-process-info:hover {
    background: #f0f0f0;
}

/* 搜索框样式 */
#process-search {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
}

#process-search:focus {
    border-color: #1E9FFF;
    box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.2);
}
</style>
{/block}

{block name="body"}
<div class="layui-tab layui-tab-brief" lay-filter="materialTab">
    <ul class="layui-tab-title">
        <li class="layui-this">基础资料</li>
        <li>质检信息</li>
        <li>价格信息</li>
        <li>价格管理</li>
        <li>工艺管理</li>
        <li>BOM管理</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基础资料 -->
        <div class="layui-tab-item layui-show">
            <form class="layui-form p-page">
                {if condition="$id > 0"}
                <input type="hidden" name="id" value="{$id}" />
                {/if}
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">物料编号<font>*</font></td>
                        <td>
                            <input type="text" name="material_code" value="{$detail.material_code ?? ''}" lay-verify="required" lay-reqText="请输入物料编号" autocomplete="off" placeholder="请输入物料编号" class="layui-input" id="material_code_input">
                            <div style="margin-top: 5px;">
                                <input type="checkbox" name="use_system_code" id="use_system_code" title="系统自动编号" lay-skin="primary" lay-filter="use_system_code">
                                <label for="use_system_code" style="margin-left: 5px; font-size: 12px; color: #666;">系统自动编号</label>
                            </div>
                        </td>
                        <td class="layui-td-gray">物料名称<font>*</font></td>
                        <td><input type="text" name="title" value="{$detail.title ?? ''}" lay-verify="required" lay-reqText="请输入物料名称" autocomplete="off" placeholder="请输入物料名称" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料分类<font>*</font></td>
                        <td>
                            <select name="cate_id" lay-verify="required" lay-reqText="请选择物料分类">
                                <option value="">请选择物料分类</option>
                                {volist name=":set_recursion(get_base_data('ProductCate'))" id="v"}
                                <option value="{$v.id}" {if condition="isset($detail['cate_id']) && $detail['cate_id'] == $v.id"}selected{/if}>{$v.title}</option>
                                {/volist}
                            </select>
                        </td>
                        <td class="layui-td-gray">基本单位<font>*</font></td>
                        <td>
                            <select name="unit" lay-verify="required" lay-reqText="请选择基本单位">
                                <option value="">请选择基本单位</option>
                                {volist name="units" id="unit"}
                                <option value="{$unit.name}" {if condition="isset($detail['unit']) && $detail['unit'] == $unit.name"}selected{/if}>{$unit.name}</option>
                                {/volist}
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料规格</td>
                        <td><input type="text" name="specs" value="{$detail.specs ?? ''}" autocomplete="off" placeholder="请输入物料规格" class="layui-input"></td>
                        <td class="layui-td-gray">物料类型<font>*</font></td>
                        <td>
                            <input type="radio" name="source_type" value="1" title="自产" {if condition="!isset($detail['source_type']) || $detail['source_type'] == 1"}checked{/if}>
                            <input type="radio" name="source_type" value="2" title="外购" {if condition="isset($detail['source_type']) && $detail['source_type'] == 2"}checked{/if}>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td>
                            <input type="radio" name="status" value="1" title="启用" {if condition="!isset($detail['status']) || $detail['status'] == 1"}checked{/if}>
                            <input type="radio" name="status" value="0" title="禁用" {if condition="isset($detail['status']) && $detail['status'] == 0"}checked{/if}>
                        </td>
                        <td class="layui-td-gray">生产商</td>
                        <td><input type="text" name="producer" value="{$detail.producer ?? ''}" autocomplete="off" placeholder="请输入生产商" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">采购周期(天)</td>
                        <td><input type="number" name="purchase_cycle" value="{$detail.purchase_cycle ?? ''}" autocomplete="off" placeholder="请输入采购周期" class="layui-input"></td>
                        <td class="layui-td-gray">库存阙值</td>
                        <td><input type="number" name="stock" value="{$detail.stock ?? ''}" autocomplete="off" placeholder="请输入库存数量" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">默认仓库</td>
                        <td>
                            <select name="default_warehouse">
                                <option value="">请选择仓库</option>
                                {volist name="warehouses" id="warehouse"}
                                <option value="{$warehouse.id}" {if condition="isset($detail['default_warehouse']) && $detail['default_warehouse'] == $warehouse.id"}selected{/if}>{$warehouse.name}</option>
                                {/volist}
                            </select>
                        </td>
                        <td class="layui-td-gray">最小起订量</td>
                        <td><input type="number" name="min_order_qty" value="{$detail.min_order_qty ?? ''}" placeholder="请输入最小起订量" class="layui-input" step="0.01"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最小包装量</td>
                        <td><input type="number" name="min_package_qty" value="{$detail.min_package_qty ?? ''}" placeholder="请输入最小包装量" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">物料等级</td>
                        <td>
                            <select name="material_level">
                                <option value="">请选择</option>
                                <option value="A" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'A'"}selected{/if}>A</option>
                                <option value="B" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'B'"}selected{/if}>B</option>
                                <option value="C" {if condition="isset($detail['material_level']) && $detail['material_level'] == 'C'"}selected{/if}>C</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料来源</td>
                        <td>
                            <select name="material_source">
                                <option value="自购" {if condition="isset($detail['material_source']) && $detail['material_source'] == '自购'"}selected{/if}>自购</option>
                                <option value="加工" {if condition="isset($detail['material_source']) && $detail['material_source'] == '加工'"}selected{/if}>加工</option>
                                <option value="客供" {if condition="isset($detail['material_source']) && $detail['material_source'] == '客供'"}selected{/if}>客供</option>
                                <option value="委外供料" {if condition="isset($detail['material_source']) && $detail['material_source'] == '委外供料'"}selected{/if}>委外供料</option>
                            </select>
                        </td>
                        <td class="layui-td-gray">物料类别</td>
                        <td>
                            <select name="category">
                                <option value="">请选择</option>
                                <option value="主料" {if condition="isset($detail['category']) && $detail['category'] == '主料'"}selected{/if}>主料</option>
                                <option value="辅料" {if condition="isset($detail['category']) && $detail['category'] == '辅料'"}selected{/if}>辅料</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">型号</td>
                        <td><input type="text" name="model" value="{$detail.model ?? ''}" placeholder="请输入型号" class="layui-input"></td>
                        <td class="layui-td-gray">颜色</td>
                        <td><input type="text" name="color" value="{$detail.color ?? ''}" placeholder="请输入颜色" class="layui-input"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">物料描述</td>
                        <td colspan="3"><textarea name="description" placeholder="请输入物料描述" class="layui-textarea">{$detail.description ?? ''}</textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">备注信息</td>
                        <td colspan="3"><textarea name="remark" placeholder="请输入备注信息" class="layui-textarea">{$detail.remark ?? ''}</textarea></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">物料图片</td>
                        <td colspan="3">
                            <button type="button" class="layui-btn" id="uploadMaterialImages">
                                <i class="layui-icon">&#xe67c;</i>上传图片
                            </button>
                            <div class="layui-form-mid layui-word-aux">最多上传6张图片,支持JPG,PNG,BMP格式，最大不超过5M</div>
                            <div id="materialImagesList" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                 
                </table>
            </form>
        </div>
        <!-- 质检信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">质检管理</td>
                        <td>
                            <input type="checkbox" name="quality_management" value="1" lay-skin="switch" lay-text="启用|禁用" lay-filter="quality_management" {if condition="isset($detail['quality_management']) && $detail['quality_management'] == 1"}checked{/if}>
                        </td>
                        <td class="layui-td-gray">免检设置</td>
                        <td>
                            <input type="checkbox" name="quality_exempt" value="1" lay-skin="switch" lay-text="关闭|开启" lay-filter="quality_exempt" {if condition="isset($detail['quality_exempt']) && $detail['quality_exempt'] == 1"}checked{/if}>
                        </td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray" style="vertical-align:top">质检项目</td>
                        <td colspan="3">
                            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="purchase_inspect" title="采购入库检" {if condition="isset($detail['quality_settings']) && in_array('purchase_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="outsource_inspect" title="外协入库检" {if condition="isset($detail['quality_settings']) && in_array('outsource_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="production_inspect" title="生产入库检" {if condition="isset($detail['quality_settings']) && in_array('production_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                                <div>
                                    <input type="checkbox" name="quality_settings[]" value="sales_inspect" title="销售出库检" {if condition="isset($detail['quality_settings']) && in_array('sales_inspect', $detail['quality_settings'])"}checked{/if}>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </form>
        </div>



        <!-- 价格信息 -->
        <div class="layui-tab-item">
            <form class="layui-form p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">参考成本价<font>*</font></td>
                        <td><input type="number" name="reference_cost" value="{$detail.reference_cost ?? ''}" placeholder="请输入参考成本价" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">销售单价（含税）</td>
                        <td><input type="number" name="sales_price" value="{$detail.sales_price ?? ''}" placeholder="请输入销售单价" class="layui-input" step="0.01"></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最低销售单价</td>
                        <td><input type="number" name="min_sales_price" value="{$detail.min_sales_price ?? ''}" placeholder="请输入最低销售单价" class="layui-input" step="0.01"></td>
                        <td class="layui-td-gray">最高销售单价</td>
                        <td><input type="number" name="max_sales_price" value="{$detail.max_sales_price ?? ''}" placeholder="请输入最高销售单价" class="layui-input" step="0.01"></td>
                    </tr>
                   
                </table>
            </form>
        </div>



        <!-- 价格管理 -->
        <div class="layui-tab-item">
            <div class="layui-tab layui-tab-brief" lay-filter="priceTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">供应商价格</li>
                    <li>外协价格</li>
                    <li>附件管理</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 供应商价格 -->
                    <div class="layui-tab-item layui-show">
                        <form class="layui-form p-page">
                            <div style="margin-bottom: 15px;">
                                <button type="button" class="layui-btn layui-btn-sm" onclick="addSupplierPriceRow()">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增供应商
                                </button>
                            </div>
                            <table class="layui-table" lay-skin="line" id="supplierPriceTable">
                                <thead>
                                    <tr>
                                        <th>供应商名称</th>
                                        <th>供应商编号</th>
                                        <th>采购优先级</th>
                                        <th>采购下限</th>
                                        <th>采购上限</th>
                                        <th>含税单价</th>
                                        <th>税率(%)</th>
                                        <th>不含税单价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="supplierPriceTableBody">
                                    <tr class="empty-row">
                                        <td colspan="9" style="text-align: center; color: #999; padding: 30px;">
                                            暂无供应商数据
                                            <br><br>
                                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">
                                                <i class="layui-icon layui-icon-add-1"></i> 添加供应商
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                    </div>

                    <!-- 外协价格 -->
                    <div class="layui-tab-item">
                        <form class="layui-form p-page">
                            <div style="margin-bottom: 15px;">
                                <button type="button" class="layui-btn layui-btn-sm" onclick="addOutsourcePriceRow()">
                                    <i class="layui-icon layui-icon-add-1"></i> 新增外协商
                                </button>
                            </div>
                            <table class="layui-table" lay-skin="line" id="outsourcePriceTable">
                                <thead>
                                    <tr>
                                        <th>外协商名称</th>
                                        <th>外协商编号</th>
                                        <th>外协优先级</th>
                                        <th>外协下限</th>
                                        <th>外协上限</th>
                                        <th>含税单价</th>
                                        <th>税率(%)</th>
                                        <th>不含税单价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="outsourcePriceTableBody">
                                    <tr class="empty-row">
                                        <td colspan="9" style="text-align: center; color: #999; padding: 30px;">
                                            暂无外协商数据
                                            <br><br>
                                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">
                                                <i class="layui-icon layui-icon-add-1"></i> 添加外协商
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                    </div>

                    <!-- 附件管理 -->
                    <div class="layui-tab-item">
                        <form class="layui-form p-page">
                            <table class="layui-table layui-table-form">
                                <tr>
                                    <td class="layui-td-gray" style="vertical-align:top">附件上传</td>
                                    <td colspan="3">
                                        <button type="button" class="layui-btn" id="uploadBtn">
                                            <i class="layui-icon">&#xe67c;</i>上传附件
                                        </button>
                                        <div class="layui-form-mid layui-word-aux">支持上传多个文件，最大不超过10M</div>
                                        <div id="attachmentList" style="margin-top: 10px;"></div>
                                    </td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工艺管理 -->
        <div class="layui-tab-item">
            <form class="layui-form p-page">
                <div style="margin-bottom: 15px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 80px;">
                            <a href="javascript:;" onclick="showProcessSelectDialog()" style="color: #1E9FFF; text-decoration: none;">
                                选择工艺
                            </a>
                        </label>
                        <div class="layui-input-block" style="margin-left: 100px;">
                            <div id="selected-process-info" style="padding: 8px 12px; background: #f8f8f8; border-radius: 4px; color: #999;">
                                请点击"选择工艺"来选择工艺路线
                            </div>
                            <input type="hidden" name="process_template_id" id="process-template-id" value="{$detail.process_template_id|default=''}">
                        </div>
                    </div>
                </div>
                <table class="layui-table" lay-skin="line" id="processTable">
                    <thead>
                        <tr>
                            <th width="80">序号</th>
                            <th>工序名称</th>
                            <th>单价</th>
                            <th>对应不良项</th>
                            <th>工序类型</th>
                        </tr>
                    </thead>
                    <tbody id="processTableBody">
                        <tr class="empty-row">
                            <td colspan="5" style="text-align: center; color: #999; padding: 30px;">
                                请先选择工艺，然后查看包含的工序
                                <br><br>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="showProcessSelectDialog()">
                                    <i class="layui-icon layui-icon-search"></i> 选择工艺
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <!-- BOM管理 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <div style="margin-bottom: 15px;" id="bom-actions">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="add-bom-btn" onclick="createBom()" style="display: none;">
                        <i class="layui-icon layui-icon-add-1"></i> 创建BOM
                    </button>
                </div>

                <div id="bom-content">
                    <div id="bom-loading" style="text-align: center; color: #999; padding: 50px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 检查BOM信息中...
                    </div>

                    <div id="bom-empty" style="text-align: center; color: #999; padding: 50px; display: none;">
                        <i class="layui-icon layui-icon-template" style="font-size: 48px; color: #d2d2d2;"></i>
                        <div style="margin-top: 15px; font-size: 16px;">暂无BOM数据</div>
                        <div style="margin-top: 10px; color: #999;">该产品还没有创建BOM表，点击上方"创建BOM"按钮开始创建</div>
                    </div>

                    <div id="bom-info" style="display: none;">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <strong id="bom-title">BOM信息</strong>
                                <span class="layui-badge layui-bg-green" id="bom-status" style="margin-left: 10px;">启用</span>
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md6">
                                        <div><strong>BOM编号：</strong><span id="bom-code">-</span></div>
                                        <div style="margin-top: 8px;"><strong>BOM名称：</strong><span id="bom-name">-</span></div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <div><strong>版本号：</strong><span id="bom-version">-</span></div>
                                        <div style="margin-top: 8px;"><strong>创建时间：</strong><span id="bom-create-time">-</span></div>
                                    </div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <strong>物料清单：</strong>
                                    <div id="bom-materials" style="margin-top: 10px;">
                                        <!-- BOM物料列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作按钮区域 -->
<div style="text-align: center; padding: 20px; border-top: 1px solid #e8e8e8; background: #fafafa;">
    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="addSubmit" id="submitBtn">
        <i class="layui-icon layui-icon-ok"></i> 保存
    </button>
    <button type="reset" class="layui-btn layui-btn-primary" style="margin-left: 10px;">
        <i class="layui-icon layui-icon-refresh"></i> 重置
    </button>
    <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()" style="margin-left: 10px;">
        <i class="layui-icon layui-icon-close"></i> 取消
    </button>
</div>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool','form','laydate','upload','element'];
function gouguInit() {
    var form = layui.form, tool = layui.tool, laydate = layui.laydate, upload = layui.upload, element = layui.element;

    // 系统自动编号功能
    form.on('checkbox(use_system_code)', function(data){
        var isChecked = data.elem.checked;
        var materialCodeInput = $('#material_code_input');

        if(isChecked){
            materialCodeInput.val('AUTO_GENERATE');
            materialCodeInput.prop('readonly', true);
            materialCodeInput.removeAttr('lay-verify');
            materialCodeInput.css({
                'background-color': '#f5f5f5',
                'color': '#999',
                'cursor': 'not-allowed'
            });
        } else {
            materialCodeInput.val('');
            materialCodeInput.prop('readonly', false);
            materialCodeInput.attr('lay-verify', 'required');
            materialCodeInput.css({
                'background-color': '',
                'color': '',
                'cursor': ''
            });
        }
    });

    // 质检管理开关
    form.on('switch(quality_management)', function(data){
        var isChecked = data.elem.checked;
        if(isChecked){
            $('input[name="quality_settings[]"]').prop('checked', true);
            $('input[name="quality_exempt"]').prop('checked', false);
            form.render();
        } else {
            $('input[name="quality_settings[]"]').prop('checked', false);
            $('input[name="quality_exempt"]').prop('checked', false);
            form.render();
        }
    });

    // 免检设置开关
    form.on('switch(quality_exempt)', function(data){
        var isChecked = data.elem.checked;
        if(isChecked){
            $('input[name="quality_settings[]"]').prop('checked', false);
            form.render('checkbox');
        } else {
            $('input[name="quality_settings[]"]').prop('checked', true);
            form.render('checkbox');
        }
    });

    // 图片上传
    upload.render({
        elem: '#uploadMaterialImages',
        url: '/upload/image',
        multiple: true,
        accept: 'images',
        acceptMime: 'image/*',
        number: 6,
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<img src="' + res.data.url + '" width="100" height="100">' +
                    '<input type="hidden" name="material_images[]" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#materialImagesList').append(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 图纸上传
    upload.render({
        elem: '#uploadMaterialDrawing',
        url: '/upload/image',
        accept: 'images',
        acceptMime: 'image/*',
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<img src="' + res.data.url + '" width="100" height="100">' +
                    '<input type="hidden" name="material_drawing" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#materialDrawingList').html(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 附件上传
    upload.render({
        elem: '#uploadBtn',
        url: '/upload/file',
        accept: 'file',
        multiple: true,
        done: function(res){
            if(res.code == 0){
                var html = '<div class="upload-item">' +
                    '<a href="' + res.data.url + '" target="_blank">' + res.data.name + '</a>' +
                    '<input type="hidden" name="attachments[]" value="' + res.data.url + '">' +
                    '<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeUploadItem(this)">删除</button>' +
                    '</div>';
                $('#attachmentList').append(html);
                layer.msg('上传成功');
            } else {
                layer.msg('上传失败：' + res.msg);
            }
        }
    });

    // 删除上传项
    window.removeUploadItem = function(btn) {
        $(btn).closest('.upload-item').remove();
    };

    // 供应商数据
    var suppliersData = [];
    {if condition="$suppliers"}
    {volist name="suppliers" id="supplier"}
    suppliersData.push({
        id: '{$supplier.id}',
        code: '{$supplier.code}',
        name: '{$supplier.name}'
    });
    {/volist}
    {/if}

    // 新增供应商价格行
    window.addSupplierPriceRow = function() {
        var tbody = $('#supplierPriceTableBody');
        var emptyRow = tbody.find('.empty-row');

        if(emptyRow.length > 0) {
            emptyRow.remove();
        }

        var rowIndex = tbody.find('tr').length;
        var supplierOptions = '';

        for(var i = 0; i < suppliersData.length; i++) {
            if(suppliersData[i].id) {
                supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '">' + suppliersData[i].name + '</option>';
            }
        }

        var newRow = '<tr>' +
            '<td><select name="supplier_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateSupplierInfo(this, ' + rowIndex + ')"><option value="">请选择供应商</option>' + supplierOptions + '</select></td>' +
            '<td><input type="text" name="supplier_prices[' + rowIndex + '][supplier_code]" class="layui-input" readonly></td>' +
            '<td><select name="supplier_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选">首选</option><option value="备选">备选</option></select></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="supplier_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeSupplierPriceRow(this)">删除</button></td>' +
            '</tr>';

        tbody.append(newRow);
        form.render('select');
    };

    // 新增外协价格行
    window.addOutsourcePriceRow = function() {
        var tbody = $('#outsourcePriceTableBody');
        var emptyRow = tbody.find('.empty-row');

        if(emptyRow.length > 0) {
            emptyRow.remove();
        }

        var rowIndex = tbody.find('tr').length;
        var supplierOptions = '';

        for(var i = 0; i < suppliersData.length; i++) {
            if(suppliersData[i].id) {
                supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '">' + suppliersData[i].name + '</option>';
            }
        }

        var newRow = '<tr>' +
            '<td><select name="outsource_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateOutsourceInfo(this, ' + rowIndex + ')"><option value="">请选择外协商</option>' + supplierOptions + '</select></td>' +
            '<td><input type="text" name="outsource_prices[' + rowIndex + '][outsource_code]" class="layui-input" readonly></td>' +
            '<td><select name="outsource_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选">首选</option><option value="备选">备选</option></select></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01"></td>' +
            '<td><input type="number" name="outsource_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01"></td>' +
            '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeOutsourcePriceRow(this)">删除</button></td>' +
            '</tr>';

        tbody.append(newRow);
        form.render('select');
    };

    // 更新供应商信息
    window.updateSupplierInfo = function(selectElement, rowIndex) {
        var selectedOption = $(selectElement).find('option:selected');
        var supplierCode = selectedOption.data('code');
        $(selectElement).closest('tr').find('input[name="supplier_prices[' + rowIndex + '][supplier_code]"]').val(supplierCode || '');
    };

    // 更新外协商信息
    window.updateOutsourceInfo = function(selectElement, rowIndex) {
        var selectedOption = $(selectElement).find('option:selected');
        var supplierCode = selectedOption.data('code');
        $(selectElement).closest('tr').find('input[name="outsource_prices[' + rowIndex + '][outsource_code]"]').val(supplierCode || '');
    };

    // 删除供应商价格行
    window.removeSupplierPriceRow = function(btn) {
        var tbody = $('#supplierPriceTableBody');
        $(btn).closest('tr').remove();

        if(tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="9" style="text-align: center; color: #999; padding: 30px;">暂无供应商数据<br><br><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addSupplierPriceRow()">添加供应商</button></td></tr>');
        }
    };

    // 删除外协价格行
    window.removeOutsourcePriceRow = function(btn) {
        var tbody = $('#outsourcePriceTableBody');
        $(btn).closest('tr').remove();

        if(tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="9" style="text-align: center; color: #999; padding: 30px;">暂无外协商数据<br><br><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="addOutsourcePriceRow()">添加外协商</button></td></tr>');
        }
    };

    // 表单提交
    form.on('submit(addSubmit)', function(data){
        // 收集所有标签页的表单数据
        var submitData = {};

        // 收集所有表单字段
        $('.layui-tab-content form').each(function() {
            $(this).find('input, select, textarea').each(function() {
                var $this = $(this);
                var name = $this.attr('name');
                var value = $this.val();
                var type = $this.attr('type');

                if (name && !$this.prop('disabled')) {
                    if (name.indexOf('supplier_prices[') === 0 || name.indexOf('outsource_prices[') === 0) {
                        return;
                    }

                    if (type === 'checkbox') {
                        if (name.indexOf('[]') > -1) {
                            if (!submitData[name]) submitData[name] = [];
                            if ($this.is(':checked')) {
                                submitData[name].push(value);
                            }
                        } else {
                            if ($this.is(':checked')) {
                                submitData[name] = value;
                            }
                            // 未选中的复选框不设置字段，让后端通过isset判断
                        }
                    } else if (type === 'radio') {
                        if ($this.is(':checked')) {
                            submitData[name] = value;
                        }
                    } else {
                        submitData[name] = value || '';
                    }
                }
            });
        });

        // 收集供应商价格数据
        var supplierPrices = [];
        $('#supplierPriceTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;

            var rowData = {};
            var hasData = false;

            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();

                if (name && value) {
                    if (name.indexOf('supplier_prices[') === 0) {
                        var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
                        var end = name.lastIndexOf(']');
                        if (start > 0 && end > start) {
                            var fieldName = name.substring(start, end);
                            rowData[fieldName] = value;
                            hasData = true;
                        }
                    }
                }
            });

            if (hasData) {
                supplierPrices.push(rowData);
            }
        });

        if (supplierPrices.length > 0) {
            submitData['supplier_prices'] = supplierPrices;
        }

        // 收集外协价格数据
        var outsourcePrices = [];
        $('#outsourcePriceTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;

            var rowData = {};
            var hasData = false;

            $(this).find('input, select').each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();

                if (name && value) {
                    if (name.indexOf('outsource_prices[') === 0) {
                        var start = name.indexOf('[', name.indexOf('[') + 1) + 1;
                        var end = name.lastIndexOf(']');
                        if (start > 0 && end > start) {
                            var fieldName = name.substring(start, end);
                            rowData[fieldName] = value;
                            hasData = true;
                        }
                    }
                }
            });

            if (hasData) {
                outsourcePrices.push(rowData);
            }
        });

        if (outsourcePrices.length > 0) {
            submitData['outsource_prices'] = outsourcePrices;
        }

        // 确保工艺模板ID被包含
        var processTemplateId = $('#process-template-id').val();
        if (processTemplateId) {
            submitData.process_template_id = processTemplateId;
        }

        let callback = function (e) {
            layer.msg(e.msg);
            if (e.code == 0) {
                parent.layui.pageTable.reload();
                parent.layer.closeAll();
            }
        }

        tool.post("/material/archive/add", submitData, callback);
        return false;
    });

    // 标签页切换事件
    element.on('tab(materialTab)', function(data){
        console.log('切换到标签页：', data.index);
    });

    element.on('tab(priceTab)', function(data){
        console.log('切换到价格管理子标签页：', data.index);
    });

    // 初始化现有数据（编辑模式）
    initExistingData();

    // 初始化工艺管理功能
    initProcessManagement();

    // 初始化BOM管理功能
    initBomManagement();

    // 渲染表单
    form.render();
}
</script>

<script>
// 数据变量
var supplierPricesData = {$supplier_prices|default='[]'|json_encode|raw};
var outsourcePricesData = {$outsource_prices|default='[]'|json_encode|raw};
var suppliersData = {$suppliers|default='[]'|json_encode|raw};

// 初始化现有数据
function initExistingData() {
    // 加载供应商价格数据
    if (supplierPricesData && supplierPricesData.length > 0) {
        $('#supplierPriceTableBody .empty-row').remove();
        for (var i = 0; i < supplierPricesData.length; i++) {
            var price = supplierPricesData[i];
            addSupplierPriceRowWithData(price, i);
        }
    }

    // 加载外协价格数据
    if (outsourcePricesData && outsourcePricesData.length > 0) {
        $('#outsourcePriceTableBody .empty-row').remove();
        for (var i = 0; i < outsourcePricesData.length; i++) {
            var price = outsourcePricesData[i];
            addOutsourcePriceRowWithData(price, i);
        }
    }
}

// 添加供应商价格行（带数据）
function addSupplierPriceRowWithData(priceData, rowIndex) {
    var tbody = $('#supplierPriceTableBody');

    var supplierOptions = '<option value="">请选择供应商</option>';
    if (suppliersData && suppliersData.length > 0) {
        for (var i = 0; i < suppliersData.length; i++) {
            var selected = (suppliersData[i].id == priceData.supplier_id) ? 'selected' : '';
            supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" ' + selected + '>' + suppliersData[i].name + '</option>';
        }
    }

    var newRow = '<tr>' +
        '<td><select name="supplier_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateSupplierInfo(this, ' + rowIndex + ')">' + supplierOptions + '</select></td>' +
        '<td><input type="text" name="supplier_prices[' + rowIndex + '][supplier_code]" class="layui-input" readonly value="' + (priceData.supplier_code || '') + '"></td>' +
        '<td><select name="supplier_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选" ' + (priceData.priority == '首选' ? 'selected' : '') + '>首选</option><option value="备选" ' + (priceData.priority == '备选' ? 'selected' : '') + '>备选</option></select></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01" value="' + (priceData.min_qty || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01" value="' + (priceData.max_qty || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01" value="' + (priceData.tax_price || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01" value="' + (priceData.tax_rate || '') + '"></td>' +
        '<td><input type="number" name="supplier_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01" value="' + (priceData.no_tax_price || '') + '"></td>' +
        '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeSupplierPriceRow(this)">删除</button></td>' +
        '</tr>';

    tbody.append(newRow);
    layui.form.render('select');
}

// 添加外协价格行（带数据）
function addOutsourcePriceRowWithData(priceData, rowIndex) {
    var tbody = $('#outsourcePriceTableBody');

    var supplierOptions = '<option value="">请选择外协商</option>';
    if (suppliersData && suppliersData.length > 0) {
        for (var i = 0; i < suppliersData.length; i++) {
            var selected = (suppliersData[i].id == priceData.supplier_id) ? 'selected' : '';
            supplierOptions += '<option value="' + suppliersData[i].id + '" data-code="' + suppliersData[i].code + '" ' + selected + '>' + suppliersData[i].name + '</option>';
        }
    }

    var newRow = '<tr>' +
        '<td><select name="outsource_prices[' + rowIndex + '][supplier_id]" class="layui-input" onchange="updateOutsourceInfo(this, ' + rowIndex + ')">' + supplierOptions + '</select></td>' +
        '<td><input type="text" name="outsource_prices[' + rowIndex + '][outsource_code]" class="layui-input" readonly value="' + (priceData.outsource_code || '') + '"></td>' +
        '<td><select name="outsource_prices[' + rowIndex + '][priority]" class="layui-input"><option value="">请选择</option><option value="首选" ' + (priceData.priority == '首选' ? 'selected' : '') + '>首选</option><option value="备选" ' + (priceData.priority == '备选' ? 'selected' : '') + '>备选</option></select></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][min_qty]" class="layui-input" step="0.01" value="' + (priceData.min_qty || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][max_qty]" class="layui-input" step="0.01" value="' + (priceData.max_qty || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_price]" class="layui-input" step="0.01" value="' + (priceData.tax_price || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][tax_rate]" class="layui-input" step="0.01" value="' + (priceData.tax_rate || '') + '"></td>' +
        '<td><input type="number" name="outsource_prices[' + rowIndex + '][no_tax_price]" class="layui-input" step="0.01" value="' + (priceData.no_tax_price || '') + '"></td>' +
        '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="removeOutsourcePriceRow(this)">删除</button></td>' +
        '</tr>';

    tbody.append(newRow);
    layui.form.render('select');
}

// 工艺管理相关功能
var selectedProcessTemplate = null;
var processTemplateList = [];

// 初始化工艺管理功能
function initProcessManagement() {
    loadProcessTemplates();

    // 延迟检查工艺模板ID，确保DOM完全加载
    setTimeout(function() {
        var $ = layui.jquery;
        var existingProcessId = $('#process-template-id').val();

        if (existingProcessId && existingProcessId != '0' && existingProcessId != '') {
            loadExistingProcessInfo(existingProcessId);
        }
    }, 1000);
}

// 加载工艺模板列表
function loadProcessTemplates() {
    layui.jquery.get('/material/archive/getProcessTemplates', function(res) {
        if (res.code === 0) {
            processTemplateList = res.data;
        }
    });
}

// 显示工艺选择对话框
function showProcessSelectDialog() {
    var content = '<div style="padding: 20px;">';
    content += '<div class="layui-form-item">';
    content += '<div class="layui-input-block">';
    content += '<input type="text" id="process-search" class="layui-input" placeholder="请输入工艺名称或编号进行搜索" style="margin-bottom: 15px;">';
    content += '</div>';
    content += '</div>';
    content += '<div style="max-height: 350px; overflow-y: auto; border: 1px solid #e6e6e6; border-radius: 4px;">';
    content += '<div id="process-list" style="padding: 10px;">';
    content += '<div style="text-align: center; color: #999; padding: 20px;">加载中...</div>';
    content += '</div>';
    content += '</div>';
    content += '</div>';

    layer.open({
        type: 1,
        title: '选择工艺路线',
        content: content,
        area: ['600px', '500px'],
        success: function(layero, index) {
            // 加载工艺列表
            loadProcessListToDialog(layero);

            // 绑定搜索事件
            var searchTimer = null;
            layero.find('#process-search').on('input', function() {
                var keyword = $(this).val();

                // 防抖处理
                if (searchTimer) {
                    clearTimeout(searchTimer);
                }

                searchTimer = setTimeout(function() {
                    searchProcessList(layero, keyword);
                }, 300);
            });
        }
    });
}

// 加载工艺列表到对话框
function loadProcessListToDialog(layero) {
    layero.find('#process-list').html('<div style="text-align: center; color: #999; padding: 20px;">加载中...</div>');

    $.get('/material/archive/getProcessTemplates', function(res) {
        if (res.code === 0) {
            processTemplateList = res.data;
            renderProcessList(layero, processTemplateList);
        } else {
            layero.find('#process-list').html('<div style="text-align: center; color: #999; padding: 20px;">暂无工艺数据</div>');
        }
    }).fail(function() {
        layero.find('#process-list').html('<div style="text-align: center; color: #ff5722; padding: 20px;">加载失败，请重试</div>');
    });
}

// 搜索工艺列表
function searchProcessList(layero, keyword) {
    layero.find('#process-list').html('<div style="text-align: center; color: #999; padding: 20px;">搜索中...</div>');

    $.get('/material/archive/getProcessTemplates', {keywords: keyword}, function(res) {
        if (res.code === 0) {
            renderProcessList(layero, res.data);
        } else {
            layero.find('#process-list').html('<div style="text-align: center; color: #999; padding: 20px;">暂无匹配的工艺</div>');
        }
    }).fail(function() {
        layero.find('#process-list').html('<div style="text-align: center; color: #ff5722; padding: 20px;">搜索失败，请重试</div>');
    });
}

// 渲染工艺列表
function renderProcessList(layero, list) {
    var html = '';
    if (list.length === 0) {
        html = '<div style="text-align: center; color: #999; padding: 20px;">暂无工艺数据</div>';
    } else {
        list.forEach(function(template) {
            var isSelected = selectedProcessTemplate && selectedProcessTemplate.id == template.id;
            html += '<div class="process-item" data-id="' + template.id + '" style="';
            html += 'padding: 12px; margin: 8px 0; border-radius: 6px; cursor: pointer; ';
            html += 'border: 1px solid #e6e6e6; transition: all 0.3s; position: relative;';
            if (isSelected) {
                html += 'background: #1E9FFF; color: white; border-color: #1E9FFF;';
            } else {
                html += 'background: #fff;';
            }
            html += '" onclick="selectProcessTemplate(' + template.id + ', \'' + template.name.replace(/'/g, "\\'") + '\', \'' + (template.template_no || '').replace(/'/g, "\\'") + '\')">';

            // 工艺名称
            html += '<div style="font-weight: bold; font-size: 14px; margin-bottom: 6px; color: ' + (isSelected ? 'white' : '#333') + ';">' + template.name + '</div>';

            // 工艺编号和工序数量
            html += '<div style="display: flex; justify-content: space-between; align-items: center;">';
            if (template.template_no) {
                html += '<span style="font-size: 12px; color: ' + (isSelected ? 'rgba(255,255,255,0.8)' : '#666') + ';">' + template.template_no + '</span>';
            }
            html += '<span style="font-size: 12px; color: ' + (isSelected ? 'rgba(255,255,255,0.8)' : '#999') + ';">' + (template.step_count_text || '暂无工序') + '</span>';
            html += '</div>';

            // 备注信息
            if (template.remark) {
                html += '<div style="font-size: 11px; color: ' + (isSelected ? 'rgba(255,255,255,0.7)' : '#999') + '; margin-top: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">' + template.remark + '</div>';
            }

            html += '</div>';
        });
    }
    layero.find('#process-list').html(html);
}

// 选择工艺模板
function selectProcessTemplate(templateId, templateName, templateNo) {
    // 更新选中状态
    $('.process-item').each(function() {
        var $item = $(this);
        if ($item.data('id') == templateId) {
            $item.css({
                'background': '#1E9FFF',
                'color': 'white',
                'border-color': '#1E9FFF'
            });
        } else {
            $item.css({
                'background': '#f8f8f8',
                'color': '#333',
                'border-color': '#e6e6e6'
            });
        }
    });

    // 设置选中的工艺信息
    selectedProcessTemplate = {
        id: templateId,
        name: templateName,
        template_no: templateNo
    };

    // 更新页面显示
    var displayText = templateName;
    if (templateNo) {
        displayText += ' (' + templateNo + ')';
    }
    $('#selected-process-info').html('<strong>' + displayText + '</strong>').css('color', '#333');
    $('#process-template-id').val(templateId);

    // 加载工序
    loadProcessSteps(templateId);

    // 关闭弹窗
    layer.closeAll();
}

// 加载已有工艺信息（编辑模式）
function loadExistingProcessInfo(processId) {
    layui.jquery.get('/material/archive/getProcessTemplates', function(res) {
        if (res.code === 0 && res.data) {
            var existingProcess = res.data.find(function(template) {
                return template.id == processId;
            });

            if (existingProcess) {
                selectedProcessTemplate = {
                    id: existingProcess.id,
                    name: existingProcess.name,
                    template_no: existingProcess.template_no
                };

                // 更新页面显示
                var displayText = existingProcess.name;
                if (existingProcess.template_no) {
                    displayText += ' (' + existingProcess.template_no + ')';
                }
                layui.jquery('#selected-process-info').html('<strong>' + displayText + '</strong>').css('color', '#333');

                // 加载工序
                loadProcessSteps(processId);
            }
        }
    });
}

// 加载工艺步骤
function loadProcessSteps(templateId) {
    $.get('/material/archive/getProcessSteps', {template_id: templateId}, function(res) {
        if (res.code === 0) {
            var tbody = $('#processTableBody');
            tbody.empty();

            if (res.data && res.data.length > 0) {
                res.data.forEach(function(step, index) {
                    var html = '<tr>';
                    html += '<td>' + (index + 1) + '</td>';
                    html += '<td>' + step.name + '</td>';
                    html += '<td>' + (step.price || '-') + '</td>';
                    html += '<td>' + (step.defect_items || '-') + '</td>';
                    html += '<td>' + (step.type || '数据记录') + '</td>';
                    html += '</tr>';
                    tbody.append(html);
                });

                selectedProcessTemplate = templateId;
                layer.msg('工艺加载成功，共 ' + res.data.length + ' 个工序');
            } else {
                tbody.html('<tr class="empty-row"><td colspan="5" style="text-align: center; color: #999; padding: 30px;">该工艺暂无工序数据</td></tr>');
                layer.msg('该工艺暂无工序数据');
            }
        } else {
            layer.msg(res.msg || '加载工序失败');
        }
    }).fail(function() {
        layer.msg('网络错误，加载工序失败');
    });
}

// 清空工序列表
function clearProcessSteps() {
    var tbody = $('#processTableBody');
    tbody.html('<tr class="empty-row"><td colspan="5" style="text-align: center; color: #999; padding: 30px;">请先选择工艺，然后查看包含的工序<br><br><button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="showProcessSelectDialog()">选择工艺</button></td></tr>');
    selectedProcessTemplate = null;
}

// BOM管理相关功能
$('#add-bom-btn').on('click', function() {
    // 跳转到BOM管理页面
    layer.open({
        type: 2,
        title: '新增BOM',
        content: '/material/bom/add?product_id=' + ({$id} || 0),
        area: ['90%', '90%'],
        end: function() {
            // 刷新BOM列表
            loadBomList();
        }
    });
});

// 加载BOM列表
function loadBomList() {
    if (!{$id}) return;

    $.get('/material/bom/getBomList', {product_id: {$id}}, function(res) {
        if (res.code === 0 && res.data.length > 0) {
            var tbody = $('#bomTableBody');
            tbody.empty();

            res.data.forEach(function(bom, index) {
                var statusText = bom.status == 1 ? '<span style="color: #5FB878;">启用</span>' : '<span style="color: #FF5722;">禁用</span>';
                var html = '<tr>';
                html += '<td>' + bom.bom_code + '</td>';
                html += '<td>' + bom.bom_name + '</td>';
                html += '<td>' + bom.version + '</td>';
                html += '<td>' + statusText + '</td>';
                html += '<td>' + bom.create_time_formatted + '</td>';
                html += '<td>';
                html += '<a href="javascript:;" onclick="viewBom(' + bom.id + ')" class="layui-btn layui-btn-xs">查看</a> ';
                html += '<a href="javascript:;" onclick="editBom(' + bom.id + ')" class="layui-btn layui-btn-xs layui-btn-normal">编辑</a> ';
                html += '<a href="javascript:;" onclick="deleteBom(' + bom.id + ')" class="layui-btn layui-btn-xs layui-btn-danger">删除</a>';
                html += '</td>';
                html += '</tr>';
                tbody.append(html);
            });
        }
    });
}

// BOM操作函数
window.viewBom = function(bomId) {
    layer.open({
        type: 2,
        title: '查看BOM',
        content: '/material/bom/view?id=' + bomId,
        area: ['90%', '90%']
    });
};

window.editBom = function(bomId) {
    layer.open({
        type: 2,
        title: '编辑BOM',
        content: '/material/bom/add?id=' + bomId,
        area: ['90%', '90%'],
        end: function() {
            loadBomList();
        }
    });
};

// BOM管理相关功能
var currentBomData = null;

// 初始化BOM管理功能
function initBomManagement() {
    // 获取产品ID
    var productId = getProductId();
    if (productId) {
        checkProductBom(productId);
    } else {
        showBomEmpty();
    }
}

// 获取产品ID
function getProductId() {
    var urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
}

// 检查产品是否已有BOM
function checkProductBom(productId) {
    layui.jquery.get('/material/bom/checkProductBom', {product_id: productId}, function(res) {
        if (res.code === 1 && res.data && res.data.bom_id) {
            // 产品已有BOM，加载BOM信息
            loadBomInfo(res.data.bom_id);
        } else {
            // 产品没有BOM，显示空状态
            showBomEmpty();
        }
    }).fail(function() {
        showBomError();
    });
}

// 加载BOM信息
function loadBomInfo(bomId) {
    layui.jquery.get('/material/bom/getBomDetail', {id: bomId}, function(res) {
        if (res.code === 0 && res.data) {
            currentBomData = res.data;
            showBomInfo(res.data);
        } else {
            showBomError();
        }
    }).fail(function() {
        showBomError();
    });
}

// 显示BOM信息
function showBomInfo(bomData) {
    layui.jquery('#bom-loading').hide();
    layui.jquery('#bom-empty').hide();
    layui.jquery('#bom-info').show();

    // 隐藏所有按钮
    layui.jquery('#add-bom-btn').hide();

    // 填充BOM信息
    layui.jquery('#bom-title').text('BOM信息 - ' + bomData.bom_name);
    layui.jquery('#bom-code').text(bomData.bom_code || '-');
    layui.jquery('#bom-name').text(bomData.bom_name || '-');
    layui.jquery('#bom-version').text(bomData.version || '1.0');
    layui.jquery('#bom-create-time').text(bomData.create_time_format || '-');

    // 设置状态
    var statusBadge = layui.jquery('#bom-status');
    if (bomData.status == 1) {
        statusBadge.removeClass('layui-bg-gray').addClass('layui-bg-green').text('启用');
    } else {
        statusBadge.removeClass('layui-bg-green').addClass('layui-bg-gray').text('禁用');
    }

    // 显示物料清单
    showBomMaterials(bomData.materials || []);
}

// 显示BOM物料清单
function showBomMaterials(materials) {
    var html = '';
    if (materials.length === 0) {
        html = '<div style="text-align: center; color: #999; padding: 20px;">暂无物料数据</div>';
    } else {
        html += '<table class="layui-table" lay-size="sm">';
        html += '<thead><tr><th>序号</th><th>物料名称</th><th>物料编码</th><th>规格</th><th>单位</th><th>用量</th></tr></thead>';
        html += '<tbody>';
        materials.forEach(function(material, index) {
            html += '<tr>';
            html += '<td>' + (index + 1) + '</td>';
            html += '<td>' + (material.material_name || '-') + '</td>';
            html += '<td>' + (material.material_code || '-') + '</td>';
            html += '<td>' + (material.specs || '-') + '</td>';
            html += '<td>' + (material.unit || '-') + '</td>';
            html += '<td>' + (material.quantity || '0') + '</td>';
            html += '</tr>';
        });
        html += '</tbody></table>';
    }
    layui.jquery('#bom-materials').html(html);
}

// 显示空状态
function showBomEmpty() {
    layui.jquery('#bom-loading').hide();
    layui.jquery('#bom-info').hide();
    layui.jquery('#bom-empty').show();

    // 显示创建按钮
    layui.jquery('#add-bom-btn').show();
}

// 显示错误状态
function showBomError() {
    layui.jquery('#bom-loading').hide();
    layui.jquery('#bom-info').hide();
    layui.jquery('#bom-empty').show();
    layui.jquery('#bom-empty').html('<div style="text-align: center; color: #ff5722; padding: 50px;"><i class="layui-icon layui-icon-close" style="font-size: 48px;"></i><div style="margin-top: 15px;">加载BOM信息失败</div></div>');

    // 显示创建按钮
    layui.jquery('#add-bom-btn').show();
}

// 创建BOM
function createBom() {
    var productId = getProductId();
    if (productId) {
        window.open('/material/bom/add?product_id=' + productId, '_blank');
    } else {
        layer.msg('产品ID无效', {icon: 2});
    }
}



</script>
{/block}
