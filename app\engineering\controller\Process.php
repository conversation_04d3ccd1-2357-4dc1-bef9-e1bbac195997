<?php
declare (strict_types = 1);
namespace app\engineering\controller;
use app\base\BaseController;
use app\engineering\model\Product ;
use app\engineering\model\Process as ProcessModel;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use function get_login_admin;

class Process extends BaseController
{
    /**
     * 工艺列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            if (!empty($param['keywords'])) {
                $where[] = ['name|code', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['product_id'])) {
                $where[] = ['product_id', '=', $param['product_id']];
            }
            if (!empty($param['status'])) {
                $where[] = ['status', '=', $param['status']];
            }
            
            // 使用数组形式的分页参数
            $list = Db::name('engineering_process')
                ->alias('ep')
                ->leftJoin('product p', 'ep.product_id = p.id')
                ->field('ep.*, p.title as product_name')
                ->where($where)
                ->order('ep.id desc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 15,
                    'page' => isset($param['page']) ? (int)$param['page'] : 1,
                    'query' => $param
                ]);
            
            return table_assign(0, '', $list);
        } else {
             // 获取产品列表
        $products = Db::name('product')
        ->field('id, title as name') // 根据实际字段名调整
        ->where('status', 1)
        ->select();
     View::assign('productList', $products);
            return view();
        }
    }

    /**
     * 添加工艺
     */
    public function add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            // 检查必填参数是否存在
            if (!isset($param['name']) || !isset($param['code'])) {
                return to_assign(1, '工艺名称和编码不能为空');
            }
            
            
            // 检查是否存在相同名称或编码的工艺
            $check = Db::name('engineering_process')
                ->where('name', $param['name'])
                ->whereOr('code', $param['code'])
                ->find();
                
            if (!empty($check)) {
                return to_assign(1, '已存在相同名称或编码的工艺');
            }
            
            // 处理工序数据
            if (!empty($param['steps'])) {
                if (is_array($param['steps'])) {
                    $param['steps'] = json_encode($param['steps'], JSON_UNESCAPED_UNICODE);
                }
            } else {
                $param['steps'] = '[]';
            }
            
            $param['create_time'] = time();
            $param['user_id'] =  $this->uid; // 临时使用固定值，以后替换为正确的获取方法 step_order_1
            // 处理工序步骤数据
            $steps = [];
            $stepKeys = ['step_order_', 'step_code_', 'step_name_', 'step_time_', 'step_workers_', 'step_desc_'];
            
            foreach ($param as $key => $value) {
                foreach ($stepKeys as $stepKey) {
                    if (strpos($key, $stepKey) === 0) {
                        $stepIndex = substr($key, strlen($stepKey));
                        if (is_numeric($stepIndex)) {
                            if (!isset($steps[$stepIndex])) {
                                $steps[$stepIndex] = [];
                            }
                            $fieldName = str_replace($stepKey, '', $key);
                            $fieldName = str_replace('_'.$stepIndex, '', $fieldName);
                            $propertyName = str_replace('step_', '', $stepKey);
                            $propertyName = rtrim($propertyName, '_');
                            $steps[$stepIndex][$propertyName] = $value;
                            
                            // 从原始参数中删除这些键
                            unset($param[$key]);
                        }
                    }
                }
            }
            unset($param['scene']);
            
            // 如果收集到了步骤数据，则转换为JSON并添加到param中
            if (!empty($steps)) {
                $param['steps'] = json_encode(array_values($steps), JSON_UNESCAPED_UNICODE);
            }
             
            
            $result = Db::name('engineering_process')->insertGetId($param);
            
            if ($result) {
                return to_assign(0, '添加成功');
            } else {
                return to_assign(1, '添加失败');
            }
        } else {
            // 获取产品列表
            $products = Db::name('product')
                ->field('id, title as name, code, status')
                ->where('status', 1)
                ->select();
            View::assign('products', $products);
            return view();
        }
    }

    /**
     * 编辑工艺
     */
    public function edit()
    {
        $param = get_params();
        
        if (request()->isAjax()) {
            // 检查是否存在相同名称或编码的其他工艺
            $check = Db::name('engineering_process')
                ->where([
                    ['name', '=', $param['name']],
                    ['id', '<>', $param['id']]
                ])
                ->whereOr([
                    ['code', '=', $param['code']],
                    ['id', '<>', $param['id']]
                ])
                ->find();
                
            if (!empty($check)) {
                return to_assign(1, '已存在相同名称或编码的工艺');
            }
            
            // 处理工序数据
            // if (!empty($param['steps']) && is_array($param['steps'])) {
            //     $param['steps'] = json_encode($param['steps'], JSON_UNESCAPED_UNICODE);
            // }
            
            
            $steps = [];
            foreach ($param as $key => $value) {
                if (preg_match('/^step_(order|code|name|time|workers|desc)_(\d+)$/', $key, $matches)) {
                    $index = $matches[2];
                    $field = $matches[1];
                    $steps[$index][$field] = $value;
                    unset($param[$key]);
                }
            }
            $param['steps'] = json_encode($steps, JSON_UNESCAPED_UNICODE);
            $formattedSteps = [];
            foreach ($steps as $step) {
                $formattedSteps[] = [
                    'order' => $step['order'],
                    'code' => $step['code'],
                    'name' => $step['name'],
                    'standard_time' => $step['time'],
                    'workers' => $step['workers'],
                    'description' => $step['desc']
                ];
            }
            $param['steps'] = json_encode($formattedSteps, JSON_UNESCAPED_UNICODE);
            $param['update_time'] = time();
            
            $result = Db::name('engineering_process')
                ->where('id', $param['id'])
                ->update($param);
                
            if ($result) {
                return to_assign(0, '修改成功');
            } else {
                return to_assign(1, '修改失败');
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = Db::name('engineering_process')->where('id', $id)->find();
            if (!empty($detail) && !empty($detail['steps'])) {
                $detail['steps'] = json_decode($detail['steps'], true);
            }
            
            // 获取产品列表
            $products = Db::name('product')->where('status', 1)->where('delete_time', 0)->select();
           
            
            View::assign('detail', $detail);
            View::assign('products', $products);
            
            return view();
        }
    }

    /**
     * 查看工艺详情
     */
    public function detail()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = Db::name('engineering_process')->where('id', $id)->find();
        if (!empty($detail) && !empty($detail['steps'])) {
            $detail['steps'] = json_decode($detail['steps'], true);
        }
        
        // 获取关联产品信息
        if (!empty($detail['product_id'])) {
            $product = Db::name('product')->where('id', $detail['product_id'])->find();
            View::assign('product', $product);
        }
        
        View::assign('detail', $detail);
        return view();
    }

    /**
     * 删除工艺
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        
        // 检查是否有关联的报工记录
        $check = Db::name('engineering_report')->where('process_id', $id)->find();
        if (!empty($check)) {
            return to_assign(1, '该工艺已有关联的报工记录，不能删除');
        }
        
        $result = Db::name('engineering_process')->where('id', $id)->delete();
        if ($result) {
            return to_assign(0, '删除成功');
        } else {
            return to_assign(1, '删除失败');
        }
    }

    /**
     * 更新工艺状态
     */
    public function status()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $status = isset($param['status']) ? $param['status'] : 0;
        
        $result = Db::name('engineering_process')
            ->where('id', $id)
            ->update(['status' => $status, 'update_time' => time()]);
            
        if ($result) {
            return to_assign(0, '操作成功');
        } else {
            return to_assign(1, '操作失败');
        }
    }

    
} 