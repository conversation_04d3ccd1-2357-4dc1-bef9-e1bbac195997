<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\model\InventoryTransaction as InventoryTransactionModel;
use think\response\Json;
use think\facade\Request;
use think\facade\View;
use think\facade\Db;

/**
 * 库存流水查询控制器
 */
class InventoryTransaction extends BaseController
{
    /**
     * 模型对象
     * @var InventoryTransactionModel
     */
    protected $model = null;
    
    public function initialize()
    {
        parent::initialize();
        $this->model = new InventoryTransactionModel();
    }
    
    /**
     * 库存流水列表
     */
    public function index()
    {
        if (Request::isAjax()) {
            // 搜索条件
            $filter = Request::get();
            
            $list = $this->model
                ->alias('it')
                ->leftJoin('product p', 'it.product_id = p.id')
                ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
                ->leftJoin('admin a', 'it.created_by = a.id')
                ->field('it.*, p.title as product_name, p.material_code, p.unit,
                        w.name as warehouse_name, a.name as creator_name')
                ->withSearch(['product_id', 'warehouse_id', 'transaction_type', 'ref_type', 'time_range', 'keywords'], $filter)
                ->order('it.create_time desc')
                ->paginate([
                    'list_rows' => $filter['limit'] ?? 20,
                    'query' => $filter
                ]);
            
            return json(['code' => 0, 'msg' => '', 'count' => $list->total(), 'data' => $list->items()]);
        }
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->field('id, name')
            ->where('status', 1)
            ->select();
        
        // 获取产品列表（用于搜索）
        $products = Db::name('product')
            ->field('id, title, material_code')
            ->where('status', 1)
            ->limit(100)
            ->select();
        
        // 获取流水类型数组
        $transactionTypes = InventoryTransactionModel::getTransactionTypeArr();
        
        // 获取关联类型数组
        $refTypes = InventoryTransactionModel::getRefTypeArr();
        
        View::assign([
            'warehouses' => $warehouses,
            'products' => $products,
            'transactionTypes' => $transactionTypes,
            'refTypes' => $refTypes
        ]);
        
        return View::fetch();
    }
    
    /**
     * 查看流水详情
     */
    public function view()
    {
        $id = Request::param('id');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $info = $this->model
            ->alias('it')
            ->leftJoin('product p', 'it.product_id = p.id')
            ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
            ->leftJoin('admin a', 'it.created_by = a.id')
            ->field('it.*, p.title as product_name, p.material_code, p.unit, p.specs,
                    w.name as warehouse_name, a.name as creator_name')
            ->where('it.id', $id)
            ->find();
        
        if (!$info) {
            return json(['code' => 1, 'msg' => '流水记录不存在']);
        }
        
        // 格式化数据
        $info['transaction_type_text'] = InventoryTransactionModel::getTransactionTypeArr()[$info['transaction_type']] ?? $info['transaction_type'];
        $info['ref_type_text'] = InventoryTransactionModel::getRefTypeArr()[$info['ref_type']] ?? $info['ref_type'];
        
        View::assign('info', $info);
        
        return View::fetch();
    }
    
    /**
     * 获取产品库存流水
     */
    public function getProductTransactions()
    {
        $productId = Request::param('product_id');
        $warehouseId = Request::param('warehouse_id', 0);
        $limit = Request::param('limit', 10);
        
        if (!$productId) {
            return json(['code' => 1, 'msg' => '产品ID不能为空']);
        }
        
        $where = ['it.product_id' => $productId];
        if ($warehouseId > 0) {
            $where['it.warehouse_id'] = $warehouseId;
        }
        
        $list = $this->model
            ->alias('it')
            ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
            ->leftJoin('admin a', 'it.created_by = a.id')
            ->field('it.*, w.name as warehouse_name, a.name as creator_name')
            ->where($where)
            ->order('it.create_time desc')
            ->limit($limit)
            ->select();
        
        return json(['code' => 0, 'msg' => '查询成功', 'data' => $list]);
    }
    
    /**
     * 库存流水统计
     */
    public function statistics()
    {
        if (Request::isAjax()) {
            $filter = Request::get();
            
            // 按仓库统计
            $warehouseStats = $this->model
                ->alias('it')
                ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
                ->field('
                    w.name as warehouse_name,
                    it.warehouse_id,
                    COUNT(it.id) as transaction_count,
                    SUM(CASE WHEN it.transaction_type IN ("inbound", "transfer_in", "adjust_increase") THEN it.quantity ELSE 0 END) as total_inbound,
                    SUM(CASE WHEN it.transaction_type IN ("outbound", "transfer_out", "adjust_decrease") THEN it.quantity ELSE 0 END) as total_outbound
                ')
                ->where('it.create_time', '>=', strtotime('-30 days'))
                ->group('it.warehouse_id')
                ->order('transaction_count desc')
                ->select();
            
            // 按产品统计
            $productStats = $this->model
                ->alias('it')
                ->leftJoin('product p', 'it.product_id = p.id')
                ->field('
                    p.title as product_name,
                    p.material_code,
                    it.product_id,
                    COUNT(it.id) as transaction_count,
                    SUM(CASE WHEN it.transaction_type IN ("inbound", "transfer_in", "adjust_increase") THEN it.quantity ELSE 0 END) as total_inbound,
                    SUM(CASE WHEN it.transaction_type IN ("outbound", "transfer_out", "adjust_decrease") THEN it.quantity ELSE 0 END) as total_outbound
                ')
                ->where('it.create_time', '>=', strtotime('-30 days'))
                ->group('it.product_id')
                ->order('transaction_count desc')
                ->limit(20)
                ->select();
            
            return json([
                'code' => 0,
                'msg' => '查询成功',
                'data' => [
                    'warehouse_stats' => $warehouseStats,
                    'product_stats' => $productStats
                ]
            ]);
        }
        
        return View::fetch();
    }
    
    /**
     * 导出库存流水
     */
    public function export()
    {
        $filter = Request::get();
        
        $list = $this->model
            ->alias('it')
            ->leftJoin('product p', 'it.product_id = p.id')
            ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
            ->leftJoin('admin a', 'it.created_by = a.id')
            ->field('it.*, p.title as product_name, p.material_code, p.unit,
                    w.name as warehouse_name, a.name as creator_name')
            ->withSearch(['product_id', 'warehouse_id', 'transaction_type', 'ref_type', 'time_range', 'keywords'], $filter)
            ->order('it.create_time desc')
            ->limit(5000) // 限制导出数量
            ->select();
        
        // 格式化数据
        $exportData = [];
        $transactionTypes = InventoryTransactionModel::getTransactionTypeArr();
        $refTypes = InventoryTransactionModel::getRefTypeArr();
        
        foreach ($list as $item) {
            $exportData[] = [
                '流水号' => $item['transaction_no'],
                '产品名称' => $item['product_name'],
                '产品编码' => $item['material_code'],
                '仓库' => $item['warehouse_name'],
                '流水类型' => $transactionTypes[$item['transaction_type']] ?? $item['transaction_type'],
                '数量' => $item['quantity'] . ' ' . $item['unit'],
                '变动前数量' => $item['before_quantity'] . ' ' . $item['unit'],
                '变动后数量' => $item['after_quantity'] . ' ' . $item['unit'],
                '关联类型' => $refTypes[$item['ref_type']] ?? $item['ref_type'],
                '关联单号' => $item['ref_no'],
                '备注' => $item['notes'],
                '创建人' => $item['creator_name'],
                '创建时间' => date('Y-m-d H:i:s', $item['create_time'])
            ];
        }
        
        // 这里应该调用实际的Excel导出功能
        // 暂时返回JSON格式
        return json([
            'code' => 0,
            'msg' => '导出成功',
            'data' => $exportData
        ]);
    }
}
