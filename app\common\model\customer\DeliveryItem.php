<?php
namespace app\common\model\customer;

use think\Model;

class DeliveryItem extends Model
{
    // 设置表名
    protected $name = 'customer_delivery_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 关联发货指令
     */
    public function delivery()
    {
        return $this->belongsTo('Delivery', 'delivery_id', 'id');
    }
    
    /**
     * 关联订单商品
     */
    public function orderItem()
    {
        return $this->belongsTo('OrderItem', 'order_item_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\\common\\model\\Product', 'product_id', 'id');
    }
} 