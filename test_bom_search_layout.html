<!DOCTYPE html>
<html>
<head>
    <title>BOM搜索布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 模拟layui样式 */
        .layui-form {
            margin: 0;
        }
        .gg-form-bar {
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e6e6e6;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .border-x {
            border-left: 1px solid #e6e6e6;
            border-right: 1px solid #e6e6e6;
        }
        .layui-input-inline {
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .layui-input {
            height: 38px;
            line-height: 1.3;
            border: 1px solid #e6e6e6;
            background-color: #fff;
            border-radius: 2px;
            padding: 0 15px;
            font-size: 14px;
            color: #666;
            width: 100%;
            box-sizing: border-box;
        }
        .layui-btn {
            display: inline-block;
            height: 38px;
            line-height: 38px;
            padding: 0 18px;
            background-color: #009688;
            color: #fff;
            white-space: nowrap;
            text-align: center;
            font-size: 14px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            text-decoration: none;
            margin-right: 5px;
        }
        .layui-btn-normal {
            background-color: #1E9FFF;
        }
        .layui-btn-primary {
            background-color: #fff;
            color: #666;
            border: 1px solid #e6e6e6;
        }
        .layui-btn:hover {
            opacity: 0.8;
        }
        .layui-icon {
            font-style: normal;
        }
        .mr-1 {
            margin-right: 5px;
        }
        
        /* Tab样式 */
        .layui-tab {
            margin: 0;
            text-align: left;
        }
        .layui-tab-title {
            position: relative;
            left: 0;
            height: 40px;
            white-space: nowrap;
            font-size: 0;
            border-bottom: 1px solid #e2e2e2;
            border-style: solid;
            border-width: 1px;
            border-color: #e2e2e2;
            background-color: #fafafa;
        }
        .layui-tab-title li {
            display: inline-block;
            vertical-align: middle;
            font-size: 14px;
            transition: all .2s;
            position: relative;
            line-height: 40px;
            min-width: 65px;
            padding: 0 15px;
            text-align: center;
            cursor: pointer;
        }
        .layui-tab-title .layui-this {
            color: #000;
        }
        .layui-tab-title .layui-this:after {
            position: absolute;
            left: 0;
            top: 0;
            content: '';
            width: 100%;
            height: 41px;
            border-width: 1px;
            border-style: solid;
            border-color: #e2e2e2;
            border-bottom-color: #fff;
            border-radius: 2px 2px 0 0;
            box-sizing: border-box;
            pointer-events: none;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin-top: 30px;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .before {
            background-color: #fff2f0;
            border-color: #ffccc7;
        }
        .after {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }
        h3 {
            margin-top: 0;
            color: #333;
        }
        .problem {
            color: #ff4d4f;
            font-weight: bold;
        }
        .solution {
            color: #52c41a;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BOM搜索布局修复</h1>
        
        <h2>修复后的搜索表单</h2>
        <div class="layui-tab" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>草稿</li>
                <li>已审核</li>
                <li>已停用</li>
            </ul>
        </div> 
        
        <form class="layui-form gg-form-bar border-x" id="barsearchform">
            <div class="layui-input-inline" style="width:150px;">
                <input type="text" name="bom_code" placeholder="请输入BOM编号" class="layui-input" autocomplete="off" />
            </div>
            <div class="layui-input-inline" style="width:150px;">
                <input type="text" name="product_code" placeholder="请输入产品编号" class="layui-input" autocomplete="off" />
            </div>
            <div class="layui-input-inline">
                <input type="hidden" name="tab" value="0" />
                <button class="layui-btn layui-btn-normal" type="button">
                    <span class="layui-icon mr-1">🔍</span>搜索
                </button>
            </div>
            <div class="layui-input-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
        
        <div class="comparison">
            <div class="before">
                <h3 class="problem">修复前的问题</h3>
                <ul>
                    <li>搜索和重置按钮在同一个div中</li>
                    <li>按钮排列不整齐</li>
                    <li>布局看起来拥挤</li>
                    <li>按钮之间间距不合适</li>
                </ul>
                <p><strong>原代码：</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; font-size: 12px;">
&lt;div class="layui-input-inline" style="width:150px"&gt;
    &lt;button class="layui-btn layui-btn-normal"&gt;搜索&lt;/button&gt;
    &lt;button type="reset" class="layui-btn layui-btn-primary"&gt;重置&lt;/button&gt;
&lt;/div&gt;</pre>
            </div>
            
            <div class="after">
                <h3 class="solution">修复后的改进</h3>
                <ul>
                    <li>搜索和重置按钮分别放在独立的div中</li>
                    <li>按钮排列整齐，间距合适</li>
                    <li>布局更加清晰美观</li>
                    <li>符合layui的设计规范</li>
                </ul>
                <p><strong>修复后代码：</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; font-size: 12px;">
&lt;div class="layui-input-inline"&gt;
    &lt;button class="layui-btn layui-btn-normal"&gt;搜索&lt;/button&gt;
&lt;/div&gt;
&lt;div class="layui-input-inline"&gt;
    &lt;button type="reset" class="layui-btn layui-btn-primary"&gt;重置&lt;/button&gt;
&lt;/div&gt;</pre>
            </div>
        </div>
        
        <h2>修复要点</h2>
        <ol>
            <li><strong>分离按钮容器</strong>：将搜索和重置按钮分别放在独立的 <code>layui-input-inline</code> div中</li>
            <li><strong>移除固定宽度</strong>：去掉按钮容器的固定宽度限制，让按钮自然排列</li>
            <li><strong>保持一致性</strong>：确保按钮样式与layui设计规范一致</li>
            <li><strong>改善用户体验</strong>：按钮排列更加整齐，操作更加直观</li>
        </ol>
        
        <h2>相关文件</h2>
        <p><strong>修改文件：</strong> <code>app/material/view/bom/index.html</code></p>
        <p><strong>修改行数：</strong> 第36-52行</p>
    </div>
</body>
</html>
