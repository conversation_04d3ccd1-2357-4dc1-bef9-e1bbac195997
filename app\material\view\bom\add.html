{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.form-container {
    padding: 20px;
    background: #fff;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1E9FFF;
}
.material-table {
    margin-top: 20px;
}
.material-table .layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.btn-group {
    margin-top: 20px;
    text-align: center;
}
.required {
    color: #FF5722;
}
/* 多级BOM样式 */
.sub-bom-row {
    background-color: #f8f9fa;
}
.sub-bom-row:hover {
    background-color: #e9ecef;
}
/* BOM等级缩进样式 */
.bom-level-1 { padding-left: 0px; }
.bom-level-2 { padding-left: 20px; }
.bom-level-3 { padding-left: 40px; }
.bom-level-4 { padding-left: 60px; }
.bom-level-5 { padding-left: 80px; }
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="form-container">
    <form class="layui-form" lay-filter="bomForm" id="bomForm">
        <input type="hidden" name="id" value="{$bom.id|default=0}" />
        <input type="hidden" name="product_id" value="{$bom.product_id|default=0}" />
        <input type="hidden" name="customer_id" value="{$bom.customer_id|default=0}" />
        
        <!-- 基础资料 -->
        <div class="section-title">基础资料</div>
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> BOM编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_code" value="{$bom.bom_code|default=$bom_code}" 
                               placeholder="使用系统编号" class="layui-input" required lay-verify="required" />
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" 
                                style="position:absolute;right:5px;top:5px;" onclick="generateBomCode()">
                            <i class="layui-icon layui-icon-refresh"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> BOM名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_name" value="{$bom.bom_name|default=''}" 
                               placeholder="请输入" maxlength="100" class="layui-input" required lay-verify="required" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span> 产品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_name" value="{$bom.product_name|default=''}" 
                               placeholder="S-18音响SS6" class="layui-input" required lay-verify="required" />
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" 
                                style="position:absolute;right:5px;top:5px;" onclick="selectProduct()">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">产品编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_code" value="{$bom.product_code|default=''}" 
                               placeholder="请输入产品编号" class="layui-input"  readonly/>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-form-item">
                    <label class="layui-form-label">客户名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="customer_name" value="{$bom.customer_name|default=''}"
                               placeholder="请选择客户" class="layui-input" readonly />
                        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs"
                                style="position:absolute;right:5px;top:5px;" onclick="selectCustomer()">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注" class="layui-textarea">{$bom.remark|default=''}</textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 物料列表 -->
        <div class="material-table">
            <div class="section-title">
                物料列表
                <div style="float:right;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="selectMaterial()">
                        选择物料
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="importBom()">
                        导入BOM表
                    </button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="clearMaterials()">
                        删除
                    </button>
                </div>
            </div>
            
            <table class="layui-table" id="materialTable">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" lay-filter="allChoose" lay-skin="primary">
                        </th>
                        <th width="60">BOM等级</th>
                        <th width="120">物料编号</th>
                        <th width="150">物料名称</th>
                        <th width="60">图片</th>
                        <th width="80">物料分类</th>
                        <th width="120">规格</th>
                        <th width="80">物料来源</th>
                        <th width="80">用量</th>
                        <th width="60">单位</th>
                        <th width="80">损耗率(%)</th>
                        <th width="60">下级BOM</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody id="materialTableBody">
                    {if isset($materials) && $materials}
                        {volist name="materials" id="material"}
                        <tr data-id="{$material.id}" data-material-id="{$material.material_id|default=$material.id}">
                            <td><input type="checkbox" lay-skin="primary"></td>
                            <td>{$material.bom_level|default='一级'}</td>
                            <td>{$material.material_code}</td>
                            <td>{$material.material_name}</td>
                            <td><img src="/static/images/default.png" width="40" height="40"></td>
                            <td>{$material.material_category}</td>
                            <td>{$material.specifications}</td>
                            <td>
                                <select name="materials[{$key}][material_source]" class="layui-input" style="width:100%;">
                                    <option value="自购" {if $material.material_source == '自购'}selected{/if}>自购</option>
                                    <option value="自制" {if $material.material_source == '自制'}selected{/if}>自制</option>
                                    <option value="委外" {if $material.material_source == '委外'}selected{/if}>委外</option>
                                </select>
                            </td>
                            <td>
                                <input type="number" name="materials[{$key}][quantity]" value="{$material.quantity|default='1.0000'}"
                                       class="layui-input" style="width:100%;" step="0.0001" min="0" placeholder="用量">
                            </td>
                            <td>
                                <input type="text" name="materials[{$key}][unit]" value="{$material.unit|default=''}"
                                       class="layui-input" style="width:100%;" placeholder="单位">
                            </td>
                            <td>
                                <input type="number" name="materials[{$key}][loss_rate]" value="{$material.loss_rate|default='0.00'}"
                                       class="layui-input" style="width:100%;" step="0.01" min="0" max="100" placeholder="损耗率">
                            </td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="expandBom(this, '{$material.id}')">展开</button>
                            </td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeMaterial(this)">删除</button>
                            </td>
                        </tr>
                        {/volist}
                    {else}
                    <tr class="empty-row">
                        <td colspan="13" style="text-align:center;color:#999;">暂无数据</td>
                    </tr>
                    {/if}
                </tbody>
            </table>
        </div>
        
        <!-- 操作按钮 -->
        <div class="btn-group">
            <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="bomSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">取消</button>
        </div>
    </form>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var form = layui.form, tool = layui.tool;
    
    // 生成BOM编号
    window.generateBomCode = function() {
        var date = new Date();
        var dateStr = date.getFullYear() + 
                     (date.getMonth() + 1).toString().padStart(2, '0') + 
                     date.getDate().toString().padStart(2, '0');
        var randomNum = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
        var bomCode = 'BOM' + dateStr + randomNum;
        $('input[name="bom_code"]').val(bomCode);
    };
    
    // 选择产品
    window.selectProduct = function() {
        layer.open({
            type: 2,
            title: '选择产品',
            shadeClose: true,
            shade: 0.3,
            maxmin: true,
            area: ['900px', '600px'],
            content: '/material/bom/selectProduct'
        });
    };

    // 选择客户
    window.selectCustomer = function() {
        layer.open({
            type: 2,
            title: '选择客户',
            shadeClose: true,
            shade: 0.3,
            maxmin: true,
            area: ['900px', '600px'],
            content: '/material/bom/selectCustomer'
        });
    };
    
    // 选择物料
    window.selectMaterial = function() {
        // 检查是否已选择产品
        var productCode = $('input[name="product_code"]').val();
        var productName = $('input[name="product_name"]').val();

        if (!productCode || !productName) {
            layer.msg('请先选择产品，再选择物料', {icon: 2});
            return;
        }

        layer.open({
            type: 2,
            title: '选择物料',
            content: '/material/bom/getMaterials?exclude_product=' + encodeURIComponent(productCode),
            area: ['90%', '80%'],
            btn: ['确定', '取消'],
            yes: function(index, layero) {
                var iframeWindow = window[layero.find('iframe')[0]['name']];
                var selectedData = iframeWindow.getSelectedData();
                if (selectedData.length === 0) {
                    layer.msg('请选择物料');
                    return;
                }

                // 过滤掉产品本身
                var filteredData = selectedData.filter(function(material) {
                    return material.material_code !== productCode;
                });

                if (filteredData.length === 0) {
                    layer.msg('不能将产品本身添加到BOM中', {icon: 2});
                    return;
                }

                if (filteredData.length < selectedData.length) {
                    layer.msg('已过滤掉产品本身，其他物料已添加', {icon: 1});
                }

                // 添加选中的物料到表格
                addMaterialsToTable(filteredData);
                layer.close(index);
            }
        });
    };
    
    // 添加物料到表格
    function addMaterialsToTable(materials) {
        var tbody = $('#materialTableBody');
        var emptyRow = tbody.find('.empty-row');
        if (emptyRow.length > 0) {
            emptyRow.remove();
        }

        // 获取当前产品编码，用于二次验证
        var productCode = $('input[name="product_code"]').val();

        materials.forEach(function(material, index) {
            // 再次检查，确保不添加产品本身
            if (material.material_code === productCode) {
                layer.msg('跳过产品本身：' + material.material_code, {icon: 2});
                return; // 跳过这个物料
            }

            // 检查是否已存在相同物料
            var existingRow = tbody.find('tr[data-id="' + material.id + '"]');
            if (existingRow.length > 0) {
                layer.msg('物料 ' + material.material_code + ' 已存在，跳过添加', {icon: 2});
                return; // 跳过这个物料
            }

            var currentIndex = tbody.find('tr').length;
            var row = `
                <tr data-id="${material.id}" data-level="1" data-parent="" data-material-code="${material.material_code}" data-material-id="${material.id}">
                    <td><input type="checkbox" lay-skin="primary"></td>
                    <td>一级</td>
                    <td>${material.material_code}</td>
                    <td>${material.name}</td>
                    <td><img src="/static/images/default.png" width="40" height="40"></td>
                    <td>${material.category || ''}</td>
                    <td>${material.specifications || ''}</td>
                    <td>
                        <select name="materials[${currentIndex}][material_source]" class="layui-input" style="width:100%;">
                            <option value="自购">自购</option>
                            <option value="自制">自制</option>
                            <option value="委外">委外</option>
                        </select>
                    </td>
                    <td>
                        <input type="number" name="materials[${currentIndex}][quantity]" value="1.0000"
                               class="layui-input" style="width:100%;" step="0.0001" min="0" placeholder="用量">
                    </td>
                    <td>
                        <input type="text" name="materials[${currentIndex}][unit]" value="${material.unit || ''}"
                               class="layui-input" style="width:100%;" placeholder="单位">
                    </td>
                    <td>
                        <input type="number" name="materials[${currentIndex}][loss_rate]" value="0.00"
                               class="layui-input" style="width:100%;" step="0.01" min="0" max="100" placeholder="损耗率">
                    </td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="expandBom(this, '${material.id}')">展开</button>
                    </td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeMaterial(this)">删除</button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
        
        form.render();
    }
    
    // 删除物料
    window.removeMaterial = function(btn) {
        var $row = $(btn).closest('tr');
        var materialId = $row.data('material-id');

        // 如果是父级物料，同时删除其下级BOM
        if (materialId) {
            collapseBom($row, materialId);
        }

        $row.remove();

        // 如果没有数据了，显示空行
        var tbody = $('#materialTableBody');
        if (tbody.find('tr').length === 0) {
            tbody.append('<tr class="empty-row"><td colspan="10" style="text-align:center;color:#999;">暂无数据</td></tr>');
        }
    };

    // 展开下级BOM
    window.expandBom = function(btn, materialId) {
        var $btn = $(btn);
        var $row = $btn.closest('tr');
        var currentLevel = parseInt($row.data('level')) || 1;
        var nextLevel = currentLevel + 1;

        // 检查是否已经展开
        if ($btn.text() === '收起') {
            // 收起下级BOM
            collapseBom($row, materialId);
            $btn.text('展开').removeClass('layui-btn-warm').addClass('layui-btn-normal');
            return;
        }

        // 显示加载状态
        $btn.text('加载中...').prop('disabled', true);

        // 请求下级BOM数据
        $.ajax({
            url: '/material/bom/getSubBom',
            type: 'GET',
            data: {
                material_id: materialId
            },
            success: function(res) {
                if (res.code === 0 && res.data && res.data.length > 0) {
                    // 添加下级BOM到表格
                    addSubBomToTable(res.data, $row, nextLevel, materialId);
                    $btn.text('收起').removeClass('layui-btn-normal').addClass('layui-btn-warm');
                } else {
                    layer.msg('该物料没有下级BOM', {icon: 1});
                    $btn.text('展开').prop('disabled', false);
                }
            },
            error: function() {
                layer.msg('获取下级BOM失败', {icon: 2});
                $btn.text('展开').prop('disabled', false);
            }
        });
    };

    // 添加下级BOM到表格
    function addSubBomToTable(bomData, parentRow, level, parentMaterialId) {
        var levelText = getLevelText(level);
        var indent = getIndent(level);

        bomData.forEach(function(bom, index) {
            var currentIndex = $('#materialTableBody tr').length;
            var row = `
                <tr data-id="${bom.id}" data-level="${level}" data-parent="${parentMaterialId}" data-material-code="${bom.material_code}" data-material-id="${bom.material_id}" class="sub-bom-row">
                    <td><input type="checkbox" lay-skin="primary"></td>
                    <td>${indent}${levelText}</td>
                    <td>${bom.material_code}</td>
                    <td>${bom.material_name}</td>
                    <td><img src="/static/images/default.png" width="40" height="40"></td>
                    <td>${bom.material_category || ''}</td>
                    <td>${bom.specifications || ''}</td>
                    <td>
                        <select name="materials[${currentIndex}][material_source]" class="layui-input">
                            <option value="自购" ${bom.material_source === '自购' ? 'selected' : ''}>自购</option>
                            <option value="自制" ${bom.material_source === '自制' ? 'selected' : ''}>自制</option>
                            <option value="委外" ${bom.material_source === '委外' ? 'selected' : ''}>委外</option>
                        </select>
                    </td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="expandBom(this, '${bom.material_id}')">展开</button>
                    </td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeMaterial(this)">删除</button>
                    </td>
                </tr>
            `;
            parentRow.after(row);
            parentRow = parentRow.next(); // 更新插入位置
        });

        form.render();
    }

    // 收起下级BOM
    function collapseBom(parentRow, parentMaterialId) {
        var nextRow = parentRow.next();
        while (nextRow.length > 0 && nextRow.data('parent') == parentMaterialId) {
            var currentRow = nextRow;
            nextRow = nextRow.next();
            currentRow.remove();
        }
    }

    // 获取等级文本
    function getLevelText(level) {
        var levelTexts = ['', '一级', '二级', '三级', '四级', '五级'];
        return levelTexts[level] || (level + '级');
    }

    // 获取缩进
    function getIndent(level) {
        var indent = '';
        for (var i = 1; i < level; i++) {
            indent += '　　'; // 全角空格用于缩进
        }
        return indent;
    }

    // 清空物料
    window.clearMaterials = function() {
        var checkedBoxes = $('#materialTableBody input[type="checkbox"]:checked');
        if (checkedBoxes.length === 0) {
            layer.msg('请选择要删除的物料');
            return;
        }
        
        layer.confirm('确定要删除选中的物料吗？', function(index) {
            checkedBoxes.each(function() {
                $(this).closest('tr').remove();
            });
            
            // 如果没有数据了，显示空行
            var tbody = $('#materialTableBody');
            if (tbody.find('tr').length === 0) {
                tbody.append('<tr class="empty-row"><td colspan="10" style="text-align:center;color:#999;">暂无数据</td></tr>');
            }
            
            layer.close(index);
        });
    };
    
    // 导入BOM表
    window.importBom = function() {
        layer.msg('BOM导入功能待开发');
    };
    
    // 全选
    form.on('checkbox(allChoose)', function(data) {
        var child = $('#materialTableBody input[type="checkbox"]');
        child.each(function(index, item) {
            item.checked = data.elem.checked;
        });
        form.render();
    });
    
    // 表单提交
    form.on('submit(bomSubmit)', function(data) {
        // 收集物料数据
        var materials = [];
        $('#materialTableBody tr').each(function() {
            if ($(this).hasClass('empty-row')) return;
            
            var materialData = {
                material_id: $(this).data('material-id') || $(this).data('id'), // 物料ID
                material_code: $(this).find('td:eq(2)').text(),
                material_name: $(this).find('td:eq(3)').text(),
                material_category: $(this).find('td:eq(5)').text(),
                specifications: $(this).find('td:eq(6)').text(),
                material_source: $(this).find('select[name*="material_source"]').val(),
                quantity: parseFloat($(this).find('input[name*="quantity"]').val()) || 1.0000, // 用量
                unit: $(this).find('input[name*="unit"]').val() || '', // 单位
                loss_rate: parseFloat($(this).find('input[name*="loss_rate"]').val()) || 0.00, // 损耗率
                bom_level: $(this).data('level') || 1, // BOM等级
                parent_material_id: $(this).data('parent') || 0 // 父级物料ID
            };
            materials.push(materialData);
        });
        
        // 添加物料数据到表单
        data.field.materials = materials;
        
        var callback = function(res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                setTimeout(function() {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }, 1000);
            }
        };
        
        tool.post("/material/bom/add", data.field, callback);
        return false;
    });
}
</script>
{/block}
<!-- /脚本 -->
