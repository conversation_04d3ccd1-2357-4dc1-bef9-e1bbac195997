{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <!-- 基本信息 -->
    <div class="layui-card">
        <div class="layui-card-header">基本信息</div>
        <div class="layui-card-body">
            <div class="layui-form">
                <table class="layui-table detail-table">
                    <colgroup>
                        <col width="15%">
                        <col width="35%">
                        <col width="15%">
                        <col width="35%">
                    </colgroup>
                    <tbody>
                        <tr>
                            <th>出库单号</th>
                            <td>{$outbound.outbound_no}</td>
                            <th>出库类型</th>
                            <td>{$outbound.outbound_type_text}</td>
                        </tr>
                        <tr>
                            <th>出库日期</th>
                            <td>{$outbound.outbound_date}</td>
                            <th>客户名称</th>
                            <td>{$outbound.customer_name}</td>
                        </tr>
                        <tr>
                            <th>关联单据类型</th>
                            <td>{$outbound.related_bill_type|default='--'}</td>
                            <th>关联单据号</th>
                            <td>{$outbound.related_bill_no|default='--'}</td>
                        </tr>
                        <tr>
                            <th>创建时间</th>
                            <td>{$outbound.create_time}</td>
                            <th>创建人</th>
                            <td>{$outbound.creator_name}</td>
                        </tr>
                        <tr>
                            <th>当前状态</th>
                            <td>
                                {if $outbound.status == 0}
                                <span class="layui-badge layui-bg-gray">草稿</span>
                                {elseif $outbound.status == 1}
                                <span class="layui-badge layui-bg-orange">已提交</span>
                                {elseif $outbound.status == 2}
                                <span class="layui-badge layui-bg-blue">已审核</span>
                                {elseif $outbound.status == 3}
                                <span class="layui-badge layui-bg-cyan">部分出库</span>
                                {elseif $outbound.status == 4}
                                <span class="layui-badge layui-bg-green">全部出库</span>
                                {elseif $outbound.status == 5}
                                <span class="layui-badge layui-bg-red">已取消</span>
                                {else}
                                <span class="layui-badge layui-bg-gray">{$outbound.status_text|default='未知状态'}</span>
                                {/if}
                            </td>
                            <th>审核人</th>
                            <td>{$outbound.approver_name|default='--'}</td>
                        </tr>
                        <tr>
                            <th>审核时间</th>
                            <td>{$outbound.approve_time|default='--'}</td>
                            <th>备注</th>
                            <td>{$outbound.notes|default='--'}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 明细信息 -->
    <div class="layui-card">
        <div class="layui-card-header">出库明细</div>
        <div class="layui-card-body">
            <table class="layui-table" id="detail-table" lay-filter="detail-table"></table>
        </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="layui-form-item text-center mt-3">
        {if $outbound.status == 0}
        <button type="button" class="layui-btn layui-btn-normal" id="btn-edit">编辑</button>
        <button type="button" class="layui-btn layui-btn-warm" id="btn-submit">提交审核</button>
        <button type="button" class="layui-btn layui-btn-danger" id="btn-cancel">取消出库单</button>
        {elseif $outbound.status == 1}
        <button type="button" class="layui-btn layui-btn-warm" id="btn-approve">审核通过</button>
        <button type="button" class="layui-btn layui-btn-danger" id="btn-reject">审核拒绝</button>
        {elseif $outbound.status == 2}
        <button type="button" class="layui-btn layui-btn-warm" id="btn-execute">执行出库</button>
        {/if}

        {if $outbound.status == 4 && $can_approve}
        <button type="button" class="layui-btn layui-btn-warm" id="btn-unapprove">撤销出库</button>
        {/if}
        
        <button type="button" class="layui-btn layui-btn-normal" id="btn-export">导出</button>
        <button type="button" class="layui-btn layui-btn-primary" id="btn-back">返回</button>
        <button type="button" class="layui-btn" id="printBtn"><i class="layui-icon layui-icon-print"></i> 打印出库单</button>
    </div>
</div>

<script>
const moduleInit = ['tool'];
function gouguInit() {
    var form = layui.form,
        table = layui.table,
        layer = layui.layer,
        $ = layui.jquery;
    
    // 渲染明细表格
    table.render({
        elem: '#detail-table',
        cols: [[
            {field: 'id', title: 'ID', width: 80, hide: true},
            {field: 'product_code', title: '产品编码', width: 120},
            {field: 'product_name', title: '产品名称', width: 200},
            {field: 'spec', title: '规格型号', width: 120},
            {field: 'warehouse_name', title: '仓库', width: 120},
            {field: 'location_name', title: '库位', width: 120},
            {field: 'batch_no', title: '批次号', width: 150},
            {field: 'unit', title: '单位', width: 80},
            {field: 'quantity', title: '出库数量', width: 100},
            {field: 'price', title: '单价', width: 100},
            {field: 'amount', title: '金额', width: 120},
            {field: 'notes', title: '备注', width: 150}
        ]],
        data: {$details|default='[]'|raw},
        limit: 1000,
        text: {
            none: '暂无出库明细'
        }
    });
    
    // 编辑按钮
    $('#btn-edit').on('click', function() {
        // 在弹窗中打开编辑页面
        parent.layer.open({
            type: 2,
            title: '编辑出库单',
            area: ['90%', '90%'],
            content: '{:url("warehouse/outbound/edit")}?id={$outbound.id}',
            maxmin: true,
            end: function() {
                // 编辑完成后刷新当前页面
                window.location.reload();
            }
        });
    });
    
    // 提交审核按钮
    $('#btn-submit').on('click', function() {
        layer.confirm('确定要直接出库吗？', function(index) {
            $.ajax({
                url: '{:url("warehouse/outbound/submitApproval")}',
                type: 'post',
                data: {id: '{$outbound.id}'},
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg || '出库成功', {icon: 1, time: 1000}, function() {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(res.msg || '出库失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 取消出库单按钮
    $('#btn-cancel').on('click', function() {
        layer.confirm('确定要取消该出库单吗？', function(index) {
            $.ajax({
                url: '{:url("warehouse/outbound/cancel")}',
                type: 'post',
                data: {id: '{$outbound.id}'},
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg || '取消成功', {icon: 1, time: 1000}, function() {
                            // 刷新父页面表格和关闭当前弹窗
                            parent.layui.table.reload('outbound-table');
                            var frameIndex = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(frameIndex);
                        });
                    } else {
                        layer.msg(res.msg || '取消失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 撤销审核按钮
    $('#btn-unapprove').on('click', function() {
        layer.confirm('确定要撤销出库吗？这将恢复商品库存', function(index) {
            $.ajax({
                url: '{:url("warehouse/outbound/unapprove")}',
                type: 'post',
                data: {id: '{$outbound.id}'},
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg(res.msg || '撤销出库成功', {icon: 1, time: 1000}, function() {
                            window.location.reload();
                        });
                    } else {
                        layer.msg(res.msg || '撤销出库失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    
    // 导出按钮
    $('#btn-export').on('click', function() {
        window.location.href = '{:url("warehouse/outbound/export")}?id={$outbound.id}';
    });
    
    // 返回按钮
    $('#btn-back').on('click', function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    });

    // 添加打印按钮点击事件
    $('#printBtn').click(function(){
        var id = '{$outbound.id}';
        // 打开新窗口进行打印
        window.open('{:url("print")}?id=' + id, '_blank');
    });
}
</script>
{/block} 