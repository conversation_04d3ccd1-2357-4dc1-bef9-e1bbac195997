-- 创建BOM相关表
-- 如果表不存在，则创建

-- 1. BOM主表 (oa_material_bom)
CREATE TABLE IF NOT EXISTS `oa_material_bom` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'BOM主键ID',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编号',
  `bom_name` varchar(100) NOT NULL COMMENT 'BOM名称',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_code` varchar(50) NOT NULL COMMENT '产品编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `customer_id` int(11) DEFAULT 0 COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT '' COMMENT '客户名称',
  `version` varchar(20) DEFAULT '1.0' COMMENT 'BOM版本',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  `admin_id` int(11) DEFAULT 0 COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bom_code` (`bom_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM主表';

-- 2. BOM明细表 (oa_material_bom_detail)
CREATE TABLE IF NOT EXISTS `oa_material_bom_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键ID',
  `bom_id` int(11) NOT NULL COMMENT 'BOM主表ID',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编号',
  `product_id` int(11) NOT NULL COMMENT '产品ID（父级物料）',
  `material_id` int(11) NOT NULL COMMENT '物料ID（子级物料）',
  `material_code` varchar(50) NOT NULL COMMENT '物料编号',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `material_category` varchar(50) DEFAULT '' COMMENT '物料分类',
  `specifications` varchar(200) DEFAULT '' COMMENT '规格',
  `material_source` varchar(20) NOT NULL COMMENT '物料来源：自购、自制、委外',
  `bom_level` tinyint(2) DEFAULT 1 COMMENT 'BOM等级：1一级，2二级，3三级...',
  `parent_material_id` int(11) DEFAULT 0 COMMENT '父级物料ID（用于多级BOM）',
  `quantity` decimal(10,4) NOT NULL COMMENT '用量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `loss_rate` decimal(5,2) DEFAULT 0.00 COMMENT '损耗率(%)',
  `effective_date` date DEFAULT NULL COMMENT '生效日期',
  `invalid_date` date DEFAULT NULL COMMENT '失效日期',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_bom_id` (`bom_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_bom_level` (`bom_level`),
  KEY `idx_parent_material` (`parent_material_id`),
  KEY `idx_bom_product` (`bom_id`, `product_id`),
  KEY `idx_level_parent` (`bom_level`, `parent_material_id`),
  KEY `idx_source_level` (`material_source`, `bom_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM明细表';

-- 3. BOM编号序列表 (oa_material_bom_sequence)
CREATE TABLE IF NOT EXISTS `oa_material_bom_sequence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prefix` varchar(10) NOT NULL DEFAULT 'BOM' COMMENT '编号前缀',
  `date_format` varchar(10) NOT NULL DEFAULT 'Ymd' COMMENT '日期格式',
  `current_number` int(11) NOT NULL DEFAULT 0 COMMENT '当前序号',
  `current_date` varchar(10) NOT NULL COMMENT '当前日期',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date` (`current_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM编号序列表';

-- 4. 插入初始数据
INSERT IGNORE INTO `oa_material_bom_sequence` (`prefix`, `date_format`, `current_number`, `current_date`, `create_time`, `update_time`) 
VALUES ('BOM', 'Ymd', 0, DATE_FORMAT(NOW(), '%Y%m%d'), UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
