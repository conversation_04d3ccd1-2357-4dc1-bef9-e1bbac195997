-- BOM管理相关数据表设计
-- 创建时间：2025-08-02
-- 说明：包含BOM主表和明细表的完整设计
-- 注意：请根据实际数据库前缀调整表名，如果使用oa_前缀，请将material_改为oa_

-- 1. BOM主表 (material_bom)
DROP TABLE IF EXISTS `material_bom`;
CREATE TABLE `material_bom` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'BOM主键ID',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编号',
  `bom_name` varchar(100) NOT NULL COMMENT 'BOM名称',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_code` varchar(50) NOT NULL COMMENT '产品编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `customer_id` int(11) DEFAULT 0 COMMENT '客户ID',
  `customer_name` varchar(100) DEFAULT '' COMMENT '客户名称',
  `version` varchar(20) DEFAULT '1.0' COMMENT 'BOM版本',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  `admin_id` int(11) NOT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bom_code` (`bom_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM主表';

-- 2. BOM明细表 (material_bom_detail)
DROP TABLE IF EXISTS `material_bom_detail`;
CREATE TABLE `material_bom_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细主键ID',
  `bom_id` int(11) NOT NULL COMMENT 'BOM主表ID',
  `bom_code` varchar(50) NOT NULL COMMENT 'BOM编号',
  `product_id` int(11) NOT NULL COMMENT '产品ID（父级物料）',
  `material_id` int(11) NOT NULL COMMENT '物料ID（子级物料）',
  `material_code` varchar(50) NOT NULL COMMENT '物料编号',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `material_category` varchar(50) DEFAULT '' COMMENT '物料分类',
  `specifications` varchar(200) DEFAULT '' COMMENT '规格',
  `material_source` varchar(20) NOT NULL COMMENT '物料来源：自购、自制、委外',
  `bom_level` tinyint(2) DEFAULT 1 COMMENT 'BOM等级：1一级，2二级，3三级...',
  `parent_material_id` int(11) DEFAULT 0 COMMENT '父级物料ID，0表示顶级',
  `quantity` decimal(10,4) DEFAULT 1.0000 COMMENT '用量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `loss_rate` decimal(5,2) DEFAULT 0.00 COMMENT '损耗率(%)',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_bom_id` (`bom_id`),
  KEY `idx_bom_code` (`bom_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_parent_material_id` (`parent_material_id`),
  KEY `idx_bom_level` (`bom_level`),
  KEY `idx_delete_time` (`delete_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM明细表';

-- 3. BOM编号序列表 (material_bom_sequence)
DROP TABLE IF EXISTS `material_bom_sequence`;
CREATE TABLE `material_bom_sequence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prefix` varchar(10) NOT NULL DEFAULT 'BOM' COMMENT '编号前缀',
  `date_format` varchar(10) NOT NULL DEFAULT 'Ymd' COMMENT '日期格式',
  `current_number` int(11) NOT NULL DEFAULT 0 COMMENT '当前序号',
  `current_date` varchar(10) NOT NULL COMMENT '当前日期',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date` (`current_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='BOM编号序列表';

-- 4. 插入初始数据
INSERT INTO `material_bom_sequence` (`prefix`, `date_format`, `current_number`, `current_date`, `create_time`, `update_time`) 
VALUES ('BOM', 'Ymd', 0, DATE_FORMAT(NOW(), '%Y%m%d'), UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 5. 创建索引优化查询性能
-- 复合索引：BOM查询优化
ALTER TABLE `material_bom_detail` ADD INDEX `idx_bom_product` (`bom_id`, `product_id`);
-- 复合索引：层级查询优化  
ALTER TABLE `material_bom_detail` ADD INDEX `idx_level_parent` (`bom_level`, `parent_material_id`);
-- 复合索引：物料来源统计
ALTER TABLE `material_bom_detail` ADD INDEX `idx_source_level` (`material_source`, `bom_level`);

-- 6. 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `material_bom` ADD CONSTRAINT `fk_bom_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT;
-- ALTER TABLE `material_bom` ADD CONSTRAINT `fk_bom_customer` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE SET NULL;
-- ALTER TABLE `material_bom_detail` ADD CONSTRAINT `fk_detail_bom` FOREIGN KEY (`bom_id`) REFERENCES `material_bom` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `material_bom_detail` ADD CONSTRAINT `fk_detail_material` FOREIGN KEY (`material_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT;
