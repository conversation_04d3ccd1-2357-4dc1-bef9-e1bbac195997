# 出库单创建流程优化

## 优化目标
简化出库单创建流程，提高用户体验和操作效率：
1. 选择出库类型后自动确定关联单据类型
2. 提供便捷的关联单据选择功能
3. 自动填充关联单据的物料信息到出库明细

## 功能设计

### 1. 出库类型与关联单据类型映射
```javascript
var billTypeMap = {
    'sales': 'customer_order',      // 销售出库 -> 客户订单
    'production': 'production_order', // 生产出库 -> 生产订单
    'transfer': 'transfer_order',    // 调拨出库 -> 调拨订单
    'return': 'return_order',        // 退货出库 -> 退货订单
    'other': ''                      // 其他出库 -> 无关联
};
```

### 2. 界面优化

#### 原有界面问题
- 需要手动输入关联单据类型
- 需要手动输入关联单据号
- 无法验证单据是否存在
- 需要手动添加物料明细

#### 优化后界面
- 选择出库类型后自动显示关联单据选择区域
- 关联单据类型自动填充且只读
- 提供"选择"按钮打开单据选择弹窗
- 选择单据后自动填充物料明细

### 3. 交互流程

#### 步骤1：选择出库类型
```
用户选择出库类型 → 系统自动：
1. 确定关联单据类型
2. 显示关联单据选择区域
3. 设置选择按钮的提示文本
```

#### 步骤2：选择关联单据
```
用户点击"选择"按钮 → 系统打开弹窗：
1. 显示对应类型的单据列表
2. 支持关键字搜索
3. 分页显示
4. 单选模式
```

#### 步骤3：自动填充物料
```
用户选择单据 → 系统自动：
1. 获取单据明细
2. 清空现有出库明细
3. 填充物料信息
4. 设置数量和价格
```

## 技术实现

### 1. 前端实现

#### HTML结构优化
```html
<!-- 关联单据选择区域 -->
<div class="layui-form-item layui-row layui-col-space15" id="related-bill-row" style="display: none;">
    <div class="layui-col-md4">
        <label class="layui-form-label">关联单据类型：</label>
        <div class="layui-input-block">
            <input type="text" name="related_bill_type" readonly class="layui-input">
        </div>
    </div>
    <div class="layui-col-md6">
        <label class="layui-form-label">关联单据：</label>
        <div class="layui-input-block">
            <div class="layui-input-group">
                <input type="text" name="related_bill_no" readonly placeholder="请选择关联单据" class="layui-input">
                <div class="layui-input-split layui-input-suffix">
                    <button type="button" class="layui-btn layui-btn-primary" id="select-related-bill">
                        <i class="layui-icon layui-icon-search"></i> 选择
                    </button>
                </div>
            </div>
            <input type="hidden" name="related_bill_id" value="">
        </div>
    </div>
</div>
```

#### JavaScript事件处理
```javascript
// 监听出库类型变化
form.on('select(outbound_type)', function(data) {
    var outboundType = data.value;
    // 自动设置关联单据类型
    // 显示/隐藏关联单据选择区域
});

// 选择关联单据
$('#select-related-bill').on('click', function() {
    // 打开单据选择弹窗
    openRelatedBillDialog(outboundType, relatedBillType);
});
```

### 2. 后端实现

#### 控制器方法
```php
// 获取各类型单据列表
public function getCustomerOrders()     // 客户订单
public function getProductionOrders()   // 生产订单
public function getTransferOrders()     // 调拨订单
public function getReturnOrders()       // 退货订单

// 获取单据明细
public function getBillDetails()
```

#### 数据处理逻辑
```php
// 只获取未完全出库的明细
$where[] = ['quantity > delivered_quantity'];

// 返回标准格式的数据
return [
    'product' => [...],    // 产品信息
    'quantity' => ...,     // 剩余数量
    'price' => ...,        // 单价
    'notes' => ...         // 备注
];
```

### 3. 单据明细获取逻辑

#### 客户订单明细
- 表：`customer_order_detail`
- 条件：`quantity > delivered_quantity`
- 字段：产品信息、剩余数量、价格等

#### 生产订单明细
- 表：`production_order_material`
- 条件：`required_quantity > issued_quantity`
- 字段：物料信息、剩余需求量、成本等

#### 调拨订单明细
- 表：`transfer_order_detail`
- 条件：`quantity > transferred_quantity`
- 字段：产品信息、剩余调拨量、成本等

#### 退货订单明细
- 表：`return_order_detail`
- 条件：`quantity > returned_quantity`
- 字段：产品信息、剩余退货量、价格等

## 用户体验提升

### 1. 操作简化
- **减少手动输入**：自动填充关联单据类型
- **避免输入错误**：通过选择代替手动输入
- **提高效率**：一键填充所有物料明细

### 2. 数据准确性
- **单据验证**：只能选择存在的单据
- **状态检查**：只显示可出库的单据
- **数量控制**：自动计算剩余可出库数量

### 3. 界面友好性
- **动态显示**：根据出库类型动态显示相关字段
- **清晰提示**：明确的操作提示和按钮文本
- **即时反馈**：操作后立即显示结果

## 扩展功能

### 1. 智能推荐
- 根据客户历史订单推荐常用产品
- 根据库存状况推荐可出库产品
- 根据紧急程度推荐优先出库订单

### 2. 批量操作
- 支持选择多个订单批量创建出库单
- 支持按客户、按产品批量处理
- 支持导入订单号批量创建

### 3. 状态同步
- 出库单创建后自动更新订单状态
- 出库完成后自动更新订单完成度
- 支持订单和出库单的双向关联查询

## 测试验证

### 1. 功能测试
- 各种出库类型的单据选择
- 单据明细的正确填充
- 数量和金额的自动计算

### 2. 边界测试
- 无可出库明细的单据处理
- 已完全出库的单据过滤
- 网络异常时的错误处理

### 3. 用户体验测试
- 操作流程的顺畅性
- 界面响应的及时性
- 错误提示的友好性

## 预期效果

1. **提高效率**：出库单创建时间减少50%以上
2. **减少错误**：手动输入错误率降低90%以上
3. **提升体验**：用户操作满意度显著提升
4. **数据准确**：单据关联准确率达到100%
