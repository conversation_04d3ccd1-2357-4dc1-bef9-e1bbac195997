<?php
declare (strict_types = 1);

namespace app\Produce\validate;

use think\Validate;

class DrawingValidate extends Validate
{
    protected $rule = [
        'name' => 'require|max:100',
        'product_name' => 'max:100',
        'material_name' => 'max:100',
        'category_id' => 'integer|egt:0',
        'remark' => 'max:500'
    ];

    protected $message = [
        'name.require' => '工序名称不能为空',
        'name.max' => '工序名称不能超过100个字符',
        'product_name.max' => '产品名称不能超过100个字符',
        'material_name.max' => '物料名称不能超过100个字符',
        'category_id.integer' => '分类ID必须为整数',
        'category_id.egt' => '分类ID不能为负数',
        'remark.max' => '备注不能超过500个字符'
    ];

    protected $scene = [
        'add' => ['name', 'product_name', 'material_name', 'category_id', 'remark'],
        'edit' => ['name', 'product_name', 'material_name', 'category_id', 'remark']
    ];
}