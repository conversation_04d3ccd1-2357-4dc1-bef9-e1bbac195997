{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.form-container {
    padding: 20px;
    background: #fff;
}
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1E9FFF;
}
.info-item {
    margin-bottom: 10px;
}
.info-label {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    color: #666;
}
.info-value {
    color: #333;
}
.material-table {
    margin-top: 20px;
}
.material-table .layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.btn-group {
    margin-top: 20px;
    text-align: center;
}
.bom-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    color: #fff;
}
.status-draft { background-color: #999; }
.status-approved { background-color: #5FB878; }
.status-disabled { background-color: #FF5722; }

/* BOM层级样式 */
.sub-bom-row {
    background-color: #f8f9fa;
}
.sub-bom-row.level-2 {
    background-color: #f1f3f4;
}
.sub-bom-row.level-3 {
    background-color: #e8eaed;
}
.bom-level-indent {
    padding-left: 20px;
}
.bom-level-indent.level-2 {
    padding-left: 40px;
}
.bom-level-indent.level-3 {
    padding-left: 60px;
}
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="form-container">
    <!-- 基础资料 -->
    <div class="section-title">基础资料</div>
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">BOM编号：</span>
                <span class="info-value">{$bom.bom_code}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">BOM名称：</span>
                <span class="info-value">{$bom.bom_name}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">产品名称：</span>
                <span class="info-value">{$bom.product_name}</span>
            </div>
        </div>
    </div>
    
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">客户名称：</span>
                <span class="info-value">{$bom.customer_name}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">审核状态：</span>
                {if $bom.status == 0}
                    <span class="bom-status status-draft">草稿</span>
                {elseif $bom.status == 1}
                    <span class="bom-status status-approved">已审核</span>
                {elseif $bom.status == 2}
                    <span class="bom-status status-disabled">已停用</span>
                {/if}
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">创建人：</span>
                <span class="info-value">{$bom.create_user}</span>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space15">
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">创建时间：</span>
                <span class="info-value">{$bom.create_time_text}</span>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="info-item">
                <span class="info-label">更新时间：</span>
                <span class="info-value">{$bom.update_time_text}</span>
            </div>
        </div>
    </div>
    
    {if $bom.remark}
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="info-item">
                <span class="info-label">备注：</span>
                <span class="info-value">{$bom.remark}</span>
            </div>
        </div>
    </div>
    {/if}
    
    <!-- 物料列表 -->
    <div class="material-table">
        <div class="section-title">物料列表</div>
        
        <table class="layui-table" id="materialTable">
            <thead>
                <tr>
                    <th width="60">ID</th>
                    <th width="80">BOM等级</th>
                    <th width="150">物料编号</th>
                    <th width="200">物料名称</th>
                    <th width="80">图片</th>
                    <th width="100">物料分类</th>
                    <th width="100">规格</th>
                    <th width="80">型号</th>
                    <th width="80">数量</th>
                    <th width="80">损耗率</th>
                    <th width="100">物料来源</th>
                    <th width="100">操作</th>
                </tr>
            </thead>
            <tbody id="materialTableBody">
                {if isset($materials) && $materials}
                    {volist name="materials" id="material" key="k"}
                    <tr data-id="{$material.id}" data-material-id="{$material.material_id}" data-level="1" class="material-row">
                        <td>{$k}</td>
                        <td>一级</td>
                        <td>{$material.material_code}</td>
                        <td>{$material.material_name}</td>
                        <td><img src="/static/images/default.png" width="40" height="40"></td>
                        <td>{$material.material_category}</td>
                        <td>{$material.specifications}</td>
                        <td>{$material.model}</td>
                        <td>{$material.quantity}</td>
                        <td>{$material.loss_rate}%</td>
                        <td>{$material.material_source}</td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="expandBom(this, '{$material.material_id}')">展开</button>
                        </td>
                    </tr>
                    {/volist}
                {else}
                <tr>
                    <td colspan="12" style="text-align:center;color:#999;">暂无物料数据</td>
                </tr>
                {/if}
            </tbody>
        </table>
    </div>
    
    <!-- 操作按钮 -->
    <div class="btn-group">
        <button type="button" class="layui-btn layui-btn-normal" onclick="editBom()">编辑</button>
        <button type="button" class="layui-btn layui-btn-warm" onclick="copyBom()">复制</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="exportBom()">导出</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="printBom()">打印</button>
        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
    </div>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var tool = layui.tool;
    var $ = layui.jquery;
    var layer = layui.layer;

    // 重新编号物料列表
    function reorderMaterials() {
        $('#materialTableBody tr').each(function(index) {
            $(this).find('td:first').text(index + 1);
        });
    }

    // 获取层级文本
    function getLevelText(level) {
        var levelMap = {1: '一级', 2: '二级', 3: '三级', 4: '四级', 5: '五级'};
        return levelMap[level] || level + '级';
    }

    // 获取缩进
    function getIndent(level) {
        var indent = '';
        for (var i = 1; i < level; i++) {
            indent += '&nbsp;&nbsp;&nbsp;&nbsp;';
        }
        return indent;
    }

    // 展开下级BOM
    window.expandBom = function(btn, materialId) {
        var $btn = $(btn);
        var $row = $btn.closest('tr');
        var currentLevel = parseInt($row.data('level')) || 1;
        var nextLevel = currentLevel + 1;

        // 检查是否已经展开
        if ($btn.text() === '收起') {
            // 收起下级BOM
            collapseBom($row, materialId);
            $btn.text('展开').removeClass('layui-btn-warm').addClass('layui-btn-normal');
            reorderMaterials();
            return;
        }

        // 显示加载状态
        $btn.text('加载中...').prop('disabled', true);

        // 请求下级BOM数据
        $.ajax({
            url: '/material/bom/getSubBom',
            type: 'GET',
            data: {
                material_id: materialId
            },
            success: function(res) {
                if (res.code === 0 && res.data && res.data.length > 0) {
                    // 添加下级BOM到表格
                    addSubBomToTable(res.data, $row, nextLevel, materialId);
                    $btn.text('收起').removeClass('layui-btn-normal').addClass('layui-btn-warm');
                    reorderMaterials();
                } else {
                    layer.msg('该物料没有下级BOM', {icon: 1});
                    $btn.text('展开').prop('disabled', false);
                }
            },
            error: function() {
                layer.msg('获取下级BOM失败', {icon: 2});
                $btn.text('展开').prop('disabled', false);
            }
        });
    };

    // 收起下级BOM
    function collapseBom($parentRow, parentMaterialId) {
        var $nextRow = $parentRow.next();
        while ($nextRow.length && $nextRow.data('parent') == parentMaterialId) {
            var $currentRow = $nextRow;
            $nextRow = $nextRow.next();
            $currentRow.remove();
        }
    }

    // 添加下级BOM到表格
    function addSubBomToTable(bomData, parentRow, level, parentMaterialId) {
        var levelText = getLevelText(level);
        var indent = getIndent(level);

        bomData.forEach(function(bom, index) {
            var row = `
                <tr data-id="${bom.id}" data-level="${level}" data-parent="${parentMaterialId}" data-material-id="${bom.material_id}" class="sub-bom-row level-${level}">
                    <td></td>
                    <td class="bom-level-indent level-${level}">${indent}${levelText}</td>
                    <td>${bom.material_code}</td>
                    <td>${bom.material_name}</td>
                    <td><img src="/static/images/default.png" width="40" height="40"></td>
                    <td>${bom.material_category || ''}</td>
                    <td>${bom.specifications || ''}</td>
                    <td>${bom.model || ''}</td>
                    <td>${bom.quantity}</td>
                    <td>${bom.loss_rate}%</td>
                    <td>${bom.material_source}</td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="expandBom(this, '${bom.material_id}')">展开</button>
                    </td>
                </tr>
            `;
            parentRow.after(row);
            parentRow = parentRow.next();
        });
    }

    // 编辑BOM
    window.editBom = function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        parent.layui.tool.side("/material/bom/edit?id={$bom.id}");
    };

    // 复制BOM
    window.copyBom = function() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        parent.layui.tool.side("/material/bom/add?copy_id={$bom.id}");
    };

    // 导出BOM
    window.exportBom = function() {
        tool.post("/material/bom/export", {ids: ["{$bom.id}"]}, function(res){
            layer.msg(res.msg);
        });
    };

    // 打印BOM
    window.printBom = function() {
        window.print();
    };

    // 初始化时重新编号
    reorderMaterials();
}
</script>
{/block}
<!-- /脚本 -->