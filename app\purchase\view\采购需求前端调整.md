# 采购需求前端表格调整文档

## 概述
根据后端API数据结构的变更，调整采购需求页面(`cgxq.html`)的表格显示，以适应新的数据源（销售订单缺货 + 生产订单物料缺货）。

## 主要调整内容

### 1. 页面提示信息更新
**原来**：`【采购需求表-销售订单通过审核会自动将物料展开】`
**现在**：`【采购需求表-显示销售订单库存缺货商品和生产订单物料需求缺口】`

### 2. 表格列结构调整

#### 新增列
- **需求类型**：区分销售缺货和生产缺料
- **缺口数量**：直接显示缺口数量
- **关联信息**：显示所属订单的产品信息

#### 修改列
| 原列名 | 新列名 | 变化说明 |
|--------|--------|----------|
| 主/BOM | 关联信息 | 显示订单关联的产品信息 |
| 来源类型 | 需求类型 | 使用徽章样式显示 |
| 状态 | 状态 | 根据来源类型显示不同状态 |

#### 删除列
- 原来的`source_type`列（合并到需求类型中）

### 3. 字段映射调整

#### 需求类型显示
```javascript
{field: 'types', title: '需求类型', width: 100, align: 'center', templet: function(d){
    if (d.source_type === 'sales_shortage') {
        return '<span class="layui-badge layui-bg-blue">销售缺货</span>';
    } else if (d.source_type === 'production_shortage') {
        return '<span class="layui-badge layui-bg-orange">生产缺料</span>';
    } else {
        return '<span class="layui-badge layui-bg-gray">未知</span>';
    }
}}
```

#### 关联信息显示
```javascript
{field: 'main_or_bom', title: '关联信息', width: 200, align: 'center', templet: function(d){
    if (d.source_type === 'sales_shortage') {
        return '<div style="text-align: center;">' +
               '<div style="color: #1890ff; font-size: 12px;">销售订单</div>' +
               '<div style="color: #666; font-size: 11px;">' + (d.main_product_name || '') + '</div>' +
               '</div>';
    } else if (d.source_type === 'production_shortage') {
        return '<div style="text-align: center;">' +
               '<div style="color: #fa8c16; font-size: 12px;">生产物料</div>' +
               '<div style="color: #666; font-size: 11px;">' + (d.main_product_name || '') + '</div>' +
               '</div>';
    } else {
        return '<span style="color: #999;">未知类型</span>';
    }
}}
```

#### 缺口数量显示
```javascript
{field: 'gap', title: '缺口数量', width: 90, align: 'center', templet: function(d){
    var gap = d.gap || 0;
    return '<span style="color: #FF5722; font-weight: bold;">' + gap + '</span>';
}}
```

#### 状态显示
```javascript
{field: 'status_text', title: '状态', width: 100, align: 'center', templet: function(d){
    if (d.source_type === 'sales_shortage') {
        return '<span class="layui-badge layui-bg-blue">销售缺货</span>';
    } else {
        return '<span class="layui-badge layui-bg-orange">生产缺料</span>';
    }
}}
```

### 4. 列宽优化
- ID列：100 → 80
- 创建时间：160 → 150
- 订单号：150 → 140
- 业务员：120 → 100
- 产品编码：160 → 140
- 新增缺口数量列：90
- 新增需求类型列：100

### 5. 视觉优化

#### 徽章样式
- **销售缺货**：蓝色徽章 (`layui-bg-blue`)
- **生产缺料**：橙色徽章 (`layui-bg-orange`)
- **未知类型**：灰色徽章 (`layui-bg-gray`)

#### 颜色编码
- **销售订单**：蓝色 (`#1890ff`)
- **生产物料**：橙色 (`#fa8c16`)
- **缺口数量**：红色 (`#FF5722`)
- **库存可用**：绿色 (`#009688`)
- **在途可用**：蓝色 (`#1E9FFF`)

## 业务逻辑变化

### 1. 数据来源
- **原来**：单一的BOM展开物料需求
- **现在**：销售订单缺货 + 生产订单物料缺货

### 2. 显示逻辑
- **销售缺货**：显示库存不足的销售订单商品
- **生产缺料**：显示生产所需但缺少的物料

### 3. 状态管理
- **销售缺货**：基于库存状态判断
- **生产缺料**：基于物料分配状态判断

## 用户体验提升

1. **信息更清晰**：明确区分销售缺货和生产缺料
2. **操作更便捷**：直接显示缺口数量，无需计算
3. **视觉更友好**：使用徽章和颜色编码提高可读性
4. **数据更准确**：实时反映库存和生产需求状况

## 测试要点

- [ ] 表格正常加载数据
- [ ] 需求类型徽章正确显示
- [ ] 关联信息正确显示
- [ ] 缺口数量正确计算
- [ ] 库存和在途数据正确显示
- [ ] 搜索功能正常
- [ ] 分页功能正常
- [ ] 批量处理功能正常
