-- 物料档案系统数据库更新脚本
-- 执行前请备份数据库

-- 1. 为 oa_product 表添加缺失的字段
ALTER TABLE `oa_product` 
ADD COLUMN `default_warehouse` int(11) DEFAULT '0' COMMENT '默认仓库ID' AFTER `stock`,
ADD COLUMN `min_order_qty` decimal(15,2) DEFAULT '0.00' COMMENT '最小起订量' AFTER `default_warehouse`,
ADD COLUMN `min_package_qty` decimal(15,2) DEFAULT '0.00' COMMENT '最小包装量' AFTER `min_order_qty`,
ADD COLUMN `material_level` varchar(10) DEFAULT '' COMMENT '物料等级(A/B/C)' AFTER `min_package_qty`,
ADD COLUMN `material_source` varchar(50) DEFAULT '' COMMENT '物料来源(自购/加工/客供/委外供料)' AFTER `material_level`,
ADD COLUMN `category` varchar(50) DEFAULT '' COMMENT '物料类别(主料/辅料)' AFTER `material_source`,
ADD COLUMN `model` varchar(100) DEFAULT '' COMMENT '型号' AFTER `category`,
ADD COLUMN `color` varchar(50) DEFAULT '' COMMENT '颜色' AFTER `model`,
ADD COLUMN `remark` text COMMENT '备注信息' AFTER `description`,
ADD COLUMN `material_images` text COMMENT '物料图片URLs,JSON格式' AFTER `remark`,
ADD COLUMN `material_drawing` varchar(255) DEFAULT '' COMMENT '物料图纸URL' AFTER `material_images`,
ADD COLUMN `quality_management` tinyint(1) DEFAULT '0' COMMENT '质检管理:0禁用 1启用' AFTER `material_drawing`,
ADD COLUMN `quality_exempt` tinyint(1) DEFAULT '0' COMMENT '免检设置:0关闭 1开启' AFTER `quality_management`,
ADD COLUMN `quality_settings` text COMMENT '质检项目设置,JSON格式' AFTER `quality_exempt`,
ADD COLUMN `reference_cost` decimal(15,2) DEFAULT '0.00' COMMENT '参考成本价' AFTER `quality_settings`,
ADD COLUMN `sales_price` decimal(15,2) DEFAULT '0.00' COMMENT '销售单价(含税)' AFTER `reference_cost`,
ADD COLUMN `min_sales_price` decimal(15,2) DEFAULT '0.00' COMMENT '最低销售单价' AFTER `sales_price`,
ADD COLUMN `max_sales_price` decimal(15,2) DEFAULT '0.00' COMMENT '最高销售单价' AFTER `min_sales_price`,
ADD COLUMN `attachments` text COMMENT '附件URLs,JSON格式' AFTER `max_sales_price`;

-- 2. 创建供应商价格表
CREATE TABLE IF NOT EXISTS `material_supplier_price` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `material_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物料ID',
  `supplier_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `supplier_code` varchar(100) NOT NULL DEFAULT '' COMMENT '供应商编号',
  `priority` varchar(20) NOT NULL DEFAULT '' COMMENT '采购优先级(首选/备选)',
  `min_qty` decimal(15,2) DEFAULT '0.00' COMMENT '采购下限',
  `max_qty` decimal(15,2) DEFAULT '0.00' COMMENT '采购上限',
  `tax_price` decimal(15,2) DEFAULT '0.00' COMMENT '含税单价',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率(%)',
  `no_tax_price` decimal(15,2) DEFAULT '0.00' COMMENT '不含税单价',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用 1启用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_material_id` (`material_id`),
  KEY `idx_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料供应商价格表';

-- 3. 创建外协价格表
CREATE TABLE IF NOT EXISTS `material_outsource_price` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `material_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '物料ID',
  `supplier_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '外协商ID',
  `outsource_code` varchar(100) NOT NULL DEFAULT '' COMMENT '外协商编号',
  `priority` varchar(20) NOT NULL DEFAULT '' COMMENT '外协优先级(首选/备选)',
  `min_qty` decimal(15,2) DEFAULT '0.00' COMMENT '外协下限',
  `max_qty` decimal(15,2) DEFAULT '0.00' COMMENT '外协上限',
  `tax_price` decimal(15,2) DEFAULT '0.00' COMMENT '含税单价',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率(%)',
  `no_tax_price` decimal(15,2) DEFAULT '0.00' COMMENT '不含税单价',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用 1启用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_material_id` (`material_id`),
  KEY `idx_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料外协价格表';

-- 4. 创建单位管理表
CREATE TABLE IF NOT EXISTS `oa_unit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(20) NOT NULL COMMENT '单位名称',
  `precision` tinyint(1) NOT NULL DEFAULT '0' COMMENT '精度(小数位数)',
  `type` varchar(10) NOT NULL DEFAULT '四舍五入' COMMENT '含入类型：四舍五入、进位',
  `remark` varchar(100) DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-禁用',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='单位管理表';

-- 5. 创建物料编号序列表(用于自动生成编号)
CREATE TABLE IF NOT EXISTS `material_code_sequence` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `prefix` varchar(20) NOT NULL DEFAULT 'WL' COMMENT '编号前缀',
  `current_number` int(11) NOT NULL DEFAULT '0' COMMENT '当前序号',
  `date_format` varchar(20) NOT NULL DEFAULT 'Ymd' COMMENT '日期格式',
  `number_length` int(2) NOT NULL DEFAULT '4' COMMENT '序号长度',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_prefix` (`prefix`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物料编号序列表';

-- 6. 插入默认单位数据
INSERT INTO `oa_unit` (`name`, `precision`, `type`, `remark`, `status`, `admin_id`, `create_time`, `update_time`) VALUES
('个', 0, '四舍五入', '计数单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('件', 0, '四舍五入', '计数单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('套', 0, '四舍五入', '计数单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('台', 0, '四舍五入', '计数单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('米', 2, '四舍五入', '长度单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('千克', 3, '四舍五入', '重量单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('吨', 3, '四舍五入', '重量单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('升', 2, '四舍五入', '体积单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('平方米', 2, '四舍五入', '面积单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('立方米', 3, '四舍五入', '体积单位', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
ON DUPLICATE KEY UPDATE `update_time` = UNIX_TIMESTAMP();

-- 7. 插入默认的编号序列配置
INSERT INTO `material_code_sequence` (`prefix`, `current_number`, `date_format`, `number_length`, `create_time`, `update_time`)
VALUES ('WL', 0, 'Ymd', 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
ON DUPLICATE KEY UPDATE `update_time` = UNIX_TIMESTAMP();

-- 8. 添加索引优化
ALTER TABLE `oa_product` ADD INDEX `idx_material_code` (`material_code`);
ALTER TABLE `oa_product` ADD INDEX `idx_source_type` (`source_type`);
ALTER TABLE `oa_product` ADD INDEX `idx_type` (`type`);

-- 执行完成后请检查表结构是否正确
