<?php
namespace app\Produce\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\Log;

/**
 * 生产计划排产调试控制器
 */
class ProductionPlanDebug extends BaseController
{
    /**
     * 检查数据库表
     */
    public function checkTables()
    {
        try {
            $results = [];
            
            // 检查生产订单表
            $orderCount = Db::name('produce_order')->count();
            $results['produce_order'] = [
                'exists' => true,
                'count' => $orderCount
            ];
            
            // 检查生产计划表
            try {
                $planCount = Db::name('production_plan')->count();
                $results['production_plan'] = [
                    'exists' => true,
                    'count' => $planCount
                ];
            } catch (\Exception $e) {
                $results['production_plan'] = [
                    'exists' => false,
                    'error' => $e->getMessage()
                ];
            }
            
            // 检查待排产订单
            $unscheduledCount = Db::name('produce_order')
                ->where('plan_status', 0)
                ->where('status', 'in', [0, 1])
                ->count();
            $results['unscheduled_orders'] = $unscheduledCount;
            
            return json([
                'code' => 0,
                'msg' => '检查完成',
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 测试API接口
     */
    public function testApi()
    {
        try {
            $results = [];
            
            // 测试获取待排产订单
            $productionPlan = new \app\Produce\controller\ProductionPlan();
            
            // 模拟请求参数
            $_GET['page'] = 1;
            $_GET['limit'] = 10;
            
            $unscheduledResult = $productionPlan->getUnscheduledOrders();
            $results['unscheduled_orders'] = json_decode($unscheduledResult->getContent(), true);
            
            // 测试获取已排产计划
            $_GET['start_date'] = date('Y-m-d');
            $_GET['end_date'] = date('Y-m-d', strtotime('+30 days'));
            
            $scheduledResult = $productionPlan->getScheduledPlans();
            $results['scheduled_plans'] = json_decode($scheduledResult->getContent(), true);
            
            return json([
                'code' => 0,
                'msg' => 'API测试完成',
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => 'API测试失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建测试数据
     */
    public function createTestData()
    {
        try {
            // 检查是否已有排产数据
            $existingPlans = 0;
            try {
                $existingPlans = Db::name('production_plan')->count();
            } catch (\Exception $e) {
                // 表不存在，需要先创建
                return json([
                    'code' => 1,
                    'msg' => '生产计划表不存在，请先执行数据库迁移脚本'
                ]);
            }
            
            if ($existingPlans > 0) {
                return json([
                    'code' => 1,
                    'msg' => '已存在排产数据，请先清理后再创建测试数据'
                ]);
            }
            
            // 获取一些生产订单作为测试
            $orders = Db::name('produce_order')
                ->where('plan_status', 0)  // 未排产的订单
                ->where('status', 'in', [0, 1])
                ->limit(5)
                ->select();
                
            if (empty($orders)) {
                return json([
                    'code' => 1,
                    'msg' => '没有可用的生产订单创建测试数据'
                ]);
            }
            
            $createdCount = 0;
            $startDate = date('Y-m-d', strtotime('+1 day'));
            
            foreach ($orders as $index => $order) {
                $planStartDate = date('Y-m-d', strtotime($startDate . ' +' . $index . ' days'));
                $planEndDate = date('Y-m-d', strtotime($planStartDate . ' +1 day'));
                
                // 创建排产计划
                $planId = Db::name('production_plan')->insertGetId([
                    'order_id' => $order['id'],
                    'plan_start_date' => $planStartDate,
                    'plan_end_date' => $planEndDate,
                    'plan_days' => 2,
                    'priority' => rand(1, 5),
                    'progress' => rand(0, 100),
                    'status' => rand(0, 2),
                    'auto_scheduled' => 1,
                    'notes' => '测试排产数据',
                    'create_time' => time(),
                    'create_uid' => 1,
                    'create_name' => '测试用户'
                ]);
                
                // 更新订单状态
                Db::name('produce_order')->where('id', $order['id'])->update([
                    'plan_id' => $planId,
                    'plan_status' => 1,
                    'scheduled_date' => $planStartDate,
                    'estimated_days' => 2,
                    'update_time' => time()
                ]);
                
                $createdCount++;
            }
            
            return json([
                'code' => 0,
                'msg' => "成功创建 {$createdCount} 条测试排产数据"
            ]);
            
        } catch (\Exception $e) {
            Log::error('创建测试数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json([
                'code' => 1,
                'msg' => '创建失败：' . $e->getMessage()
            ]);
        }
    }
}
