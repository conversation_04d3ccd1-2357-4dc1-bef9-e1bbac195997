<!DOCTYPE html>
<html>
<head>
    <title>BOM状态管理功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        /* 模拟layui样式 */
        .layui-btn {
            display: inline-block;
            height: 38px;
            line-height: 38px;
            padding: 0 18px;
            background-color: #009688;
            color: #fff;
            white-space: nowrap;
            text-align: center;
            font-size: 14px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            text-decoration: none;
            margin-right: 5px;
        }
        .layui-btn-xs {
            height: 22px;
            line-height: 22px;
            padding: 0 5px;
            font-size: 12px;
        }
        .layui-btn-warm {
            background-color: #FFB800;
        }
        .layui-btn-group {
            display: inline-block;
        }
        .layui-btn-group .layui-btn {
            margin-right: 0;
            border-radius: 0;
        }
        .layui-btn-group .layui-btn:first-child {
            border-radius: 2px 0 0 2px;
        }
        .layui-btn-group .layui-btn:last-child {
            border-radius: 0 2px 2px 0;
        }
        
        /* 状态样式 */
        .bom-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        .status-draft {
            background-color: #909399;
        }
        .status-enabled {
            background-color: #67C23A;
        }
        .status-disabled {
            background-color: #F56C6C;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #e6e6e6;
            padding: 10px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 400px;
            border-radius: 5px;
        }
        .modal-header {
            border-bottom: 1px solid #e6e6e6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .modal-footer {
            border-top: 1px solid #e6e6e6;
            padding-top: 15px;
            margin-top: 20px;
            text-align: right;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        
        .layui-form-item {
            margin-bottom: 15px;
        }
        .layui-form-label {
            display: inline-block;
            width: 80px;
            text-align: right;
            padding-right: 10px;
        }
        .layui-input-block {
            margin-left: 90px;
        }
        .layui-input, .layui-select {
            width: 100%;
            height: 38px;
            line-height: 1.3;
            border: 1px solid #e6e6e6;
            background-color: #fff;
            border-radius: 2px;
            padding: 0 15px;
            font-size: 14px;
            color: #666;
            box-sizing: border-box;
        }
        
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .feature-list h3 {
            margin-top: 0;
            color: #333;
        }
        .feature-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 5px 0;
        }
        
        .code-block {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BOM状态管理功能</h1>
        
        <div class="feature-list">
            <h3>功能特点</h3>
            <ul>
                <li><strong>三种状态</strong>：草稿(0)、启用(1)、禁用(2)</li>
                <li><strong>弹窗选择</strong>：点击"状态"按钮打开状态选择弹窗</li>
                <li><strong>状态验证</strong>：后端验证状态值的有效性</li>
                <li><strong>实时更新</strong>：状态修改后立即刷新表格</li>
                <li><strong>操作日志</strong>：记录状态修改的时间</li>
            </ul>
        </div>
        
        <h2>BOM列表示例</h2>
        <table>
            <thead>
                <tr>
                    <th>BOM编号</th>
                    <th>产品名称</th>
                    <th>状态</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>BOM001</td>
                    <td>测试产品A</td>
                    <td><span class="bom-status status-draft">草稿</span></td>
                    <td>2025-01-15 10:30:00</td>
                    <td>
                        <div class="layui-btn-group">
                            <span class="layui-btn layui-btn-xs">详细</span>
                            <span class="layui-btn layui-btn-xs">编辑</span>
                            <span class="layui-btn layui-btn-xs layui-btn-warm" onclick="openStatusModal('BOM001', 0)">状态</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>BOM002</td>
                    <td>测试产品B</td>
                    <td><span class="bom-status status-enabled">启用</span></td>
                    <td>2025-01-14 14:20:00</td>
                    <td>
                        <div class="layui-btn-group">
                            <span class="layui-btn layui-btn-xs">详细</span>
                            <span class="layui-btn layui-btn-xs">编辑</span>
                            <span class="layui-btn layui-btn-xs layui-btn-warm" onclick="openStatusModal('BOM002', 1)">状态</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>BOM003</td>
                    <td>测试产品C</td>
                    <td><span class="bom-status status-disabled">禁用</span></td>
                    <td>2025-01-13 09:15:00</td>
                    <td>
                        <div class="layui-btn-group">
                            <span class="layui-btn layui-btn-xs">详细</span>
                            <span class="layui-btn layui-btn-xs">编辑</span>
                            <span class="layui-btn layui-btn-xs layui-btn-warm" onclick="openStatusModal('BOM003', 2)">状态</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <h2>实现代码</h2>
        
        <h3>1. 前端操作列模板</h3>
        <div class="code-block">
&lt;script type="text/html" id="operationTpl"&gt;
&lt;div class="layui-btn-group"&gt;
    &lt;span class="layui-btn layui-btn-xs" lay-event="view"&gt;详细&lt;/span&gt;
    &lt;span class="layui-btn layui-btn-xs" lay-event="edit"&gt;编辑&lt;/span&gt;
    &lt;span class="layui-btn layui-btn-xs layui-btn-warm" lay-event="changeStatus"&gt;状态&lt;/span&gt;
&lt;/div&gt;
&lt;/script&gt;
        </div>
        
        <h3>2. 状态修改弹窗</h3>
        <div class="code-block">
&lt;div id="statusChangeForm" style="display: none; padding: 20px;"&gt;
    &lt;form class="layui-form" lay-filter="statusForm"&gt;
        &lt;input type="hidden" id="currentBomId" /&gt;
        &lt;div class="layui-form-item"&gt;
            &lt;label class="layui-form-label"&gt;BOM状态&lt;/label&gt;
            &lt;div class="layui-input-block"&gt;
                &lt;select id="statusSelect" name="status" lay-verify="required"&gt;
                    &lt;option value="0"&gt;草稿&lt;/option&gt;
                    &lt;option value="1"&gt;启用&lt;/option&gt;
                    &lt;option value="2"&gt;禁用&lt;/option&gt;
                &lt;/select&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/form&gt;
&lt;/div&gt;
        </div>
        
        <h3>3. 后端控制器方法</h3>
        <div class="code-block">
public function changeStatus()
{
    $param = get_params();
    
    if (empty($param['id'])) {
        return to_assign(1, '参数错误');
    }
    
    if (!isset($param['status'])) {
        return to_assign(1, '状态参数错误');
    }
    
    // 验证状态值
    $allowedStatus = [0, 1, 2]; // 0:草稿, 1:启用, 2:禁用
    if (!in_array($param['status'], $allowedStatus)) {
        return to_assign(1, '状态值无效');
    }
    
    try {
        $result = Db::name('material_bom')
            -&gt;where('id', $param['id'])
            -&gt;update([
                'status' =&gt; $param['status'],
                'update_time' =&gt; time()
            ]);
        
        if ($result) {
            $statusText = ['草稿', '启用', '禁用'];
            return to_assign(0, 'BOM状态已修改为：' . $statusText[$param['status']]);
        } else {
            return to_assign(1, '状态修改失败');
        }
    } catch (\Exception $e) {
        return to_assign(1, '状态修改失败：' . $e-&gt;getMessage());
    }
}
        </div>
        
        <h2>状态说明</h2>
        <table>
            <thead>
                <tr>
                    <th>状态值</th>
                    <th>状态名称</th>
                    <th>显示样式</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>0</td>
                    <td>草稿</td>
                    <td><span class="bom-status status-draft">草稿</span></td>
                    <td>新建或编辑中的BOM，可以修改产品信息</td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>启用</td>
                    <td><span class="bom-status status-enabled">启用</span></td>
                    <td>已审核通过的BOM，可以正常使用</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>禁用</td>
                    <td><span class="bom-status status-disabled">禁用</span></td>
                    <td>已停用的BOM，不可使用</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 状态修改弹窗 -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeStatusModal()">&times;</span>
                <h3>修改BOM状态</h3>
            </div>
            <form class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">BOM状态</label>
                    <div class="layui-input-block">
                        <select id="demoStatusSelect" class="layui-select">
                            <option value="0">草稿</option>
                            <option value="1">启用</option>
                            <option value="2">禁用</option>
                        </select>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button class="layui-btn" onclick="saveStatus()">保存</button>
                <button class="layui-btn layui-btn-primary" onclick="resetStatus()">重置</button>
            </div>
        </div>
    </div>
    
    <script>
        var currentBomCode = '';
        var originalStatus = 0;
        
        function openStatusModal(bomCode, status) {
            currentBomCode = bomCode;
            originalStatus = status;
            document.getElementById('demoStatusSelect').value = status;
            document.getElementById('statusModal').style.display = 'block';
        }
        
        function closeStatusModal() {
            document.getElementById('statusModal').style.display = 'none';
        }
        
        function saveStatus() {
            var newStatus = document.getElementById('demoStatusSelect').value;
            var statusText = ['草稿', '启用', '禁用'];
            alert('BOM ' + currentBomCode + ' 状态已修改为：' + statusText[newStatus]);
            closeStatusModal();
        }
        
        function resetStatus() {
            document.getElementById('demoStatusSelect').value = originalStatus;
        }
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {
            var modal = document.getElementById('statusModal');
            if (event.target == modal) {
                closeStatusModal();
            }
        }
    </script>
</body>
</html>
