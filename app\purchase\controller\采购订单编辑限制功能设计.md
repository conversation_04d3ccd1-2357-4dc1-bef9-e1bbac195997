# 采购订单编辑限制功能设计

## 需求背景
当采购订单的来源类型是 `customer_order`（销售订单）时，不允许添加和删除产品，因为这会影响销售订单的成本核算串联。

## 业务逻辑

### 限制条件
- **触发条件**：`source_type = 'customer_order'`
- **限制操作**：
  1. 不允许添加新产品
  2. 不允许删除现有产品
  3. 允许修改产品的数量、单价等其他属性

### 业务原因
1. **成本核算准确性**：销售订单的成本需要与采购订单的产品一一对应
2. **数据一致性**：避免采购订单与销售订单的产品不匹配
3. **财务合规性**：确保成本核算的可追溯性和准确性

## 技术实现

### 1. 控制器修改 (Order.php)
```php
// 在edit方法中添加判断
$is_from_customer_order = ($detail['source_type'] == 'customer_order');
View::assign('is_from_customer_order', $is_from_customer_order);
```

### 2. 前端界面修改 (edit.html)

#### 按钮控制
```html
{if !$is_from_customer_order}
<button type="button" class="layui-btn layui-btn-sm" id="addProduct">
    <i class="layui-icon layui-icon-add-1"></i> 添加商品
</button>
<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" id="removeProduct">
    <i class="layui-icon layui-icon-delete"></i> 删除选中
</button>
{else}
<div class="layui-text" style="color:#FF5722; padding:5px 0;">
    <i class="layui-icon layui-icon-tips"></i> 
    此采购订单来源于销售订单，不允许添加或删除产品，以确保成本核算的准确性
</div>
{/if}
```

#### 表格行删除按钮
```html
<td>
    {if !$is_from_customer_order}
    <a class="layui-btn layui-btn-xs layui-btn-danger btn-remove-row">
        <i class="layui-icon layui-icon-delete"></i>
    </a>
    {else}
    <span class="layui-text" style="color:#999;">锁定</span>
    {/if}
</td>
```

### 3. JavaScript事件控制
```javascript
// 传递限制标识
let isFromCustomerOrder = {$is_from_customer_order ? 'true' : 'false'};

// 添加产品限制
$('#addProduct').click(function() {
    if (isFromCustomerOrder) {
        layer.msg('此采购订单来源于销售订单，不允许添加产品', {icon: 2});
        return;
    }
    // 原有添加逻辑...
});

// 删除产品限制
$(document).on('click', '.btn-remove-row', function() {
    if (isFromCustomerOrder) {
        layer.msg('此采购订单来源于销售订单，不允许删除产品', {icon: 2});
        return;
    }
    // 原有删除逻辑...
});
```

## 用户体验设计

### 1. 视觉提示
- **提示信息**：明确说明限制原因
- **按钮状态**：隐藏添加/删除按钮，显示"锁定"状态
- **颜色标识**：使用橙色警告色显示提示信息

### 2. 操作反馈
- **阻止操作**：在用户尝试添加/删除时显示友好提示
- **说明原因**：解释为什么不允许此操作
- **替代方案**：提示用户可以修改其他属性

### 3. 界面一致性
- **保持布局**：不破坏原有页面布局
- **状态清晰**：明确区分可操作和不可操作的元素

## 测试用例

### 1. 销售订单来源的采购订单
- ✅ 不显示"添加商品"按钮
- ✅ 不显示"删除选中"按钮
- ✅ 表格行显示"锁定"而不是删除按钮
- ✅ 点击时显示友好提示信息
- ✅ 允许修改数量、单价等其他字段

### 2. 非销售订单来源的采购订单
- ✅ 正常显示所有添加/删除按钮
- ✅ 所有添加/删除功能正常工作
- ✅ 界面与原来保持一致

### 3. 边界情况
- ✅ source_type 为空时的处理
- ✅ source_type 为其他值时的处理
- ✅ 数据库字段不存在时的处理

## 注意事项

1. **数据完整性**：确保 source_type 字段正确设置
2. **向后兼容**：对于没有 source_type 字段的历史数据，默认允许编辑
3. **权限控制**：此限制是业务逻辑限制，不影响其他权限控制
4. **扩展性**：设计支持未来可能的其他来源类型限制

## 相关文档
- 销售订单与采购订单关联设计
- 成本核算业务流程
- 采购订单数据字典
