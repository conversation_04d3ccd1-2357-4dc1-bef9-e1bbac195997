# 生产工单重新设计方案

## 1. 功能概述

重新设计生产工单模块，增加生产类型选择功能，支持两种生产模式：
1. **库存生产**：调用系统产品表，用于补充库存的生产
2. **销售订单生产**：调用销售订单，用于满足特定客户订单的生产

## 2. 界面设计

### 2.1 生产类型选择
在生产工单添加页面顶部增加选项卡切换：
- **产品配置** - 库存生产模式
- **工序配置** - 销售订单生产模式

### 2.2 库存生产模式界面
```
生产类型: [库存生产] [销售订单] (选项卡形式)

产品配置:
┌─────────────────────────────────────────────────────────────────┐
│ 产品编码    产品名称      产品规格    产品单位   基本单位   可用库存   计划数量   操作 │
│ 204H      CP2025072002   0.15*194     KG        KG        100      100     添加产品│
└─────────────────────────────────────────────────────────────────┘
```

### 2.3 销售订单生产模式界面
```
生产类型: [库存生产] [销售订单] (选项卡形式)

销售订单选择:
订单编号: [下拉选择销售订单]

工序配置:
┌─────────────────────────────────────────────────────────────────┐
│ 产品编码    产品名称      产品规格    产品单位   每套件数   可用库存   计划数量   操作 │
│ 204H      CP2025072002   0.15*194     KG        KG        100      100     删除  │
└─────────────────────────────────────────────────────────────────┘
```

## 3. 数据库设计

### 3.1 生产订单表修改 (oa_produce_order)
```sql
ALTER TABLE `oa_produce_order` ADD COLUMN `production_type` TINYINT(1) DEFAULT 1 COMMENT '生产类型:1=库存生产,2=销售订单生产';
ALTER TABLE `oa_produce_order` ADD COLUMN `customer_order_id` INT(11) DEFAULT 0 COMMENT '关联销售订单ID(销售订单生产时使用)';
ALTER TABLE `oa_produce_order` ADD COLUMN `customer_order_no` VARCHAR(50) DEFAULT '' COMMENT '关联销售订单号';
```

### 3.2 生产订单明细表 (oa_produce_order_detail)
```sql
CREATE TABLE IF NOT EXISTS `oa_produce_order_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_code` varchar(100) NOT NULL COMMENT '产品编码',
  `product_name` varchar(255) NOT NULL COMMENT '产品名称',
  `specification` varchar(255) DEFAULT '' COMMENT '规格型号',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '计划生产数量',
  `completed_qty` decimal(10,2) DEFAULT 0.00 COMMENT '已完成数量',
  `customer_order_detail_id` int(11) DEFAULT 0 COMMENT '关联销售订单明细ID',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单明细表';
```

## 4. 控制器方法设计

### 4.1 添加/编辑页面 (add方法)
```php
public function add()
{
    $param = get_params();
    $id = $param['id'] ?? 0;
    
    if (request()->isPost()) {
        // 保存逻辑
        return $this->save($param);
    }
    
    // 获取销售订单列表（未完全生产的订单）
    $customerOrders = $this->getAvailableCustomerOrders();
    
    // 获取产品列表
    $productList = $this->getProductList();
    
    View::assign([
        'customerOrders' => $customerOrders,
        'productList' => $productList,
        'info' => $id > 0 ? $this->getOrderInfo($id) : []
    ]);
    
    return view();
}
```

### 4.2 获取可用销售订单
```php
private function getAvailableCustomerOrders()
{
    return Db::name('customer_order')
        ->alias('o')
        ->join('customer c', 'o.customer_id = c.id')
        ->where('o.status', '>', 0) // 已审核的订单
        ->where('o.check_status', 2) // 审核通过
        ->field('o.id,o.order_no,c.name as customer_name,o.create_time')
        ->order('o.id desc')
        ->select();
}
```

### 4.3 获取销售订单明细
```php
public function getCustomerOrderDetails()
{
    $orderId = input('order_id/d', 0);
    
    $details = Db::name('customer_order_detail')
        ->alias('d')
        ->join('product p', 'd.product_id = p.id')
        ->where('d.order_id', $orderId)
        ->field('d.id,d.product_id,p.material_code,p.title as product_name,p.specs,p.unit,d.quantity,d.produced_quantity')
        ->select();
    
    // 计算剩余需要生产的数量
    foreach ($details as &$detail) {
        $detail['remaining_qty'] = $detail['quantity'] - $detail['produced_quantity'];
        $detail['available_stock'] = $this->getProductStock($detail['product_id']);
    }
    
    return json(['code' => 0, 'data' => $details]);
}
```

## 5. 前端JavaScript设计

### 5.1 选项卡切换
```javascript
// 生产类型切换
$('.production-type-tab').on('click', function() {
    var type = $(this).data('type');
    $('.production-type-tab').removeClass('active');
    $(this).addClass('active');
    
    if (type === 'inventory') {
        $('#inventory-production').show();
        $('#order-production').hide();
    } else {
        $('#inventory-production').hide();
        $('#order-production').show();
    }
});
```

### 5.2 销售订单选择
```javascript
// 销售订单选择变化
form.on('select(customer_order)', function(data) {
    if (data.value) {
        loadCustomerOrderDetails(data.value);
    } else {
        clearOrderDetails();
    }
});

// 加载销售订单明细
function loadCustomerOrderDetails(orderId) {
    $.ajax({
        url: '/Produce/order/getCustomerOrderDetails',
        data: {order_id: orderId},
        success: function(res) {
            if (res.code === 0) {
                renderOrderDetails(res.data);
            }
        }
    });
}
```

## 6. 实施步骤

### 第一阶段：数据库结构调整
1. 修改生产订单表，增加生产类型字段
2. 创建生产订单明细表
3. 数据迁移脚本

### 第二阶段：后端功能开发
1. 修改控制器方法
2. 增加销售订单相关接口
3. 完善保存逻辑

### 第三阶段：前端界面改造
1. 重新设计添加页面
2. 实现选项卡切换
3. 完善交互逻辑

### 第四阶段：测试和优化
1. 功能测试
2. 界面优化
3. 性能调优

## 7. 注意事项

1. **数据兼容性**：确保现有生产订单数据的兼容性
2. **权限控制**：不同类型的生产订单可能需要不同的权限
3. **库存管理**：两种生产类型对库存的影响需要区别处理
4. **报表统计**：需要支持按生产类型进行统计分析
