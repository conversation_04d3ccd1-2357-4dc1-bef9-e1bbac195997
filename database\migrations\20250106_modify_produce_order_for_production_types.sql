-- 生产工单重新设计 - 数据库结构调整
-- 执行时间: 2025-01-06

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 修改生产订单表，增加生产类型相关字段
-- ----------------------------
ALTER TABLE `oa_produce_order` 
ADD COLUMN `production_type` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '生产类型:1=库存生产,2=销售订单生产' AFTER `source_order_id`,
ADD COLUMN `customer_order_id` INT(11) NOT NULL DEFAULT 0 COMMENT '关联销售订单ID(销售订单生产时使用)' AFTER `production_type`,
ADD COLUMN `customer_order_no` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联销售订单号' AFTER `customer_order_id`;

-- 添加索引
ALTER TABLE `oa_produce_order` 
ADD KEY `idx_production_type` (`production_type`),
ADD KEY `idx_customer_order_id` (`customer_order_id`);

-- ----------------------------
-- 2. 创建生产订单明细表
-- ----------------------------
DROP TABLE IF EXISTS `oa_produce_order_detail`;
CREATE TABLE `oa_produce_order_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_code` varchar(100) NOT NULL DEFAULT '' COMMENT '产品编码',
  `product_name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品名称',
  `specification` varchar(255) DEFAULT '' COMMENT '规格型号',
  `unit` varchar(20) NOT NULL DEFAULT '' COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '计划生产数量',
  `completed_qty` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '已完成数量',
  `customer_order_detail_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联销售订单明细ID',
  `bom_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的BOM ID',
  `process_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的工艺ID',
  `notes` text COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_customer_order_detail_id` (`customer_order_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单明细表';

-- ----------------------------
-- 3. 为销售订单明细表增加生产相关字段（如果不存在）
-- ----------------------------
-- 检查并添加销售订单明细表的相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'oa_customer_order_detail'
     AND COLUMN_NAME = 'delivered_quantity') = 0,
    'ALTER TABLE `oa_customer_order_detail` ADD COLUMN `delivered_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT ''已发货数量'' AFTER `quantity`',
    'SELECT ''Column delivered_quantity already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'oa_customer_order_detail'
     AND COLUMN_NAME = 'produced_quantity') = 0,
    'ALTER TABLE `oa_customer_order_detail` ADD COLUMN `produced_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT ''已生产数量'' AFTER `delivered_quantity`',
    'SELECT ''Column produced_quantity already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ----------------------------
-- 4. 数据迁移：将现有生产订单数据迁移到明细表
-- ----------------------------
INSERT INTO `oa_produce_order_detail` 
(`order_id`, `product_id`, `product_code`, `product_name`, `specification`, `unit`, `quantity`, `completed_qty`, `customer_order_detail_id`, `bom_id`, `process_id`, `notes`, `create_time`, `update_time`)
SELECT 
    o.id as order_id,
    o.product_id,
    COALESCE(p.material_code, '') as product_code,
    o.product_name,
    COALESCE(p.specs, '') as specification,
    COALESCE(p.unit, '') as unit,
    o.quantity,
    COALESCE(o.completed_qty, 0) as completed_qty,
    0 as customer_order_detail_id,
    COALESCE(o.bom_id, 0) as bom_id,
    COALESCE(o.process_id, 0) as process_id,
    o.remark as notes,
    o.create_time,
    o.update_time
FROM `oa_produce_order` o
LEFT JOIN `oa_product` p ON o.product_id = p.id
WHERE NOT EXISTS (
    SELECT 1 FROM `oa_produce_order_detail` d WHERE d.order_id = o.id
);

-- ----------------------------
-- 5. 更新现有生产订单的生产类型
-- ----------------------------
-- 默认设置为库存生产
UPDATE `oa_produce_order` SET `production_type` = 1 WHERE `production_type` = 0;

-- 如果有来源订单ID，尝试匹配销售订单
UPDATE `oa_produce_order` o 
SET 
    `production_type` = 2,
    `customer_order_id` = o.source_order_id,
    `customer_order_no` = (
        SELECT co.order_no 
        FROM `oa_customer_order` co 
        WHERE co.id = o.source_order_id 
        LIMIT 1
    )
WHERE o.source_order_id > 0 
AND EXISTS (
    SELECT 1 FROM `oa_customer_order` co WHERE co.id = o.source_order_id
);

-- ----------------------------
-- 6. 创建视图：生产订单完整信息视图
-- ----------------------------
DROP VIEW IF EXISTS `v_produce_order_full`;
CREATE VIEW `v_produce_order_full` AS
SELECT 
    o.id,
    o.order_no,
    o.production_type,
    o.customer_order_id,
    o.customer_order_no,
    o.delivery_date,
    o.priority,
    o.status,
    o.source,
    o.remark,
    o.create_time,
    o.update_time,
    COUNT(d.id) as detail_count,
    SUM(d.quantity) as total_quantity,
    SUM(d.completed_qty) as total_completed_qty,
    CASE 
        WHEN SUM(d.quantity) = 0 THEN 0
        ELSE ROUND(SUM(d.completed_qty) / SUM(d.quantity) * 100, 2)
    END as completion_rate
FROM `oa_produce_order` o
LEFT JOIN `oa_produce_order_detail` d ON o.id = d.order_id
GROUP BY o.id;

SET FOREIGN_KEY_CHECKS = 1;

-- 执行完成提示
SELECT '生产工单数据库结构调整完成！' as message;
