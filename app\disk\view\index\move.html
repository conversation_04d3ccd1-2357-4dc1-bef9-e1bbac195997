{extend name="../../base/view/common/base" /}
{block name="style"}
<style>	
	.disk-table{overflow:hidden;height:372px; background-color:#fff;}
	.layui-btn.tool-add{padding:0 12px;}
	
	.disk-path{height:43px;padding:0 4px 0 12px;}
	.disk-breadcrumb{float:left; padding:11px 0}
	.disk-breadcrumb span{color:#666; margin:0 8px; font-size:10px;}
	.layui-tab{margin:0;}
    .file-item {height:calc(100vh - 120px); overflow-y: auto;}

    /** 文件 **/
    .disk-folder {height:372px; overflow: hidden; overflow-y: auto; box-sizing: border-box; padding-bottom:44px;}
    .disk-folder ul li { display: flex; position: relative; padding: 12px 16px; border-bottom: 1px solid rgba(0, 0, 0, 0.05);display: flex; flex-wrap: wrap; line-height:24px;}
    .disk-folder ul li:hover {background-color:#f8f8f8;}
	.disk-folder ul li img{margin-right:12px;}

    /** 无数据 **/
    .disk-folder .empty {text-align: center; color: #999; padding:48px 0}
    .disk-folder .empty p{padding:12px 0}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="disk-table">
	<div class="disk-path layui-form border-b">
		<div class="disk-breadcrumb" id="diskPath">
		<i class="layui-icon layui-icon-windows"></i> <a href="/disk/index/move?pid=0">全部文件</a>
		{volist name="$path" id="vo"}
		<span> 〉</span><a href="/disk/index/move?pid={$vo.id}">{$vo.name}</a>
		{/volist}
		</div>
	</div>
	<div class="disk-folder">
		{empty name="$folder"}
			<div class="empty"><img src="/static/home/<USER>/icon/folder.png" width="100" height="100" /><p>移动到 【{$pfolder}】 文件夹</p></div>
		{/empty}
		<ul>
			{volist name="$folder" id="vo"}
			<li><a href="/disk/index/move?pid={$vo.id}"><img src="/static/home/<USER>/icon/folder.png" width="24" height="24" /> {$vo.name}</a></li>
			{/volist}
		</ul>
	</div>
	<input type="hidden" name="folder" id="folder" value="{$pid}">
</div>
{/block}
<!-- /主体 -->
{block name="copyright"}{/block}
<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var tool = layui.tool;
		
	}
</script>
{/block}
<!-- /脚本 -->