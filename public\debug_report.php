<?php
/**
 * 调试报工逻辑
 */

// 模拟报工数据
$data = [
    'order_id' => 27,
    'step_id' => 3,
    'quantity' => 10,
    'qualified_qty' => 8
];

echo "<pre>";
echo "=== 调试报工逻辑 ===\n\n";
echo "模拟数据：\n";
print_r($data);
echo "\n";

// 引入框架
require_once __DIR__ . '/../vendor/autoload.php';
$app = new \think\App();
$app->initialize();

use think\facade\Db;

try {
    // 1. 获取订单的所有工序实例
    $orderProcesses = Db::name('produce_order_process')
        ->where('order_id', $data['order_id'])
        ->order('step_no asc')
        ->select()
        ->toArray();
    
    echo "订单工序列表：\n";
    foreach ($orderProcesses as $i => $process) {
        echo "  索引{$i}: step_no={$process['step_no']}, name={$process['process_name']}\n";
    }
    echo "\n";
    
    if (empty($orderProcesses)) {
        echo "❌ 未找到订单工序信息\n";
        exit;
    }
    
    // 2. 查找当前工序在工序列表中的位置
    $currentStepIndex = -1;
    $currentStep = null;
    $maxStepNo = 0;
    
    foreach ($orderProcesses as $index => $orderProcess) {
        echo "处理工序索引{$index}: step_no={$orderProcess['step_no']}\n";
        
        // 更新最大工序号
        if ($orderProcess['step_no'] > $maxStepNo) {
            $maxStepNo = $orderProcess['step_no'];
            echo "  更新最大工序号为: {$maxStepNo}\n";
        }
        
        // 查找当前报工的工序
        if ($orderProcess['step_no'] == $data['step_id']) {
            $currentStepIndex = $index;
            $currentStep = $orderProcess;
            echo "  找到当前工序: 索引={$currentStepIndex}, step_no={$orderProcess['step_no']}\n";
        }
    }
    
    echo "\n分析结果：\n";
    echo "  最大工序号: {$maxStepNo}\n";
    echo "  当前工序索引: {$currentStepIndex}\n";
    
    if ($currentStepIndex === -1) {
        echo "❌ 未找到当前工序信息\n";
        exit;
    }
    
    echo "  当前工序step_no: {$currentStep['step_no']}\n";
    echo "  当前工序名称: {$currentStep['process_name']}\n";
    
    // 3. 判断是否为最后工序
    $data['is_last_process'] = 0;
    echo "\n判断逻辑：\n";
    echo "  初始化 is_last_process = 0\n";
    echo "  比较: {$currentStep['step_no']} == {$maxStepNo} ?\n";
    
    if ($currentStep['step_no'] == $maxStepNo) {
        $data['is_last_process'] = 1;
        echo "  ✅ 条件成立，设置 is_last_process = 1\n";
    } else {
        echo "  ❌ 条件不成立，保持 is_last_process = 0\n";
    }
    
    echo "\n最终结果：\n";
    echo "  is_last_process = {$data['is_last_process']}\n";
    
    // 4. 模拟更新completed_qty
    if ($data['is_last_process'] == 1) {
        echo "\n🎯 这是最后工序，应该更新completed_qty\n";
        
        $order = Db::name('produce_order')->where('id', $data['order_id'])->find();
        echo "  当前completed_qty: {$order['completed_qty']}\n";
        echo "  合格数量: {$data['qualified_qty']}\n";
        echo "  预期completed_qty: " . ($order['completed_qty'] + $data['qualified_qty']) . "\n";
        
        // 实际更新（取消注释来执行）
        /*
        $affected = Db::name('produce_order')
            ->where('id', $data['order_id'])
            ->setInc('completed_qty', $data['qualified_qty']);
        echo "  更新结果: 影响行数 {$affected}\n";
        
        $updatedOrder = Db::name('produce_order')->where('id', $data['order_id'])->find();
        echo "  更新后completed_qty: {$updatedOrder['completed_qty']}\n";
        */
        
    } else {
        echo "\nℹ️ 这不是最后工序，不会更新completed_qty\n";
    }
    
    echo "\n✅ 调试完成\n";
    
} catch (\Exception $e) {
    echo "❌ 调试失败: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
