{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部工序</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="group_id">
                <option value="">选择工作组</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <select name="pricing_method">
                <option value="">计价方式</option>
                <option value="1">按件计价</option>
                <option value="2">按时计价</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，工序编号/工序名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_process" lay-filter="table_process"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 添加工序</span>
    </button>
  </div>
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        // 加载工作组选项
        tool.get("/Produce/process/getGroupList", {}, function(res) {
            if (res.code == 0) {
                var options = '<option value="">选择工作组</option>';
                $.each(res.data, function(i, item) {
                    options += '<option value="' + item.id + '">' + item.name + '</option>';
                });
                $('[name="group_id"]').html(options);
                layui.form.render('select');
            }
        });
        
        layui.pageTable = table.render({
            elem: "#table_process"
            ,title: "工序管理列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/Produce/process/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'code',
                    title: '工序编号',
                    align: 'center',
                    width: 120,
                    templet: function(d) {
                        return '<span class="layui-badge layui-bg-gray">' + (d.code || '自动编号') + '</span>';
                    }
                },{
                    field: 'name',
                    title: '工序名称',
                    minWidth: 150,
                    templet: '<div><a data-href="/Produce/process/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
                },{
                    field: 'group_name',
                    title: '工作组',
                    align: 'center',
                    width: 120
                },{
                    field: 'report_user',
                    title: '报工用户',
                    align: 'center',
                    width: 120
                },{
                    field: 'standard_price',
                    title: '标准单价',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return '¥' + parseFloat(d.standard_price || 0).toFixed(2);
                    }
                },{
                    field: 'efficiency',
                    title: '标准效率',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return parseFloat(d.efficiency || 0).toFixed(2) + '/小时';
                    }
                },{
                    field: 'pricing_method_name',
                    title: '计价方式',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        var className = d.pricing_method == 1 ? 'layui-bg-blue' : 'layui-bg-orange';
                        return '<span class="layui-badge ' + className + '">' + d.pricing_method_name + '</span>';
                    }
                },{
                    field: 'quantity_ratio',
                    title: '数量单比',
                    align: 'center',
                    width: 100
                },{
                    field: 'create_time',
                    title: '创建时间',
                    align: 'center',
                    width: 160
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 180,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
                        var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
                        return html+btn0+btn1+btn2+'</div>';
                    }                        
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_process)', function(obj){
            if (obj.event === 'add'){
                tool.side("/Produce/process/add");
                return;
            }
        });    
            
        table.on('tool(table_process)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/Produce/process/view?id="+data.id);
                return;
            }
            if (obj.event === 'edit') {
                tool.side("/Produce/process/add?id="+data.id);
                return;
            }
            if (obj.event === 'del') {
                layer.confirm('确定要删除该工序吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.del();
                        }
                    }
                    tool.post("/Produce/process/delete", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
        });
    }
</script>
{/block}