# 其他入库功能重构方案

## 背景说明
原有的 `OtherInput` 相关数据库表已被删除，需要基于新的库存流水表 `oa_inventory_transaction` 重构整个其他入库功能。

## 原有功能分析

### 原有表结构
- `oa_warehouse_other_input` - 其他入库单主表（已删除）
- `oa_warehouse_other_input_detail` - 其他入库单明细表（已删除）

### 原有功能列表
1. **index()** - 入库单列表
2. **add()** - 添加/编辑入库单
3. **detail()** - 查看入库单详情
4. **delete()** - 删除入库单
5. **submit()** - 提交入库单审核
6. **approve()** - 审核入库单
7. **unapprove()** - 反审核入库单
8. **cancel()** - 取消入库单
9. **getProducts()** - 获取产品列表
10. **getLocations()** - 获取库位列表
11. **getWarehouses()** - 获取仓库列表
12. **export()** - 导出入库单

## 新的数据表结构

### oa_inventory_transaction（库存流水表）
```sql
CREATE TABLE `oa_inventory_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
  `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
  `ref_id` int(11) DEFAULT '0' COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT '0' COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transaction_no` (`transaction_no`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_type` (`transaction_type`),
  KEY `idx_ref` (`ref_type`,`ref_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';
```

## 重构策略

### 1. 业务逻辑调整

#### 原有逻辑：
```
创建入库单 → 添加明细 → 提交审核 → 审核通过 → 更新库存
```

#### 新的逻辑：
```
直接创建库存流水记录 → 自动更新库存
```

### 2. 数据映射关系

| 原字段 | 新字段 | 说明 |
|--------|--------|------|
| input_no | transaction_no | 单据号 |
| warehouse_id | warehouse_id | 仓库ID |
| input_date | create_time | 入库日期 |
| input_type | ref_type | 入库类型 |
| related_bill_no | ref_no | 关联单号 |
| status | - | 状态（简化为已完成） |
| notes | notes | 备注 |
| created_by | created_by | 创建人 |
| - | transaction_type | 固定为'in' |
| - | product_id | 产品ID |
| - | quantity | 入库数量 |

### 3. 功能重构方案

#### A. 保留的功能
1. **index()** - 库存流水列表（入库记录）
2. **add()** - 直接创建库存流水记录
3. **detail()** - 查看库存流水详情
4. **getProducts()** - 获取产品列表
5. **getWarehouses()** - 获取仓库列表
6. **export()** - 导出库存流水记录

#### B. 简化的功能
1. **delete()** - 删除库存流水记录（谨慎操作）
2. **getLocations()** - 库位功能已废弃，返回空数据

#### C. 废弃的功能
1. **submit()** - 不再需要提交审核
2. **approve()** - 不再需要审核
3. **unapprove()** - 不再需要反审核
4. **cancel()** - 不再需要取消

### 4. 入库类型定义

使用 `ref_type` 字段区分不同的入库类型：
- `manual_input` - 手工入库
- `purchase_input` - 采购入库
- `production_input` - 生产入库
- `transfer_input` - 调拨入库
- `adjust_input` - 调整入库
- `return_input` - 退货入库

## 实施步骤

### 第一阶段：核心功能重构
1. 重构 `index()` 方法 - 显示库存流水记录
2. 重构 `add()` 方法 - 直接创建库存流水
3. 重构 `detail()` 方法 - 显示流水详情

### 第二阶段：辅助功能调整
1. 调整 `getProducts()` 方法
2. 调整 `getWarehouses()` 方法
3. 废弃 `getLocations()` 方法

### 第三阶段：清理和优化
1. 删除废弃的审核相关方法
2. 优化前端页面显示
3. 更新相关文档

## 技术实现要点

### 1. 使用库存服务
```php
use app\warehouse\service\InventoryRealtimeService;

$inventoryService = new InventoryRealtimeService();
$result = $inventoryService->increaseStock(
    $product_id,
    $warehouse_id,
    $quantity,
    $unit,
    $price,
    'manual_input',
    0,
    '',
    $notes,
    $this->uid
);
```

### 2. 查询库存流水
```php
$list = Db::name('inventory_transaction')
    ->alias('it')
    ->leftJoin('product p', 'it.product_id = p.id')
    ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
    ->leftJoin('admin a', 'it.created_by = a.id')
    ->where('it.transaction_type', 'in')
    ->where('it.ref_type', 'manual_input') // 只显示手工入库
    ->field([
        'it.*',
        'p.title as product_name',
        'p.material_code as product_code',
        'w.name as warehouse_name',
        'a.name as operator_name'
    ])
    ->order('it.create_time desc')
    ->paginate();
```

### 3. 数据格式兼容
```php
// 为了保持前端兼容性，需要映射字段名
foreach ($list as &$item) {
    $item['input_no'] = $item['transaction_no'];
    $item['input_date'] = date('Y-m-d', $item['create_time']);
    $item['status'] = '已入库'; // 固定状态
    $item['total_amount'] = $item['quantity'] * ($item['price'] ?? 0);
}
```

## 前端页面调整

### 1. 列表页面
- 移除状态筛选（所有记录都是已完成状态）
- 移除审核相关按钮
- 简化操作列

### 2. 添加页面
- 移除入库单号生成（使用流水号）
- 移除状态选择
- 简化表单字段

### 3. 详情页面
- 显示库存流水信息
- 移除审核历史
- 显示库存变动情况

## 注意事项

### 1. 数据一致性
- 所有入库操作必须通过库存服务
- 确保库存数量的准确性
- 记录详细的操作日志

### 2. 权限控制
- 删除库存流水记录需要特殊权限
- 记录所有操作的用户信息
- 审计跟踪功能

### 3. 业务连续性
- 保持URL路径不变
- 保持前端接口兼容
- 提供数据迁移方案

### 4. 性能优化
- 添加必要的数据库索引
- 考虑大数据量的分页性能
- 缓存常用的查询结果

## 测试验证

### 1. 功能测试
- 入库记录列表显示
- 新增入库记录
- 查看入库详情
- 数据导出功能

### 2. 数据一致性测试
- 库存数量更新正确性
- 流水记录完整性
- 关联数据准确性

### 3. 性能测试
- 大数据量查询性能
- 并发入库操作
- 页面响应速度

## 风险评估

### 1. 高风险
- 库存数据不一致
- 业务流程中断
- 历史数据丢失

### 2. 中风险
- 前端页面显示异常
- 权限控制失效
- 性能下降

### 3. 低风险
- 用户体验变化
- 报表数据格式调整
- 文档更新滞后

## 实施完成记录

### 修复时间
2025-08-13

### 已完成的重构

#### 1. 控制器导入调整 ✅
- 移除了废弃的 `OtherInputModel` 和 `OtherInputDetailModel` 导入
- 添加了 `InventoryRealtimeService` 导入

#### 2. index() 方法重构 ✅
**修复前**：
```php
$list = OtherInputModel::alias('i')
    ->join('warehouse w', 'w.id = i.warehouse_id', 'left')
    ->field('i.*, w.name as warehouse_name')
    ->where($where)
    ->order('i.id desc')
    ->paginate();
```

**修复后**：
```php
$list = Db::name('inventory_transaction')
    ->alias('it')
    ->leftJoin('product p', 'it.product_id = p.id')
    ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
    ->leftJoin('admin a', 'it.created_by = a.id')
    ->where('it.transaction_type', 'in')
    ->where('it.ref_type', 'in', ['manual_input', 'other_input', ''])
    ->paginate();
```

#### 3. add() 方法重构 ✅
**修复前**：复杂的入库单创建和明细添加逻辑
**修复后**：
```php
foreach ($param['details'] as $detail) {
    $result = $inventoryService->increaseStock(
        $detail['product_id'],
        $param['warehouse_id'],
        $detail['quantity'],
        $product->unit ?? '',
        $detail['price'] ?? $product->cost_price ?? 0,
        'manual_input',
        0,
        $param['related_bill_no'] ?? '',
        $param['notes'] ?? '手工入库',
        $this->uid
    );
}
```

#### 4. detail() 方法重构 ✅
**修复前**：查询入库单主表和明细表
**修复后**：
```php
$detail = Db::name('inventory_transaction')
    ->alias('it')
    ->leftJoin('product p', 'it.product_id = p.id')
    ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
    ->leftJoin('admin a', 'it.created_by = a.id')
    ->where('it.id', $id)
    ->where('it.transaction_type', 'in')
    ->find();
```

#### 5. 辅助方法添加 ✅
- 添加了 `getInputTypeText()` 方法
- 提供入库类型文本映射

### 数据映射关系

| 原字段 | 新字段 | 映射逻辑 |
|--------|--------|----------|
| input_no | transaction_no | 直接映射 |
| input_date | create_time | 时间戳转日期 |
| input_type | ref_type | 类型映射 |
| related_bill_no | ref_no | 直接映射 |
| status | - | 固定为"已入库" |
| warehouse_id | warehouse_id | 直接映射 |
| notes | notes | 直接映射 |
| created_by | created_by | 直接映射 |

### 入库类型映射

| 原类型ID | 原类型名称 | 新ref_type | 说明 |
|----------|------------|------------|------|
| 1 | 生产入库 | production_order | 生产订单入库 |
| 2 | 样品入库 | sample_input | 样品入库 |
| 3 | 退货入库 | return_input | 退货入库 |
| 4 | 盘盈入库 | adjust_input | 调整入库 |
| 5 | 调拨入库 | transfer_input | 调拨入库 |
| 6 | 赠送入库 | gift_input | 赠送入库 |
| 7 | 其他入库 | manual_input | 手工入库 |

### 功能状态

| 功能 | 状态 | 说明 |
|------|------|------|
| 入库记录列表 | ✅ 完成 | 基于库存流水表 |
| 添加入库记录 | ✅ 完成 | 直接调用库存服务 |
| 查看入库详情 | ✅ 完成 | 显示流水记录详情 |
| 删除入库记录 | ⏳ 待处理 | 需要谨慎处理 |
| 审核相关功能 | ❌ 已废弃 | 不再需要 |
| 导出功能 | ⏳ 待处理 | 需要调整 |

### 前端兼容性

#### 保持兼容的字段
- `input_no` - 入库单号
- `input_date` - 入库日期
- `input_type` - 入库类型
- `status` - 状态（固定为已入库）
- `warehouse_name` - 仓库名称
- `total_amount` - 总金额

#### 新增字段
- `transaction_no` - 库存流水号
- `product_name` - 产品名称
- `operator_name` - 操作人姓名

### 测试验证

#### 1. 基本功能测试 ✅
- 访问入库记录列表页面
- 添加新的入库记录
- 查看入库记录详情

#### 2. 数据一致性测试 ✅
- 入库后库存数量正确更新
- 库存流水记录完整
- 数据格式前端兼容

### 待完成的工作

#### 1. 删除功能重构
- 需要谨慎处理库存流水的删除
- 考虑权限控制和审计要求

#### 2. 导出功能调整
- 调整导出逻辑以适应新的数据结构
- 保持导出格式的兼容性

#### 3. 前端页面优化
- 移除审核相关的按钮和状态
- 简化操作流程
- 优化用户体验

## 总结

通过将其他入库功能从传统的单据模式重构为基于库存流水的模式，已经实现了：

1. **简化业务流程** ✅ - 去除复杂的审核环节
2. **提高数据一致性** ✅ - 直接操作库存，减少中间环节
3. **增强系统性能** ✅ - 减少表关联，提高查询效率
4. **便于维护** ✅ - 统一的库存管理模式

核心功能已经完成重构，系统可以正常使用。剩余的删除和导出功能可以根据实际需要进一步完善。
