# 系统最新数据库定义

## ThinkPHP表前缀机制说明

**重要**：本系统使用ThinkPHP框架，配置了表前缀 `oa_`

### 表名映射规则：
- 代码中：`Db::name('warehouse')`
- 实际查询：`oa_warehouse` 表
- 代码中：`Db::name('inventory_transaction')`
- 实际查询：`oa_inventory_transaction` 表

### 正确写法：
```php
// ✅ 正确
Db::name('warehouse')->select();           // 查询 oa_warehouse 表
Db::name('inventory_transaction')->select(); // 查询 oa_inventory_transaction 表

// ❌ 错误
Db::name('oa_warehouse')->select();        // 会查询 oa_oa_warehouse 表（不存在）
```

## 库存管理相关表

### 1. 实时库存表 (oa_inventory_realtime)
**用途**：记录产品在各仓库的实时库存数量
```sql
CREATE TABLE `oa_inventory_realtime` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总库存数量',
  `available_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '可用数量',
  `locked_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '锁定数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_inventory` (`product_id`,`warehouse_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时库存表';
```

**重要说明**：
- **不支持批次管理**：该表没有 `batch_no` 字段，按产品+仓库维度管理库存
- **字段限制**：没有 `receipt_id` 字段，使用 `locked_quantity` 而不是 `allocated_quantity`
- **唯一约束**：`(product_id, warehouse_id)` 组合唯一，一个产品在一个仓库只有一条库存记录

### 2. 库存流水表 (oa_inventory_transaction)
**用途**：记录所有库存变动的流水记录
```sql
CREATE TABLE `oa_inventory_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
  `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
  `ref_id` int(11) DEFAULT 0 COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transaction_no` (`transaction_no`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_ref` (`ref_type`,`ref_id`),
  KEY `idx_type` (`transaction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';
```

### 3. 库存锁定表 (oa_inventory_lock)
**用途**：记录库存锁定信息
```sql
CREATE TABLE `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '锁定数量',
  `ref_type` varchar(50) NOT NULL COMMENT '关联类型(order,production,transfer等)',
  `ref_id` int(11) NOT NULL COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1锁定中,2已使用,3已释放',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_ref` (`ref_type`,`ref_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表';
```

## 采购管理相关表

### 1. 采购订单表 (oa_purchase_order)
**用途**：采购订单主表
```sql
-- 关键字段说明：
check_status: 审核状态，0=待审核，1=待审核，2=已通过，3=已拒绝
status: 订单状态，1=草稿，2=待审核，3=已审核，4=部分入库，5=全部入库，6=已取消
created_by: 创建人ID
approved_by: 审批人ID
approved_time: 审批时间
source_type: 来源类型，'customer_order'=销售订单，null/其他=手动创建
source_order_id: 来源订单ID，关联销售订单ID
```

**重要业务规则**：
- 当 source_type = 'customer_order' 时，不允许在编辑页面添加或删除产品
- 此限制确保销售订单成本核算的准确性和数据一致性

### 2. 采购入库单表 (oa_purchase_receipt)
**用途**：采购入库记录
```sql
-- 关键字段说明：
order_id: 关联的采购订单ID
warehouse_id: 入库仓库ID
status: 入库单状态
```

### 3. 仓库表 (oa_warehouse)
**用途**：仓库基础信息
```sql
-- 关键字段说明：
code: 仓库编码
name: 仓库名称
address: 仓库地址
contact_name: 联系人
contact_phone: 联系电话
type: 仓库类型，1=普通仓库，2=退货仓库，3=虚拟仓库
is_default: 是否默认仓库
status: 状态，0=禁用，1=启用
```

**重要说明**：
- 数据库中的实际表名是 `oa_warehouse`
- 在ThinkPHP代码中使用 `Db::name('warehouse')`，框架会自动添加 `oa_` 前缀
- 不要在代码中写成 `Db::name('oa_warehouse')`，这样会变成 `oa_oa_warehouse`

## 重要说明

### 库存变动记录查询
**正确的表名和字段**：
- 表名：`oa_inventory_transaction` （不是 `inventory_log` 或 `oa_inventory_log`）
- 关联字段：`ref_type` 和 `ref_id`
- 采购入库的 `ref_type` 值：`purchase_receipt`

### 反审核检查逻辑
```php
// 1. 检查入库记录
$receiptCount = Db::name('purchase_receipt')
    ->where('order_id', $order_id)
    ->count();

// 2. 检查库存变动记录
if ($receiptCount > 0) {
    $receiptIds = Db::name('purchase_receipt')
        ->where('order_id', $order_id)
        ->column('id');
    
    $inventoryTransactionCount = Db::name('inventory_transaction')
        ->where('ref_type', 'purchase_receipt')
        ->where('ref_id', 'in', $receiptIds)
        ->count();
}
```

### 常见错误
1. ❌ 使用 `inventory_log` 表名（此表不存在）
2. ❌ 使用 `oa_inventory_log` 表名（此表不存在）
3. ✅ 正确使用 `oa_inventory_transaction` 表名
4. ❌ 在代码中写 `Db::name('oa_warehouse')`（会变成 `oa_oa_warehouse`）
5. ✅ 正确写法 `Db::name('warehouse')`（ThinkPHP自动添加前缀变成 `oa_warehouse`）

### 模型对应关系
- `app\warehouse\model\InventoryRealtime` → `oa_inventory_realtime`
- `app\warehouse\model\InventoryTransaction` → `oa_inventory_transaction`
- `app\warehouse\model\Warehouse` → `oa_warehouse`
- `app\oa\model\InventoryLog` → `inventory_log` (此模型定义有误，实际表不存在)

### 模型表名设置规则
在ThinkPHP模型中，当系统配置了表前缀 `oa_` 时：
```php
// ✅ 正确写法
class Warehouse extends Model {
    protected $table = 'warehouse';  // 实际查询 oa_warehouse 表
}

// ❌ 错误写法
class Warehouse extends Model {
    protected $table = 'oa_warehouse';  // 会查询 oa_oa_warehouse 表（不存在）
}
```

### ThinkPHP方法重写兼容性
当重写ThinkPHP模型方法时，必须保持方法签名兼容：
```php
// ✅ 正确的save方法重写
public function save(object|array $data = [], ?string $sequence = null): bool
{
    // 自定义逻辑
    return parent::save($data, $sequence);
}

// ❌ 错误的方法签名（旧版本格式）
public function save($data = [], $where = [], $sequence = null)
{
    return parent::save($data, $where, $sequence);
}
```

## 更新记录
- 2025-08-05: 修复采购订单反审核功能中的库存变动检查逻辑
- 2025-08-05: 确认实际库存表名为 `oa_inventory_transaction`
- 2025-08-05: 添加采购订单编辑限制功能，销售订单来源的采购订单不允许添加/删除产品
- 2025-08-05: 理解ThinkPHP表前缀机制，代码中使用 `Db::name('warehouse')` 自动映射到 `oa_warehouse` 表
- 2025-08-05: 修复Warehouse模型表名设置，使用 `$name` 属性而不是 `$table` 属性
- 2025-08-05: 修复Inventory模型save方法签名兼容性问题
- 2025-08-05: 修复Inventory模型双重前缀问题，改为继承InventoryRealtime模型
- 2025-08-05: 修复Inventory模型where方法数组参数处理错误
- 2025-08-05: 修复仓库入库单中不存在的字段引用（batch_no, receipt_id, allocated_quantity）
- 2025-08-05: 修复库存流水记录方法，使用正确的inventory_transaction表
- 2025-08-05: 完全简化Inventory模型，移除所有字段映射和修改器，直接继承InventoryRealtime
- 2025-08-05: 修复str_pad函数类型错误，将mt_rand返回值转换为字符串
- 2025-08-05: 完善入库单反审核功能，添加出库检查、锁定释放和分配需求恢复
- 2025-08-05: 设计完整的出库系统，包括8个核心数据表和业务流程
- 2025-08-05: 重大修复：入库反审核逻辑，正确处理库存扣减、锁定释放和分配需求恢复
- 2025-08-05: 移除质检状态限制：所有入库产品在反审核时都扣减库存，不再仅限于质检合格产品
- 2025-08-05: 简化Inventory模型，移除有问题的save方法重写，直接继承父类功能
- 2025-08-05: 修复Warehouse模型表名设置错误，避免双重前缀问题

## 出库系统设计说明

### 核心表结构
1. **oa_outbound** - 出库单主表
2. **oa_outbound_detail** - 出库单明细表
3. **oa_outbound_picking_task** - 拣货任务表
4. **oa_outbound_picking_detail** - 拣货明细表
5. **oa_outbound_package** - 包装表
6. **oa_outbound_package_detail** - 包装明细表
7. **oa_outbound_shipment** - 发货表
8. **oa_outbound_approval_log** - 审核日志表

### 业务流程
```
创建出库单 → 审核通过 → 生成拣货任务 → 拣货作业 →
包装作业 → 发货作业 → 出库完成
```

### 与入库系统对应
- 出库系统基于入库系统设计，保持数据结构和业务流程的一致性
- 支持反审核检查：入库单反审核时会检查是否有后续出库记录
- 库存集成：出库时自动扣减库存，支持锁定和分配机制