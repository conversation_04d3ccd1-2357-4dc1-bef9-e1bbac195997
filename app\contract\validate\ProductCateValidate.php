<?php


namespace app\contract\validate;

use think\Validate;

class ProductCateValidate extends Validate
{
    protected $rule = [
        'title' => 'require|unique:product_cate',
        'id' => 'require',
    ];

    protected $message = [
        'title.require' => '名称不能为空',
        'title.unique' => '同样的名称已经存在',
        'id.require' => '缺少更新条件',
    ];

    protected $scene = [
        'add' => ['title'],
        'edit' => ['id', 'title'],
    ];
}
