{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-3">
    <div class="layui-card">
		<div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
			<ul class="layui-tab-title" style="border-bottom:none;">
				<li class="layui-this" data-index="1">车辆费用类型</li>
				<li data-index="2">其他</li>
			</ul>
			<div class="layui-tab-content" style="padding:0">
				<table class="layui-hide" id="test" lay-filter="test"></table>
			</div>
		</div> 
	</div> 
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm add-new" type="button">+ 新增内容</button>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var table = layui.table, tool = layui.tool, element = layui.element,form = layui.form;
		var data_type=1;
		element.on('tab(tab)', function(data){
			data_type = data.index+1;
			layui.pageTable.reload({where:{types:data_type},page:{curr:1}});
			return false;
		});
		
		layui.pageTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,title:'基础数据列表'
			,url: "/adm/basic/datalist"
			,where:{types:data_type}
			,page: false
			,cellMinWidth: 80
			,cols: [[
					{field:'id',width:80, title: 'ID号', align:'center'}
					,{field:'title',title: '名称'}
					,{field:'status', title: '状态',width:80,align:'center',templet: function(d){
						var html1='<span class="green">正常</span>';
						var html2='<span class="yellow">禁用</span>';
						if(d.status==1){
							return html1;
						}
						else{
							return html2;
						}
					}}
					,{width:100,title: '操作', align:'center',templet: function(d){
						var html='';
						var btn='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">编辑</a>';
						var btn1='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>';
						var btn2='<a class="layui-btn layui-btn-xs" lay-event="open">启用</a>';
						if(d.status==1){
							html = '<div class="layui-btn-group">'+btn+btn1+'</div>';
						}
						else{
							html = '<div class="layui-btn-group">'+btn+btn2+'</div>';
						}
						return html;
					}}
				]]
			});
			
			table.on('tool(test)',function (obj) {
				if(obj.event === 'edit'){					
					addExpense(obj.data.id,obj.data.title);
				}
				if(obj.event === 'disable'){
					layer.confirm('确定要禁用该数据吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/adm/basic/set", { id: obj.data.id,status: 0,title: obj.data.title}, callback);
						layer.close(index);
					});
				}
				if(obj.event === 'open'){
					layer.confirm('确定要启用该数据吗?', {icon: 3, title:'提示'}, function(index){
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layui.pageTable.reload();
							}
						}
						tool.post("/adm/basic/set", { id: obj.data.id,status: 1,title: obj.data.title}, callback);
						layer.close(index);
					});
				}
			});
			
			$('body').on('click','.add-new',function(){
				addExpense(0,'');	
			});
			
			function addExpense(id,val){
				var title = '新增内容';
				if(id>0){
					title = '编辑内容';
				}
				layer.prompt({
					title: title,
					value: val,
					yes: function(index, layero) {
						// 获取文本框输入的值
						var value = layero.find(".layui-layer-input").val();
						if (value) {
							let callback = function (e) {
								layer.msg(e.msg);
								if (e.code == 0) {
									layui.pageTable.reload();				
								}
							}
							tool.post("/adm/basic/add", {id: id,types:data_type,title: value}, callback);
							layer.close(index);
						} else {
							layer.msg('请填写内容');
						}
					}
				})
			}
		}
	</script>
{/block}
<!-- /脚本 -->