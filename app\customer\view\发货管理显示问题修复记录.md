# 发货管理显示问题修复记录

## 当前问题

1. **主品/子品区分不明显** - 需要更强的视觉对比
2. **每套/件列显示错误** - 显示的是数量而不是单位
3. **序号显示问题** - 序号没有正确显示

## 重要说明

⚠️ **数据重复是正确的，不要处理重复数据** - 用户明确指出数据的重复性是业务需求，不应该去重或重新组织数据结构。

## 修复方案

### 参考订单查看页面的实现方式

参考 `app/customer/view/order/view.html` 的显示效果：
- 序号只在主品行显示
- 主品显示蓝色"主品"徽章和绿色子品数量徽章
- 子品显示橙色"子品"徽章，并有缩进和连接线
- 每套/件列：子品显示比例数值，主品显示"-"

### 1. 简化模板实现 ✅

#### 产品名称模板
```html
<script type="text/html" id="productNameTpl">
    {{# if(d.parent_product_id && d.parent_product_id > 0) { }}
        <!-- 子品显示 -->
        <div style="padding-left: 20px;">
            <span style="background-color: #FF9800; color: white; padding: 2px 6px; border-radius: 10px; font-size: 11px; margin-right: 8px;">子品</span>
            <span>{{ d.product_name || d.title }}</span>
            <span style="color: #666; margin-left: 8px;">{{ d.material_code }}</span>
        </div>
    {{# } else { }}
        <!-- 主品显示 -->
        <div>
            <span style="background-color: #1E9FFF; color: white; padding: 2px 6px; border-radius: 10px; font-size: 11px; margin-right: 8px;">主品</span>
            {{# if(d.product_number && d.product_number > 0) { }}
            <span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 8px;">{{ d.product_number }}</span>
            {{# } }}
            <span>{{ d.product_name || d.title }}</span>
        </div>
    {{# } }}
</script>
```

#### 单位显示模板
```html
<script type="text/html" id="unitTpl">
    <div style="text-align: center;">
        {{# if(d.parent_product_id && d.parent_product_id > 0) { }}
            <!-- 子品显示单位 -->
            {{ d.unit || '个' }}
        {{# } else { }}
            <!-- 主品不显示单位，显示横线 -->
            -
        {{# } }}
    </div>
</script>
```

#### 序号显示模板
```html
<script type="text/html" id="sequenceTpl">
    <div style="text-align: center;">
        {{# if(!d.parent_product_id || d.parent_product_id == 0) { }}
            <span style="font-weight: bold; color: #1E9FFF;">{{ d.sequence_number || '' }}</span>
        {{# } else { }}
            
        {{# } }}
    </div>
</script>
```

### 2. 增强表格行样式 ✅

```css
/* 表格行样式 */
.layui-table tbody tr.main-product-row {
    background-color: #f0f8ff !important;
    border-left: 4px solid #1E9FFF !important;
}

.layui-table tbody tr.sub-product-row {
    background-color: #fffaf0 !important;
    border-left: 4px solid #FF9800 !important;
}

.layui-table tbody tr.main-product-row td {
    border-bottom: 1px solid #e6e6e6 !important;
    font-weight: 500 !important;
}

.layui-table tbody tr.sub-product-row td {
    border-bottom: 1px solid #f0f0f0 !important;
}
```

### 3. 添加调试输出 ✅

```javascript
// 检查每个商品的parent_product_id
items.forEach(function(item, index) {
    console.log('商品' + index + ':', {
        id: item.id,
        product_name: item.product_name,
        parent_product_id: item.parent_product_id,
        unit: item.unit,
        sequence_number: item.sequence_number
    });
});
```

## 预期效果

### 主品显示
```
1  1-0659B.0102.001  [主品] [2] 659下壳                1.00  -      1.00  1.00
```

### 子品显示
```
   3-0659B10            [子品] 659下壳 ZC-659         1.00  个     0     1.00
   1-0201.010           [子品] M3*8圆头尖尾           1.00  个     0     1.00
```

## 测试检查点

### 1. 数据结构检查
- [ ] `parent_product_id` 字段是否正确
- [ ] `sequence_number` 字段是否生成
- [ ] `product_number` 字段是否计算正确
- [ ] `unit` 字段是否存在

### 2. 模板渲染检查
- [ ] 主品是否显示蓝色"主品"徽章
- [ ] 子品是否显示橙色"子品"徽章
- [ ] 绿色数字徽章是否显示子品数量
- [ ] 子品是否有缩进显示

### 3. 样式应用检查
- [ ] 主品行是否有蓝色左边框和浅蓝背景
- [ ] 子品行是否有橙色左边框和浅橙背景
- [ ] 序号是否只在主品行显示
- [ ] 单位列是否显示正确的单位而不是数量

### 4. 功能验证
- [ ] 表格是否正常渲染
- [ ] 数据是否正确加载
- [ ] 编辑功能是否正常
- [ ] 样式是否在不同浏览器中一致

## 调试步骤

### 1. 检查浏览器控制台
```javascript
// 查看数据结构
console.log('订单商品数据:', items);

// 查看每个商品的关键字段
items.forEach(function(item, index) {
    console.log('商品' + index + ':', {
        id: item.id,
        product_name: item.product_name,
        parent_product_id: item.parent_product_id,
        unit: item.unit,
        sequence_number: item.sequence_number
    });
});
```

### 2. 检查模板渲染
- 在浏览器开发者工具中查看生成的HTML
- 确认模板中的条件判断是否正确执行
- 检查CSS样式是否正确应用

### 3. 检查后端数据
- 确认 `getOrderItems` 接口返回的数据结构
- 验证 `parent_product_id` 字段的值
- 检查产品分组和序号生成逻辑

## 可能的问题

### 1. 数据问题
- `parent_product_id` 字段可能为null或undefined
- 产品分组逻辑可能有误
- 序号生成可能不正确

### 2. 模板问题
- Layui模板语法可能有误
- 条件判断可能不正确
- 字段名称可能不匹配

### 3. 样式问题
- CSS选择器可能不正确
- 样式优先级可能不够
- 浏览器兼容性问题

## 下一步行动

1. **检查控制台输出** - 确认数据结构是否正确
2. **验证模板渲染** - 确认模板是否正确执行
3. **调试样式应用** - 确认CSS样式是否生效
4. **测试功能完整性** - 确认所有功能正常工作

**当前状态**: 🔄 调试中
**预期完成**: ⏳ 待测试验证
