# 生产领料出库状态同步修复

## 🔍 问题描述

执行生产领料单的出库后，生产单的领料状态没有更新，仍然显示"未投料"，生产领料模块显示也不正确。

## 🎯 问题原因

出库单执行完成后，缺少回调机制来更新关联的生产订单状态。具体表现为：

1. **出库单执行成功**：库存正确扣减，出库单状态更新为"已出库"
2. **生产单状态未更新**：`produce_order`表的`material_status`字段没有更新
3. **业务流程断裂**：出库操作与生产管理模块之间缺少状态同步

## 🛠️ 解决方案

### 1. 修改出库执行逻辑

在 `app\warehouse\controller\Outbound.php` 的 `doExecute()` 方法中添加关联业务状态更新：

```php
// 更新出库单状态为全部出库
Db::name('outbound')
    ->where('id', $outboundId)
    ->update([
        'status' => 4, // 全部出库
        'outbound_by' => $this->uid,
        'outbound_time' => time(),
        'update_time' => time()
    ]);

// 处理关联业务单据状态更新
$this->updateRelatedBusinessStatus($outbound);
```

### 2. 添加关联业务状态更新方法

```php
/**
 * 更新关联业务单据状态
 * @param object $outbound 出库单对象
 */
private function updateRelatedBusinessStatus($outbound)
{
    try {
        // 处理生产领料单出库
        if ($outbound->related_bill_type == 'production_material_request') {
            $this->updateProductionOrderMaterialStatus($outbound);
        }
        
        // 可以在这里添加其他业务类型的处理逻辑
        
    } catch (\Exception $e) {
        \think\facade\Log::error('更新关联业务单据状态失败', [
            'outbound_id' => $outbound->id,
            'related_bill_type' => $outbound->related_bill_type,
            'error' => $e->getMessage()
        ]);
    }
}
```

### 3. 添加生产订单物料状态更新逻辑

```php
/**
 * 更新生产订单物料状态
 * @param object $outbound 出库单对象
 */
private function updateProductionOrderMaterialStatus($outbound)
{
    try {
        // 1. 通过领料单号查找生产订单ID
        $materialRequest = Db::name('production_material_request')
            ->where('request_no', $outbound->related_bill_no)
            ->find();
        
        if (!$materialRequest) {
            return;
        }
        
        $productionOrderId = $materialRequest['production_order_id'];
        
        // 2. 检查该生产订单的所有领料单是否都已出库完成
        $allMaterialRequests = Db::name('production_material_request')
            ->where('production_order_id', $productionOrderId)
            ->select()
            ->toArray();
        
        $allCompleted = true;
        foreach ($allMaterialRequests as $request) {
            // 检查该领料单对应的出库单是否都已完成
            $outboundCount = Db::name('outbound')
                ->where('related_bill_type', 'production_material_request')
                ->where('related_bill_no', $request['request_no'])
                ->where('status', 4) // 已出库
                ->count();
            
            $totalOutboundCount = Db::name('outbound')
                ->where('related_bill_type', 'production_material_request')
                ->where('related_bill_no', $request['request_no'])
                ->count();
            
            if ($outboundCount < $totalOutboundCount) {
                $allCompleted = false;
                break;
            }
        }
        
        // 3. 更新生产订单的领料状态
        $materialStatus = $allCompleted ? 2 : 1; // 2=已投料, 1=部分投料
        
        Db::name('produce_order')
            ->where('id', $productionOrderId)
            ->update([
                'material_status' => $materialStatus,
                'update_time' => time()
            ]);
        
    } catch (\Exception $e) {
        \think\facade\Log::error('更新生产订单物料状态失败', [
            'outbound_id' => $outbound->id,
            'error' => $e->getMessage()
        ]);
    }
}
```

## 📋 业务逻辑说明

### 状态更新规则

1. **部分投料** (`material_status = 1`)：
   - 生产订单有多个领料单
   - 部分领料单已完成出库，部分未完成

2. **已投料** (`material_status = 2`)：
   - 生产订单的所有领料单都已完成出库
   - 所有物料都已发放到生产线

### 数据流转

```
生产领料单创建 → 出库单生成 → 出库执行 → 更新生产订单状态
     ↓              ↓           ↓            ↓
  领料申请      → 库存锁定   → 库存扣减   → 状态同步
```

## 🔧 修复效果

### 修复前
- ✅ 出库单执行成功
- ❌ 生产单状态仍为"未投料"
- ❌ 生产领料模块显示不正确

### 修复后
- ✅ 出库单执行成功
- ✅ 生产单状态自动更新为"部分投料"或"已投料"
- ✅ 生产领料模块显示正确的状态

## 📝 注意事项

1. **事务完整性**：状态更新在出库事务中完成，确保数据一致性
2. **错误处理**：状态更新失败不影响出库操作，只记录日志
3. **扩展性**：可以轻松添加其他业务类型的状态同步逻辑
4. **日志记录**：详细记录状态更新过程，便于问题排查

## 🎯 测试验证

1. 创建生产订单和领料单
2. 执行领料单出库
3. 检查生产订单状态是否正确更新
4. 验证生产领料模块显示是否正确

修复完成后，生产领料出库与生产订单状态将保持同步。

## 🔧 实际出库数量回写修复

### 问题补充
除了生产订单状态不更新外，还发现生产领料单详情页面显示的"实际出库数量"为0，这是因为出库执行时只更新了`outbound_detail`表，没有回写到`production_material_request_detail`表。

### 解决方案
在出库执行成功后，添加回写逻辑：

```php
// 回写实际出库数量到关联业务单据
$this->updateRelatedBusinessActualQuantity($outbound, $details);
```

### 回写逻辑实现
```php
/**
 * 更新生产领料单明细的实际出库数量
 */
private function updateMaterialRequestActualQuantity($outbound, $details)
{
    foreach ($details as $detail) {
        // 根据产品ID和出库数量更新领料单明细的实际出库数量
        Db::name('production_material_request_detail')
            ->where('request_no', $outbound->related_bill_no)
            ->where('material_id', $detail['product_id'])
            ->update([
                'actual_quantity' => Db::raw('actual_quantity + ' . floatval($detail['actual_quantity'])),
                'update_time' => time()
            ]);
    }
}
```

### 修复效果
- ✅ 出库执行成功后，自动回写实际出库数量
- ✅ 生产领料单详情页面正确显示实际出库数量
- ✅ 支持多次部分出库的累计统计
- ✅ 数据完整性和一致性得到保障

### 完整的数据流转
```
生产领料单创建 → 出库单生成 → 出库执行 → 回写实际数量 → 更新生产订单状态
     ↓              ↓           ↓            ↓              ↓
  领料申请      → 库存锁定   → 库存扣减   → 明细更新     → 状态同步
```

现在生产领料出库的完整业务流程已经修复，包括：
1. 库存扣减和锁定释放
2. 实际出库数量回写
3. 生产订单状态同步
4. 数据一致性保障

## 🔧 字段名称修复

### 问题发现
在测试过程中发现生产订单列表页面的领料状态仍然显示不正确，经过排查发现字段名称不一致：

- **OrderModel显示字段**：`feeding_flag`
- **出库同步更新字段**：`material_status`

### 解决方案
修正出库同步逻辑中的字段名称：

```php
// 修改前
Db::name('produce_order')
    ->where('id', $productionOrderId)
    ->update([
        'material_status' => $materialStatus,  // ❌ 错误字段
        'update_time' => time()
    ]);

// 修改后
Db::name('produce_order')
    ->where('id', $productionOrderId)
    ->update([
        'feeding_flag' => $feedingFlag,        // ✅ 正确字段
        'update_time' => time()
    ]);
```

### 历史数据修复
为已经出库但状态没有正确更新的生产订单提供修复脚本：

```bash
# 修复所有生产订单的领料状态
php app/warehouse/修复生产订单领料状态脚本.php

# 检查特定生产订单
php app/warehouse/修复生产订单领料状态脚本.php check 订单ID
```

### 修复脚本功能
1. **自动扫描**：检查所有有领料单的生产订单
2. **状态计算**：根据出库单完成情况重新计算领料状态
3. **批量更新**：自动更新不正确的状态
4. **详细日志**：记录所有修复操作
5. **安全检查**：只更新确实需要修复的记录

### 状态映射关系
```php
$feedingStatusArr = [
    0 => '未投料',      // 没有任何出库单
    1 => '部分投料',    // 有出库单但未全部完成
    2 => '投料完成',    // 所有出库单都已完成
    3 => '补料'         // 补料情况（手动设置）
];
```

## 🎯 最终效果

### 修复前
- ❌ 生产订单领料状态显示"未投料"
- ❌ 出库完成后状态不更新
- ❌ 字段名称不一致导致同步失败

### 修复后
- ✅ 出库执行后自动更新领料状态
- ✅ 历史数据通过脚本批量修复
- ✅ 字段名称统一使用`feeding_flag`
- ✅ 状态显示准确反映实际情况

现在整个生产领料出库流程已经完全修复，包括：
1. 库存扣减和锁定释放
2. 实际出库数量回写
3. 生产订单领料状态同步（使用正确字段）
4. 历史数据修复脚本
5. 数据完整性保障
