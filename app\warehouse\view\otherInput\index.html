{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar border-x border-t" id="barsearchform">
        <div class="layui-form-item layui-row layui-col-space10">
            <div class="layui-col-md2">
                <select name="warehouse_id">
                    <option value="">所有仓库</option>
                    {volist name="warehouses" id="warehouse"}
                    <option value="{$warehouse.id}">{$warehouse.name}</option>
                    {/volist}
                </select>
            </div>
            <div class="layui-col-md2">
                <select name="ref_type">
                    <option value="">所有入库类型</option>
                    <option value="production_order">生产入库</option>
                    <option value="sample_input">样品入库</option>
                    <option value="return_input">退货入库</option>
                    <option value="adjust_input">盘盈入库</option>
                    <option value="transfer_input">调拨入库</option>
                    <option value="gift_input">赠送入库</option>
                    <option value="manual_input">手工入库</option>
                </select>
            </div>
            <div class="layui-col-md2">
                <input type="text" name="start_date" id="start_date" placeholder="开始日期" class="layui-input" />
            </div>
            <div class="layui-col-md2">
                <input type="text" name="end_date" id="end_date" placeholder="结束日期" class="layui-input" />
            </div>
            <div class="layui-col-md2">
                <input type="text" name="keywords" placeholder="流水号/产品名称/产品编码" class="layui-input" />
            </div>
            <div class="layui-col-md12 text-right">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
                <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
            </div>
        </div>
    </form>
    <table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <div class="layui-btn-group">
        <button class="layui-btn layui-btn-sm add-production" type="button">+ 生产入库</button>
        <button class="layui-btn layui-btn-sm add-sample" type="button">+ 样品入库</button>
        <button class="layui-btn layui-btn-sm add-return" type="button">+ 退货入库</button>
        <button class="layui-btn layui-btn-sm add-inventory" type="button">+ 盘盈入库</button>
        <button class="layui-btn layui-btn-sm add-transfer" type="button">+ 调拨入库</button>
        <button class="layui-btn layui-btn-sm add-gift" type="button">+ 赠送入库</button>
        <button class="layui-btn layui-btn-sm add-other" type="button">+ 其他入库</button>
    </div>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','tablePlus','laydate'];
function gouguInit() {
    var table = layui.tablePlus, tool = layui.tool, form = layui.form, laydate = layui.laydate;
    
    // 初始化日期选择器
    laydate.render({
        elem: '#start_date'
    });
    
    laydate.render({
        elem: '#end_date'
    });
    
    layui.pageTable = table.render({
        elem: '#test'
        ,toolbar: '#toolbarDemo'
        ,title:'入库记录列表'
        ,url: "/warehouse/OtherInput/index"
        ,page: true
        ,cellMinWidth: 80
        ,cols: [[
            {field:'id',width:80, title: 'ID', align:'center'}
            ,{field:'transaction_no', title: '流水号', width:160}
            ,{field:'product_name', title: '产品名称', width:200}
            ,{field:'product_code', title: '产品编码', width:120}
            ,{field:'quantity', title: '入库数量', width:100, align:'right', templet: function(d){
                return (d.quantity || '0') + ' ' + (d.unit || '个');
            }}
            ,{field:'warehouse_name', title: '仓库', width:120}
            ,{field:'input_type_text', title: '入库类型', width:100, align:'center'}
            ,{field:'input_date', title: '入库日期', width:110, align:'center'}
            ,{field:'ref_no', title: '关联单号', width:150}
            ,{field:'operator_name', title: '操作人', width:100, align:'center'}
            ,{field:'create_time', title: '创建时间', width:160, align:'center'}
            ,{field:'status_text', title: '状态', width:80, align:'center', templet: function(d){
                return '<span class="layui-badge layui-bg-green">已入库</span>';
            }}
            ,{width:120, title: '操作', align:'center', templet: function(d){
                var html = '<div class="layui-btn-group">';
                html += '<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</a>';
                // 只有管理员可以删除库存流水记录
                if (window.admin_level && window.admin_level <= 2) {
                    html += '<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>';
                }
                html += '</div>';
                return html;
            }}
        ]]
    });
    
    // 监听工具条事件
    table.on('tool(test)', function(obj) {
        var data = obj.data;
        
        // 查看详情
        if(obj.event === 'view') {
            tool.side("/warehouse/OtherInput/detail?id=" + data.id);
        }

        // 删除库存流水记录（谨慎操作）
        if(obj.event === 'del') {
            layer.confirm('警告：删除库存流水记录是危险操作，会影响库存数据一致性！\n确定要删除该记录吗?', {
                icon: 2,
                title:'危险操作',
                btn: ['确定删除', '取消']
            }, function(index){
                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layui.pageTable.reload();
                    }
                }
                tool.delete("/warehouse/OtherInput/delete", { id: data.id }, callback);
                layer.close(index);
            });
        }
    });
    
    // 监听搜索表单提交
    form.on('submit(table-search)', function(data){
        layui.pageTable.reload({
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });
    
    // 点击添加按钮 - 统一使用手工入库
    $('body').on('click', '.add-production', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=production_order");
    });

    $('body').on('click', '.add-sample', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=sample_input");
    });

    $('body').on('click', '.add-return', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=return_input");
    });

    $('body').on('click', '.add-inventory', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=adjust_input");
    });

    $('body').on('click', '.add-transfer', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=transfer_input");
    });

    $('body').on('click', '.add-gift', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=gift_input");
    });

    $('body').on('click', '.add-other', function(){
        tool.side("/warehouse/OtherInput/add?ref_type=manual_input");
    });
}
</script>
{/block}
