<?php

use think\facade\Route;

// 物料档案管理路由
Route::group('material', function () {
    // 物料档案
    Route::get('archive/index', 'Archive/index');
    Route::any('archive/add', 'Archive/add');
    Route::get('archive/view', 'Archive/view');
    Route::post('archive/delete', 'Archive/delete');
    Route::post('archive/batchDelete', 'Archive/batchDelete');
    Route::get('archive/import', 'Archive/import');
    Route::post('archive/export', 'Archive/export');
    Route::get('archive/getCategoryTree', 'Archive/getCategoryTree');
    Route::get('archive/printBarcode', 'Archive/printBarcode');
    Route::get('archive/printBarcode1D', 'Archive/printBarcode1D');
    Route::get('archive/batchPrintBarcode', 'Archive/batchPrintBarcode');
    Route::get('archive/generateQrCode', 'Archive/generateQrCode');

    // 物料分类
    Route::get('category/index', 'Category/index');
    Route::any('category/add', 'Category/add');
    Route::post('category/delete', 'Category/delete');
    Route::get('category/getTree', 'Category/getTree');
    Route::post('category/updateStatus', 'Category/updateStatus');

    // BOM管理
    Route::get('bom/index', 'Bom/index');
    Route::any('bom/add', 'Bom/add');
    Route::get('bom/view', 'Bom/view');
    Route::post('bom/delete', 'Bom/delete');
    Route::any('bom/selectProduct', 'Bom/selectProduct');
    Route::any('bom/selectCustomer', 'Bom/selectCustomer');
    Route::any('bom/getMaterials', 'Bom/getMaterials');
    Route::get('bom/getSubBom', 'Bom/getSubBom');
});