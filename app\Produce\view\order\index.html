{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
		<div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
			<ul class="layui-tab-title">
				<li class="layui-this">全部</li>
				<li>待排产</li>
				<li>已排产</li>
				<li>生产中</li>
				<li>已完成</li>
				<li>已取消</li>
			</ul>
		</div> 
	</div>
	<form class="layui-form gg-form-bar border-x" id="barsearchform">
		<div class="layui-input-inline" style="width:122px;">
			<select name="priority">
				<option value="">选择优先级</option>
				<option value="1">低</option>
				<option value="2">中</option>
				<option value="3">高</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:175px;">
			<input type="text" class="layui-input" id="delivery_date" placeholder="选择交期时间" readonly name="delivery_date">
		</div>
		<div class="layui-input-inline" style="width:220px;">
			<input type="text" name="keywords" placeholder="输入关键字，订单编号/产品名称" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<input type="hidden" name="tab" value="0" />
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="table_order" lay-filter="table_order"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	<button class="layui-btn layui-btn-sm" lay-event="add">
		<span>+ 添加订单</span>
	</button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus','laydatePlus'];
	function gouguInit() {
		var table = layui.tablePlus,element = layui.element, tool = layui.tool,laydatePlus = layui.laydatePlus;
		
		//tab切换
		element.on('tab(tab)', function(data){
			var statusMap = {0: "", 1: "0", 2: "1", 3: "2", 4: "3", 5: "4"};
			$('[name="tab"]').val(data.index);
			$("#barsearchform")[0].reset();
			layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
			return false;
		});
		
		//日期范围
		var delivery_date = new laydatePlus({'target':'delivery_date'});
		
		layui.pageTable = table.render({
			elem: "#table_order"
			,title: "生产订单列表"
			,toolbar: "#toolbarDemo"
			,url: "/Produce/order/getList"
			,page: true
			,limit: 20
			,cellMinWidth: 80
			,height: 'full-152'
			,cols: [[ //表头
				{
					field: 'id',
					title: 'ID号',
					align: 'center',
					width: 80
				},{ 
					field: 'status', 
					title: '工单状态', 
					align: 'center', 
					width: 110, 
					templet: function (d) {
						var statusClass = {
							0: 'layui-bg-gray', // 待排产
							1: 'layui-bg-orange', // 已排产
							2: 'layui-bg-blue', // 生产中
							3: 'layui-bg-green', // 已完成
							4: 'layui-bg-red' // 已取消
						};
						var html = '<span class="layui-badge ' + statusClass[d.status] + '">'+d.status_name+'</span>';
						return html;
					}
				},{
					field: 'order_no',
					title: '工单编号',
					align: 'center',
					width: 150
				},{
					field: 'priority_name',
					title: '优先级',
					align: 'center',
					width: 80,
					templet: function (d) {
						var priorityClass = {
							1: 'layui-bg-gray', // 低
							2: 'layui-bg-orange', // 中
							3: 'layui-bg-red' // 高
						};
						var html = '<span class="layui-badge ' + priorityClass[d.priority] + '">'+d.priority_name+'</span>';
						return html;
					}
				},{
					field: 'product_name',
					title: '产品名称',
					minWidth:240,
					templet: '<div><a data-href="/Produce/order/view/id/{{d.id}}.html" class="side-a">{{d.product_name}}</a></div>'
				},{
					field: 'quantity',
					title: '工单数量',
					align: 'center',
					width: 80
				},
				{
					field: 'completed_qty',
					title: '完成数量',
					align: 'center',
					width: 80
				},
				{
					field: 'warehousing',
					title: '入库数量',
					align: 'center',
					width: 80
				},{
					field: 'delivery_date_format',
					title: '交期时间',
					align: 'center',
					width: 120
				},{
					field: 'plan_start_date',
					title: '排产开始',
					align: 'center',
					width: 110,
					templet: function (d) {
						return d.plan_start_date_format || '-';
					}
				},{
					field: 'plan_end_date',
					title: '排产结束',
					align: 'center',
					width: 110,
					templet: function (d) {
						return d.plan_end_date_format || '-';
					}
				},{
					field: 'feeding_flag',
					title: '领料状态',
					align: 'center',
					width: 100,
					templet: function (d) {
						var statusClass = {
							0: 'layui-bg-gray',    // 未投料
							1: 'layui-bg-orange',  // 部分投料
							2: 'layui-bg-green',   // 投料完成
							3: 'layui-bg-blue'     // 补料
						};
						var html = '<span class="layui-badge ' + statusClass[d.feeding_flag] + '">' + d.feeding_status_name + '</span>';
						return html;
					}
				},{
					field: 'process_info',
					title: '生产工序数',
					align: 'center',
					width: 100,
					templet: function (d) {
						var completed = d.completed_processes || 0;
						var total = d.total_processes || 0;
						var percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
						var color = percentage === 100 ? '#5FB878' : (percentage > 0 ? '#FF5722' : '#999');
						return '<span style="color: ' + color + ';">' + completed + '/' + total + '</span>';
					}
				},{
					field: 'latest_work_process',
					title: '最近报工工序',
					align: 'center',
					width: 120,
					templet: function (d) {
						return d.latest_work_process || '-';
					}
				},
				{
					field: 'first_article_required',
					title: '是否首检',
					align: 'center',
					width: 100,
					templet: function(d) {
						return d.first_article_required ? '是' : '否';
					}
				},
				{
					field: 'first_article_status',
					title: '首件检验状态',
					align: 'center',
					width: 100,
					templet: function(d) {
						if (d.first_article_required == 1) {
							return d.first_article_status_name ;

						} else {
							return '';
						}	
					}
				},{
					field: 'create_time',
					title: '创建时间',
					align: 'center',
					width: 160
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 300,
					align: 'center',
					ignoreExport:true,
					templet: function (d) {
						var html = '<div class="layui-btn-group">';
						var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
						var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
						var btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="changeStatus">状态</span>';
						var btn4='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="warehousing">入库</span>';
						var btn5='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="work">报工</span>';
						var btn7='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="processes">工序</span>';

						// 新增操作按钮
						var btnCopy='';
						var btnStart='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="start">开始</span>';
						var btnMaterial='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="material">领料</span>';
						var btnPlan='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="plan">生成计划</span>';
						var btnComplete='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="complete">完工</span>';
						var btnWithdraw='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="withdraw">撤回</span>';
						var btnEnd='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="end">结束</span>';
						var btnInvalid='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="invalid">作废</span>';
						var btnDelegate='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="delegate">按单代报</span>';
						switch(d.status){
							case 0: // 待排产
								return html+btn1+btnCopy+btnStart+btn0+btn2+btnMaterial+btnPlan+btnComplete+'</div>';
							case 1: // 已排产 - 不允许编辑
								return html+btnCopy+btnStart+btn0+btnMaterial+btnPlan+btnComplete+'</div>';
							case 2: // 生产中
								return html+btnCopy+btnWithdraw+btnEnd+btn0+btnMaterial+btn4+btnDelegate+btnComplete+'</div>';
							case 3: // 已完成 - 只显示详情和入库
								return html+btn0+btn4+'</div>';
							case 4: // 已取消
								return html+btn0+btn7+btn2+'</div>';
							case 5: // 暂停
								return html+btn0+btn7+'</div>';
						}
					}						
				}
			]]
		});
		
		//表头工具栏事件
		table.on('toolbar(table_order)', function(obj){
			if (obj.event === 'add'){
				tool.side("/Produce/order/add");
				return;
			}
		});
			
		table.on('tool(table_order)',function (obj) {
			var data = obj.data;
			//console.log(data);
			if (obj.event === 'view') {
				tool.side("/Produce/order/view?id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/Produce/order/add?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.post("/Produce/order/delete", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'work') {
				tool.side("/Produce/Report/add?order_id="+data.id);
				return;
			}

			if (obj.event === 'processes') {
				tool.side("/Produce/order/viewProcesses?order_id="+data.id);
				return;
			}
			// 新增按钮事件处理
		 
			if (obj.event === 'start') {
				layer.confirm('确定要开始该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/Produce/order/start", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'material') {
				tool.side("/Produce/MaterialRequest/add?order_id="+data.id, "生产领料");
				return;
			}
			if (obj.event === 'plan') {
				// 跳转到排产页面
				window.open('/Produce/productionPlan/index', '_blank');
				return;
			}
			if (obj.event === 'complete') {
				layer.confirm('确定要完工该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/Produce/order/complete", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'withdraw') {
				layer.confirm('确定要撤回该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/Produce/order/withdraw", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'end') {
				layer.confirm('确定要结束该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/Produce/order/end", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'invalid') {
				layer.confirm('确定要作废该订单吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/Produce/order/invalid", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'delegate') {
				tool.side("/Produce/order/delegate?id="+data.id, "按单代报");
				return;
			}

			if (obj.event === 'warehousing') {
				// 打开入库表单
				layer.open({
					type: 1,
					title: '生产订单入库',
					content: `<div style="padding: 20px;">
						<form class="layui-form" lay-filter="warehousingForm">
							<input type="hidden" name="order_id" value="${data.id}" />
							<div class="layui-form-item">
								<label class="layui-form-label">订单编号</label>
								<div class="layui-input-block">
									<input type="text" class="layui-input" value="${data.order_no}" readonly />
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">产品名称</label>
								<div class="layui-input-block">
									<input type="text" class="layui-input" value="${data.product_name}" readonly />
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">总订单数量</label>
								<div class="layui-input-block">
									<input type="text" class="layui-input" value="${data.quantity}" readonly />
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">可入库数量</label>
								<div class="layui-input-block">
									<input type="text" class="layui-input" value="${data.completed_qty}" readonly />
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">入库数量</label>
								<div class="layui-input-block">
									<input type="number" name="quantity" class="layui-input" required lay-verify="required|number" placeholder="请输入入库数量" value="${data.completed_qty-data.warehousing}"/>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">选择仓库</label>
								<div class="layui-input-block">
									<select name="warehouse_id" required lay-verify="required" id="warehouse_select">
										<option value="">请选择仓库</option>
									</select>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">备注</label>
								<div class="layui-input-block">
									<textarea name="notes" class="layui-textarea" placeholder="请输入备注信息"></textarea>
								</div>
							</div>
							<div class="layui-form-item">
								<div class="layui-input-block">
									<input type="checkbox" name="submit" value="1" title="提交审核" checked>
								</div>
							</div>
							<div class="layui-form-item">
								<div class="layui-input-block">
									<button class="layui-btn" lay-submit lay-filter="warehousingSubmit">确认入库</button>
									<button type="reset" class="layui-btn layui-btn-primary">重置</button>
								</div>
							</div>
						</form>
					</div>`,
					area: ['550px', '650px'],
					success: function(layero, index){
						// 加载仓库列表
						tool.get("/warehouse/OtherInput/getWarehouses", {}, function(res) {
							if (res.code == 0) {
								var options = '';
								$.each(res.data, function(i, item) {
									options += '<option value="' + item.id + '">' + item.name + '</option>';
								});
								$('#warehouse_select').append(options);
								layui.form.render('select');
							}
						});
						

						
						// 给仓库选择器添加过滤器
						$('#warehouse_select').attr('lay-filter', 'warehouse_id');
						
						layui.form.render();
						layui.form.on('submit(warehousingSubmit)', function(formData){
							let callback = function (e) {
								layer.msg(e.msg);
								if (e.code == 0) {
									layer.close(index);
									layui.pageTable.reload();
								}
							}
							tool.post("/Produce/order/warehousing", formData.field, callback);
							return false;
						});
					}
				});
				return;
			}
			if (obj.event === 'changeStatus') {
				var statusArr = {
					//0: '待排产',
					1: '已排产',
					//2: '生产中',
					//3: '已完成',
					4: '取消'
				};
				
				// 构建状态选择下拉菜单
				var options = '';
				for(var key in statusArr) {
					options += '<option value="'+key+'" '+(data.status == key ? 'selected' : '')+'>'+statusArr[key]+'</option>';
				}
				
				// 弹出状态修改框
				layer.open({
					type: 1,
					title: '修改订单状态',
					content: '<div style="padding: 20px;"><form class="layui-form" lay-filter="statusForm"><div class="layui-form-item"><label class="layui-form-label">订单状态</label><div class="layui-input-block"><select name="status">'+options+'</select></div></div><div class="layui-form-item"><div class="layui-input-block"><button class="layui-btn" lay-submit lay-filter="statusSubmit">保存</button><button type="reset" class="layui-btn layui-btn-primary">重置</button></div></div></form></div>',
					area: ['400px', '230px'],
					success: function(layero, index){
						layui.form.render();
						layui.form.on('submit(statusSubmit)', function(formData){
							let callback = function (e) {
								layer.msg(e.msg);
								if (e.code == 0) {
									layer.close(index);
									layui.pageTable.reload();
								}
							}
							tool.post("/Produce/order/updateStatus", { id: data.id, status: formData.field.status }, callback);
							return false;
						});
					}
				});
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 --> 