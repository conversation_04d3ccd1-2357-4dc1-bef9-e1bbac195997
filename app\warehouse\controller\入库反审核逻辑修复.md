# 入库反审核逻辑修复

## 问题分析

### 原有问题
1. **库存扣减逻辑错误**：反审核时没有正确扣减实时库存
2. **锁定库存处理顺序错误**：先重置质检状态，再检查质检状态来扣减库存
3. **分配需求处理时机错误**：库存扣减后才释放锁定，导致数据不一致
4. **可用库存计算错误**：没有考虑已分配的库存情况

### 根本原因
入库和反审核的库存处理逻辑不对称，没有正确处理库存分配和锁定的关系。

## 修复方案

### 1. 正确的反审核流程
```
1. 参数验证和状态检查
2. 出库记录检查
3. 开启事务
4. 【关键】先释放锁定库存和分配需求
5. 更新订单明细收货数量
6. 扣减实时库存（只处理质检合格的产品）
7. 重置质检状态
8. 更新订单状态
9. 提交事务
```

### 2. 库存处理逻辑

#### 入库时的库存变化
```php
// 增加总库存和可用库存
$inventory->quantity += $detail->quantity;
$inventory->available_quantity += $detail->quantity;

// 触发自动分配（可能会锁定部分库存）
// available_quantity 减少，locked_quantity 增加
```

#### 反审核时的库存变化
```php
// 1. 先释放锁定库存（通过分配服务）
$allocationService->releaseOnReverseAudit([...]);

// 2. 再扣减总库存
$inventory->quantity -= $detail->quantity;

// 3. 智能处理可用库存
$availableToDeduct = min($beforeAvailableQuantity, $detail->quantity);
$inventory->available_quantity -= $availableToDeduct;
```

### 3. 关键修复点

#### 修复1：分配服务调用时机
```php
// 修复前：在库存扣减后调用
// 修复后：在库存扣减前调用
$allocationService->releaseOnReverseAudit([
    'ref_type' => 'purchase_receipt',
    'ref_id' => $receipt->id,
    'reason' => '采购入库单反审核',
    'operator_id' => $this->uid
]);
```

#### 修复2：库存扣减逻辑
```php
// 修复前：简单扣减
$inventory->quantity -= $detail->quantity;
$inventory->available_quantity -= $detail->quantity;

// 修复后：智能扣减
$inventory->quantity -= $detail->quantity;
$availableToDeduct = min($beforeAvailableQuantity, $detail->quantity);
$inventory->available_quantity -= $availableToDeduct;
```

#### 修复3：质检状态处理时机
```php
// 修复前：先重置质检状态，再检查质检状态
$detail->quality_status = 0; // 重置
if ($detail->quality_status == QUALITY_GOOD) { // 永远为false
    // 扣减库存
}

// 修复后：先检查质检状态扣减库存，再重置
if ($detail->quality_status == QUALITY_GOOD) {
    // 扣减库存
}
// 最后统一重置质检状态
```

#### 修复4：库存流水记录
```php
// 使用正确的库存流水记录
$this->recordInventoryLog(
    $detail->product_id,
    $receipt->warehouse_id,
    '', // 不使用批次号
    'out', // 出库类型
    $detail->quantity, // 出库数量
    $beforeQuantity,
    $beforeQuantity - $detail->quantity,
    $detail->unit,
    'purchase_receipt_reverse',
    $receipt->id,
    $receipt->receipt_no,
    '采购入库单反审核: ' . $receipt->receipt_no
);
```

## 数据一致性保证

### 1. 库存数据一致性
- **总库存**：入库增加，反审核减少
- **可用库存**：考虑分配锁定情况，智能计算扣减数量
- **锁定库存**：通过分配服务正确释放

### 2. 分配需求一致性
- **释放锁定**：反审核时释放所有相关锁定
- **删除需求**：删除反审核业务的分配需求
- **重新分配**：释放的库存重新分配给其他等待需求

### 3. 业务状态一致性
- **收货单状态**：回到草稿状态
- **订单明细**：收货数量正确减少
- **质检状态**：重置为待检状态

## 测试验证

### 测试场景1：有分配锁定的反审核
1. 入库单提交成功，触发自动分配
2. 部分库存被锁定（available_quantity < quantity）
3. 执行反审核
4. 验证：锁定释放，库存正确扣减，分配需求删除

### 测试场景2：无分配锁定的反审核
1. 入库单提交成功，无自动分配
2. 库存完全可用（available_quantity = quantity）
3. 执行反审核
4. 验证：库存正确扣减，无分配相关操作

### 测试场景3：部分质检合格的反审核
1. 入库单有合格和不合格产品
2. 只有合格产品增加了库存
3. 执行反审核
4. 验证：只扣减合格产品的库存

## 监控和日志

### 关键日志点
1. 分配服务调用结果
2. 库存扣减前后状态
3. 质检状态重置
4. 库存流水记录

### 监控指标
1. 反审核成功率
2. 库存数据一致性
3. 分配需求处理准确性
4. 业务状态同步正确性

## 预期效果

修复后的反审核功能应该：
1. ✅ 正确扣减实时库存
2. ✅ 正确释放锁定库存
3. ✅ 正确删除分配需求
4. ✅ 保持数据一致性
5. ✅ 完整的操作日志
6. ✅ 智能的库存重新分配
