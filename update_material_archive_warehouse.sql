-- 更新物料档案表的默认仓库字段
-- 将default_warehouse字段从varchar改为int类型，存储仓库ID

-- 1. 首先备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS `oa_material_archive_warehouse_backup` AS 
SELECT id, material_code, material_name, default_warehouse 
FROM `oa_material_archive` 
WHERE default_warehouse IS NOT NULL AND default_warehouse != '';

-- 2. 清空default_warehouse字段的数据（因为要改变数据类型）
UPDATE `oa_material_archive` SET default_warehouse = NULL;

-- 3. 修改字段类型
ALTER TABLE `oa_material_archive` 
MODIFY COLUMN `default_warehouse` int(11) DEFAULT NULL COMMENT '默认仓库ID';

-- 4. 添加外键索引（可选，提高查询性能）
ALTER TABLE `oa_material_archive` 
ADD INDEX `idx_default_warehouse` (`default_warehouse`);

-- 5. 如果需要，可以根据仓库名称更新为仓库ID
-- 示例：将原来的仓库名称映射为仓库ID
-- UPDATE `oa_material_archive` a 
-- JOIN `oa_warehouse` w ON w.name = '原料仓'
-- SET a.default_warehouse = w.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_warehouse_backup` WHERE default_warehouse = '原料仓');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_warehouse` w ON w.name = '成品仓'
-- SET a.default_warehouse = w.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_warehouse_backup` WHERE default_warehouse = '成品仓');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_warehouse` w ON w.name = '半成品仓'
-- SET a.default_warehouse = w.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_warehouse_backup` WHERE default_warehouse = '半成品仓');

-- 6. 验证更新结果
-- SELECT a.id, a.material_code, a.material_name, a.default_warehouse, w.name as warehouse_name
-- FROM `oa_material_archive` a 
-- LEFT JOIN `oa_warehouse` w ON a.default_warehouse = w.id
-- WHERE a.default_warehouse IS NOT NULL;
