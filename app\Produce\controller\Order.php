<?php
declare (strict_types = 1);

namespace app\Produce\controller;

use app\base\BaseController;
use app\Produce\model\Order as OrderModel;
use app\Produce\model\OrderLog as OrderLogModel;
use app\Produce\model\OrderProcess as OrderProcessModel;
use app\purchase\model\RequirementOrder as RequirementOrderModel;
use app\warehouse\model\OtherInput as OtherInputModel;
use app\warehouse\model\OtherInputDetail as OtherInputDetailModel;
// use app\engineering\model\BomMaster as BomMasterModel; // 已废弃，使用新的BOM表结构

// 新的库存管理服务
use app\warehouse\service\InventoryRealtimeService;

use think\facade\View;
use think\facade\Db;
use think\facade\Request;
use think\Controller;


class Order extends BaseController
{
    /**
     * 订单列表页面
     */
    public function index()
    {
        // 加载视图
        return View::fetch('index');
    }
    
    /**
     * 获取订单列表数据
     */
    public function getList()
    {
        // 获取请求参数
        $param = get_params();
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        // 获取订单列表
        $model = new OrderModel();
        $result = $model->getList($param, $page, $limit);
        
        // 返回数据
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $result['count'],
            'data' => $result['data']
        ]);
    }
    
    /**
     * 添加/编辑订单页面
     */
    public function add()
    {
        // 获取订单ID
        $param = get_params();
        $id = $param['id'] ?? 0;
        $info = ['id' => 0, 'status' => 1, 'priority' => 1, 'source' => 1, 'order_no' => '', 'production_type' => 1];
        $ztype = 0;

        // 如果是编辑操作，获取订单信息
        if ($id > 0) {
            $model = new OrderModel();
            $info = $model->with(['product', 'details'])->find($id);
            $ztype = 1;
            if (!$info) {
                return $this->error('订单不存在');
            }
            // 格式化交期日期
            if ($info['delivery_date']) {
                $info['delivery_date_format'] = date('Y-m-d', $info['delivery_date']);
            }
        }

        // 获取状态选项
        $statusArr = OrderModel::getStatusArr();
        // 获取优先级选项
        $priorityArr = OrderModel::getPriorityArr();
        // 获取来源选项
        $sourceArr = OrderModel::getSourceArr();

        // 获取可用的销售订单列表（未完全生产的订单）
        $customerOrders = $this->getAvailableCustomerOrders();

        // 获取产品列表
        $productList = Db::name('product')
            ->where('status', 1)
            ->field('id, title as name, material_code as code, specs, unit')
            ->order('id desc')
            ->select()
            ->toArray();
        
        // 分配变量到模板
        View::assign([
            'info' => $info,
            'statusArr' => $statusArr,
            'priorityArr' => $priorityArr,
            'sourceArr' => $sourceArr,
            'productList' => $productList,
            'customerOrders' => $customerOrders,
            'ztype' => $ztype
        ]);

        // 加载视图
        return View::fetch('add');
    }

    /**
     * 获取可用的销售订单列表
     */
    private function getAvailableCustomerOrders()
    {
        return Db::name('customer_order')
            ->alias('o')
            ->join('customer c', 'o.customer_id = c.id', 'left')
            ->where('o.status', '>', 0) // 已审核的订单
            ->where('o.check_status', 2) // 审核通过
            ->field('o.id,o.order_no,c.name as customer_name,o.create_time,o.delivery_date')
            ->order('o.id desc')
            ->limit(100)
            ->select()
            ->toArray();
    }

    /**
     * 获取销售订单明细
     */
    public function getCustomerOrderDetails()
    {
        $orderId = input('order_id/d', 0);

        if (!$orderId) {
            return json(['code' => 1, 'msg' => '订单ID不能为空']);
        }

        try {
            $details = Db::name('customer_order_detail')
                ->alias('d')
                ->join('product p', 'd.product_id = p.id', 'left')
                ->where('d.order_id', $orderId)
                ->where('d.status',0)
                ->field('d.id,d.product_id,p.material_code,p.title as product_name,p.specs,p.unit,d.quantity')
                ->select()
                ->toArray();

            // 计算剩余需要生产的数量和库存信息
            foreach ($details as &$detail) {
                // 暂时使用0作为已生产数量，后续可以从生产记录中获取
                $producedQty = 0;
                $detail['remaining_qty'] = max(0, $detail['quantity'] - $producedQty);
                $detail['available_stock'] = $this->getProductStock($detail['product_id']);
                $detail['can_produce'] = $detail['remaining_qty'] > 0;
                $detail['produced_quantity'] = $producedQty;
            }

            return json(['code' => 0, 'data' => $details]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取订单明细失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取产品库存
     */
    private function getProductStock($productId)
    {
        try {
            // 从实时库存表获取可用库存
            $stock = Db::name('inventory_realtime')
                ->where('product_id', $productId)
                ->sum('available_quantity');

            return $stock ?: 0;
        } catch (\Exception $e) {
            // 如果查询失败，尝试从其他可能的库存表获取
            try {
                // 尝试从 oa_inventory 表获取
                $stock = Db::name('inventory')
                    ->where('product_id', $productId)
                    ->sum('available_quantity');

                return $stock ?: 0;
            } catch (\Exception $e2) {
                // 如果都失败，返回0
                return 0;
            }
        }
    }
    
    /**
     * 获取产品列表（用于搜索选择）
     */
    public function getProductList()
    {
        
       

        $keyword = request()->param('keyword', '');
        $page = request()->param('page/d', 1);
        $limit = request()->param('limit/d', 10);

        
        $where = [['status', '=', 1]];
        if ($keyword) {
            $where[] = ['title|material_code', 'like', "%{$keyword}%"];
        }
        
        $list = Db::name('product')
            ->where($where)
            ->field('id, title, material_code')
            ->order('id desc')
            ->page($page, $limit)
            ->select()
            ->toArray();
            
        $count = Db::name('product')
            ->where($where)
            ->count();
            
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);
    }
    
    /**
     * 保存订单数据
     */
    public function save()
    {
        // 获取表单数据
        $param = get_params();

        // 基础验证
        $rule = [
            'production_type' => 'require|in:1,2',
            'delivery_date' => 'require|date',
            'priority' => 'require|in:1,2,3',
        ];

        // 根据生产类型添加不同的验证规则
        if ($param['production_type'] == 1) {
            // 库存生产验证
            $rule['details'] = 'require|array';
        } else {
            // 销售订单生产验证
            $rule['customer_order_id'] = 'require|number|gt:0';
            $rule['details'] = 'require|array';
        }

        $validate = validate($rule);
        if(!$validate->check($param)) {
            return json(['code' => 1, 'msg' => $validate->getError()]);
        }

        // 验证明细数据
        if (empty($param['details']) || !is_array($param['details'])) {
            return json(['code' => 1, 'msg' => '请至少添加一个产品']);
        }

        // 开启事务
        Db::startTrans();
        try {
            $model = new OrderModel();

            // 处理订单主表数据
            $data = [
                'production_type' => $param['production_type'],
                'delivery_date' => strtotime($param['delivery_date']),
                'priority' => $param['priority'],
                'status' => $param['status'] ?? 0,
                'source' => $param['source'] ?? 0,
                'remark' => $param['remark'] ?? '',
                'process_template_id' => $param['process_template_id'] ?? 0,
                'update_time' => time()
            ];

            // 设置产品ID（取第一个明细的产品ID）
            if (!empty($param['details']) && !empty($param['details'][0]['product_id'])) {
                $data['product_id'] = $param['details'][0]['product_id'];
                $data['quantity'] = $param['details'][0]['quantity'] ?? 0;

                // 设置关联单据信息
                if ($param['production_type'] == 2 && !empty($param['details'][0]['customer_order_detail_id'])) {
                    // 销售订单生产：ref_id 存储销售订单明细ID
                    $data['ref_type'] = 'customer_order_detail';
                    $data['ref_id'] = $param['details'][0]['customer_order_detail_id'];
                } else {
                    // 库存生产：无关联单据
                    $data['ref_type'] = null;
                    $data['ref_id'] = 0;
                }

                // 获取产品信息
                $product = Db::name('product')->where('id', $data['product_id'])->find();
                if ($product) {
                    $data['product_name'] = $product['title'] ?? '';
                }

                // 只设置表中存在的字段
                // customer_order_detail_id 通过 ref_id 字段存储
                // bom_id 字段存在，可以设置
                $data['bom_id'] = $param['details'][0]['bom_id'] ?? 0;
                // process_id 和 notes 字段可能不存在，暂时不设置

                \think\facade\Log::info('设置生产订单关联信息', [
                    'production_type' => $param['production_type'],
                    'ref_type' => $data['ref_type'],
                    'ref_id' => $data['ref_id'],
                    'customer_order_detail_id' => $param['details'][0]['customer_order_detail_id'] ?? 'null'
                ]);
            }

            // 处理销售订单相关字段
            if ($param['production_type'] == 2) {
                $data['customer_order_id'] = $param['customer_order_id'];
                // 获取销售订单号
                $customerOrder = Db::name('customer_order')->where('id', $param['customer_order_id'])->find();
                if ($customerOrder) {
                    $data['customer_order_no'] = $customerOrder['order_no'];
                }
            }

            // 调试：记录即将保存的数据
            \think\facade\Log::info('准备保存生产订单数据', [
                'data_keys' => array_keys($data),
                'data' => $data
            ]);

            $isEdit = isset($param['id']) && $param['id'] > 0;

            if ($isEdit) {
                // 编辑订单
                $orderId = $param['id'];
                $model->where('id', $orderId)->update($data);

                // 单一产品订单，无需删除明细表
            } else {
                // 新增订单
                $data['create_time'] = time();
                $data['order_no'] = $this->generateOrderNo();
                $data['create_uid'] = $this->uid ?: 1; // 如果uid为空，使用默认值1
                $data['create_name'] = 'a';
                $orderId = $model->insertGetId($data);
            }

            // 单一产品生产订单，不需要明细表

            // 创建订单工序实例
            \think\facade\Log::info('工序保存检查', [
                'order_id' => $orderId,
                'process_template_id' => $param['process_template_id'] ?? 'null',
                'has_processes' => !empty($param['processes']),
                'processes_count' => is_array($param['processes']) ? count($param['processes']) : 0,
                'processes_data' => $param['processes'] ?? 'null'
            ]);

            // 保存工序数据到 oa_produce_order_process 表
            if (!empty($param['processes']) && is_array($param['processes'])) {
                \think\facade\Log::info('使用自定义工序数据', ['order_id' => $orderId, 'processes_count' => count($param['processes'])]);
                $this->createCustomOrderProcesses($orderId, $param['processes'], $param['process_template_id'] ?? 0);
            } elseif (!empty($param['process_template_id'])) {
                \think\facade\Log::info('使用模板工序数据', ['order_id' => $orderId, 'template_id' => $param['process_template_id']]);
                $this->createOrderProcesses($orderId, $param['process_template_id']);
            } else {
                \think\facade\Log::warning('跳过工序创建：没有工序数据和模板', ['order_id' => $orderId]);
            }

            // 如果是销售订单生产，检查并更新销售订单明细状态
            if ($param['production_type'] == 2 && !empty($data['ref_id'])) {
                $this->updateCustomerOrderDetailStatus($data['ref_id']);
            }

            // 处理MRP物料需求计算
            $this->processMaterialRequirements($orderId, (int)$data['product_id'], (float)$data['quantity'], (int)$data['delivery_date']);

            // 提交事务
            Db::commit();

            return json(['code' => 0, 'msg' => '保存成功']);
        } catch(\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 保存订单明细数据（已废弃：单一产品订单不需要明细表）
     */
    /*
    private function saveOrderDetails($orderId, $details, $productionType)
    {
        // 调试：记录接收到的明细数据
        \think\facade\Log::info('保存订单明细数据', [
            'order_id' => $orderId,
            'details' => $details,
            'production_type' => $productionType
        ]);

        $detailData = [];
        $sort = 1;

        foreach ($details as $detail) {
            // 调试：记录每个明细项的数据
            \think\facade\Log::info('处理明细项', [
                'detail' => $detail,
                'product_id_empty' => empty($detail['product_id']),
                'quantity_empty' => empty($detail['quantity']),
                'quantity_value' => $detail['quantity'] ?? 'null'
            ]);

            // 验证必要字段
            if (empty($detail['product_id']) || empty($detail['quantity']) || $detail['quantity'] <= 0) {
                \think\facade\Log::warning('跳过无效明细项', ['detail' => $detail]);
                continue;
            }

            // 获取产品信息
            $product = Db::name('product')->where('id', $detail['product_id'])->find();
            if (!$product) {
                continue;
            }

            $detailItem = [
                'order_id' => $orderId,
                'product_id' => $detail['product_id'],
                'product_code' => $product['material_code'] ?? '',
                'product_name' => $product['title'] ?? '',
                'specification' => $product['specs'] ?? '',
                'unit' => $product['unit'] ?? '',
                'quantity' => $detail['quantity'],
                'completed_qty' => 0,
                'customer_order_detail_id' => $detail['customer_order_detail_id'] ?? 0,
                'bom_id' => $detail['bom_id'] ?? 0,
                'process_id' => $detail['process_id'] ?? 0,
                'notes' => $detail['notes'] ?? '',
                'sort' => $sort++,
                'create_time' => time(),
                'update_time' => time()
            ];

            $detailData[] = $detailItem;
        }

        if (!empty($detailData)) {
            Db::name('produce_order_detail')->insertAll($detailData);
        }
    }
    */

    /**
     * 处理MRP物料需求计算
     * @param int $orderId 生产订单ID
     * @param int $productId 产品ID
     * @param float $quantity 生产数量
     * @param int $deliveryDate 交期时间
     */
    private function processMaterialRequirements(int $orderId, int $productId, float $quantity, int $deliveryDate): void
    {
        try {
            $mrpService = new \app\Produce\service\ProductionMrpService();
            $result = $mrpService->processMaterialRequirements($orderId, $productId, $quantity, $deliveryDate);

            \think\facade\Log::info('MRP物料需求处理结果', [
                'order_id' => $orderId,
                'result' => $result
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('MRP物料需求处理失败', [
                'order_id' => $orderId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 清理MRP物料需求相关数据
     * @param int $orderId 生产订单ID
     */
    private function cleanupMrpData(int $orderId): void
    {
        try {
            \think\facade\Log::info('开始清理MRP相关数据', ['order_id' => $orderId]);

            // 1. 获取物料需求记录
            $materialRequirements = Db::name('produce_order_material_requirement')
                ->where('order_id', $orderId)
                ->select()
                ->toArray();

            foreach ($materialRequirements as $requirement) {
                // 2. 释放已锁定的库存
                if ($requirement['locked_quantity'] > 0) {
                    try {
                        $unlockResult = app('app\warehouse\service\InventoryLockService')->unlockInventory([
                            'product_id' => $requirement['material_id'],
                            'quantity' => $requirement['locked_quantity'],
                            'ref_type' => 'production_order',
                            'ref_id' => $orderId
                        ]);

                        \think\facade\Log::info('释放物料库存锁定', [
                            'material_id' => $requirement['material_id'],
                            'locked_quantity' => $requirement['locked_quantity'],
                            'unlock_result' => $unlockResult
                        ]);

                    } catch (\Exception $e) {
                        \think\facade\Log::warning('释放库存锁定失败', [
                            'material_id' => $requirement['material_id'],
                            'locked_quantity' => $requirement['locked_quantity'],
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // 3. 删除库存分配请求
                if ($requirement['allocation_request_id'] > 0) {
                    Db::name('inventory_allocation_request')
                        ->where('id', $requirement['allocation_request_id'])
                        ->delete();

                    \think\facade\Log::info('删除库存分配请求', [
                        'allocation_request_id' => $requirement['allocation_request_id'],
                        'material_id' => $requirement['material_id']
                    ]);
                }
            }

            // 4. 删除物料需求记录
            $deletedCount = Db::name('produce_order_material_requirement')
                ->where('order_id', $orderId)
                ->delete();

            \think\facade\Log::info('MRP数据清理完成', [
                'order_id' => $orderId,
                'deleted_requirements' => $deletedCount,
                'processed_materials' => count($materialRequirements)
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('清理MRP数据失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // 不抛出异常，避免影响主删除流程
        }
    }

    /**
     * 更新销售订单明细状态
     * 统计该销售订单明细的所有生产订单数量，如果总量>=销售数量则更新状态为1
     * @param int $customerOrderDetailId 销售订单明细ID
     */
    private function updateCustomerOrderDetailStatus($customerOrderDetailId)
    {
        try {
            // 获取销售订单明细信息
            $customerOrderDetail = Db::name('customer_order_detail')
                ->where('id', $customerOrderDetailId)
                ->find();

            if (!$customerOrderDetail) {
                \think\facade\Log::warning('销售订单明细不存在', ['detail_id' => $customerOrderDetailId]);
                return;
            }

            // 统计该销售订单明细对应的所有生产订单数量
            $totalProduceQuantity = Db::name('produce_order')
                ->where('production_type', 2) // 销售订单生产
                ->where('ref_type', 'customer_order_detail')
                ->where('ref_id', $customerOrderDetailId)
                ->where('status', 'not in', [4]) // 排除已取消的订单
                ->where('delete_time', 0) // 排除已删除的订单
                ->sum('quantity');

            $totalProduceQuantity = floatval($totalProduceQuantity);
            $salesQuantity = floatval($customerOrderDetail['quantity']);

            \think\facade\Log::info('检查销售订单明细生产状态', [
                'customer_order_detail_id' => $customerOrderDetailId,
                'sales_quantity' => $salesQuantity,
                'total_produce_quantity' => $totalProduceQuantity,
                'current_status' => $customerOrderDetail['status']
            ]);

            // 根据生产总量更新销售订单明细状态
            $newStatus = ($totalProduceQuantity >= $salesQuantity) ? 1 : 0;

            $updateResult = Db::name('customer_order_detail')
                ->where('id', $customerOrderDetailId)
                ->update([
                    'status' => $newStatus,
                    'update_time' => time()
                ]);

            \think\facade\Log::info('更新销售订单明细状态', [
                'customer_order_detail_id' => $customerOrderDetailId,
                'old_status' => $customerOrderDetail['status'],
                'new_status' => $newStatus,
                'sales_quantity' => $salesQuantity,
                'total_produce_quantity' => $totalProduceQuantity,
                'update_result' => $updateResult
            ]);

        } catch (\Exception $e) {
            \think\facade\Log::error('更新销售订单明细状态失败', [
                'customer_order_detail_id' => $customerOrderDetailId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 触发自动投料功能
     * @param int $orderId 工单ID
     * @param array $orderData 工单数据
     */
    private function triggerAutoFeeding($orderId, $orderData)
    {
        try {
            // 添加锁定机制，防止并发调用
            $lockKey = 'autofeeding_lock_'.$orderId;
            if (cache($lockKey)) {
                \think\facade\Log::info('该工单正在处理自动投料，跳过', ['order_id' => $orderId]);
                return false;
            }
            cache($lockKey, true, 60); // 锁定60秒
            
            // 检查是否启用自动投料功能（简化为硬编码值）
            $autoFeeding = true;
                  
            if (!$autoFeeding) {
                // 自动投料功能未启用
                \think\facade\Log::info('自动投料未启用，跳过投料单创建', ['order_id' => $orderId]);
                cache($lockKey, null); // 释放锁
                return false;
            }
           
            // 检查工单状态，只有在已排产或生产中的状态才自动创建投料单
            $validStatus = [1, 2]; // 1=已排产, 2=生产中
            if (!isset($orderData['status']) || !in_array($orderData['status'], $validStatus)) {
                \think\facade\Log::info('工单状态不符合自动投料条件，跳过投料单创建', [
                    'order_id' => $orderId,
                    'status' => $orderData['status'] ?? '未知'
                ]);
                cache($lockKey, null); // 释放锁
                return false;
            }
            
            // 检查是否已有投料单 - 更严格检查
            $feedingCount = Db::name('production_feeding')
                ->where('production_order_id', $orderId)
                ->where('delete_time', 0)
                ->count();
            
            if ($feedingCount > 0) {
                \think\facade\Log::info('工单已存在投料单，跳过自动创建', [
                    'order_id' => $orderId,
                    'feeding_count' => $feedingCount
                ]);
                cache($lockKey, null); // 释放锁
                return false;
            }
         
            // 上述检查都通过，创建投料单
            \think\facade\Log::info('开始为工单创建投料单', ['order_id' => $orderId]);
            
            // 尝试创建投料单
            $result = $this->createFeeding($orderId);
            
            // 释放锁
            cache($lockKey, null);
            
            if ($result === false) {
                \think\facade\Log::info('自动创建投料单失败', ['order_id' => $orderId]);
                return false;
            } else {
                \think\facade\Log::info('自动创建投料单成功', ['order_id' => $orderId, 'feeding_id' => $result]);
                return true;
            }
        } catch (\Exception $e) {
            // 释放锁
            cache('autofeeding_lock_'.$orderId, null);
            
            \think\facade\Log::error('触发自动投料异常', [
                'order_id' => $orderId, 
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 查看订单详情页面
     */
    public function view()
    {
        // 获取订单ID
        $id = request()->param('id/d', 0); 
        
        // 获取订单信息
        $model = new OrderModel();
        
        $info = $model->with(['product'])->find($id);


        if (!$info) {
            return json(['code' => 1, 'msg' => '订单不存在']);
        }

        // 调试：检查产品关联是否成功
        if (!$info['product']) {
            // 手动查询产品信息作为备用
            $product = Db::name('product')->find($info['product_id']);
            if ($product) {
                $info['product'] = $product;
            } else {
                // 如果产品不存在，设置默认值避免错误
                $info['product'] = [
                    'title' => '产品不存在',
                    'material_code' => '',
                    'specs' => '',
                    'unit' => ''
                ];
            }
        }

        // 手动查询BOM信息（替代废弃的BomMaster关联）
        $bom = Db::name('material_bom')
            ->where('product_id', $info['product_id'])
            ->where('status', 1)
            ->where('delete_time', 0)
            ->order('version desc')
            ->find();

        if ($bom) {
            $info['BomMaster'] = $bom;
        } else {
            $info['BomMaster'] = [
                'bom_code' => '无'
            ];
        }
        $info['delivery_date'] = date('Y-m-d', $info['delivery_date']);
       
        // 获取生产进度数据
        $progressData = $this->getProductionProgress($id);

        
        // 获取状态选项
        $statusArr = OrderModel::getStatusArr();
        // 获取优先级选项
        $priorityArr = OrderModel::getPriorityArr();
        // 获取来源选项
        $sourceArr = OrderModel::getSourceArr();

        // 分配变量到模板
        View::assign([
            'info' => $info,
            'statusArr' => $statusArr,
            'priorityArr' => $priorityArr,
            'sourceArr' => $sourceArr,
            'progressData' => $progressData
        ]);
        
        // 加载视图
        return View::fetch('view');
    }
    
    /**
     * 获取生产订单的进度数据
     * @param int $orderId 订单ID
     * @return array 包含进度数据的数组
     */
    private function getProductionProgress($orderId)
    {
     
        
        // 获取订单信息
        $order = Db::name('produce_order')->where('id', $orderId)->find();
        if (!$order) {
            $writeLog("订单不存在 - 订单ID: {$orderId}");
            return [
                'stages' => ['总量'],
                'planned' => [0],
                'completed' => [0]
            ];
        }
        
        // 总计划数量
        $plannedQuantity = $order['quantity'];
        $productId = $order['product_id'];


        
        // 获取订单的工序信息（从订单工序表获取）
        $processes = Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();

        if (empty($processes)) {
            // 如果没有工序步骤，返回基本数据
            return [
                'stages' => ['总量'],
                'planned' => [$plannedQuantity],
                'completed' => [0]
            ];
        }
        

        // 准备阶段数据
        $stages = ['总量'];
        foreach ($processes as $process) {
            $stages[] = $process['process_name'];
        }

        // 准备计划数量数据
        $planned = array_fill(0, count($stages), $plannedQuantity);

        // 获取每个工序的完成数量
        $completed = [0]; // 初始化总量为0

        // 查询每个工序的报工记录
        foreach ($processes as $index => $process) {
            $completedQty = Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('step_id', $process['step_no'])
                ->where('status', 1) // 只统计有效的报工记录
                ->sum('qualified_qty') ?: 0;

            $completed[] = $completedQty;

            // 如果是最后一道工序的完成数量，更新总量 completed
            if ($index == count($processes) - 1) {
                $completed[0] = $completedQty;
            }
        }
        
        
        return [
            'stages' => $stages,
            'planned' => $planned,
            'completed' => $completed
        ];
    }
    
    /**
     * 更新订单状态
     */
    public function updateStatus()
    {
        // 判断是否为POST请求
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }
        
        // 获取参数
        $id = request()->param('id/d');
        $status = request()->param('status/d');
        
        // 参数验证
        if (empty($id) || !isset($status)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 更新状态
        $model = new OrderModel();
        $order = $model->find($id);

       
        // 如果订单状态是已排产(1)，只允许修改为取消(4)
        if ($order['status'] == 1 && $status != 4) {
            return json(['code' => 1, 'msg' => '订单已排产，只能取消不能修改为其他状态']);
        }
        
        // 如果订单状态是生产中(2)或已完成(3)，不允许修改状态
        if ($order['status'] == 2 || $order['status'] == 3) {
            return json(['code' => 1, 'msg' => '订单已在生产中或已完成，不能修改状态']);
        }

        $orderData = [
            'id' => $id,
            'status' => $status,
            // 其他可能需要的数据字段
        ];
        // 触发自动投料
        // print_r($ww);

       // exit;
        // 更新状态
        $result = $model->updateStatus($id, $status);
        
        if ($result['code'] == 0) {
            // 更新状态成功，判断是否需要创建投料单
            if ($status == 1 || $status == 2) { // 1=已排产, 2=生产中
                //此处增加物料预占
                $this->reserveMaterialsForProduction($id);
                
                // 此处不再重复触发自动投料，因为之前的代码已经处理
                 $this->triggerAutoFeeding($id, $orderData);
            }
            
            // 如果订单被取消，释放预占
            if ($status == 4) { // 4=已取消
                //释放物料预占
                $this->releaseReservationsByOrderId($id);
            }
            
            // 更新采购需求订单状态
            if ($status == 1) {
            //oa_purchase_requirement_order 更新处理状态 采购与订单关联表
            Db::name('purchase_requirement_order')
            ->where('purchase_id', $id)
            ->update([
                'is_processed' => 1
            ]);
            }
        }
       
        // 返回结果
        if ($result['code'] == 0) {
            return json(['code' => 0, 'msg' => $result['msg']]);
        } else {
            return json(['code' => 1, 'msg' => $result['msg']]);
        }
    }
    
    /**
     * 删除订单
     */
    public function delete()
    {
        // 判断是否为POST请求
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        // 获取订单ID
        $id = request()->param('id/d');

        // 参数验证
        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 获取订单信息
            $order = Db::name('produce_order')->where('id', $id)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            // 只有待排产和已取消的订单可以删除
            if (!in_array($order['status'], [0, 4])) {
                return json(['code' => 1, 'msg' => '只有待排产和已取消的订单可以删除']);
            }

            \think\facade\Log::info('开始物理删除生产订单', [
                'order_id' => $id,
                'order_no' => $order['order_no'],
                'status' => $order['status']
            ]);

            // 1. 删除订单工序数据
            $processCount = Db::name('produce_order_process')->where('order_id', $id)->count();
            if ($processCount > 0) {
                Db::name('produce_order_process')->where('order_id', $id)->delete();
                \think\facade\Log::info('删除订单工序数据', ['order_id' => $id, 'process_count' => $processCount]);
            }

            // 2. 单一产品订单，无需删除明细表数据

            // 3. 处理采购需求订单关联
            $reqOrders = Db::name('purchase_requirement_order')
                ->where('purchase_id', $id)
                ->select()
                ->toArray();

            foreach ($reqOrders as $reqOrder) {
                // 恢复销售订单明细的缺口数量
                Db::name('customer_order_detail')
                    ->where('id', $reqOrder['order_detail_id'])
                    ->dec('gap', (float)$reqOrder['quantity'])
                    ->update();
            }

            // 4. 释放物料预占
            $this->releaseReservationsByOrderId($id);

            // 5. 清理MRP物料需求相关数据
            $this->cleanupMrpData($id);

            // 6. 删除订单主表数据（物理删除）
            Db::name('produce_order')->where('id', $id)->delete();

            // 7. 如果是销售订单生产，重新计算销售订单明细状态
            if ($order['production_type'] == 2 && !empty($order['ref_id'])) {
                $this->updateCustomerOrderDetailStatus($order['ref_id']);
                \think\facade\Log::info('删除生产订单后更新销售订单明细状态', [
                    'order_id' => $id,
                    'customer_order_detail_id' => $order['ref_id']
                ]);
            }

            // 提交事务
            Db::commit();

            \think\facade\Log::info('生产订单物理删除成功', [
                'order_id' => $id,
                'order_no' => $order['order_no'],
                'deleted_processes' => $processCount
            ]);

            return json(['code' => 0, 'msg' => '删除成功']);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            \think\facade\Log::error('生产订单删除失败', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取订单日志列表
     */
    public function getLogList()
    {
        // 获取请求参数
        $orderId = $this->request->param('order_id/d');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        
        // 参数验证
        if (empty($orderId)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取订单日志列表
        $model = new OrderLogModel();
        $result = $model->getLogList($orderId, $page, $limit);
        
        // 返回数据
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $result['count'],
            'data' => $result['data']
        ]);
    }
    
    /**
     * 从ERP导入订单
     */
    public function importFromErp()
    {
        // TODO: 实现从ERP系统导入订单的功能
        // 这里需要根据实际情况对接ERP系统
        return $this->success('导入功能尚未实现');
    }

    /**
     * 获取产品关联的工艺并计算可用产能
     */
    public function getProductProcess()
    {
        $product_id =  request()->param('product_id/d', 0);
        if($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        

        // 注意：工艺模板系统已废弃，现在使用个性化工序系统
        // 每个订单都有独立的工序配置，不再使用统一的工艺模板
        $processList = [];

        // 可以返回提示信息，建议使用新的工序配置方式
        $processList[] = [
            'id' => 0,
            'title' => '工艺模板系统已废弃',
            'material_code' => '',
            'daily_capacity' => 0,
            'name' => '请在订单中直接配置工序',
            'available_capacity' => 0,
            'note' => '现在每个订单都有独立的工序配置，请在创建订单时直接添加工序'
        ];
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $processList]);
    }

    /**
     * 计算工艺的已占用产能
     */
    private function calculateOccupiedCapacity($processId)
    {
        // 获取使用该工艺模板的未完成订单
        $orders = Db::name('produce_order')
            ->where('process_template_id', $processId)
            ->where('status', 'in', [1, 2]) // 待处理和生产中的订单
            ->field('id, quantity, priority, delivery_date')
            ->select()
            ->toArray();
        
        if(empty($orders)) {
            return 0;
        }
        
        $occupiedCapacity = 0;
        $now = time();
        
        // 优先级影响系数
        $priorityFactors = [
            1 => 1.5, // 紧急订单权重1.5倍
            2 => 1.2, // 高优先级权重1.2倍
            3 => 1.0, // 普通优先级权重1.0倍
            4 => 0.8, // 低优先级权重0.8倍
        ];
        
        foreach ($orders as $order) {
             $deliveryTimestamp = $order['delivery_date'];
            
              
            $daysLeft = max(1, ceil(($deliveryTimestamp - $now) / 86400)); // 剩余天数
            
            // 计算订单每天占用的产能
            $weight = $priorityFactors[$order['priority']] ?? 1.0;
            $dailyOccupied = ($order['quantity'] * $weight) / $daysLeft;
            
            $occupiedCapacity += $dailyOccupied;
        }
        
        return round($occupiedCapacity, 2);
    }

    /**
     * 生成生产单号
     * 格式：SC年月日+5位递增数字
     * @return string
     */
     function generateOrderNo()
    {
        $date = date('Ymd');
        $prefix = 'SC' . $date;
        
        // 查询当天最大单号
        $maxOrder = Db::name('produce_order')
            ->where('order_no', 'like', $prefix . '%')
            ->order('order_no', 'desc')
            ->value('order_no');
        
        if ($maxOrder) {
            // 提取后5位数字并加1
            $serialNumber = intval(substr($maxOrder, -5)) + 1;
        } else {
            // 当天第一个单号
            $serialNumber = 1;
        }
        
        // 使用 sprintf 格式化为5位数字
        $serialNumberStr = sprintf('%05d', $serialNumber);
        
        return $prefix . $serialNumberStr;
    }
    //销售订单缺口
    public function getSalesOrderGap(){
        $param = get_params();
        if (request()->isAjax()) {
            $param = get_params();
            $page = input('page/d', 1);
            $limit = input('limit/d', 15);
            $productName = input('product_name/s', '');
            $materialCode = input('material_code/s', '');
            $processStatus = input('process_status/s', '');
            $where = [];
           
            //$where[] = ['inventory_status', '=', 2];
            $where[] = ['purchase_id', '=', 0];
            $where[] = ['type', '=', 0];
    
            

            // 获取所有有效的需求记录ID
            $validRequirementIds = Db::name('purchase_requirement_order')
                ->alias('pro')
                ->join('customer_order co', 'pro.order_id = co.id')
                ->where(function($q) {
                    $q->where(function($subQ) {
                        $subQ->where('co.order_type', 1)
                             ->where('co.paid_amount', '>', 0);
                    })
                    ->whereOr('co.order_type', 2);
                })
                ->where($where)
                ->column('pro.id');

            // 主查询，使用whereIn过滤有关联销售订单的记录
            $list = RequirementOrderModel::with(['salesOrder' => function($query) {
                    // 预加载销售订单时同时预加载其关联的客户
                    $query->with(['customer']);
                }, 'product'])
                ->where($where)
                ->whereIn('id', $validRequirementIds)
                ->order('id desc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 15,
                    'page' => isset($param['page']) ? (int)$param['page'] : 1,
                    'query' => $param
                ]);
        
            // 处理结果，添加标记字段
            foreach ($list as &$item) {
                // 添加字段标记是否有有效订单关联
                $item->has_valid_order = false;
                
                if (isset($item->salesOrder) && $item->salesOrder) {
                    $salesOrder = $item->salesOrder;
                    
                    // 对于order_type=1的订单，检查paid_amount
                    if ($salesOrder->order_type == 1 && $salesOrder->paid_amount > 0) {
                        $item->has_valid_order = true;
                    } 
                    // 对于order_type=2的订单，直接标记为有效
                    else if ($salesOrder->order_type == 2) {
                        $item->has_valid_order = true;
                    }
                }
                //是否有bom
                 // 统计产品关联的有效BOM数量
                 $item->has_bom = has_product_bom($item['product_id']) ? '有' : '无';

            }
            return table_assign(0, '', $list);
        
        }else{
            return View::fetch('sales_order_gap');
        }
    }
    //根据采购需求创建生产订单
    public function getSalesOrderGapsav(){
       
        if (!request()->isPost()) {
            return to_assign(1, '请求方式错误');
        }
        
        $param = get_params();
        
        // 检查提交的数据是否包含products
        if (empty($param['products']) || !is_array($param['products'])) {
            return to_assign(1, '参数错误：缺少产品信息');
        }
       
        // 开始事务
        Db::startTrans();
        try {
            $results = []; // 存储创建结果
            
            // 循环处理每个产品
            foreach ($param['products'] as $productData) {
                // 提取关键数据
                $productId = $productData['product_id'];
                $quantity = $productData['quantity'];
                $detailIds = $productData['detail_ids'];
                $firstArticleRequired = $productData['first_article_required'];
                
                // 确保detail_ids是数组格式
                if (!is_array($detailIds)) {
                    $detailIds = [$detailIds];
                }
                
                // 获取产品信息
                $productInfo = Db::name('product')
                    ->where('id', $productId)
                    ->find();
                    
                if (!$productInfo) {
                    throw new \Exception("产品ID {$productId} 不存在");
                }
               
                // 1. 生成生产订单编号
                $orderNo = $this->generateOrderNo();
               
                // 2. 创建生产订单
                $orderData = [
                    'order_no' => $orderNo,
                    'product_id' => $productId,
                    'product_name' => $productInfo['title'],
                    'quantity' => $quantity,
                    'priority' => 2, // 默认中优先级
                    'delivery_date' => !empty($param['expected_delivery_date']) ? 
                                      strtotime($param['expected_delivery_date']) : 
                                      strtotime('+15 days'),
                    'status' => 0, // 待排产
                    'source' => 1, // 手动录入
                    'remark' => $param['decomposition_note'] ?? '从采购需求合并创建',
                    'create_uid' => $this->uid,
                    'create_time' => time(),
                    'update_time' => time(),   
                    'first_article_required' => $firstArticleRequired ?? 0
                ];
                
                 
                
                // 插入生产订单并获取ID
                $produceOrderId = Db::name('produce_order')->insertGetId($orderData);
                
                if (!$produceOrderId) {
                    throw new \Exception("创建产品 {$productInfo['title']} 的生产订单失败");
                }
                
                
                // 3. 更新purchase_requirement_order表中的记录，将produce_id更新为新创建的生产订单ID
                if (!empty($detailIds)) {
                    $updateCount = Db::name('purchase_requirement_order')
                        ->whereIn('id', $detailIds)
                        ->update([
                            'purchase_id' => $produceOrderId // 重要：更新produce_id字段
                        ]);

                    // 更新customer_order_detail表的order_detail_id字段
                    $requirementOrders = Db::name('purchase_requirement_order')
                        ->whereIn('id', $detailIds)
                        ->select()
                        ->toArray();

                    foreach ($requirementOrders as $requirementOrder) {
                        Db::name('customer_order_detail')
                            ->where('id', $requirementOrder['order_detail_id'])
                            ->update([
                                'gap' => 0
                            ]);
                    }
                    
                    if ($updateCount <= 0) {
                        throw new \Exception("更新采购需求关联失败，没有找到指定的记录");
                    }
                    
                   
                }
                
                
                
            }
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 0, 'msg' => '成功创建生产订单并合并需求']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return to_assign(1, '创建失败：' . $e->getMessage());
        }
       

       


    }

    /**
     * 获取产品的BOM列表
     */
    public function getBomByProductId()
    {
        $product_id = request()->param('product_id/d', 0);
        if($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        // 查询产品关联的BOM
        try {
            // 首先尝试使用新的BOM表 oa_material_bom
            $bomList = Db::name('material_bom')
                ->where('product_id', $product_id)
                ->where('status', 1) // 1表示启用状态
                ->field('id, bom_code, version')
                ->order('version', 'desc') // 按版本号降序排列，最新版本排在前面
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            // 使用新的BOM表结构
            try {
                $bomList = Db::name('material_bom')
                    ->where('product_id', $product_id)
                    ->where('status', 1) // 启用状态的BOM
                    ->where('delete_time', 0) // 未删除的BOM
                    ->field('id, bom_code, bom_name, version')
                    ->order('version', 'desc') // 按版本号降序排列，最新版本排在前面
                    ->select()
                    ->toArray();
            } catch (\Exception $e2) {
                $bomList = [];
            }
        }
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomList]);
    }

       /**
     * 获取订单列表数据
     */
    public function getOrderList()
    {
        // 获取请求参数
        $param = get_params();
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        // 获取订单列表
        $model = new OrderModel();
        $result = $model->getList($param, $page, $limit);
        
        // 返回数据
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $result['count'],
            'data' => $result['data']
        ]);
    }

    
    /**
     * 生产收货单创建（重构版本）
     */
    public function warehousing()
    {
        $param = get_params();

        // 验证参数
        if (empty($param['order_id'])) {
            return json(['code' => 1, 'msg' => '缺少订单ID']);
        }

        if (empty($param['quantity']) || !is_numeric($param['quantity']) || $param['quantity'] <= 0) {
            return json(['code' => 1, 'msg' => '收货数量必须大于0']);
        }

        if (empty($param['warehouse_id'])) {
            return json(['code' => 1, 'msg' => '请选择仓库']);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 获取订单信息
            $order = Db::name('produce_order')->where('id', $param['order_id'])->find();
            if (!$order) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            // 检查订单状态
            if ($order['status'] != 2 && $order['status'] != 3) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '只有生产中或已完成的订单才能创建收货单']);
            }

            // 查询最后一道工序的良品数量
            $lastProcessReport = Db::name('production_work_report')
                ->where('order_id', $param['order_id'])
                ->where('is_last_process', 1)  // 最后一道工序标记
                ->field('SUM(qualified_qty) as total_qualified_qty')
                ->find();

            $totalQualifiedQty = $lastProcessReport ? $lastProcessReport['total_qualified_qty'] : 0;

            if ($totalQualifiedQty <= 0) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '没有可入库的产品，请先完成最后一道工序的报工']);
            }

            // 查询已入库数量（从库存流水表查询）
            $alreadyInputQty = Db::name('inventory_transaction')
                ->where('ref_type', 'production_order')
                ->where('ref_id', $param['order_id'])
                ->where('transaction_type', 'in')
                ->sum('quantity') ?: 0;

            // 计算可入库数量 = 总良品数 - 已入库数量
            $availableQty = $totalQualifiedQty - $alreadyInputQty;

            if ($availableQty <= 0) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '所有产品已入库，无法再次入库']);
            }

            // 检查入库数量是否超出可入库数量
            if ($param['quantity'] > $availableQty) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '入库数量不能超过可入库数量' . $availableQty]);
            }

            // 检查入库数量是否超出订单数量
            if ($alreadyInputQty + $param['quantity'] > $order['quantity']) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '总入库数量不能超过订单数量' . $order['quantity']]);
            }

            // 获取产品信息
            $product = Db::name('product')->where('id', $order['product_id'])->find();
            if (!$product) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '产品信息不存在']);
            }

            // 使用库存服务直接入库
            $inventoryService = new InventoryRealtimeService();
            $result = $inventoryService->increaseStock(
                $order['product_id'],           // 产品ID
                $param['warehouse_id'],         // 仓库ID
                $param['quantity'],             // 入库数量
                $product['unit'] ?? '',         // 单位
                $product['cost_price'] ?? 0,    // 成本价
                'production_order',             // 关联类型
                $order['id'],                   // 关联ID
                $order['order_no'],             // 关联单号
                $param['notes'] ?? '生产订单入库', // 备注
                $this->uid                      // 操作人
            );

            if (!$result) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '库存入库操作失败']);
            }

            // 检查是否全部入库完成，更新订单状态
            $newAlreadyInputQty = $alreadyInputQty + $param['quantity'];
            if ($newAlreadyInputQty >= $totalQualifiedQty) {
                // 全部入库完成，可以考虑更新订单状态为已入库
                // 这里暂时不更新状态，保持原有逻辑
                error_log("PRODUCTION_WAREHOUSING_COMPLETE: order_id={$order['id']}, total_input_qty={$newAlreadyInputQty}, total_qualified_qty={$totalQualifiedQty}");
            }

            // 提交事务
            Db::commit();

            // 记录成功日志
            error_log("PRODUCTION_WAREHOUSING_SUCCESS: order_id={$order['id']}, product_id={$order['product_id']}, warehouse_id={$param['warehouse_id']}, quantity={$param['quantity']}, operator_id={$this->uid}");

            // 发送入库通知（可选）
            $this->sendWarehouseNotification($order, $param, $result);

            return json([
                'code' => 0,
                'msg' => '生产入库成功',
                'data' => [
                    'order_id' => $order['id'],
                    'order_no' => $order['order_no'],
                    'product_name' => $order['product_name'],
                    'warehouse_id' => $param['warehouse_id'],
                    'quantity' => $param['quantity'],
                    'available_qty' => $availableQty - $param['quantity'], // 剩余可入库数量
                    'total_input_qty' => $newAlreadyInputQty, // 累计入库数量
                    'transaction_no' => $result['transaction_no'] ?? '', // 库存流水号
                    'inventory_url' => '/warehouse/inventory/transaction?ref_type=production_order&ref_id=' . $order['id'] // 库存流水查看链接
                ]
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();

            // 记录错误日志
            error_log("PRODUCTION_WAREHOUSING_ERROR: order_id={$param['order_id']}, error=" . $e->getMessage());

            return json(['code' => 1, 'msg' => '入库操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 生成并输出二维码
     */
    public function qrcode()
    {
        // 获取订单ID
        $id = request()->param('id/d', 0);
        
        if ($id <= 0) {
            return $this->error('参数错误');
        }
        
        // 获取订单详细信息
        $orderInfo = Db::name('produce_order')
            ->alias('o')
            ->join('product p', 'o.product_id = p.id', 'left')
            ->field('o.*, p.title as product_title, p.material_code, p.specs as product_specs, p.unit as product_unit')
            ->where('o.id', $id)
            ->find();
            
        if (!$orderInfo) {
            return $this->error('订单不存在');
        }
        
        // 获取工序信息（从订单工序表获取）
        $processSteps = [];
        $orderProcesses = Db::name('produce_order_process')
            ->where('order_id', $id)
            ->order('step_no asc')
            ->select()
            ->toArray();

        // 转换为原有的格式以保持兼容性
        foreach ($orderProcesses as $process) {
            $processSteps[] = [
                'order' => $process['step_no'],
                'code' => $process['process_code'],
                'name' => $process['process_name'],
                'description' => $process['description'] ?? '',
                'standard_time' => $process['standard_time'] ?? 0,
                'workers' => $process['workers'] ?? 1
            ];
        }
        
        // 获取生产进度数据
        $progressData = $this->getProductionProgress($id);
        
        // 构建报工URL，包含订单ID
        $baseUrl = request()->domain();
        //$url = $baseUrl . '/Produce/report/mobile?order_id=' . $id . '&access_type=qrcode';
        $url=config('app.auth_host').'/qiye/produce/report/id/'.$id.'.html';

        // 使用endroid/qr-code库直接输出二维码图片
        if (class_exists('Endroid\QrCode\QrCode')) {
            // 生成二维码
            $qrCode = new \Endroid\QrCode\QrCode($url);
            $qrCode->setSize(300);
            $qrCode->setMargin(10);
            
            // 获取二维码图片的base64编码
            $writer = new \Endroid\QrCode\Writer\PngWriter();
            $result = $writer->write($qrCode);
            $qrcodeBase64 = $result->getDataUri();
            
            // 格式化日期
            if (isset($orderInfo['delivery_date']) && !empty($orderInfo['delivery_date'])) {
                $orderInfo['delivery_date_format'] = date('Y-m-d', $orderInfo['delivery_date']);
            } else {
                $orderInfo['delivery_date_format'] = '';
            }
            
            // 获取状态和优先级文本
            $statusArr = OrderModel::getStatusArr();
            $priorityArr = OrderModel::getPriorityArr();
            $orderInfo['status_text'] = $statusArr[$orderInfo['status']] ?? '';
            $orderInfo['priority_text'] = $priorityArr[$orderInfo['priority']] ?? '';
            
            // 分配变量到模板
            View::assign([
                'qrcodeBase64' => $qrcodeBase64,
                'reportUrl' => $url,
                'orderId' => $id,
                'orderInfo' => $orderInfo,
                'process' => $process,
                'processSteps' => $processSteps,
                'progressData' => $progressData
            ]);
            
            return View::fetch('qrcode');
        } else {
            // 如果没有找到endroid/qr-code库，提示错误
            return $this->error('QR code library not found. Please install endroid/qr-code');
        }
    }

    /**
 * 获取订单首件检验状态
 */
public function getFirstArticleStatus()
{
    $param = get_params();
    $orderId = isset($param['order_id']) ? intval($param['order_id']) : 0;
    
    if ($orderId <= 0) {
        return json(['code' => 1, 'msg' => '参数错误']);
    }
    
    $model = new OrderModel();
    $result = $model->getFirstArticleStatus($orderId);
    
    if ($result === false) {
        return json(['code' => 1, 'msg' => '订单不存在']);
    }
    
    return json(['code' => 0, 'msg' => '获取成功', 'data' => $result]);
}

/**
 * 设置订单首件检验要求
 */
public function setFirstArticleRequired()
{
    $param = get_params();
    $orderId = isset($param['order_id']) ? intval($param['order_id']) : 0;
    $required = isset($param['required']) ? intval($param['required']) : 1;
    $qty = isset($param['qty']) ? intval($param['qty']) : 1;
    
    if ($orderId <= 0) {
        return json(['code' => 1, 'msg' => '参数错误']);
    }
    
    $model = new OrderModel();
    $order = $model->find($orderId);
    
    if (!$order) {
        return json(['code' => 1, 'msg' => '订单不存在']);
    }
    
    // 更新首件检验设置
    $model->where('id', $orderId)->update([
        'first_article_required' => $required,
        'first_article_qty' => $qty,
        'update_time' => time()
    ]);
    
    return json(['code' => 0, 'msg' => '设置成功']);
}

// 创建投料单
public function createFeeding($orderId){
    try {
        // 记录开始创建投料单
        \think\facade\Log::info('开始创建投料单', ['order_id' => $orderId]);
        
    $model = new OrderModel();
    $order = $model->find($orderId);
       
       
        if(!$order){
            \think\facade\Log::error('创建投料单失败：找不到工单', ['order_id' => $orderId]);
            return false;
        }
        
        \think\facade\Log::info('已获取工单信息', ['order_id' => $orderId, 'product_id' => $order['product_id']]);
        
        // 使用新的BOM表结构查询
        $bom = Db::name('material_bom')
            ->where('product_id', $order['product_id'])
            ->where('status', 1) // 启用状态
            ->where('delete_time', 0)
            ->order('version desc')
            ->find();
        if(!empty($bom)){
            \think\facade\Log::info('已获取BOM信息', ['order_id' => $orderId, 'bom_id' => $bom['id']]);
            
            // 创建投料单数据
            $feedingController = new \app\Produce\controller\Feeding();
            
            // 获取当前管理员ID - 简化为使用会话中可靠的值或默认值
            $adminId = session('admin.id');
            if(empty($adminId)){
                $adminId = session('tc_admin');
            }
            if(empty($adminId)){
                $adminId = 1; // 默认管理员ID
                \think\facade\Log::warning('创建投料单时使用默认管理员ID', ['order_id' => $orderId]);
            }
            
            // 获取部门ID - 如果订单中没有，则使用默认值
            $departmentId = $order['did'] ?? 0;
            
            // 获取仓库
            $warehouseId = 1;//$this->getDefaultWarehouse();
            if(!$warehouseId){
                \think\facade\Log::error('创建投料单失败：找不到有效的仓库', ['order_id' => $orderId]);
                return false;
            }
            
            \think\facade\Log::info('已获取基础信息', [
                'order_id' => $orderId, 
                'admin_id' => $adminId,
                'department_id' => $departmentId,
                'warehouse_id' => $warehouseId
            ]);
            
            // 构建投料单数据
            $data = [
                'production_order_id' => $orderId,
                'bom_id' => $bom['id'],
                'product_id' => $order['product_id'],
                'warehouse_id' => $warehouseId,
                'status' => 0, // 修改为草稿状态，与add_auto一致
                'admin_id' => $adminId,
                'did' => $departmentId,
                'remark' => '系统自动创建投料单'
            ];
            
            // 获取BOM物料明细
            $bomItems = get_bom_items_with_product($bom['id']);
            
            // 如果没有BOM物料明细，则不创建投料单
            if(empty($bomItems)){
                \think\facade\Log::info('创建投料单失败：BOM物料明细为空', ['order_id' => $orderId, 'bom_id' => $bom['id']]);
                return false;
            }
            
            \think\facade\Log::info('已获取BOM明细', ['order_id' => $orderId, 'items_count' => count($bomItems)]);
            
            // 计算每个物料的需求量
            $details = [];
            foreach($bomItems as $item){
                
                 // 计算所需数量
                $requiredQty = $item['quantity'] ?? $item['qty'] ?? 0; // 兼容不同字段名
                $requiredQty = $requiredQty * $order['quantity'];
                
                // 记录具体的BOM项数据，用于调试
                \think\facade\Log::info('BOM明细项', [
                    'material_id' => $item['material_id'],
                    'material_name' => $item['material_name'] ?? '',
                    'quantity_field' => isset($item['quantity']) ? 'quantity' : (isset($item['qty']) ? 'qty' : 'none'),
                    'required_qty' => $requiredQty
                ]);

                // 获取当前库存
                $currentStock = Db::name('inventory')
                    ->where('product_id', $item['material_id'])
                    ->where('warehouse_id', $warehouseId)
                    ->where('quantity', '>', 0)
                    ->sum('quantity');
                
                \think\facade\Log::info('物料库存查询', [
                    'material_id' => $item['material_id'],
                    'warehouse_id' => $warehouseId,
                    'current_stock' => $currentStock
                ]);
                   
                // 构建明细数据
                $detail = [
                    'material_id' => $item['material_id'],
                    'material_code' => $item['material_code'] ?? '',
                    'material_name' => $item['material_name'] ?? '',
                    'specs' => $item['specs'] ?? '',
                    'unit' => $item['unit'] ?? '',
                    'required_quantity' => $requiredQty,
                    'feeding_quantity' => min($requiredQty, $currentStock), // 取所需和库存中的较小值
                    'current_stock' => $currentStock,
                    'remaining_quantity' => max(0, $requiredQty - min($requiredQty, $currentStock)),
                    'status' => ($currentStock <= 0) ? 0 : (($currentStock < $requiredQty) ? 1 : 2), // 根据库存情况设置状态
                    'remark' => ''
                ];
                
                $details[] = $detail;
            }
            \think\facade\Log::info('已准备投料明细数据', ['order_id' => $orderId, 'details_count' => count($details)]);
            
            // 记录明细数据，用于调试
            \think\facade\Log::debug('投料明细数据', [
                'count' => count($details),
                'first_item' => isset($details[0]) ? $details[0] : null
            ]);
            
            \think\facade\Log::info('准备调用saveFeedingFromOrder方法', [
                'order_id' => $orderId,
                'details_count' => count($details),
                'has_admin_id' => isset($data['admin_id']),
                'has_warehouse_id' => isset($data['warehouse_id']),
                'status' => $data['status']
            ]);
            
            // 添加明细到投料单数据
            $data['details'] = $details;
            
            // 设置投料状态
            $data['feeding_status'] = 1; // 1=部分投料
            
            // 调用Feeding控制器的save方法创建投料单
            $result = $feedingController->saveFeedingFromOrder($data);
            
           
            \think\facade\Log::info('saveFeedingFromOrder调用结果', [
                'order_id' => $orderId, 
                'result_code' => $result['code'] ?? 'missing',
                'result_msg' => $result['msg'] ?? 'missing'
            ]);
            
            if(isset($result['code']) && $result['code'] == 0){
                \think\facade\Log::info('自动创建投料单成功', ['order_id' => $orderId, 'feeding_id' => $result['data']['id'] ?? 0]);
                return $result['data']['id'] ?? 0;
            }else{
                \think\facade\Log::error('自动创建投料单失败', ['order_id' => $orderId, 'error' => $result['msg'] ?? '未知错误']);
                return false;
            }
        }else{
            \think\facade\Log::error('自动创建投料单失败：找不到有效的BOM', ['order_id' => $orderId, 'product_id' => $order['product_id']]);
            return false;
        }
    } catch(\Exception $e) {
        \think\facade\Log::error('创建投料单异常', [
            'order_id' => $orderId,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        return false;
    }
}

/**
 * 获取默认仓库ID
 * @return int 默认仓库ID
 */
private function getDefaultWarehouse(){
    try {
        // 尝试获取配置的默认原料仓库
        $defaultWarehouseId = Db::name('system_config')
            ->where('name', 'default_material_warehouse')
            ->value('value');
        
        if($defaultWarehouseId){
            \think\facade\Log::info('从系统配置获取到默认仓库', ['warehouse_id' => $defaultWarehouseId]);
            return intval($defaultWarehouseId);
        }
        
        // 如果没有配置，尝试找到一个原料仓库
        $warehouse = Db::name('warehouse')
            ->where('status', 1)
            ->where('type', 1) // 原料仓
            ->order('id', 'asc')
            ->find();
        
        if($warehouse){
            \think\facade\Log::info('找到默认原料仓库', ['warehouse_id' => $warehouse['id'], 'name' => $warehouse['name']]);
            return $warehouse['id'];
        }
        
        // 如果没有找到原料仓，返回第一个有效仓库
        $anyWarehouse = Db::name('warehouse')
            ->where('status', 1)
            ->order('id', 'asc')
            ->find();
        
        if($anyWarehouse){
            \think\facade\Log::info('使用首个有效仓库作为默认仓库', ['warehouse_id' => $anyWarehouse['id'], 'name' => $anyWarehouse['name']]);
            return $anyWarehouse['id'];
        }
        
        \think\facade\Log::error('找不到有效的仓库');
        return 0;
    } catch (\Exception $e) {
        \think\facade\Log::error('获取默认仓库异常', [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return 0;
    }
}

/**
 * 为生产订单预占物料
 * 
 * @param int $orderId 生产订单ID
 * @return bool 预占结果，成功返回true，失败返回false
 */
private function reserveMaterialsForProduction($orderId)
{
    try {
        // 获取订单信息
        $order = Db::name('produce_order')->find($orderId);
        if (!$order) {
            \think\facade\Log::error('物料预占失败：找不到订单', ['order_id' => $orderId]);
            return false;
        }
        
        // 查询产品最新版本的BOM
        $bom = get_product_bom($order['product_id']);
        
        if (!$bom) {
            \think\facade\Log::error('物料预占失败：找不到有效的BOM', [
                'order_id' => $orderId, 
                'product_id' => $order['product_id']
            ]);
            return false;
        }
        
        \think\facade\Log::info('为生产订单预占物料', [
            'order_id' => $orderId,
            'order_no' => $order['order_no'],
            'product_id' => $order['product_id'],
            'quantity' => $order['quantity'],
            'bom_id' => $bom['id']
        ]);
        
        // 获取BOM物料明细
        $bomItems = get_bom_items_with_product($bom['id']);
        
        if (empty($bomItems)) {
            \think\facade\Log::info('物料预占跳过：BOM物料明细为空', [
                'order_id' => $orderId, 
                'bom_id' => $bom['id']
            ]);
            return true; // 没有物料项时认为成功
        }
        
        // 实例化预占服务
        $reservationService = new \app\warehouse\service\InventoryReservationService();
        
        // 获取当前操作员ID
        $operatorId = session('admin.id');
        if (empty($operatorId)) {
            $operatorId = session('tc_admin');
        }
        if (empty($operatorId)) {
            $operatorId = 1; // 默认管理员ID
        }
        
        // 清除旧的预占记录（如果存在）
        try {
            Db::name('oa_inventory_reserve')
                ->where('ref_type', 'produce_order')
                ->where('ref_id', $orderId)
                ->where('status', \app\warehouse\service\InventoryReservationService::STATUS_ACTIVE)
                ->update([
                    'status' => \app\warehouse\service\InventoryReservationService::STATUS_RELEASED,
                    'update_time' => time(),
                    'notes' => '生产订单状态变更，释放旧预占'
                ]);
        } catch (\Exception $e) {
            \think\facade\Log::warning('清除旧预占记录失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
        }
        
        // 记录预占成功和失败的物料
        $successItems = [];
        $failedItems = [];
        
        // 遍历所有物料进行预占
        foreach ($bomItems as $item) {

           
            // 计算所需数量 (BOM数量 × 订单数量)
            $requiredQty = floatval($item['quantity'] ?? $item['qty'] ?? 0) * floatval($order['quantity']);
           
            
            if ($requiredQty <= 0) {
                \think\facade\Log::info('跳过物料预占：数量为零', [
                    'material_id' => $item['material_id'],
                    'material_name' => $item['material_name'],
                    'bom_quantity' => $item['quantity'] ?? $item['qty'] ?? 0,
                    'order_quantity' => $order['quantity']
                ]);
                continue;
            }
            // 调用预占服务创建预占
            $result = $reservationService->createReservation(
                $item['material_id'],                   // 物料ID
                $requiredQty,                           // 预占数量（BOM数量×订单数量）
                'produce_order',                        // 关联类型
                $orderId,                               // 关联ID（订单ID）
                $order['order_no'],                     // 关联单号（订单编号）
                [
                    'operator_id' => $operatorId,       // 操作人ID
                    'allow_partial' => true,            // 允许部分预占
                    'notes' => "生产订单 [{$order['order_no']}] 物料预占",
                    'strategy' => 'fifo'                // 使用FIFO策略（先进先出）
                ]
            );
            
            // 记录预占结果
            \think\facade\Log::info('物料预占结果', [
                'order_id' => $orderId,
                'material_id' => $item['material_id'],
                'material_name' => $item['material_name'] ?? '',
                'required_quantity' => $requiredQty,
                'success' => $result['success'],
                'total_reserved' => $result['total_reserved'],
                'remaining_qty' => $result['remaining_qty']
            ]);
            
            // 统计预占结果
            if ($result['success']) {
                $successItems[] = [
                    'material_id' => $item['material_id'],
                    'material_name' => $item['material_name'] ?? '',
                    'required_qty' => $requiredQty,
                    'reserved_qty' => $result['total_reserved'],
                    'remaining_qty' => $result['remaining_qty']
                ];
            } else {
                $failedItems[] = [
                    'material_id' => $item['material_id'],
                    'material_name' => $item['material_name'] ?? '',
                    'required_qty' => $requiredQty,
                    'message' => $result['message']
                ];
            }
        }
        
        // 记录最终的预占统计
        \think\facade\Log::info('生产订单物料预占完成', [
            'order_id' => $orderId,
            'order_no' => $order['order_no'],
            'bom_id' => $bom['id'],
            'total_items' => count($bomItems),
            'success_items' => count($successItems),
            'failed_items' => count($failedItems)
        ]);
        
        return true; // 预占处理完成，返回成功
    } catch (\Exception $e) {
        \think\facade\Log::error('物料预占过程发生异常', [
            'order_id' => $orderId,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

/**
 * 释放生产订单的物料预占
 * 
 * @param int $orderId 生产订单ID
 * @return bool 是否成功
 */
private function releaseReservationsByOrderId($orderId)
{
    try {
        // 实例化预占服务
        $reservationService = new \app\warehouse\service\InventoryReservationService();
        
        // 查询与订单相关的预占记录
        $reservations = Db::name('oa_inventory_reserve')
            ->where('ref_type', 'produce_order')
            ->where('ref_id', $orderId)
            ->where('status', \app\warehouse\service\InventoryReservationService::STATUS_ACTIVE)
            ->select()
            ->toArray();
        
        if (empty($reservations)) {
            \think\facade\Log::info('订单没有需要释放的预占记录', ['order_id' => $orderId]);
            return true; // 没有记录需要释放，视为成功
        }
        
        // 获取当前操作员ID
        $operatorId = session('admin.id');
        if (empty($operatorId)) {
            $operatorId = session('tc_admin');
        }
        if (empty($operatorId)) {
            $operatorId = 1; // 默认管理员ID
        }
        
        // 收集预占ID列表
        $reserveIds = array_column($reservations, 'id');
        
        // 调用预占服务的释放方法
        $result = $reservationService->releaseReservation(
            $reserveIds,
            $operatorId,
            [
                'notes' => "生产订单取消或删除，释放物料预占",
                'continue_on_error' => true // 允许部分释放
            ]
        );
        
        // 记录释放结果
        \think\facade\Log::info('生产订单物料预占释放结果', [
            'order_id' => $orderId,
            'reserve_count' => count($reserveIds),
            'result' => $result
        ]);
        
        return $result['success'];
    } catch (\Exception $e) {
        \think\facade\Log::error('释放物料预占发生异常', [
            'order_id' => $orderId,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return false;
    }
}

    /**
     * 获取产品的工艺模板信息
     */
    public function getProductProcessTemplate()
    {
        $param = get_params();
        $productId = $param['product_id'] ?? 0;

        if ($productId <= 0) {
            return json(['code' => 1, 'msg' => '产品ID无效', 'data' => null]);
        }

        try {
            // 获取产品的工艺模板ID
            $product = Db::name('product')
                ->where('id', $productId)
                ->field('id, title, material_code, process_template_id')
                ->find();

            if (!$product) {
                return json(['code' => 1, 'msg' => '产品不存在', 'data' => null]);
            }

            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'product_id' => $product['id'],
                    'product_name' => $product['title'],
                    'product_code' => $product['material_code'],
                    'process_template_id' => $product['process_template_id'] ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage(), 'data' => null]);
        }
    }

    /**
     * 创建订单工序实例
     * @param int $orderId 订单ID
     * @param int $templateId 工艺模板ID
     * @return bool
     */
    private function createOrderProcesses($orderId, $templateId)
    {
        try {
            // 删除已存在的工序实例（编辑订单时）
            Db::name('produce_order_process')->where('order_id', $orderId)->delete();

            // 根据工艺模板创建工序实例
            return OrderProcessModel::createFromTemplate($orderId, $templateId);

        } catch (\Exception $e) {
            // 记录错误日志
            trace('创建订单工序实例失败：' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 创建自定义订单工序实例
     * @param int $orderId 订单ID
     * @param array $processes 工序数据
     * @param int $templateId 工艺模板ID
     * @return bool
     */
    private function createCustomOrderProcesses($orderId, $processes, $templateId)
    {
        try {
            // 删除已存在的工序实例（编辑订单时）
            Db::name('produce_order_process')->where('order_id', $orderId)->delete();

            $processData = [];
            foreach ($processes as $index => $process) {
                $processData[] = [
                    'order_id' => $orderId,
                    'step_no' => $process['step_no'] ?? ($index + 1),
                    'process_code' => $process['process_code'] ?? '',
                    'process_name' => $process['process_name'] ?? '',
                    'process_type' => $process['process_type'] ?? '数据记录',
                    'standard_time' => $process['standard_time'] ?? 0,
                    'standard_price' => $process['standard_price'] ?? 0,
                    'efficiency' => $process['efficiency'] ?? 0,
                    'description' => $process['description'] ?? '',
                    'quality_standard' => $process['quality_standard'] ?? '',
                    'equipment_required' => $process['equipment_required'] ?? '',
                    'skill_required' => $process['skill_required'] ?? '',
                    'processing_type' => $process['processing_type'] ?? '自制',
                    'inspection_method' => $process['inspection_method'] ?? '免检',
                    'time_unit' => $process['time_unit'] ?? '小时',
                    'completion_time' => $process['completion_time'] ?? 0,
                    'is_key_process' => $process['is_key_process'] ?? 0,
                    'status' => OrderProcessModel::STATUS_NOT_STARTED,
                    'create_time' => time(),
                    'update_time' => time(),
                ];
            }

            // 批量插入工序数据
            if (!empty($processData)) {
                \think\facade\Log::info('批量插入工序数据', [
                    'order_id' => $orderId,
                    'process_count' => count($processData),
                    'process_data' => $processData
                ]);

                Db::name('produce_order_process')->insertAll($processData);

                // 更新订单的总工序数量
                Db::name('produce_order')
                    ->where('id', $orderId)
                    ->update([
                        'total_processes' => count($processData),
                        'process_template_id' => $templateId
                    ]);

                \think\facade\Log::info('工序数据保存成功', [
                    'order_id' => $orderId,
                    'total_processes' => count($processData)
                ]);
            } else {
                \think\facade\Log::warning('工序数据为空，跳过保存', ['order_id' => $orderId]);
            }

            return true;

        } catch (\Exception $e) {
            // 记录错误日志
            trace('创建自定义订单工序实例失败：' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 获取订单工序列表
     */
    public function getOrderProcesses()
    {
        $param = get_params();
        $orderId = $param['order_id'] ?? 0;

        if (empty($orderId)) {
            return json(['code' => 1, 'msg' => '订单ID不能为空']);
        }

        try {
            $processes = OrderProcessModel::getOrderProcesses($orderId);

            // 格式化数据
            foreach ($processes as &$process) {
                $process['status_text'] = OrderProcessModel::$statusArr[$process['status']] ?? '未知';
                $process['create_time_format'] = $process['create_time'] ? date('Y-m-d H:i:s', $process['create_time']) : '';
                $process['actual_start_time_format'] = $process['actual_start_time'] ? date('Y-m-d H:i:s', $process['actual_start_time']) : '';
                $process['actual_end_time_format'] = $process['actual_end_time'] ? date('Y-m-d H:i:s', $process['actual_end_time']) : '';
            }

            return json(['code' => 0, 'data' => $processes]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新工序状态
     */
    public function updateProcessStatus()
    {
        $param = get_params();
        $processId = $param['process_id'] ?? 0;
        $status = $param['status'] ?? 0;

        if (empty($processId)) {
            return json(['code' => 1, 'msg' => '工序ID不能为空']);
        }

        try {
            $result = OrderProcessModel::updateStatus($processId, $status, $param);

            if ($result) {
                return json(['code' => 0, 'msg' => '更新成功']);
            } else {
                return json(['code' => 1, 'msg' => '更新失败']);
            }

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 查看订单工序页面
     */
    public function viewProcesses()
    {
        $param = get_params();
        $orderId = $param['order_id'] ?? 0;

        if (empty($orderId)) {
            return $this->error('订单ID不能为空');
        }

        // 获取订单信息
        $order = Db::name('produce_order')->find($orderId);
        if (!$order) {
            return $this->error('订单不存在');
        }

        // 获取工序列表
        $processes = OrderProcessModel::getOrderProcesses($orderId);

        // 格式化数据
        foreach ($processes as &$process) {
            $process['status_text'] = OrderProcessModel::$statusArr[$process['status']] ?? '未知';
            $process['create_time_format'] = $process['create_time'] ? date('Y-m-d H:i:s', $process['create_time']) : '';
            $process['actual_start_time_format'] = $process['actual_start_time'] ? date('Y-m-d H:i:s', $process['actual_start_time']) : '';
            $process['actual_end_time_format'] = $process['actual_end_time'] ? date('Y-m-d H:i:s', $process['actual_end_time']) : '';
            $process['planned_start_time_format'] = $process['planned_start_time'] ? date('Y-m-d H:i:s', $process['planned_start_time']) : '';
            $process['planned_end_time_format'] = $process['planned_end_time'] ? date('Y-m-d H:i:s', $process['planned_end_time']) : '';
        }

        View::assign([
            'order' => $order,
            'processes' => $processes,
            'statusArr' => OrderProcessModel::$statusArr
        ]);

        return View::fetch('viewProcesses');
    }

    /**
     * 生成生产计划
     */
    public function generatePlan()
    {
        try {
            $orderId = Request::param('id');

            if (empty($orderId)) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }

            $order = Db::name('produce_order')->where('id', $orderId)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '生产订单不存在']);
            }

            // 检查是否已有排产计划
            $existingPlan = Db::name('production_plan')->where('order_id', $orderId)->find();
            if ($existingPlan) {
                return json(['code' => 1, 'msg' => '该订单已有排产计划']);
            }

            // 估算生产天数（可以根据产品复杂度、数量等因素计算）
            $estimatedDays = $this->estimateProductionDays($order);

            // 查找可用的排产日期
            $startDate = date('Y-m-d', strtotime('+1 day')); // 从明天开始
            $availableDate = $this->findAvailableDate($startDate, $estimatedDays);

            if (!$availableDate) {
                return json(['code' => 1, 'msg' => '近期无可用排产时间，请手动安排']);
            }

            $endDate = date('Y-m-d', strtotime($availableDate . ' +' . ($estimatedDays - 1) . ' days'));

            Db::startTrans();
            try {
                // 创建排产计划
                $planId = Db::name('production_plan')->insertGetId([
                    'order_id' => $orderId,
                    'plan_start_date' => $availableDate,
                    'plan_end_date' => $endDate,
                    'plan_days' => $estimatedDays,
                    'priority' => $order['priority'],
                    'auto_scheduled' => 1,
                    'create_time' => time(),
                    'create_uid' => $this->uid,
                    'create_name' => $this->uname
                ]);

                // 更新订单状态
                Db::name('produce_order')->where('id', $orderId)->update([
                    'plan_id' => $planId,
                    'plan_status' => 1,
                    'scheduled_date' => $availableDate,
                    'estimated_days' => $estimatedDays,
                    'update_time' => time()
                ]);

                Db::commit();

                return json([
                    'code' => 0,
                    'msg' => "生产计划生成成功，排产时间：{$availableDate} 至 {$endDate}"
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('生成生产计划失败', [
                'order_id' => $orderId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '生成失败：' . $e->getMessage()]);
        }
    }

    /**
     * 估算生产天数
     */
    private function estimateProductionDays($order)
    {
        // 基础天数
        $baseDays = 1;

        // 根据数量调整
        if ($order['quantity'] > 1000) {
            $baseDays += 2;
        } elseif ($order['quantity'] > 500) {
            $baseDays += 1;
        }

        // 根据优先级调整
        if ($order['priority'] >= 4) {
            $baseDays = max(1, $baseDays - 1); // 高优先级减少天数
        }

        return $baseDays;
    }

    /**
     * 查找可用的排产日期
     */
    private function findAvailableDate($startDate, $requiredDays, $maxDailyOrders = 5)
    {
        $currentDate = $startDate;
        $maxSearchDays = 30; // 最多搜索30天
        $searchedDays = 0;

        while ($searchedDays < $maxSearchDays) {
            $canSchedule = true;

            // 检查连续的天数是否都可用
            for ($i = 0; $i < $requiredDays; $i++) {
                $checkDate = date('Y-m-d', strtotime($currentDate . ' +' . $i . ' days'));

                // 跳过周末
                $dayOfWeek = date('w', strtotime($checkDate));
                if ($dayOfWeek == 0 || $dayOfWeek == 6) {
                    $canSchedule = false;
                    break;
                }

                // 检查当天的订单数量
                $dailyOrderCount = Db::name('production_plan')
                    ->where('plan_start_date', '<=', $checkDate)
                    ->where('plan_end_date', '>=', $checkDate)
                    ->count();

                if ($dailyOrderCount >= $maxDailyOrders) {
                    $canSchedule = false;
                    break;
                }
            }

            if ($canSchedule) {
                return $currentDate;
            }

            // 移动到下一天
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
            $searchedDays++;
        }

        return null;
    }

    /**
     * 订单开始生产
     */
    public function start()
    {
        try {
            $orderId = Request::param('id');

            if (empty($orderId)) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }

            $order = Db::name('produce_order')->where('id', $orderId)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '生产订单不存在']);
            }

            if ($order['status'] != 1) {
                return json(['code' => 1, 'msg' => '只有已排产状态的订单才能开始生产']);
            }

            Db::startTrans();
            try {
                // 更新订单状态
                Db::name('produce_order')->where('id', $orderId)->update([
                    'status' => 2, // 生产中
                    'update_time' => time()
                ]);

                // 更新排产计划状态
                if ($order['plan_id'] > 0) {
                    Db::name('production_plan')->where('id', $order['plan_id'])->update([
                        'status' => 1, // 生产中
                        'actual_start_date' => date('Y-m-d'),
                        'update_time' => time()
                    ]);
                }

                Db::commit();

                return json(['code' => 0, 'msg' => '订单已开始生产']);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('开始生产失败', [
                'order_id' => $orderId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 订单完工
     */
    public function complete()
    {
        try {
            $orderId = Request::param('id');

            if (empty($orderId)) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }

            $order = Db::name('produce_order')->where('id', $orderId)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '生产订单不存在']);
            }

            Db::startTrans();
            try {
                // 更新订单状态
                Db::name('produce_order')->where('id', $orderId)->update([
                    'status' => 3, // 已完成
                    'completed_qty' => $order['quantity'], // 完成数量等于订单数量
                    'progress' => 100,
                    'update_time' => time()
                ]);

                // 更新排产计划状态
                if ($order['plan_id'] > 0) {
                    Db::name('production_plan')->where('id', $order['plan_id'])->update([
                        'status' => 2, // 已完成
                        'progress' => 100,
                        'actual_end_date' => date('Y-m-d'),
                        'update_time' => time()
                    ]);
                }

                Db::commit();

                return json(['code' => 0, 'msg' => '订单已完工']);

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('订单完工失败', [
                'order_id' => $orderId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 按单代报页面
     */
    public function delegate()
    {
        if (request()->isPost()) {
            $data = request()->post();

            // 验证数据
            if (empty($data['order_id'])) {
                return json(['code' => 1, 'msg' => '订单ID不能为空']);
            }

            if (empty($data['reports']) || !is_array($data['reports'])) {
                return json(['code' => 1, 'msg' => '报工数据不能为空']);
            }

            // 获取订单信息
            $order = Db::name('produce_order')
                ->where('id', $data['order_id'])
                ->find();

            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            // 开始事务
            Db::startTrans();
            try {
                // 获取该订单的最大工序号（用于判断最后工序）
                $maxStepNo = Db::name('produce_order_process')
                    ->where('order_id', $data['order_id'])
                    ->max('step_no');

                foreach ($data['reports'] as $reportData) {
                    if (empty($reportData['process_id']) ||
                        empty($reportData['quantity']) || empty($reportData['qualified_qty'])) {
                        continue; // 跳过不完整的数据
                    }

                    // 获取订单工序实例信息（考虑订单生产差异化）
                    $orderProcess = Db::name('produce_order_process')
                        ->where('id', $reportData['process_id'])
                        ->where('order_id', $data['order_id'])
                        ->find();

                    if (!$orderProcess) {
                        continue;
                    }

                    // 验证报工数量是否超过可报工量
                    $validationResult = $this->validateReportQuantity($data['order_id'], $orderProcess, $reportData['quantity']);
                    if (!$validationResult['valid']) {
                        throw new \Exception($validationResult['message']);
                    }

                    // 获取工人信息
                    $worker = Db::name('admin')
                        ->where('id', $reportData['worker_id'])
                        ->find();

                    // 判断是否为最后工序
                    $isLastProcess = ($orderProcess['step_no'] == $maxStepNo) ? 1 : 0;

                    // 准备报工数据
                    $workReportData = [
                        'order_id' => $data['order_id'],
                        'process_id' => $orderProcess['id'], // 使用订单工序实例ID
                        'process_code' => $orderProcess['process_code'],
                        'process_name' => $orderProcess['process_name'],
                        'step_id' => $orderProcess['step_no'], // 工序序号
                        'step_code' => $orderProcess['process_code'],
                        'step_name' => $orderProcess['process_name'],
                        'step' => $orderProcess['step_no'],
                        'product_id' => $order['product_id'],
                        'product_name' => $order['product_name'],
                        'quantity' => (int)$reportData['quantity'],
                        'qualified_qty' => (int)$reportData['qualified_qty'],
                        'unqualified_qty' => (int)$reportData['quantity'] - (int)$reportData['qualified_qty'],
                        'work_time' => (int)($reportData['work_time'] * 60), // 转换为分钟
                        'worker_id' => $reportData['worker_id'],
                        'worker_name' => $worker['nickname'] ?? '',
                        'report_date' => $reportData['report_date'] ?? date('Y-m-d'),
                        'has_exception' => 0,
                        'remark' => $reportData['remark'] ?? '',
                        'status' => 1,
                        'is_last_process' => $isLastProcess, // 标记是否最后工序
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    // 插入报工记录
                    $reportId = Db::name('production_work_report')->insertGetId($workReportData);

                    if (!$reportId) {
                        throw new \Exception('报工记录保存失败');
                    }

                    // 更新订单工序状态
                    Db::name('produce_order_process')
                        ->where('id', $orderProcess['id'])
                        ->update([
                            'status' => 1, // 进行中
                            'completed_qty' => Db::raw('completed_qty + ' . (int)$reportData['quantity']),
                            'qualified_qty' => Db::raw('qualified_qty + ' . (int)$reportData['qualified_qty']),
                            'actual_work_time' => Db::raw('actual_work_time + ' . (float)$reportData['work_time']),
                            'update_time' => time()
                        ]);

                    // 如果是最后工序，更新订单完成数量和状态
                    if ($isLastProcess) {
                        // 更新订单完成数量
                        Db::name('produce_order')
                            ->where('id', $data['order_id'])
                            ->setInc('completed_qty', (int)$reportData['qualified_qty']);

                        // 获取更新后的订单信息，判断是否需要更新状态
                        $updatedOrder = Db::name('produce_order')
                            ->where('id', $data['order_id'])
                            ->find();

                        $updateOrderData = [];

                        // 如果完成数量达到或超过订单数量，更新订单状态为已完成
                        if ($updatedOrder['completed_qty'] >= $updatedOrder['quantity']) {
                            $updateOrderData['status'] = 3; // 已完成
                            $updateOrderData['progress'] = 100;
                        } else if ($updatedOrder['status'] == 1) {
                            // 如果订单状态为已排产，更新为生产中
                            $updateOrderData['status'] = 2; // 生产中
                        }

                        // 计算进度百分比
                        if ($updatedOrder['quantity'] > 0) {
                            $updateOrderData['progress'] = min(100, round(($updatedOrder['completed_qty'] / $updatedOrder['quantity']) * 100));
                        }

                        // 更新订单状态和进度
                        if (!empty($updateOrderData)) {
                            $updateOrderData['update_time'] = time();
                            Db::name('produce_order')
                                ->where('id', $data['order_id'])
                                ->update($updateOrderData);
                        }

                        // 记录调试日志
                        error_log("DELEGATE_LAST_PROCESS: order_id={$data['order_id']}, step_no={$orderProcess['step_no']}, max_step_no={$maxStepNo}, qualified_qty={$reportData['qualified_qty']}, report_id={$reportId}");
                    }
                }

                Db::commit();
                return json(['code' => 0, 'msg' => '代报成功']);

            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '代报失败：' . $e->getMessage()]);
            }
        }

        // GET请求 - 显示页面
        $param = get_params();
        $orderId = $param['id'] ?? 0;

        if (empty($orderId)) {
            return view('error', ['msg' => '订单ID不能为空']);
        }

        // 获取订单信息，包含生产计划信息
        $order = Db::name('produce_order')
            ->alias('o')
            ->leftJoin('production_plan p', 'o.plan_id = p.id')
            ->where('o.id', $orderId)
            ->field('o.*, p.plan_start_date, p.plan_end_date')
            ->find();

        if (!$order) {
            return view('error', ['msg' => '订单不存在']);
        }

        // 获取订单的工序列表
        $orderProcesses = Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();

        // 如果没有工序数据，尝试从工艺模板创建
        if (empty($orderProcesses) && !empty($order['process_template_id'])) {
            $this->createOrderProcesses($orderId, $order['process_template_id']);
            $orderProcesses = Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->order('step_no asc')
                ->select()
                ->toArray();
        }

        // 获取所有工序的报工数据统计
        $processIds = array_column($orderProcesses, 'id');
        $reportStats = [];
        if (!empty($processIds)) {
            $reports = Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('process_id', 'in', $processIds)
                ->where('status', 1) // 有效状态
                ->field('process_id, SUM(quantity) as total_quantity, SUM(qualified_qty) as total_qualified, SUM(unqualified_qty) as total_unqualified')
                ->group('process_id')
                ->select()
                ->toArray();

            foreach ($reports as $report) {
                $reportStats[$report['process_id']] = [
                    'total_quantity' => $report['total_quantity'] ?: 0,
                    'total_qualified' => $report['total_qualified'] ?: 0,
                    'total_unqualified' => $report['total_unqualified'] ?: 0
                ];
            }
        }

        // 格式化工序数据，添加状态文本、时间格式、逾期状态和报工统计
        $currentTime = time();
        foreach ($orderProcesses as &$process) {
            $process['status_text'] = $this->getProcessStatusText($process['status']);

            // 优先使用工序的计划时间，如果没有则使用生产计划的时间，最后使用交期时间
            $plannedStartTime = $process['planned_start_time'];
            $plannedEndTime = $process['planned_end_time'];

            if (!$plannedStartTime && !empty($order['plan_start_date'])) {
                $plannedStartTime = strtotime($order['plan_start_date']);
            } elseif (!$plannedStartTime) {
                $plannedStartTime = $order['delivery_date'];
            }

            if (!$plannedEndTime && !empty($order['plan_end_date'])) {
                $plannedEndTime = strtotime($order['plan_end_date']);
            } elseif (!$plannedEndTime) {
                $plannedEndTime = $order['delivery_date'];
            }

            $process['planned_start_time_format'] = $plannedStartTime ? date('Y-m-d H:i:s', $plannedStartTime) : '';
            $process['planned_end_time_format'] = $plannedEndTime ? date('Y-m-d H:i:s', $plannedEndTime) : '';
            $process['actual_start_time_format'] = $process['actual_start_time'] ? date('Y-m-d H:i:s', $process['actual_start_time']) : '';
            $process['actual_end_time_format'] = $process['actual_end_time'] ? date('Y-m-d H:i:s', $process['actual_end_time']) : '';

            // 计算逾期状态
            $process['is_overdue'] = false;
            $process['overdue_text'] = '正常';
            $process['overdue_class'] = 'layui-bg-green';

            if ($plannedEndTime && $process['status'] != 2) { // 未完成的工序
                if ($currentTime > $plannedEndTime) {
                    $process['is_overdue'] = true;
                    $process['overdue_text'] = '逾期';
                    $process['overdue_class'] = 'layui-bg-red';
                } elseif ($currentTime > $plannedStartTime) {
                    $process['overdue_text'] = '进行中';
                    $process['overdue_class'] = 'layui-bg-blue';
                }
            } elseif ($process['status'] == 2) {
                $process['overdue_text'] = '已完成';
                $process['overdue_class'] = 'layui-bg-green';
            }

            // 添加报工统计数据
            $processId = $process['id'];
            $process['report_quantity'] = $reportStats[$processId]['total_quantity'] ?? 0;
            $process['report_qualified'] = $reportStats[$processId]['total_qualified'] ?? 0;
            $process['report_unqualified'] = $reportStats[$processId]['total_unqualified'] ?? 0;
        }

        // 获取工人列表
        $workers = Db::name('admin')
            ->field('id, nickname as name')
            ->where('status', 1)
            ->select();

        View::assign([
            'order' => $order,
            'orderProcesses' => $orderProcesses,
            'workers' => $workers
        ]);

        return View();
    }

    /**
     * 获取工序可报工数量
     */
    public function getReportableQuantity()
    {
        $orderId = Request::param('order_id');
        $processId = Request::param('process_id');

        if (empty($orderId) || empty($processId)) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }

        // 获取工序信息
        $process = Db::name('produce_order_process')
            ->where('id', $processId)
            ->where('order_id', $orderId)
            ->find();

        if (!$process) {
            return json(['code' => 1, 'msg' => '工序不存在']);
        }

        // 获取订单的所有工序，按step_no排序
        $allProcesses = Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();

        // 找到当前工序在工序列表中的位置
        $currentStepIndex = -1;
        foreach ($allProcesses as $index => $p) {
            if ($p['id'] == $processId) {
                $currentStepIndex = $index;
                break;
            }
        }

        if ($currentStepIndex === -1) {
            return json(['code' => 1, 'msg' => '未找到工序信息']);
        }

        // 获取当前工序已报工的总数量
        $currentReportedQty = Db::name('production_work_report')
            ->where('order_id', $orderId)
            ->where('process_id', $processId)
            ->where('status', 1)
            ->sum('quantity') ?: 0;

        if ($currentStepIndex == 0) {
            // 第一道工序，可报工量 = 订单总量 - 已报工量
            $order = Db::name('produce_order')->where('id', $orderId)->find();
            $reportableQty = $order['quantity'] - $currentReportedQty;
            $info = [
                'type' => 'first_step',
                'order_qty' => $order['quantity'],
                'reported_qty' => $currentReportedQty
            ];
        } else {
            // 非第一道工序，可报工量 = 上一道工序合格数量 - 当前工序已报工量
            $prevProcess = $allProcesses[$currentStepIndex - 1];

            // 获取上一道工序的合格数量
            $prevQualifiedQty = Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('process_id', $prevProcess['id'])
                ->where('status', 1)
                ->sum('qualified_qty') ?: 0;

            $reportableQty = $prevQualifiedQty - $currentReportedQty;
            $info = [
                'type' => 'next_step',
                'prev_step_name' => $prevProcess['process_name'],
                'prev_qualified_qty' => $prevQualifiedQty,
                'reported_qty' => $currentReportedQty
            ];
        }

        // 确保可报工量不为负数
        $reportableQty = max(0, $reportableQty);

        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'reportable_qty' => $reportableQty,
                'step_name' => $process['process_name'],
                'step_index' => $currentStepIndex,
                'info' => $info
            ]
        ]);
    }

    /**
     * 获取工序列表（工艺模板系统已废弃）
     */
    public function getProcessSteps()
    {
        // 工艺模板系统已废弃，现在使用个性化工序系统
        // 每个订单都有独立的工序配置

        return json([
            'code' => 1,
            'msg' => '工艺模板系统已废弃，请使用订单工序配置',
            'data' => [],
            'note' => '现在每个订单都有独立的工序配置，不再使用统一的工艺模板'
        ]);
    }

    /**
     * 验证报工数量是否合法
     * @param int $orderId 订单ID
     * @param array $currentProcess 当前工序信息
     * @param int $reportQuantity 要报工的数量
     * @return array
     */
    private function validateReportQuantity($orderId, $currentProcess, $reportQuantity)
    {
        // 获取订单的所有工序，按step_no排序
        $allProcesses = Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();

        if (empty($allProcesses)) {
            return ['valid' => false, 'message' => '未找到订单工序信息'];
        }

        // 找到当前工序在工序列表中的位置
        $currentStepIndex = -1;
        foreach ($allProcesses as $index => $process) {
            if ($process['id'] == $currentProcess['id']) {
                $currentStepIndex = $index;
                break;
            }
        }

        if ($currentStepIndex === -1) {
            return ['valid' => false, 'message' => '未找到当前工序信息'];
        }

        // 获取当前工序已报工的总数量
        $currentReportedQty = Db::name('production_work_report')
            ->where('order_id', $orderId)
            ->where('process_id', $currentProcess['id'])
            ->where('status', 1)
            ->sum('quantity') ?: 0;

        if ($currentStepIndex == 0) {
            // 第一道工序，可报工量 = 订单总量 - 已报工量
            $order = Db::name('produce_order')->where('id', $orderId)->find();
            $maxReportableQty = $order['quantity'] - $currentReportedQty;

            if ($reportQuantity > $maxReportableQty) {
                return [
                    'valid' => false,
                    'message' => "第一道工序报工数量不能超过{$maxReportableQty}件（订单总量{$order['quantity']}件 - 已报工{$currentReportedQty}件）"
                ];
            }
        } else {
            // 非第一道工序，可报工量 = 上一道工序合格数量 - 当前工序已报工量
            $prevProcess = $allProcesses[$currentStepIndex - 1];

            // 获取上一道工序的合格数量
            $prevQualifiedQty = Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('process_id', $prevProcess['id'])
                ->where('status', 1)
                ->sum('qualified_qty') ?: 0;

            $maxReportableQty = $prevQualifiedQty - $currentReportedQty;

            if ($reportQuantity > $maxReportableQty) {
                return [
                    'valid' => false,
                    'message' => "工序【{$currentProcess['process_name']}】报工数量不能超过{$maxReportableQty}件（上一道工序【{$prevProcess['process_name']}】合格数量{$prevQualifiedQty}件 - 当前工序已报工{$currentReportedQty}件）"
                ];
            }
        }

        return ['valid' => true, 'message' => '验证通过'];
    }

    /**
     * 获取工序状态文本
     */
    private function getProcessStatusText($status)
    {
        $statusArr = [
            0 => '未开始',
            1 => '进行中',
            2 => '已完成',
            3 => '暂停'
        ];

        return $statusArr[$status] ?? '未知';
    }

    /**
     * 发送仓库入库通知
     */
    private function sendWarehouseNotification($order, $param, $result)
    {
        try {
            // 这里可以实现各种通知方式：
            // 1. 系统内消息通知
            // 2. 邮件通知
            // 3. 微信/钉钉通知
            // 4. 短信通知

            // 示例：记录到系统日志，仓库人员可以查看
            $notificationData = [
                'type' => 'production_warehousing',
                'title' => '生产入库通知',
                'content' => "生产订单 {$order['order_no']} 产品 {$order['product_name']} 已入库 {$param['quantity']} 件",
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
                'warehouse_id' => $param['warehouse_id'],
                'quantity' => $param['quantity'],
                'transaction_no' => $result['transaction_no'] ?? '',
                'create_time' => time()
            ];

            // 可以将通知数据插入到通知表中
            // Db::name('warehouse_notification')->insert($notificationData);

            // 记录通知日志
            error_log("WAREHOUSE_NOTIFICATION_SENT: " . json_encode($notificationData));

        } catch (\Exception $e) {
            // 通知失败不影响主流程
            error_log("WAREHOUSE_NOTIFICATION_FAILED: " . $e->getMessage());
        }
    }

}