<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class CheckLocation extends Command
{
    protected function configure()
    {
        $this->setName('check:location')
            ->setDescription('检查数据库中是否有库位数据');
    }

    protected function execute(Input $input, Output $output)
    {
        // 检查库位表
        $locationCount = Db::name('warehouse_location')->count();
        $output->writeln("库位表记录数: " . $locationCount);
        
        // 检查仓库表
        $warehouseCount = Db::name('warehouse')->count();
        $output->writeln("仓库表记录数: " . $warehouseCount);
        
        // 如果有库位，显示一些示例数据
        if ($locationCount > 0) {
            $locations = Db::name('warehouse_location')
                ->alias('l')
                ->join('warehouse w', 'l.warehouse_id = w.id')
                ->field('l.id, l.name as location_name, l.code, l.warehouse_id, w.name as warehouse_name')
                ->limit(5)
                ->select()
                ->toArray();
                
            $output->writeln("库位示例数据:");
            foreach ($locations as $location) {
                $output->writeln("ID: {$location['id']}, 编码: {$location['code']}, 名称: {$location['location_name']}, 仓库ID: {$location['warehouse_id']}, 仓库名称: {$location['warehouse_name']}");
            }
        }
        
        // 检查收货单明细表中是否有库位ID
        $receiptDetailWithLocationCount = Db::name('purchase_receipt_detail')
            ->where('location_id', '>', 0)
            ->count();
        $output->writeln("收货单明细表中有库位ID的记录数: " . $receiptDetailWithLocationCount);
    }
} 