<?php

declare(strict_types=1);

namespace app\Produce\model;

use think\Model;
use think\facade\Db;

/**
 * 生产订单工序实例模型
 */
class OrderProcess extends Model
{
    // 设置当前模型对应的数据表名称
    protected $name = 'produce_order_process';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 类型转换
    protected $type = [
        'standard_time' => 'float',
        'standard_price' => 'float',
        'efficiency' => 'float',
        'completion_time' => 'float',
        'actual_work_time' => 'float',
        'completed_qty' => 'integer',
        'qualified_qty' => 'integer',
        'unqualified_qty' => 'integer',
        'is_outsourced' => 'boolean',
        'is_key_process' => 'boolean',
    ];
    
    // 工序状态常量
    const STATUS_NOT_STARTED = 0;  // 未开始
    const STATUS_IN_PROGRESS = 1;  // 进行中
    const STATUS_COMPLETED = 2;    // 已完成
    const STATUS_PAUSED = 3;       // 暂停
    
    // 工序状态数组
    public static $statusArr = [
        self::STATUS_NOT_STARTED => '未开始',
        self::STATUS_IN_PROGRESS => '进行中',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_PAUSED => '暂停',
    ];
    
    /**
     * 关联生产订单
     */
    public function order()
    {
        return $this->belongsTo('app\Produce\model\Order', 'order_id', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::$statusArr[$data['status']] ?? '未知';
    }
    
    /**
     * 根据工艺模板创建订单工序
     * @param int $orderId 订单ID
     * @param int $templateId 工艺模板ID
     * @return bool
     */
    public static function createFromTemplate($orderId, $templateId)
    {
        try {
            // 获取工艺模板
            $template = Db::name('process_template')->find($templateId);
            if (!$template || empty($template['steps'])) {
                return false;
            }
            
            // 解析工艺步骤
            $steps = json_decode($template['steps'], true);
            if (!is_array($steps)) {
                return false;
            }
            
            // 开启事务
            Db::startTrans();
            
            $processData = [];
            foreach ($steps as $index => $step) {
                $processData[] = [
                    'order_id' => $orderId,
                    'step_no' => $step['step'] ?? ($index + 1),
                    'process_code' => $step['code'] ?? '',
                    'process_name' => $step['name'] ?? '',
                    'process_type' => $step['type'] ?? '数据记录',
                    'standard_time' => $step['standard_time'] ?? 0,
                    'standard_price' => $step['standard_price'] ?? 0,
                    'efficiency' => $step['efficiency'] ?? 0,
                    'description' => $step['description'] ?? '',
                    'quality_standard' => $step['quality_standard'] ?? '',
                    'equipment_required' => $step['equipment_required'] ?? '',
                    'skill_required' => $step['skill_required'] ?? '',
                    'processing_type' => $step['processing_type'] ?? '自制',
                    'inspection_method' => $step['inspection_method'] ?? '免检',
                    'time_unit' => $step['time_unit'] ?? '小时',
                    'completion_time' => $step['completion_time'] ?? 0,
                    'is_key_process' => $step['is_key_process'] ?? 0,
                    'predecessor_step' => $step['predecessor_step'] ?? '',
                    'successor_step' => $step['successor_step'] ?? '',
                    'status' => self::STATUS_NOT_STARTED,
                    'create_time' => time(),
                    'update_time' => time(),
                ];
            }
            
            // 批量插入工序数据
            if (!empty($processData)) {
                Db::name('produce_order_process')->insertAll($processData);
                
                // 更新订单的总工序数量
                Db::name('produce_order')
                    ->where('id', $orderId)
                    ->update(['total_processes' => count($processData)]);
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
    
    /**
     * 更新工序状态
     * @param int $id 工序ID
     * @param int $status 状态
     * @param array $data 其他更新数据
     * @return bool
     */
    public static function updateStatus($id, $status, $data = [])
    {
        try {
            $updateData = array_merge($data, [
                'status' => $status,
                'update_time' => time()
            ]);
            
            // 如果是完成状态，记录完成时间
            if ($status == self::STATUS_COMPLETED && !isset($data['actual_end_time'])) {
                $updateData['actual_end_time'] = time();
            }
            
            // 如果是开始状态，记录开始时间
            if ($status == self::STATUS_IN_PROGRESS && !isset($data['actual_start_time'])) {
                $updateData['actual_start_time'] = time();
            }
            
            $result = Db::name('produce_order_process')
                ->where('id', $id)
                ->update($updateData);
            
            // 更新订单进度
            if ($result) {
                $process = Db::name('produce_order_process')->find($id);
                if ($process) {
                    self::updateOrderProgress($process['order_id']);
                }
            }
            
            return $result !== false;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 更新订单进度
     * @param int $orderId 订单ID
     * @return bool
     */
    public static function updateOrderProgress($orderId)
    {
        try {
            // 统计工序完成情况
            $totalProcesses = Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->count();
            
            $completedProcesses = Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->where('status', self::STATUS_COMPLETED)
                ->count();
            
            // 计算进度百分比
            $progress = $totalProcesses > 0 ? round(($completedProcesses / $totalProcesses) * 100) : 0;
            
            // 确定订单状态
            $orderStatus = 0; // 待排产
            if ($completedProcesses > 0 && $completedProcesses < $totalProcesses) {
                $orderStatus = 2; // 生产中
            } elseif ($completedProcesses == $totalProcesses && $totalProcesses > 0) {
                $orderStatus = 3; // 已完成
            } elseif ($totalProcesses > 0) {
                $orderStatus = 1; // 已排产
            }
            
            // 更新订单信息
            return Db::name('produce_order')
                ->where('id', $orderId)
                ->update([
                    'progress' => $progress,
                    'status' => $orderStatus,
                    'completed_processes' => $completedProcesses,
                    'total_processes' => $totalProcesses,
                    'update_time' => time()
                ]) !== false;
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取订单的工序列表
     * @param int $orderId 订单ID
     * @return array
     */
    public static function getOrderProcesses($orderId)
    {
        return Db::name('produce_order_process')
            ->where('order_id', $orderId)
            ->order('step_no asc')
            ->select()
            ->toArray();
    }
    
    /**
     * 复制工序（用于订单复制等场景）
     * @param int $sourceOrderId 源订单ID
     * @param int $targetOrderId 目标订单ID
     * @return bool
     */
    public static function copyProcesses($sourceOrderId, $targetOrderId)
    {
        try {
            $sourceProcesses = self::getOrderProcesses($sourceOrderId);
            if (empty($sourceProcesses)) {
                return false;
            }
            
            $newProcesses = [];
            foreach ($sourceProcesses as $process) {
                unset($process['id']);
                $process['order_id'] = $targetOrderId;
                $process['status'] = self::STATUS_NOT_STARTED;
                $process['actual_start_time'] = 0;
                $process['actual_end_time'] = 0;
                $process['completed_qty'] = 0;
                $process['qualified_qty'] = 0;
                $process['unqualified_qty'] = 0;
                $process['actual_work_time'] = 0;
                $process['create_time'] = time();
                $process['update_time'] = time();
                $newProcesses[] = $process;
            }
            
            return Db::name('produce_order_process')->insertAll($newProcesses) !== false;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
