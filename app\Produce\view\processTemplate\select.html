{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="请输入编号/工艺名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">重置</button>
        </div>
    </form>
    <table class="layui-hide" id="table_process_select" lay-filter="table_process_select"></table>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        layui.pageTable = table.render({
            elem: "#table_process_select"
            ,title: "工艺路线选择"
            ,url: "/Produce/processTemplate/select"
            ,page: true
            ,limit: 15
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    field: 'code',
                    title: '工艺编号',
                    align: 'center',
                    width: 180,
                    templet: function(d) {
                        return '<span class="layui-badge layui-bg-blue">' + (d.code || '') + '</span>';
                    }
                },{
                    field: 'name',
                    title: '工艺名称',
                    minWidth: 200
                },{
                    field: 'remark',
                    title: '备注',
                    width: 200,
                    templet: function(d) {
                        if (d.remark && d.remark.length > 30) {
                            return '<span title="' + d.remark + '">' + d.remark.substring(0, 30) + '...</span>';
                        }
                        return d.remark || '';
                    }
                },{
                    field: 'create_time',
                    title: '创建时间',
                    align: 'center',
                    width: 160,
                    templet: function(d) {
                        return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm:ss');
                    }
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 120,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        return '<button class="layui-btn layui-btn-xs" lay-event="select">选择</button>';
                    }                        
                }
            ]]
        });
            
        table.on('tool(table_process_select)',function (obj) {
            var data = obj.data;
            if (obj.event === 'select') {
                // 选择工艺路线
                if (parent && parent.selectProcessTemplate) {
                    parent.selectProcessTemplate(data);
                } else {
                    layer.msg('选择成功');
                }
                
                // 关闭弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
                return;
            }
        });
    }
</script>
{/block}