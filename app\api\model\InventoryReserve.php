<?php
namespace app\api\model;

use think\Model;

class InventoryReserve extends Model {
    protected $name = 'inventory_reserve';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'product_id'     => 'int',
        'quantity'       => 'float',
        'used_quantity'  => 'float',
        'ref_type'       => 'string',
        'ref_id'         => 'int',
        'ref_no'         => 'string',
        'status'         => 'int',
        'created_by'     => 'int',
        'create_time'    => 'int',
        'update_time'    => 'int',
        'locked_by'      => 'int',
        'notes'          => 'string',
    ];

    // 关联库存模型
    public function inventory() {
        return $this->belongsTo(Inventory::class, 'inventory_id');
    }
    
    // 关联产品模型
    public function product() {
        return $this->belongsTo(Product::class, 'product_id');
    }
    
    // 关联仓库模型
    public function warehouse() {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }
    
    // 关联订单模型
    public function order() {
        return $this->belongsTo(CustomerOrder::class, 'ref_id')
            ->where('ref_type', 'order');
    }
    
    // 获取状态文本
    public function getStatusTextAttr($value, $data) {
        $status = [
            0 => '已释放',
            1 => '已预占',
            2 => '已转锁定'
        ];
        return isset($status[$data['status']]) ? $status[$data['status']] : '未知';
    }
}
