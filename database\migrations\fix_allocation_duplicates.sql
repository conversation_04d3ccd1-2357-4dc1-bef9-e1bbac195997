-- 修复库存分配重复需求问题
-- 执行时间: 2025-08-05

-- 1. 清理现有的重复记录（保留最新的）
DELETE t1 FROM oa_inventory_allocation_request t1
INNER JOIN oa_inventory_allocation_request t2 
WHERE t1.id < t2.id 
  AND t1.ref_type = t2.ref_type 
  AND t1.ref_id = t2.ref_id 
  AND t1.product_id = t2.product_id 
  AND t1.warehouse_id = t2.warehouse_id
  AND t1.status IN (1, 2, 3)  -- 只清理活跃状态的重复记录
  AND t2.status IN (1, 2, 3);

-- 2. 添加唯一索引，防止未来的重复记录
-- 注意：这个索引只对非取消状态的记录生效
ALTER TABLE `oa_inventory_allocation_request` 
ADD UNIQUE INDEX `uk_active_allocation` (`ref_type`, `ref_id`, `product_id`, `warehouse_id`, `status`) 
COMMENT '防止活跃状态下的重复分配需求';

-- 3. 添加检查约束（MySQL 8.0+支持）
-- ALTER TABLE `oa_inventory_allocation_request` 
-- ADD CONSTRAINT `chk_quantity_positive` CHECK (`quantity` > 0);

-- 4. 优化现有索引
ALTER TABLE `oa_inventory_allocation_request` 
ADD INDEX `idx_business_product` (`ref_type`, `ref_id`, `product_id`, `warehouse_id`),
ADD INDEX `idx_status_priority` (`status`, `priority` DESC, `request_time` ASC);

-- 5. 创建清理重复记录的存储过程
DELIMITER $$
CREATE PROCEDURE `sp_cleanup_duplicate_allocation_requests`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE duplicate_count INT DEFAULT 0;
    
    -- 统计重复记录数量
    SELECT COUNT(*) INTO duplicate_count
    FROM (
        SELECT ref_type, ref_id, product_id, warehouse_id, COUNT(*) as cnt
        FROM oa_inventory_allocation_request 
        WHERE status IN (1, 2, 3)
        GROUP BY ref_type, ref_id, product_id, warehouse_id
        HAVING cnt > 1
    ) as duplicates;
    
    IF duplicate_count > 0 THEN
        -- 删除重复记录，保留最新的
        DELETE t1 FROM oa_inventory_allocation_request t1
        INNER JOIN oa_inventory_allocation_request t2 
        WHERE t1.id < t2.id 
          AND t1.ref_type = t2.ref_type 
          AND t1.ref_id = t2.ref_id 
          AND t1.product_id = t2.product_id 
          AND t1.warehouse_id = t2.warehouse_id
          AND t1.status IN (1, 2, 3)
          AND t2.status IN (1, 2, 3);
          
        -- 记录清理日志
        INSERT INTO oa_reverse_audit_log (
            ref_type, ref_id, ref_no, reason, operator_id, operator_name, create_time
        ) VALUES (
            'system_cleanup', 0, 'DUPLICATE_CLEANUP', 
            CONCAT('自动清理重复分配需求，清理数量：', ROW_COUNT()), 
            0, 'SYSTEM', UNIX_TIMESTAMP()
        );
    END IF;
    
END$$
DELIMITER ;

-- 6. 创建监控视图：重复分配需求监控
CREATE OR REPLACE VIEW `v_duplicate_allocation_monitor` AS
SELECT 
    ref_type,
    ref_id,
    product_id,
    warehouse_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY id) as request_ids,
    GROUP_CONCAT(status ORDER BY id) as statuses,
    MIN(create_time) as first_created,
    MAX(create_time) as last_created
FROM oa_inventory_allocation_request 
WHERE status IN (1, 2, 3)
GROUP BY ref_type, ref_id, product_id, warehouse_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, last_created DESC;

-- 7. 创建分配需求统计视图
CREATE OR REPLACE VIEW `v_allocation_request_stats` AS
SELECT 
    ref_type,
    status,
    COUNT(*) as request_count,
    SUM(quantity) as total_quantity,
    SUM(allocated_quantity) as total_allocated,
    SUM(quantity - allocated_quantity) as total_pending,
    AVG(priority) as avg_priority,
    MIN(request_time) as earliest_request,
    MAX(request_time) as latest_request,
    AVG(UNIX_TIMESTAMP() - request_time) / 86400 as avg_wait_days
FROM oa_inventory_allocation_request 
GROUP BY ref_type, status
ORDER BY ref_type, status;
