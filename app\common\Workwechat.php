<?php
namespace app\common;

use GuzzleHttp\Client;
use GuzzleHttp\Promise;
use think\facade\Log;
use think\facade\Cache;
use think\exception\HttpException;
use think\facade\Db;


class Workwechat
{
    /**
     * 配置参数
     * @var array
     */
    protected $config = [
        'app_id'     => 'client_zc',       // 应用ID
        'app_secret' => 'k7d#Fg!2s9*ZmWpQ154gdsfasEGF3EG$%&%#$',       // 应用密钥
        'base_uri'   => 'https://work.xinqiyu.cn/server/', // 服务端地址
        'timeout'    => 10        // 请求超时时间（秒）
    ];

       /**
     * 获取通讯录secret（自动缓存）
     */
    public function getSecret(): string
    {
        $config = get_config('workchat');
         $redisKey = $config['secret_redis_key'];
        // 尝试从Redis获取Token
        $accessToken = Cache::get($redisKey);
        
        if ($accessToken) {
            return $accessToken;
        }
       
        // 无缓存或过期，重新获取
        return $this->refreshSecret($config);
    }
    /**
     * 强制刷新通讯录secret
     */
    private function refreshSecret(array $config): string
    {
        $client = new Client();
        $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
        try {
            $response = $client->get($url, [
                'query' => [
                    'corpid'     => $config['corpid'],
                    'corpsecret' => $config['secret']
                ],
                'timeout' => 5
            ]);
            $data = json_decode($response->getBody(), true);
            if (isset($data['errcode']) && $data['errcode'] != 0) {
                throw new \Exception("企业微信API错误: {$data['errmsg']}", $data['errcode']);
            }
            // 存储到Redis（有效期减少60秒防过期）
            Cache::set($config['secret_redis_key'], $data['access_token'], $data['expires_in'] - 60);
            return $data['access_token'];
        } catch (\Exception $e) {
            Log::error('企业微信Token获取失败: ' . $e->getMessage());
            throw new HttpException(500, '企业微信服务暂不可用');
        }
    }



    
     /**
     * 获取AccessToken（自动缓存）
     */
    public function getAccessToken(): string
    {
        $config = get_config('workchat');

        $redisKey = $config['redis_key'];
        // 尝试从Redis获取Token
        $accessToken = Cache::get($redisKey);
        if ($accessToken) {
            return $accessToken;
        }
        // 无缓存或过期，重新获取
        return $this->refreshAccessToken($config);
    }

    /**
     * 强制刷新AccessToken
     */
    private function refreshAccessToken(array $config): string
    {
        $client = new Client();
        $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
        try {
            $response = $client->get($url, [
                'query' => [
                    'corpid'     => $config['corpid'],
                    'corpsecret' => $config['corpsecret']
                ],
                'timeout' => 5
            ]);
            
            $data = json_decode($response->getBody(), true);
            if (isset($data['errcode']) && $data['errcode'] != 0) {
                throw new \Exception("企业微信API错误: {$data['errmsg']}", $data['errcode']);
            }
            // 存储到Redis（有效期减少60秒防过期）
            Cache::set($config['redis_key'], $data['access_token'], $data['expires_in'] - 60);
            return $data['access_token'];
        } catch (\Exception $e) {
            Log::error('企业微信Token获取失败: ' . $e->getMessage());
            throw new HttpException(500, '企业微信服务暂不可用');
        }
    }

    /**
     * HTTP客户端实例
     * @var Client
     */
    protected $httpClient;

    /**
     * 构造函数
     * @param array $config 配置参数
     */
    public function __construct(array $config = [])
    {
        $this->config = array_merge($this->config, $config);
        $this->httpClient = new Client([
            'base_uri' => $this->config['base_uri'],
            'timeout'  => $this->config['timeout']
        ]);
    }

    /**
     * 生成随机Nonce
     * @return string
     */
    protected function generateNonce(): string
    {
        return bin2hex(random_bytes(16));
    }

    /**
     * 生成签名
     * @param array $params 请求参数（不含sign）
     * @return string
     */
    protected function generateSign(array $params): string
    {
        // 1. 排除sign字段并按键名升序排序
        unset($params['sign']);
        ksort($params);

        // 2. 拼接参数字符串
        $signStr = http_build_query($params);

         
        // 3. HMAC-SHA256加密
        return hash_hmac('sha256', $signStr, $this->config['app_secret']);
    }
    public function access_token(){
        $params = [
            'action'    => 'getToken',
            'app_id'    => $this->config['app_id'],
            'nonce'     => $this->generateNonce(),
        ];
    }

    /**
     * 发送异步POST请求
     * @param string $endpoint 接口路径（如 'getData'）
     * @param array $data      业务参数
     * @return \GuzzleHttp\Promise\PromiseInterface
     */
    public function postAsync(string $endpoint, array $data = []): \GuzzleHttp\Promise\PromiseInterface
    {
        
        // 生成基础参数
        $baseParams = [
            'action'    => $endpoint,
            'app_id'    => $this->config['app_id'],
            'nonce'     => $this->generateNonce(),
            'timestamp' => time(),
        ];

        // 合并参数并生成签名
        $params = array_merge($baseParams, $data);
        $params['sign'] = $this->generateSign($params);
           // print_r($params);
        // 发送异步请求
        return $this->httpClient->postAsync($endpoint, [
            'json' => $params
        ])->then(
            // 成功回调
            function ($response) {
                $body = json_decode($response->getBody(), true);
                // 检查业务状态码
                if ($body['code'] != 200) {
                    Log::error('API业务错误', $body);
                    throw new \Exception($body['msg'], $body['code']);
                }
                return $body;
            },
            // 失败回调
            function ($e) {
                Log::error('API请求失败', [
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        );
    }

    /**
     * 并发多个异步请求
     * @param array $requests 请求数组 [['endpoint' => 'getData', 'data' => [...]], ...]
     * @return array
     */
    public function multiPostAsync(array $requests): array
    {
        $promises = [];
        foreach ($requests as $key => $request) {
            $promises[$key] = $this->postAsync($request['endpoint'], $request['data'] ?? []);
        }
        return Promise\Utils::settle($promises)->wait();
    }

    /**
     * 生成带签名的 GET 请求 URL
     * @param string $endpoint 接口路径
     * @param array $params    业务参数
     * @return string 完整URL
     */
    public function buildSignedUrl(string $endpoint, array $params = []): string
    {
        // 基础参数
        $baseParams = [
            'action'    => $endpoint,
            'app_id'    => $this->config['app_id'],
            'nonce'     => $this->generateNonce(),
            'timestamp' => time(),
        ];

        // 合并参数并生成签名
        $allParams = array_merge($baseParams, $params);
        $allParams['sign'] = $this->generateSign($allParams);
        // 构建查询字符串
        $query = http_build_query($allParams);
        //print_r($query);
        return $this->config['base_uri'] . $endpoint . '?' . $query;
    }
    //一下微信代码

    //创建部门
    public function Department($action,$data)
    {
        switch($action){
            case 'create':
                $url = '/cgi-bin/department/'.$action.'?access_token='.$this->getSecret();
                break;
            case 'delete':
                $url = '/cgi-bin/department/'.$action.'?access_token='.$this->getSecret().'&id='.$data['id'];
                break;
            case 'update':
                $url = '/cgi-bin/department/'.$action.'?access_token='.$this->getSecret();
                break;
        }
        $promise = $this->postAsync('getData', [
            'url' => $url,
            'data' => $data
        ]);
        $result_body = $promise->wait(); 
         if($result_body['data']['errcode'] !=0){
            return to_assign(1, $result_body['data']['errmsg'],$result_body['data']['errcode']);
        }
    }

        //创建成员
        public function User($action,$data)
        {
            switch($action){
                case 'create':
                    $url = '/cgi-bin/user/'.$action.'?access_token='.$this->getSecret();
                    break;
                case 'delete':
                    $url = '/cgi-bin/user/'.$action.'?access_token='.$this->getSecret().'&userid='.$data['userid'];
                    break;
                case 'update':
                    $url = '/cgi-bin/user/'.$action.'?access_token='.$this->getSecret();
                    break;
            }
            $promise = $this->postAsync('getData', [
                'url' => $url,
                'data' => $data
            ]);
            $result_body = $promise->wait(); 
             if($result_body['data']['errcode'] !=0){
                return to_assign(1, $result_body['data']['errmsg'],$result_body['data']['errcode']);
            }
        }

        //发送消息
        public function qiyeMessage($users,$title,$content,$msg_link)
        {	
            $workchat = get_config('workchat');
            if($workchat['workweixin']==false){
                return false;
            }
    
    
            $userid = Db::name('Admin')->where([['id','in',$users]])->column('username');
            if (empty($userid)) {
               return false;
            }
            $users = implode('|' ,$userid);
            $corpid=$workchat['corpid'];
            $agentid=$workchat['agentid'];
            $host=$workchat['host'];
            $url = '/cgi-bin/message/send?access_token='.$this->getAccessToken();
            $time_str = date('Y年m月d日 H:i:s',time());
            $data = array(
                'touser' => $users,
                'agentid' => $agentid,
                'msgtype' => 'textcard',
                "textcard" => array(
                    "title" => $title,
                    "description" => "<div class=\"gray\">".$time_str."</div><div class=\"normal\">".$content."</div><div class=\"highlight\">点击【详情】可查看详细内容。</div>",
                    "url" => $host.$msg_link,
                    "btntxt"=>"详情"
                )
            );


            $promise = $this->postAsync('getData', [
                'url' => $url,
                'data' => $data
            ]);
            $result_body = $promise->wait(); 

             if($result_body['data']['errcode'] !=0){
                return false;
               // return to_assign(1, $result_body['data']['errmsg'],$result_body['data']['errcode']);
            }
                return true;
           
        }

         //构造请求登录
         public function Login($action,$data)
         {
            $redirect_uri=urlencode('http://server.gh-life.com:8830/home/<USER>');
            $url='https://open.weixin.qq.com/connect/oauth2/authorize?appid='.get_config('workchat.corpid').'&redirect_uri='.$redirect_uri.'&response_type=code&scope=snsapi_base&agentid='.get_config('workchat.agentid').'&state=STATE#wechat_redirect';
             redirect($url)->send();
            
         }
    
    
}

 