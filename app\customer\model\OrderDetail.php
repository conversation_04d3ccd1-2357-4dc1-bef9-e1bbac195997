<?php
namespace app\customer\model;

use think\Model;
use app\customer\model\Requirement as RequirementModel;
use app\warehouse\model\Inventory as InventoryModel;
use app\engineering\model\Product as ProductModel;
use think\facade\Db;

class OrderDetail extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'customer_order_detail';
    
    // 设置主键
    protected $pk = 'id';

     // 软删除
     use \think\model\concern\SoftDelete;
     protected $deleteTime = 'delete_time';
     protected $defaultSoftDelete = 0;
    
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 关联订单
    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id');
    }
    
    // 关联产品
    public function product()
    {
        return $this->belongsTo('app\engineering\model\Product', 'product_id', 'id');
    }
    
    // 通过主单关联客户
    public function customer()
    {
        return $this->hasOneThrough(
            'app\customer\model\Customer',
            'app\customer\model\Order',
            'id', // Order表的外键
            'id', // Customer表的主键
            'order_id', // OrderDetail表的外键
            'customer_id' // Order表的外键
        );
    }

    
    //检查库存情况  、//审批通过调用

    
    public function checkInventory($order_id)
    {
        $orderDetails = self::where('order_id', $order_id)->select();

        foreach ($orderDetails as $orderDetail) {
            $inventory = InventoryModel::where('product_id', $orderDetail['product_id'])->find();
            if ($inventory['quantity'] < $orderDetail['quantity']) {
                // 库存不足，计算差额并更新orderDetails的gpa
                $difference = $orderDetail['quantity'] - $inventory['quantity'];
                $orderDetail->gpa = $difference;
                $orderDetail->inventory_status = 2; // 库存不足

                $product = ProductModel::where('id', $orderDetail['product_id'])->find();
                if ($product['source_type'] == 1) {
                    // 自产产品，创建生产
                        // 记录关联关系 
                        Db::name('purchase_requirement_order')->insert([
                            'purchase_id' => 0,
                            'product_id' => $orderDetail['product_id'],
                            'order_detail_id' => $orderDetail['id'],
                            'order_id' => $orderDetail['order_id'],
                            'quantity' => $difference,
                            'type' => 0,
                            'create_time' => time()
                        ]);
                    
                }

                //向需求表插入数据
                // $existingRequirement = RequirementModel::where('material_id', $orderDetail['product_id'])
                //     ->where('req_type', 'oa_customer_order_detail')
                //     ->find();

                // if ($existingRequirement) {
                //     // 更新数量
                //     $existingRequirement->$difference;
                //     $existingRequirement->save();
                // } else {
                //     // 插入新数据
                //     $requirement = new RequirementModel();
                //     $requirement->material_id = $orderDetail['product_id'];
                //     $requirement->req_id = $order_id;
                //     $requirement->total_qty = $difference;
                //     $requirement->req_type = 'oa_customer_order_detail';
                //     $requirement->status = 0;
                //     $requirement->created_at = time();
                //     $requirement->save();
                // }

                //先判产品是否是自产，如果是自产，则不进行库存检查
                

            } else {
                // 库存充足，更新inventory_status
                $orderDetail->inventory_status = 1; // 库存充足
            }
            $orderDetail->save();
        }
    }

    //检查需求表
        public function checkRequirement($order_id){
            $orderDetails = self::where('order_id', $order_id)->select();
            foreach ($orderDetails as $orderDetail) {
                $product = ProductModel::where('id', $orderDetail['product_id'])->find();
                if ($product['source_type'] == 1) {
                    // 自产产品，创建生产
                    // 记录关联关系 
                    Db::name('purchase_requirement_order')->insert([
                        'purchase_id' => 0,
                        'product_id' => $orderDetail['product_id'],
                        'order_detail_id' => $orderDetail['id'],
                        'order_id' => $orderDetail['order_id'],
                        'quantity' => $orderDetail['gap'],
                        'type' => 0,
                        'create_time' => time()
                    ]);
                
            }

        }
    }
        

} 