<?php
// 测试JSON解析问题

// 从您提供的数据中提取一个示例
$testJson = '[{"process_id":"7","name":"\\u7ec8\\u68c0","type":"\\u6570\\u636e\\u8bb0\\u5f55","completion_time":"1","time_unit":"\\u5929","processing_type":"\\u81ea\\u5236","inspection_method":"\\u514d\\u68c0","description":"AA","step":"1"},{"process_id":"8","name":"\\u5305\\u88c5","type":"\\u72b6\\u6001\\u8bb0\\u5f55","completion_time":"2","time_unit":"\\u5929","processing_type":"\\u81ea\\u5236","inspection_method":"\\u514d\\u68c0","description":"BB","step":"2"}]';

echo "原始JSON数据:\n";
echo $testJson . "\n\n";

// 方法1：直接解析
echo "方法1：直接解析\n";
$result1 = json_decode($testJson, true);
echo "解析结果: " . (is_array($result1) ? "成功" : "失败") . "\n";
echo "错误信息: " . json_last_error_msg() . "\n";
if (is_array($result1)) {
    foreach ($result1 as $step) {
        echo "工序名称: " . ($step['name'] ?? '未知') . "\n";
    }
}
echo "\n";

// 方法2：处理Unicode转义字符
echo "方法2：处理Unicode转义字符\n";
$processedJson = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function ($match) {
    return mb_convert_encoding(pack('H*', $match[1]), 'UTF-8', 'UCS-2BE');
}, $testJson);

echo "处理后的JSON:\n";
echo $processedJson . "\n";

$result2 = json_decode($processedJson, true);
echo "解析结果: " . (is_array($result2) ? "成功" : "失败") . "\n";
echo "错误信息: " . json_last_error_msg() . "\n";
if (is_array($result2)) {
    foreach ($result2 as $step) {
        echo "工序名称: " . ($step['name'] ?? '未知') . "\n";
    }
}
echo "\n";

// 方法3：使用json_decode的JSON_UNESCAPED_UNICODE选项
echo "方法3：测试正确的JSON格式\n";
$correctJson = json_encode([
    ["process_id" => "7", "name" => "终检", "type" => "数据记录", "completion_time" => "1", "time_unit" => "天", "processing_type" => "自制", "inspection_method" => "免检", "description" => "AA", "step" => "1"],
    ["process_id" => "8", "name" => "包装", "type" => "状态记录", "completion_time" => "2", "time_unit" => "天", "processing_type" => "自制", "inspection_method" => "免检", "description" => "BB", "step" => "2"]
], JSON_UNESCAPED_UNICODE);

echo "正确的JSON格式:\n";
echo $correctJson . "\n";

$result3 = json_decode($correctJson, true);
echo "解析结果: " . (is_array($result3) ? "成功" : "失败") . "\n";
if (is_array($result3)) {
    foreach ($result3 as $step) {
        echo "工序名称: " . ($step['name'] ?? '未知') . "\n";
    }
}
