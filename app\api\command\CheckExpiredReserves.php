<?php
namespace app\api\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\api\service\InventoryReserveService;

class CheckExpiredReserves extends Command
{
    protected function configure()
    {
        $this->setName('check:expired_reserves')
            ->setDescription('检查并处理过期的库存预占记录');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始检查过期预占记录...');
        
        $service = new InventoryReserveService();
        $result = $service->checkExpiredReserves();
        
        if ($result['success']) {
            if (isset($result['count']) && $result['count'] > 0) {
                $output->writeln('成功释放 ' . $result['count'] . ' 条过期预占记录');
            } else {
                $output->writeln('没有发现过期预占记录');
            }
        } else {
            $output->writeln('处理过期预占失败: ' . $result['message']);
        }
        
        $output->writeln('检查完成');
    }
}
