<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 仓库间调拨单模型
 */
class InventoryTransfer extends Model
{
    // 设置表名
    protected $name = 'inventory_transfer';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_PENDING = 0;    // 待审核
    const STATUS_APPROVED = 1;   // 已审核
    const STATUS_COMPLETED = 2;  // 已完成
    const STATUS_CANCELLED = 3;  // 已取消
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'transfer_no'       => 'string',
        'from_warehouse_id' => 'int',
        'to_warehouse_id'   => 'int',
        'status'            => 'int',
        'total_amount'      => 'float',
        'notes'             => 'string',
        'created_by'        => 'int',
        'approved_by'       => 'int',
        'approved_time'     => 'int',
        'create_time'       => 'int',
        'update_time'       => 'int',
    ];
    
    /**
     * 关联源仓库
     */
    public function fromWarehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'from_warehouse_id', 'id');
    }
    
    /**
     * 关联目标仓库
     */
    public function toWarehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'to_warehouse_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\user\model\User', 'created_by', 'id');
    }
    
    /**
     * 关联审核人
     */
    public function approver()
    {
        return $this->belongsTo('app\user\model\User', 'approved_by', 'id');
    }
    
    /**
     * 关联调拨明细
     */
    public function details()
    {
        return $this->hasMany('app\warehouse\model\InventoryTransferDetail', 'transfer_id', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : $this->status;
        $statusArray = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已审核',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];
        
        return isset($statusArray[$status]) ? $statusArray[$status] : '未知';
    }
    
    /**
     * 获取状态数组
     */
    public static function getStatusArr()
    {
        return [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已审核',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];
    }
    
    /**
     * 生成调拨单号
     * 
     * @return string
     */
    public static function generateTransferNo()
    {
        return 'TF' . date('Ymd') . str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 创建调拨单
     * 
     * @param array $data 调拨单数据
     * @param array $details 调拨明细数据
     * @return InventoryTransfer
     * @throws \Exception
     */
    public static function createTransfer($data, $details)
    {
        if (empty($details)) {
            throw new \Exception('调拨明细不能为空');
        }
        
        // 开启事务
        \think\facade\Db::startTrans();
        try {
            // 创建调拨单主表
            $transferData = [
                'transfer_no' => self::generateTransferNo(),
                'from_warehouse_id' => $data['from_warehouse_id'],
                'to_warehouse_id' => $data['to_warehouse_id'],
                'status' => self::STATUS_PENDING,
                'notes' => $data['notes'] ?? '',
                'created_by' => $data['created_by'] ?? 0
            ];
            
            $transfer = self::create($transferData);
            
            // 创建调拨明细
            $totalAmount = 0;
            foreach ($details as $detail) {
                $detailData = [
                    'transfer_id' => $transfer->id,
                    'product_id' => $detail['product_id'],
                    'quantity' => $detail['quantity'],
                    'unit' => $detail['unit'] ?? '',
                    'cost_price' => $detail['cost_price'] ?? 0,
                    'amount' => $detail['quantity'] * ($detail['cost_price'] ?? 0),
                    'notes' => $detail['notes'] ?? ''
                ];
                
                InventoryTransferDetail::create($detailData);
                $totalAmount += $detailData['amount'];
            }
            
            // 更新总金额
            $transfer->total_amount = $totalAmount;
            $transfer->save();
            
            \think\facade\Db::commit();
            return $transfer;
            
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 审核调拨单
     * 
     * @param int $transferId 调拨单ID
     * @param int $approvedBy 审核人ID
     * @return bool
     * @throws \Exception
     */
    public static function approveTransfer($transferId, $approvedBy)
    {
        $transfer = self::find($transferId);
        if (!$transfer) {
            throw new \Exception('调拨单不存在');
        }
        
        if ($transfer->status != self::STATUS_PENDING) {
            throw new \Exception('调拨单状态不正确，当前状态: ' . $transfer->status_text);
        }
        
        $transfer->status = self::STATUS_APPROVED;
        $transfer->approved_by = $approvedBy;
        $transfer->approved_time = time();
        
        return $transfer->save();
    }
    
    /**
     * 完成调拨
     * 
     * @param int $transferId 调拨单ID
     * @return bool
     * @throws \Exception
     */
    public static function completeTransfer($transferId)
    {
        $transfer = self::find($transferId);
        if (!$transfer) {
            throw new \Exception('调拨单不存在');
        }
        
        if ($transfer->status != self::STATUS_APPROVED) {
            throw new \Exception('调拨单状态不正确，当前状态: ' . $transfer->status_text);
        }
        
        $transfer->status = self::STATUS_COMPLETED;
        return $transfer->save();
    }
    
    /**
     * 取消调拨单
     * 
     * @param int $transferId 调拨单ID
     * @return bool
     * @throws \Exception
     */
    public static function cancelTransfer($transferId)
    {
        $transfer = self::find($transferId);
        if (!$transfer) {
            throw new \Exception('调拨单不存在');
        }
        
        if ($transfer->status == self::STATUS_COMPLETED) {
            throw new \Exception('已完成的调拨单不能取消');
        }
        
        $transfer->status = self::STATUS_CANCELLED;
        return $transfer->save();
    }
    
    /**
     * 搜索器：源仓库ID
     */
    public function searchFromWarehouseIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('from_warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：目标仓库ID
     */
    public function searchToWarehouseIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('to_warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：状态
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '' && $value !== null) {
            $query->where('status', $value);
        }
    }
    
    /**
     * 搜索器：时间范围
     */
    public function searchTimeRangeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $timeRange = explode(',', $value);
            if (count($timeRange) == 2) {
                $query->whereBetween('create_time', [strtotime($timeRange[0]), strtotime($timeRange[1])]);
            }
        }
    }
    
    /**
     * 搜索器：关键字（调拨单号）
     */
    public function searchKeywordsAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where(function($q) use ($value) {
                $q->whereOr('transfer_no', 'like', '%' . $value . '%')
                  ->whereOr('notes', 'like', '%' . $value . '%');
            });
        }
    }
}
