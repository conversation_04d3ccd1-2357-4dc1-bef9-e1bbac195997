<?php
/**
 * 测试报工功能
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

use think\facade\Db;

echo "<pre>";
echo "=== 测试报工功能 ===\n\n";

try {
    // 模拟报工数据
    $data = [
        'order_id' => 27,
        'step_id' => 3,  // 钻孔加工（最后一道工序）
        'quantity' => 10,
        'qualified_qty' => 8,
        'work_time' => 2.5,
        'worker_id' => 1,
        'report_date' => date('Y-m-d'),
        'processid' => 58  // 从报工记录中看到的process_id
    ];

    echo "模拟报工数据：\n";
    print_r($data);
    echo "\n";

    // 1. 获取订单信息
    $order = Db::name('produce_order')->where('id', $data['order_id'])->find();
    if (!$order) {
        echo "❌ 订单不存在\n";
        exit;
    }
    echo "✅ 订单信息：{$order['order_no']} - {$order['product_name']}\n";
    echo "   当前完成数量：{$order['completed_qty']}\n\n";

    // 2. 获取当前工序信息
    $currentProcess = Db::name('produce_order_process')
        ->where('order_id', $data['order_id'])
        ->where('step_no', $data['step_id'])
        ->find();
    
    if (!$currentProcess) {
        echo "❌ 工序信息不存在\n";
        exit;
    }
    echo "✅ 当前工序：{$currentProcess['process_name']} (step_no: {$currentProcess['step_no']})\n\n";

    // 3. 获取所有工序，判断是否为最后工序
    $orderProcesses = Db::name('produce_order_process')
        ->where('order_id', $data['order_id'])
        ->order('step_no asc')
        ->select()
        ->toArray();
    
    $maxStepNo = 0;
    foreach ($orderProcesses as $process) {
        if ($process['step_no'] > $maxStepNo) {
            $maxStepNo = $process['step_no'];
        }
    }
    
    $isLastProcess = ($currentProcess['step_no'] == $maxStepNo) ? 1 : 0;
    echo "✅ 工序分析：\n";
    echo "   总工序数：" . count($orderProcesses) . "\n";
    echo "   最大工序号：{$maxStepNo}\n";
    echo "   当前工序号：{$currentProcess['step_no']}\n";
    echo "   是否最后工序：" . ($isLastProcess ? "是" : "否") . "\n\n";

    // 4. 模拟报工记录插入
    $reportData = [
        'order_id' => $data['order_id'],
        'process_code' => $currentProcess['process_code'],
        'process_name' => $currentProcess['process_name'],
        'product_id' => $order['product_id'],
        'product_name' => $order['product_name'],
        'process_id' => $data['processid'],
        'step_id' => $data['step_id'],
        'step_name' => $currentProcess['process_name'],
        'quantity' => $data['quantity'],
        'qualified_qty' => $data['qualified_qty'],
        'unqualified_qty' => $data['quantity'] - $data['qualified_qty'],
        'work_time' => $data['work_time'],
        'worker_id' => $data['worker_id'],
        'worker_name' => 'test_user',
        'report_date' => $data['report_date'],
        'has_exception' => 0,
        'remark' => '测试报工',
        'create_time' => time(),
        'update_time' => time(),
        'is_last_process' => $isLastProcess,
        'step' => 1
    ];

    echo "✅ 准备插入报工记录：\n";
    echo "   工序：{$reportData['step_name']}\n";
    echo "   数量：{$reportData['quantity']}\n";
    echo "   合格数量：{$reportData['qualified_qty']}\n";
    echo "   是否最后工序：" . ($reportData['is_last_process'] ? "是" : "否") . "\n\n";

    // 开始事务
    Db::startTrans();
    
    try {
        // 插入报工记录
        $reportId = Db::name('production_work_report')->insertGetId($reportData);
        echo "✅ 报工记录插入成功，ID：{$reportId}\n\n";

        // 如果是最后工序，更新completed_qty
        if ($isLastProcess == 1) {
            $qualifiedQty = intval($data['qualified_qty']);
            $affected = Db::name('produce_order')
                ->where('id', $data['order_id'])
                ->setInc('completed_qty', $qualifiedQty);
            
            echo "🎯 最后工序报工，更新completed_qty：\n";
            echo "   增加数量：{$qualifiedQty}\n";
            echo "   影响行数：{$affected}\n\n";
            
            // 查询更新后的数量
            $updatedOrder = Db::name('produce_order')->where('id', $data['order_id'])->find();
            echo "✅ 更新后的完成数量：{$updatedOrder['completed_qty']}\n";
        } else {
            echo "ℹ️ 非最后工序，不更新completed_qty\n";
        }

        Db::commit();
        echo "\n🎉 测试成功！\n";

    } catch (\Exception $e) {
        Db::rollback();
        echo "❌ 事务失败：" . $e->getMessage() . "\n";
    }

} catch (\Exception $e) {
    echo "❌ 测试失败：" . $e->getMessage() . "\n";
}

echo "</pre>";
?>
