{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">按单代报</li>
            </ul>
        </div>
    </div>

    <!-- 订单信息栏 -->
    <form class="layui-form gg-form-bar border-x" id="orderInfo">
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" value="订单编号: {$order.order_no}" readonly class="layui-input">
        </div>
        <div class="layui-input-inline" style="width:200px;">
            <input type="text" value="产品: {$order.product_name}" readonly class="layui-input">
        </div>
        <div class="layui-input-inline" style="width:120px;">
            <input type="text" value="数量: {$order.quantity}" readonly class="layui-input">
        </div>
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" value="交期: {$order.delivery_date|date='Y-m-d'}" readonly class="layui-input">
        </div>
        <div class="layui-input-inline" style="width:100px">
            <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
        </div>
    </form>

    <!-- 工序列表 -->
    <div class="layui-card border-x">
        <div class="layui-card-header">
            <h3>工序列表</h3>
        </div>
        <div class="layui-card-body">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="100">工序名称</th>
                        <th width="80">工序类型</th>
                        <th width="80">工序状态</th>
                        <th width="80">逾期状态</th>
                        <th width="80">计价方式</th>
                        <th width="80">标准单价(元)</th>
                        <th width="120">计划开始时间</th>
                        <th width="120">计划结束时间</th>
                        <th width="80">报工数</th>
                        <th width="80">合格数</th>
                        <th width="80">不合格数</th>
                        <th width="80">检验方式</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="orderProcesses" id="process"}
                    <tr data-process-id="{$process.id}">
                        <td>{$process.process_name}</td>
                        <td>{$process.process_type}</td>
                        <td>
                            <span class="layui-badge {if condition="$process.status eq 0"}layui-bg-gray{elseif condition="$process.status eq 1"}layui-bg-blue{elseif condition="$process.status eq 2"}layui-bg-green{else}layui-bg-orange{/if}">
                                {$process.status_text}
                            </span>
                        </td>
                        <td>
                            <span class="layui-badge {$process.overdue_class}">
                                {$process.overdue_text}
                            </span>
                        </td>
                        <td>{$process.processing_type}</td>
                        <td>{$process.standard_price}</td>
                        <td>{$process.planned_start_time_format}</td>
                        <td>{$process.planned_end_time_format}</td>
                        <td>
                            <span class="layui-badge layui-bg-cyan">{$process.report_quantity}</span>
                        </td>
                        <td>
                            <span class="layui-badge layui-bg-green">{$process.report_qualified}</span>
                        </td>
                        <td>
                            <span class="layui-badge layui-bg-red">{$process.report_unqualified}</span>
                        </td>
                        <td>{$process.inspection_method}</td>
                        <td>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="reportProcess({$process.id})">
                                代报工
                            </button>
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 报工弹窗 -->
<div id="reportModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="reportForm" lay-filter="reportForm">
        <input type="hidden" name="order_id" value="{$order.id}">
        <input type="hidden" name="process_id" id="modal_process_id">

        <div class="layui-form-item">
            <label class="layui-form-label">工序名称</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" id="modal_process_name" style="font-size: 14px; color: #333;"></div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">可报工量</label>
            <div class="layui-input-block">
                <div class="layui-form-mid layui-word-aux" id="reportable_quantity" style="font-size: 14px; color: #009688; font-weight: bold;">计算中...</div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">完成数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="quantity" lay-verify="required|number" placeholder="完成数量" class="layui-input" id="quantity_input">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">合格数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="qualified_qty" lay-verify="required|number" placeholder="合格数量" class="layui-input" id="qualified_qty_input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">工时(小时)</label>
                    <div class="layui-input-block">
                        <input type="number" name="work_time" lay-verify="required|number" placeholder="工时" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">报工日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="report_date" id="report_date" placeholder="报工日期" class="layui-input">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">报工人员</label>
            <div class="layui-input-block">
                <select name="worker_id" lay-verify="required">
                    <option value="">请选择报工人员</option>
                    {volist name="workers" id="worker"}
                    <option value="{$worker.id}">{$worker.name}</option>
                    {/volist}
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="备注信息" class="layui-textarea" rows="3"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="submitReport">提交代报</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 数量输入模板 -->
<script type="text/html" id="quantityTpl">
    <input type="number" class="layui-input" placeholder="完成数量" lay-verify="required|number" value="{{d.quantity || ''}}">
</script>

<!-- 合格数量输入模板 -->
<script type="text/html" id="qualifiedQtyTpl">
    <input type="number" class="layui-input" placeholder="合格数量" lay-verify="required|number" value="{{d.qualified_qty || ''}}">
</script>

<!-- 工时输入模板 -->
<script type="text/html" id="workTimeTpl">
    <input type="number" class="layui-input" placeholder="工时" lay-verify="required|number" value="{{d.work_time || ''}}">
</script>

<!-- 报工日期模板 -->
<script type="text/html" id="reportDateTpl">
    <input type="text" class="layui-input report-date" placeholder="报工日期" value="{{d.report_date || new Date().toISOString().split('T')[0]}}">
</script>

<!-- 备注输入模板 -->
<script type="text/html" id="remarkTpl">
    <input type="text" class="layui-input" placeholder="备注" value="{{d.remark || ''}}">
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="operationTpl">
    <button class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</button>
</script>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var tool = layui.tool, form = layui.form, layer = layui.layer, laydate = layui.laydate, $ = layui.jquery;

    // 初始化日期选择器
    laydate.render({
        elem: '#report_date',
        format: 'yyyy-MM-dd',
        value: new Date()
    });

    // 获取可报工数量
    function getReportableQuantity(processId) {
        $.ajax({
            url: '/Produce/order/getReportableQuantity',
            type: 'POST',
            data: {
                order_id: '{$order.id}',
                process_id: processId
            },
            success: function(res) {
                if (res.code === 0) {
                    $('#reportable_quantity').text('最多可报工：' + res.data.reportable_qty + '件');

                    // 设置完成数量的最大值
                    $('#quantity_input').attr('max', res.data.reportable_qty);
                    $('#qualified_qty_input').attr('max', res.data.reportable_qty);
                } else {
                    $('#reportable_quantity').text('获取失败：' + res.msg);
                }
            },
            error: function() {
                $('#reportable_quantity').text('获取可报工数量失败');
            }
        });
    }

    // 报工按钮点击事件
    window.reportProcess = function(processId) {
        // 获取工序信息
        var processRow = $('tr[data-process-id="' + processId + '"]');
        var processName = processRow.find('td:eq(0)').text(); // 第一列是工序名称

        // 设置弹窗数据
        $('#modal_process_id').val(processId);
        $('#modal_process_name').text(processName); // 使用text()而不是val()

        // 重置表单
        $('#reportForm')[0].reset();
        // 重新设置隐藏字段
        $('#modal_process_id').val(processId);
        form.render();

        // 获取可报工数量
        getReportableQuantity(processId);

        // 重新初始化日期选择器
        laydate.render({
            elem: '#report_date',
            format: 'yyyy-MM-dd',
            value: new Date()
        });

        // 打开弹窗
        layer.open({
            type: 1,
            title: '按单代报 - ' + processName,
            content: $('#reportModal'),
            area: ['600px', '480px'],
            btn: false,
            shadeClose: false
        });
    };

    // 单个报工表单提交
    form.on('submit(submitReport)', function(data) {
        var formData = data.field;

        $.ajax({
            url: '/Produce/order/delegate',
            type: 'POST',
            data: {
                order_id: formData.order_id,
                reports: [formData]
            },
            dataType: 'json',
            success: function(res) {
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1}, function() {
                        layer.closeAll();
                        location.reload(); // 刷新页面显示最新状态
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });

        return false;
    });

    // 页面初始化完成
    form.render();
}
</script>
{/block}
