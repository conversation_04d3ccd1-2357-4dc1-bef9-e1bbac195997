<?php
declare (strict_types = 1);
namespace app\api\controller;
use app\api\BaseController;
use app\api\model\EditLog;
use think\facade\Db;
use app\Produce\model\ProductionFeeding;

class Material extends BaseController
{
    /**
     * 获取物料列表（通用）
     * @return \think\Response
     */
    public function getList()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $param = get_params();
        
        // 基础查询条件
        $where = [
            ['delete_time', '=', 0],
            ['status', '=', 1]  // 启用状态
        ];
        
        // 关键词搜索
        if (!empty($param['keywords'])) {
            $where[] = ['title|material_code', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 查询物料基础信息
        $query = Db::name('product')
            ->where($where)
            ->field('id, material_code, title as name, specs, unit, purchase_price');
            
        // 分页查询
        $list = $query->order('material_code asc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
        
        return json([
            'code' => 0,
            'msg' => 'success',
            'count' => $list->total(),
            'data' => $list->items()
        ]);
    }

    /**
     * 获取物料库存信息
     * @return \think\Response
     */
    public function getStock()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $materialId = input('material_id/d', 0);
        $warehouseId = input('warehouse_id/d', 0);

        if ($materialId <= 0) {
            return json(['code' => 1, 'msg' => '物料ID不能为空']);
        }

        // 查询物料信息
        $material = Db::name('product')->where('id', $materialId)->find();
        if (!$material) {
            return json(['code' => 1, 'msg' => '物料不存在']);
        }

        // 查询库存信息
        $stockQuery = Db::name('inventory')
            ->where('product_id', $materialId)
            ->where('status', 1);

        // 如果指定了仓库，则只查询指定仓库的库存
        if ($warehouseId > 0) {
            $stockQuery->where('warehouse_id', $warehouseId);
        }

        $stockInfo = $stockQuery->field('SUM(quantity) as total_quantity')->find();

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'material' => $material,
                'stock' => [
                    'total_quantity' => $stockInfo['total_quantity'] ?? 0,
                    'warehouse_id' => $warehouseId
                ]
            ]
        ]);
    }
} 