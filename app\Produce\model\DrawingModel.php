<?php
declare (strict_types = 1);

namespace app\Produce\model;

use think\Model;
use think\facade\Db;

class DrawingModel extends Model
{
    protected $table = 'oa_drawing';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'drawing_no'      => 'string',
        'name'            => 'string',
        'product_name'    => 'string',
        'material_name'   => 'string',
        'category_id'     => 'int',
        'file_path'       => 'string',
        'file_size'       => 'int',
        'file_type'       => 'string',
        'remark'          => 'text',
        'create_time'     => 'int',
        'update_time'     => 'int',
        'delete_time'     => 'int'
    ];

    /**
     * 获取图纸列表
     * @param array $param 查询参数
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getList($param = [], $page = 1, $limit = 15)
    {
        $where = [];
        
        // 搜索条件
        if (!empty($param['keywords'])) {
            $where[] = ['name|product_name|material_name', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 分类筛选
        if (!empty($param['category_id'])) {
            $where[] = ['category_id', '=', $param['category_id']];
        }
        
        // 查询数据
        $list = $this->where($where)
            ->field('*')
            ->order('create_time desc')
            ->page((int)$page, (int)$limit)
            ->select()
            ->each(function ($item) {
                // 格式化时间
                $item['create_time_format'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                $item['update_time_format'] = date('Y-m-d H:i:s', (int)$item['update_time']);
                
                // 获取分类名称
                if ($item['category_id'] > 0) {
                    $categoryName = Db::name('drawing_category')
                        ->where('id', $item['category_id'])
                        ->value('name');
                    $item['category_name'] = $categoryName ?: '未分类';
                } else {
                    $item['category_name'] = '未分类';
                }
                
                // 格式化文件大小
                if ($item['file_size'] > 0) {
                    $item['file_size_format'] = $this->formatFileSize($item['file_size']);
                } else {
                    $item['file_size_format'] = '-';
                }
                
                return $item;
            })
            ->toArray();
        
        // 获取总数
        $count = $this->where($where)->count();
        
        return [
            'data' => $list,
            'count' => $count
        ];
    }

    /**
     * 获取图纸详情
     * @param int $id 图纸ID
     * @return array|null
     */
    public function getDetail($id)
    {
        $info = $this->find($id);
        if (!$info) {
            return null;
        }
        
        $info = $info->toArray();
        
        // 格式化时间
        $info['create_time_format'] = date('Y-m-d H:i:s', (int)$info['create_time']);
        $info['update_time_format'] = date('Y-m-d H:i:s', (int)$info['update_time']);
        
        // 获取分类信息
        if ($info['category_id'] > 0) {
            $category = Db::name('drawing_category')
                ->where('id', $info['category_id'])
                ->find();
            $info['category_name'] = $category['name'] ?? '未分类';
        } else {
            $info['category_name'] = '未分类';
        }
        
        // 格式化文件大小
        if ($info['file_size'] > 0) {
            $info['file_size_format'] = $this->formatFileSize($info['file_size']);
        } else {
            $info['file_size_format'] = '-';
        }
        
        return $info;
    }

    /**
     * 检查图纸是否被使用
     * @param int $id 图纸ID
     * @return bool
     */
    public function isUsed($id)
    {
        // 检查是否被生产订单使用
        $orderCount = Db::name('produce_order')
            ->where('drawing_id', $id)
            ->count();
        
        return $orderCount > 0;
    }

    /**
     * 获取图纸选项列表（用于下拉选择）
     * @return array
     */
    public function getOptions()
    {
        return $this->field('id, drawing_no, name, product_name')
            ->order('create_time desc')
            ->select()
            ->toArray();
    }
    
    /**
     * 格式化文件大小
     * @param int $size 文件大小（字节）
     * @return string
     */
    private function formatFileSize($size)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
}