# 生产订单入库数量显示优化

## 需求分析

### 问题描述
在生产订单列表页面(`http://tc.xinqiyu.cn:8830/Produce/order/index`)中，入库数量字段需要根据库存流水表(`oa_inventory_transaction`)的关联数据动态显示实际已入库数量。

### 当前状态
- 生产订单列表显示入库数量字段(`warehousing`)
- 入库操作通过`InventoryRealtimeService`服务记录到`oa_inventory_transaction`表
- 关联关系：`ref_type='production_order'`, `ref_id=订单ID`, `transaction_type='in'`

## 技术实现方案

### 1. 数据库关联查询
需要在生产订单模型的`getList`方法中添加入库数量的计算逻辑：

```php
// 从库存流水表查询已入库数量
$alreadyInputQty = Db::name('inventory_transaction')
    ->where('ref_type', 'production_order')
    ->where('ref_id', $order['id'])
    ->where('transaction_type', 'in')
    ->sum('quantity') ?: 0;
```

### 2. 修改位置
- **文件**: `app/Produce/model/Order.php`
- **方法**: `getList()`
- **位置**: 在foreach循环中为每个订单计算入库数量

### 3. 实现步骤

#### 步骤1: 修改Order模型的getList方法
在`app/Produce/model/Order.php`的`getList`方法中，为每个订单添加入库数量计算：

```php
// 在foreach循环中添加
foreach ($list as &$order) {
    // ... 现有代码 ...
    
    // 计算已入库数量
    $order['warehousing'] = $this->getWarehousingQuantity($order['id']);
    
    // ... 其他代码 ...
}
```

#### 步骤2: 添加入库数量计算方法
在Order模型中添加专门的方法来计算入库数量：

```php
/**
 * 获取订单已入库数量
 * @param int $orderId 订单ID
 * @return float 已入库数量
 */
private function getWarehousingQuantity($orderId)
{
    try {
        // 从库存流水表查询已入库数量
        $warehousingQty = Db::name('inventory_transaction')
            ->where('ref_type', 'production_order')
            ->where('ref_id', $orderId)
            ->where('transaction_type', 'in')
            ->sum('quantity') ?: 0;
            
        return floatval($warehousingQty);
    } catch (\Exception $e) {
        \think\facade\Log::error('获取入库数量失败', [
            'order_id' => $orderId,
            'error' => $e->getMessage()
        ]);
        return 0;
    }
}
```

### 4. 相关文件
- **模型文件**: `app/Produce/model/Order.php`
- **视图文件**: `app/Produce/view/order/index.html`
- **控制器**: `app/Produce/controller/Order.php`
- **库存流水表**: `oa_inventory_transaction`

### 5. 数据表关联关系
```sql
-- 查询生产订单的入库记录
SELECT 
    it.quantity,
    it.transaction_no,
    it.create_time,
    it.notes
FROM oa_inventory_transaction it
WHERE it.ref_type = 'production_order'
  AND it.ref_id = {订单ID}
  AND it.transaction_type = 'in'
ORDER BY it.create_time DESC;
```

## 实施计划

### 任务分解
1. ✅ 修改Order模型添加入库数量计算方法
2. ✅ 在getList方法中集成入库数量计算
3. 🔄 测试验证数据显示正确性
4. 待定 优化性能（如需要）

## 已完成的修改

### 1. 添加入库数量计算方法
在`app/Produce/model/Order.php`中添加了`getWarehousingQuantity`方法：

```php
/**
 * 获取订单已入库数量
 * @param int $orderId 订单ID
 * @return float 已入库数量
 */
private function getWarehousingQuantity($orderId)
{
    try {
        // 从库存流水表查询已入库数量
        $warehousingQty = \think\facade\Db::name('inventory_transaction')
            ->where('ref_type', 'production_order')
            ->where('ref_id', $orderId)
            ->where('transaction_type', 'in')
            ->sum('quantity') ?: 0;

        return floatval($warehousingQty);
    } catch (\Exception $e) {
        \think\facade\Log::error('获取入库数量失败', [
            'order_id' => $orderId,
            'error' => $e->getMessage()
        ]);
        return 0;
    }
}
```

### 2. 集成到getList方法
在`getList`方法的foreach循环中添加了入库数量计算：

```php
// 计算已入库数量
$order['warehousing'] = $this->getWarehousingQuantity($order['id']);
```

### 3. 前端兼容性
前端页面(`app/Produce/view/order/index.html`)已经准备好接收`warehousing`字段：
- 列表显示：`field: 'warehousing', title: '入库数量'`
- 入库表单：`value="${data.completed_qty-data.warehousing}"`（计算可入库数量）

## 测试验证

### 验证要点
1. 生产订单列表页面入库数量列是否正确显示
2. 入库操作后数量是否实时更新
3. 入库表单中可入库数量计算是否正确
4. 性能是否满足要求

### 注意事项
- 确保查询性能，考虑添加数据库索引
- 处理异常情况，避免查询失败影响列表显示
- 保持数据一致性，确保入库数量准确反映实际情况
