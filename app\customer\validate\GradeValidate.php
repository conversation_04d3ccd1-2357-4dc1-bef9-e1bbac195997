<?php


namespace app\customer\validate;
use think\Validate;
use think\facade\Db;

class GradeValidate extends Validate
{
	// 自定义验证规则
	protected function checkOne($value,$rule,$data=[])
	{
		$id = isset($data['id'])?$data['id']:0;
		$count = Db::name('CustomerGrade')->where([['title','=',$data['title']],['id','<>',$id],['delete_time','=',0]])->count();
		return $count == 0 ? true : false;
	}
	
    protected $rule = [
		'title' => 'require|checkOne',
		'id' => 'require',
	];

    protected $message = [
		'title.require' => '名称不能为空',
		'title.checkOne' => '同样的名称已经存在',
		'id.require' => '缺少更新条件',
	];
	
    protected $scene = [
        'add' => ['title'],
        'edit' => ['title', 'id'],
    ];
}