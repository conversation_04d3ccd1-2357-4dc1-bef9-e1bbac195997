<?php
// 调试实际出库数量脚本
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化数据库配置
Config::load('config/database.php', 'database');

echo "=== 调试实际出库数量 ===\n\n";

// 查询MR202508110001的领料单明细
$requestNo = 'MR202508110001';
echo "查询领料单: {$requestNo}\n\n";

$details = Db::name('production_material_request_detail')
    ->where('request_no', $requestNo)
    ->field('id, request_no, material_id, material_name, request_quantity, actual_quantity')
    ->select()
    ->toArray();

echo "领料单明细:\n";
foreach ($details as $detail) {
    echo "ID: {$detail['id']}, 物料ID: {$detail['material_id']}, 物料名: {$detail['material_name']}, 申请数量: {$detail['request_quantity']}, 实际数量: {$detail['actual_quantity']}\n";
}

echo "\n";

// 查询对应的出库单
$outbound = Db::name('outbound')
    ->where('ref_type', 'production_material_request')
    ->where('ref_no', $requestNo)
    ->field('id, outbound_no, status, ref_no')
    ->find();

if ($outbound) {
    echo "关联出库单: ID={$outbound['id']}, 单号={$outbound['outbound_no']}, 状态={$outbound['status']}\n\n";
    
    // 查询出库单明细
    $outboundDetails = Db::name('outbound_detail')
        ->where('outbound_id', $outbound['id'])
        ->field('id, product_id, product_name, quantity, actual_quantity')
        ->select()
        ->toArray();
    
    echo "出库单明细:\n";
    foreach ($outboundDetails as $detail) {
        echo "ID: {$detail['id']}, 产品ID: {$detail['product_id']}, 产品名: {$detail['product_name']}, 计划数量: {$detail['quantity']}, 实际数量: {$detail['actual_quantity']}\n";
    }
    
    echo "\n";
    
    // 检查字段匹配情况
    echo "=== 字段匹配检查 ===\n";
    foreach ($outboundDetails as $outDetail) {
        $matchingRequest = null;
        foreach ($details as $reqDetail) {
            if ($reqDetail['material_id'] == $outDetail['product_id']) {
                $matchingRequest = $reqDetail;
                break;
            }
        }
        
        if ($matchingRequest) {
            echo "✅ 匹配: 出库明细产品ID {$outDetail['product_id']} = 领料明细物料ID {$matchingRequest['material_id']}\n";
            echo "   出库实际数量: {$outDetail['actual_quantity']}, 领料实际数量: {$matchingRequest['actual_quantity']}\n";
        } else {
            echo "❌ 不匹配: 出库明细产品ID {$outDetail['product_id']} 在领料明细中找不到对应的物料ID\n";
        }
    }
} else {
    echo "❌ 未找到关联的出库单\n";
}

echo "\n=== 手动测试回写逻辑 ===\n";

// 模拟回写逻辑
if ($outbound && !empty($outboundDetails)) {
    foreach ($outboundDetails as $detail) {
        $actualQty = floatval($detail['actual_quantity']);
        if ($actualQty > 0) {
            echo "尝试更新: request_no={$requestNo}, material_id={$detail['product_id']}, actual_quantity={$actualQty}\n";
            
            // 检查是否存在匹配的记录
            $exists = Db::name('production_material_request_detail')
                ->where('request_no', $requestNo)
                ->where('material_id', $detail['product_id'])
                ->count();
            
            echo "  匹配记录数: {$exists}\n";
        }
    }
}

echo "\n调试完成\n";
?>
