-- 为采购订单表添加自审批需要的字段（如果不存在）
-- 检查并添加 approved_by 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'approved_by' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "approved_by field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN approved_by int(11) DEFAULT 0 COMMENT "审批人ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 approved_time 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'approved_time' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "approved_time field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN approved_time int(11) DEFAULT 0 COMMENT "审批时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保 check_status 字段存在（如果不存在则添加）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'check_status' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "check_status field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN check_status tinyint(1) NOT NULL DEFAULT 0 COMMENT "审核状态，0未审核，1待审核，2已通过，3已拒绝"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保 check_time 字段存在（如果不存在则添加）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'check_time' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "check_time field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN check_time int(11) NOT NULL DEFAULT 0 COMMENT "审核时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保 check_last_uid 字段存在（如果不存在则添加）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'check_last_uid' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "check_last_uid field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN check_last_uid int(11) NOT NULL DEFAULT 0 COMMENT "上一步审核人id"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保 created_by 字段存在（如果不存在则添加）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'purchase_order' 
     AND column_name = 'created_by' 
     AND table_schema = DATABASE()) > 0,
    'SELECT "created_by field already exists"',
    'ALTER TABLE purchase_order ADD COLUMN created_by int(11) DEFAULT 0 COMMENT "创建人ID"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
