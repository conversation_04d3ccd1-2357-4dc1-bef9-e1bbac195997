{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<style>
    .stat-card {
        background: #fff;
        border-radius: 5px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
        margin-bottom: 20px;
    }
    .stat-number {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .stat-label {
        color: #666;
        font-size: 14px;
    }
    .stat-pending { color: #FF5722; }
    .stat-checking { color: #2196F3; }
    .stat-completed { color: #4CAF50; }
    .stat-amount { color: #FF9800; }
</style>

<div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <!-- 统计卡片 -->
            <div class="layui-col-md3">
                <div class="stat-card">
                    <div class="stat-number stat-pending" id="totalChecks">0</div>
                    <div class="stat-label">总盘点单数</div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stat-card">
                    <div class="stat-number stat-checking" id="pendingChecks">0</div>
                    <div class="stat-label">待盘点</div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stat-card">
                    <div class="stat-number stat-completed" id="completedChecks">0</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="stat-card">
                    <div class="stat-number stat-amount" id="totalDifferenceAmount">¥0</div>
                    <div class="stat-label">差异金额</div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>盘点统计查询</h3>
                    </div>
                    <div class="layui-card-body">
                        <!-- 搜索区域 -->
                        <div class="layui-form layui-row layui-col-space10" lay-filter="searchForm">
                            <div class="layui-col-md3">
                                <select name="warehouse_id" lay-search>
                                    <option value="">选择仓库</option>
                                    {volist name="warehouses" id="warehouse"}
                                    <option value="{$warehouse.id}">{$warehouse.name}</option>
                                    {/volist}
                                </select>
                            </div>
                            <div class="layui-col-md3">
                                <input type="text" name="date_range" placeholder="选择时间范围" class="layui-input" id="dateRange">
                            </div>
                            <div class="layui-col-md2">
                                <button class="layui-btn" lay-submit lay-filter="search">
                                    <i class="layui-icon layui-icon-search"></i> 查询
                                </button>
                            </div>
                        </div>

                        <!-- 统计表格 -->
                        <table class="layui-hide" id="statsTable" lay-filter="statsTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
        layui.use(['table', 'form', 'laydate', 'layer', 'tool'], function(){
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            var tool = layui.tool;

            // 初始化日期范围选择器
            laydate.render({
                elem: '#dateRange',
                type: 'date',
                range: true
            });

            // 加载基础数据
            loadStatistics();

            // 渲染统计表格
            table.render({
                elem: '#statsTable',
                url: '/warehouse/inventory_check/index',
                page: true,
                limit: 15,
                limits: [15, 30, 50, 100],
                cols: [[
                    {field: 'check_no', title: '盘点单号', width: 150},
                    {field: 'warehouse.name', title: '仓库', width: 120},
                    {field: 'total_products', title: '产品总数', width: 100, align: 'center'},
                    {field: 'checked_products', title: '已盘点', width: 100, align: 'center'},
                    {field: 'surplus_count', title: '盘盈数量', width: 100, align: 'center', templet: function(d){
                        return d.surplus_count || 0;
                    }},
                    {field: 'deficit_count', title: '盘亏数量', width: 100, align: 'center', templet: function(d){
                        return d.deficit_count || 0;
                    }},
                    {field: 'difference_amount', title: '差异金额', width: 120, align: 'center', templet: function(d){
                        var amount = d.difference_amount || 0;
                        var color = amount > 0 ? '#4CAF50' : (amount < 0 ? '#f56c6c' : '#666');
                        return '<span style="color: ' + color + '">¥' + amount + '</span>';
                    }},
                    {field: 'create_time', title: '创建时间', width: 160, templet: function(d){
                        return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm');
                    }},
                    {field: 'status', title: '状态', width: 100, align: 'center', templet: function(d){
                        var statusMap = {
                            0: '<span style="color: #FF5722;">待盘点</span>',
                            1: '<span style="color: #2196F3;">盘点中</span>',
                            2: '<span style="color: #4CAF50;">已完成</span>',
                            3: '<span style="color: #9E9E9E;">已取消</span>'
                        };
                        return statusMap[d.status] || '未知';
                    }}
                ]]
            });

            // 搜索
            form.on('submit(search)', function(data){
                var searchData = data.field;
                
                // 处理日期范围
                if (searchData.date_range) {
                    var dates = searchData.date_range.split(' - ');
                    if (dates.length === 2) {
                        searchData.start_date = dates[0];
                        searchData.end_date = dates[1];
                    }
                    delete searchData.date_range;
                }

                table.reload('statsTable', {
                    where: searchData,
                    page: {curr: 1}
                });

                // 重新加载统计数据
                loadStatistics(searchData);
                
                return false;
            });

            // 仓库列表已通过服务端渲染

            // 加载统计数据
            function loadStatistics(params) {
                tool.get('/warehouse/inventory_check/statistics', params || {}, function(res) {
                    if (res.code == 0) {
                        var data = res.data;
                        $('#totalChecks').text(data.total_checks || 0);
                        $('#pendingChecks').text(data.pending_checks || 0);
                        $('#completedChecks').text(data.completed_checks || 0);
                        $('#totalDifferenceAmount').text('¥' + (data.total_difference_amount || 0));
                    }
                });
            }
        });
</script>
{/block}
