<?php

namespace app\material\model;

use think\Model;

class Material extends Model
{
    protected $table = 'product';
    
    // 设置字段信息
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'title' => 'string',
        'cate_id' => 'int',
        'material_code' => 'string',
        'barcode' => 'string',
        'unit' => 'string',
        'specs' => 'string',
        'brand' => 'string',
        'producer' => 'string',
        'base_price' => 'decimal',
        'purchase_price' => 'decimal',
        'sale_price' => 'decimal',
        'purchase_cycle' => 'int',
        'description' => 'string',
        'file_ids' => 'string',
        'stock' => 'int',
        'status' => 'int',
        'admin_id' => 'int',
        'create_time' => 'int',
        'update_time' => 'int',
        'update_id' => 'int',
        'delete_time' => 'int',
        'source_type' => 'int',
        'code' => 'string',
        'type' => 'int'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = false;



    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '停用',
            1 => '启用'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
    /**
     * 获取物料类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            0 => '成品',
            1 => '半成品'
        ];
        return $types[$data['type']] ?? '成品';
    }

    /**
     * 获取物料来源文本
     */
    public function getSourceTypeTextAttr($value, $data)
    {
        $sources = [
            1 => '自产',
            2 => '外购'
        ];
        return $sources[$data['source_type']] ?? '自产';
    }

    /**
     * 创建时间格式化
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['create_time']);
    }

    /**
     * 更新时间格式化
     */
    public function getUpdateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['update_time']);
    }
}