-- 单位管理表
CREATE TABLE IF NOT EXISTS `oa_unit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(20) NOT NULL COMMENT '单位名称',
  `precision` tinyint(1) NOT NULL DEFAULT '0' COMMENT '精度(小数位数)',
  `type` varchar(10) NOT NULL DEFAULT '四舍五入' COMMENT '含入类型：四舍五入、进位',
  `remark` varchar(100) DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-开启，0-禁用',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单位管理表';

-- 插入默认数据
INSERT INTO `oa_unit` (`name`, `precision`, `type`, `remark`, `status`, `admin_id`, `create_time`, `update_time`) VALUES
('件', 0, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('平米', 0, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('片', 0, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('PCS-包材', 1, '进位', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('PCS', 6, '进位', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('套', 6, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('KG', 6, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('个', 0, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('米', 6, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('支', 6, '四舍五入', '', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());