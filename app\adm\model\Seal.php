<?php

namespace app\adm\model;
use think\model;
use think\facade\Db;
class Seal extends Model
{
    /**
    * 获取分页列表
    * @param $where
    * @param $param
    */
    public function datalist($where, $whereOr, $param)
    {
		$rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
		$order = empty($param['order']) ? 'id desc' : $param['order'];
        try {
            $list = self::where($where)
			->where(function ($query) use($whereOr) {
				if (!empty($whereOr)){
					$query->whereOr($whereOr);
				}
			})
			->order($order)
			->paginate(['list_rows'=> $rows])
			->each(function ($item, $key){
				$item->check_status_str = check_status_name($item->check_status);
				$item->use_time = date('Y-m-d', $item->use_time);
				$item->admin_name = Db::name('Admin')->where('id','=',$item->admin_id)->value('name');
				$item->use_dname = Db::name('Department')->where('id','=',$item->did)->value('title');
				$item->seal_cate = Db::name('SealCate')->where('id','=',$item->seal_cate_id)->value('title');
			});
			return $list;
        } catch(\Exception $e) {
            return ['code' => 1, 'data' => [], 'msg' => $e->getMessage()];
        }
    }
    /**
    * 添加数据
    * @param $param
    */
    public function add($param)
    {
		$insertId = 0;
        try {
			$param['create_time'] = time();
			$insertId = self::strict(false)->field(true)->insertGetId($param);
			add_log('add', $insertId, $param);
        } catch(\Exception $e) {
			return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
		return to_assign(0,'操作成功',['return_id'=>$insertId]);
    }

    /**
    * 编辑信息
    * @param $param
    */
    public function edit($param)
    {
        try {
            $param['update_time'] = time();
            self::where('id', $param['id'])->strict(false)->field(true)->update($param);
			add_log('edit', $param['id'], $param);
        } catch(\Exception $e) {
			return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
		return to_assign(0,'操作成功',['return_id'=> $param['id']]);
    }
	
    /**
    * 根据id获取信息
    * @param $id
    */
    public function getById($id)
    {
        $info = self::find($id);
		$info['admin_name'] = Db::name('Admin')->where('id','=',$info['admin_id'])->value('name');
		$info['use_dname'] = Db::name('Department')->where('id','=',$info['did'])->value('title');
		$info['seal_cate'] = Db::name('SealCate')->where('id','=',$info['seal_cate_id'])->value('title');
		if($info['start_time']>0){
			$info['start_time'] = date('Y-m-d',$info['start_time']);
			$info['end_time'] = date('Y-m-d',$info['end_time']);
		}
		else{
			$info['start_time'] = '';
			$info['end_time'] = '';	
		}
		if($info['use_time']>0){
			$info['use_time'] = date('Y-m-d',$info['use_time']);
		}
		else{
			$info['use_time'] = '';
		}
		$status_str = '未使用';
		if($info['status'] == 1){
			$status_str = '已使用';
			if($info['is_borrow']==1){
				$status_str = '已外借';
			}
		}
		if($info['status'] == 2){
			$status_str = '已归还';
		}
		$info['status_str'] = $status_str;
		return $info;
    }

    /**
    * 删除信息
    * @param $id
    * @param $type
    * @return array
    */
    public function delById($id,$type=0)
    {
		if($type==0){
			//逻辑删除
			try {
				$param['delete_time'] = time();
				self::where('id', $id)->update(['delete_time'=>time()]);
				add_log('delete', $id);
			} catch(\Exception $e) {
				return to_assign(1, '操作失败，原因：'.$e->getMessage());
			}
		}
		else{
			//物理删除
			try {
				self::destroy($id);
				add_log('delete', $id);
			} catch(\Exception $e) {
				return to_assign(1, '操作失败，原因：'.$e->getMessage());
			}
		}
		return to_assign();
    }
}

