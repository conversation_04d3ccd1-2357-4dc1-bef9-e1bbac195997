{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-1">新增采购品</h3>
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray-2">采购品名称<font>*</font></td>
			<td colspan="3"><input type="text" name="title" autocomplete="off" placeholder="请输入采购品名称" lay-verify="required" lay-reqText="请输入采购品名称" class="layui-input"></td>
			<td class="layui-td-gray-2">采购品分类<font>*</font></td>
			<td>
				<select name="cate_id" lay-verify="required" lay-reqText="请选择分类">
					<option value="">请选择分类</option>
					{volist name=":set_recursion(get_base_data('PurchasedCate'))" id="v"}
					<option value="{$v.id}">{$v.title}</option>
					{/volist}
				</select>
			</td>
			<td class="layui-td-gray" rowspan="3">缩略图</td>
			<td rowspan="3" style="width: 150px; vertical-align:top:">
				<div class="layui-upload">
					<button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="test1">缩略图(尺寸：240x136)</button>
					<div class="layui-upload-list" id="demo1" style="width: 150px; height:90px; overflow: hidden;">
						<img src="" style="max-width: 100%; height:90px;" />
						<input type="hidden" name="thumb" value="">
					</div>
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">采购品编码<font>*</font></td>
			<td><input type="text" name="code" autocomplete="off" value="{:get_codeno('PR')}" readonly class="layui-input" lay-verify="required" lay-reqText="请输入采购品编码"></td>
			<td class="layui-td-gray">单位<font>*</font></td>
			<td><input type="text" name="unit" autocomplete="off" value="" class="layui-input" lay-verify="required" lay-reqText="请输入采购品单位"></td>
			<td class="layui-td-gray">规格</td>
			<td><input type="text" name="specs" autocomplete="off" placeholder="请输入采购品规格" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">采购价(元)<font>*</font></td>
			<td><input type="text" name="purchase_price" autocomplete="off" placeholder="请输入采购品采购价" class="layui-input" lay-verify="required|number" lay-reqText="请输入采购价"></td>
			<td class="layui-td-gray">是否实物<font>*</font></td>
			<td colspan="3">
				<input type="radio" name="is_object" value="1" title="是" checked>
				<input type="radio" name="is_object" value="2" title="否" >
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">
				<div class="layui-input-inline">采购品附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="7" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					<input type="hidden" id="fileBoxInput" data-type="file" name="file_ids" value="">
				</div>
			</td>
		</tr> 
		<tr>
			<td class="layui-td-gray" style="vertical-align:top;">采购品描述<font>*</font></td>
			<td colspan="7">
				<textarea name="content" placeholder="请输入内容" class="layui-textarea" id="container" style="border:0;padding:0"></textarea>
			</td>
		</tr>
	</table>
	<div class="pt-1">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','tinymce','uploadPlus'];
function gouguInit() {
	var form = layui.form,tool=layui.tool,upload = layui.upload,uploadPlus = layui.uploadPlus;
	
    //封面上传
    var uploadInst = upload.render({
		elem: '#test1'
		, url: "/api/index/upload"
		, done: function (res) {
			layer.msg(res.msg);
			if (res.code == 0) {
				//上传成功
				$('#demo1 input').attr('value', res.data.id);
				$('#demo1 img').attr('src', res.data.filepath);
			}
		}
    });
	
    //编辑器初始化
	var editor = layui.tinymce;
	var edit = editor.render({
		selector: "#container",
		images_upload_url: '/api/index/upload/sourse/tinymce',//图片上传接口
		height: 480
	});	
	
	//附件上传
	var attachment = new uploadPlus({
		"target":'uploadBtn',
		"targetBox":'uploadBox'
	});
	
	//监听提交
	form.on('submit(webform)', function(data){
		data.field.content = tinyMCE.editors['container'].getContent();
		if (data.field.content == '') {
			layer.msg('请先完善采购品的描述内容');
			return false;
		}
		let callback = function (e) {
			layer.msg(e.msg);
			if (e.code == 0) {
				tool.sideClose(1000);				
			}
		}
		let clickbtn = $(this);
		tool.post("/contract/purchased/add", data.field, callback,clickbtn);
		return false;
	});
}
</script>
{/block}
<!-- /脚本 -->