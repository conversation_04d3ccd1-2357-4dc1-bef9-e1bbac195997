# 发货单重构方案

## 问题分析

### 当前问题
1. **数据库表不存在错误**：`oa_inventory` 表不存在
2. **库存查询逻辑过时**：使用了废弃的库存表结构
3. **发货流程不规范**：没有按照标准的出库流程处理

### 错误位置
**文件**：`app/customer/controller/Delivery.php`
**方法**：`save()`
**错误行**：第503行
```php
$inventoryQty = \think\facade\Db::name('inventory')
    ->where('product_id', $orderItem['product_id'])
    ->where('status', 1)
    ->sum('available_quantity') ?: 0;
```

## 重构方案

### 设计思路
参照生产领料单(`/Produce/MaterialRequest/index`)的模式：
1. **发货单创建** → **生成出库单** → **仓库出库执行**
2. **使用统一的出库流程** → `/warehouse/Outbound/index`
3. **使用新的库存系统** → `oa_inventory_realtime` + `oa_inventory_transaction`

### 实际数据库表结构
```
oa_outbound                    # 出库单主表
oa_outbound_detail            # 出库单明细表
oa_outbound_approval_log      # 出库审批日志
oa_outbound_package           # 出库包装
oa_outbound_package_detail    # 出库包装明细
oa_outbound_picking_detail    # 出库拣货明细
oa_outbound_picking_task      # 出库拣货任务
oa_outbound_shipment          # 出库发运
```

### 数据流转模式

#### 当前领料单模式（参考）
```
生产领料单 → 创建oa_outbound → 仓库执行出库 → 库存流水记录
MaterialRequest → createOutboundOrdersByWarehouse → InventoryRealtimeService → inventory_transaction
```

#### 新的发货单模式（目标）
```
客户发货单 → 创建oa_outbound → 仓库执行出库 → 库存流水记录
Delivery → createOutboundFromDelivery → InventoryRealtimeService → inventory_transaction
```

## 实施步骤

### 第一步：修复当前发货单的库存查询错误 ✅
1. ✅ 将`oa_inventory`表查询改为`oa_inventory_realtime`表
2. ✅ 更新库存验证逻辑
3. ✅ 使用新的库存服务进行库存检查

### 第二步：重构发货单保存逻辑 ✅
1. ✅ 移除直接的库存操作
2. ✅ 改为创建出库单到`warehouse/Outbound`
3. ✅ 使用`PendingOutbound`模型添加到待出库清单

### 第三步：集成出库流程 ✅
1. ✅ 发货单创建后自动生成出库单
2. ✅ 出库单通过仓库模块执行
3. ✅ 出库完成后更新发货单状态

## 已完成的修复

### 1. 库存查询错误修复
**文件**：`app/customer/controller/Delivery.php`
**位置**：第503行
**修复内容**：
```php
// 修复前（错误）
$inventoryQty = \think\facade\Db::name('inventory')
    ->where('product_id', $orderItem['product_id'])
    ->where('status', 1)
    ->sum('available_quantity') ?: 0;

// 修复后（正确）
$inventoryQty = \think\facade\Db::name('inventory_realtime')
    ->where('product_id', $orderItem['product_id'])
    ->sum('available_quantity') ?: 0;
```

### 2. 发货单流程重构完成 ✅
**修复前问题**：
- 使用不存在的 `oa_warehouse_pending_outbound` 表
- 调用不存在的 `PendingOutbound::addFromCustomerOrder()` 方法

**修复后实现**：
- 发货单创建 → `createOutboundFromDelivery()` → 直接创建 `oa_outbound` 出库单
- 仓库人员在 `/warehouse/Outbound/index` 处理出库
- 使用 `InventoryRealtimeService` 执行库存操作
- 自动记录到 `oa_inventory_transaction` 流水表

**新增方法**：
1. `createOutboundFromDelivery()` - 从发货单创建出库单
2. `generateOutboundNo()` - 生成出库单号（格式：CK+日期+序号）

**数据流转**：
```
客户发货单 → oa_outbound(销售出库) → 仓库执行 → 库存更新
outbound_type: 'sales' (销售出库)
ref_type: 'customer_order' (关联销售订单)
ref_id: 销售订单ID
ref_no: '销售订单号/发货单号' (如: XS20250813001/FH20250813001)
customer_id: 客户ID (用于显示客户名称)
```

## 技术实现

### 1. 库存查询修复
```php
// 修复前（错误）
$inventoryQty = \think\facade\Db::name('inventory')
    ->where('product_id', $orderItem['product_id'])
    ->where('status', 1)
    ->sum('available_quantity') ?: 0;

// 修复后（正确）
$inventoryQty = \think\facade\Db::name('inventory_realtime')
    ->where('product_id', $orderItem['product_id'])
    ->sum('available_quantity') ?: 0;
```

### 2. 发货单保存逻辑重构
```php
// 参照领料单模式，创建出库单
$result = \app\warehouse\model\PendingOutbound::addFromCustomerDelivery($delivery->id, $pendingOutboundItems);
```

### 3. 出库单数据结构
```php
$pendingOutboundItems[] = [
    'product_id' => $orderItem['product_id'],
    'product_name' => $orderItem['product_name'],
    'quantity' => $item['delivery_qty'],
    'unit' => $orderItem['unit'],
    'specs' => $orderItem['specs'],
    'material_code' => $orderItem['material_code'],
    'warehouse_id' => 0, // 由仓库人员决定
    'warehouse_name' => '',
    'location_id' => 0, // 由仓库人员决定
    'location_name' => '',
    'priority' => 2, // 中等优先级
    'remark' => $item['remark'] ?? ''
];
```

## 相关文件

### 需要修改的文件
1. `app/customer/controller/Delivery.php` - 主要修复文件
2. `app/warehouse/model/PendingOutbound.php` - 可能需要添加客户发货支持
3. `app/customer/model/Delivery.php` - 可能需要状态更新

### 参考文件
1. `app/Produce/controller/MaterialRequest.php` - 领料单实现参考
2. `app/warehouse/controller/Outbound.php` - 出库流程参考
3. `app/warehouse/service/InventoryRealtimeService.php` - 库存服务参考

## 完整修复总结

### ✅ 已完成的所有修改

#### 1. 库存查询错误修复
- **位置**：`app/customer/controller/Delivery.php` 第503行
- **修复**：`Db::name('inventory')` → `Db::name('inventory_realtime')`

#### 2. 发货单保存逻辑重构
- **移除**：不存在的 `PendingOutbound::addFromCustomerOrder()` 调用
- **新增**：`createOutboundFromDelivery()` 方法直接创建出库单
- **新增**：`generateOutboundNo()` 方法生成出库单号

#### 3. 出库单数据结构
```php
// 出库单主表 (oa_outbound)
'outbound_type' => 'customer_delivery'
'ref_type' => 'customer_delivery'
'ref_id' => 发货单ID
'ref_no' => 发货单号
'status' => 2 // 已审核，可直接出库

// 出库单明细 (oa_outbound_detail)
包含产品信息、数量、单价、金额等完整信息
```

### 修复后的完整流程
1. **客户下单** → 创建发货单
2. **发货单保存** → 自动生成 `oa_outbound` 出库单
3. **仓库人员** → 在 `/warehouse/Outbound/index` 执行出库
4. **库存系统** → 使用 `InventoryRealtimeService` 更新库存
5. **流水记录** → 自动记录到 `oa_inventory_transaction`

### 技术优势
1. **统一出库流程**：与领料单使用相同的出库模式
2. **库存数据一致性**：使用新的实时库存系统
3. **流程可追溯**：完整的库存流水记录
4. **无依赖问题**：不依赖不存在的表或方法

## 库存锁定机制集成

### 需要添加的功能
在创建发货单时，需要检查和创建库存锁定：

1. **检查现有锁定**：查询是否已有该订单的库存锁定记录
2. **创建库存锁定**：如果没有锁定记录，需要锁定库存防止被其他订单占用
3. **使用锁定库存**：出库时优先使用已锁定的库存

## API接口表名映射修复

### 问题描述
发货记录API接口报错：`SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sem_erp_oa.oa_customer_delivery' doesn't exist`

### 根本原因
- **前端调用**：`/api/index/get_list?name=customer_delivery&action_id=102`
- **实际表名**：`oa_customer_order_delivery`
- **API直接使用**：传入的name参数作为表名，导致表名不匹配

### 修复方案
在 `app/api/controller/Index.php` 的 `get_list` 方法中添加表名映射：

```php
// 表名映射，处理前端传入的逻辑表名到实际数据库表名的转换
$tableMapping = [
    'customer_delivery' => 'customer_order_delivery',  // 客户发货记录
    'customer_payment' => 'customer_order_payment',    // 客户付款记录
];

// 如果存在映射，使用映射后的表名
$actualTableName = isset($tableMapping[$name]) ? $tableMapping[$name] : $name;
```

## 仓库出库状态显示功能

### 需求背景
发货记录显示"待发货"状态，但仓库实际已经出库，需要显示真实的仓库出库状态。

### 实现方案
1. **关联查询出库单**：通过 `ref_id` 和 `ref_type` 关联 `oa_outbound` 表
2. **返回仓库状态字段**：
   - `warehouse_status` - 仓库出库状态文本
   - `warehouse_status_color` - 状态颜色（用于前端显示）

### 状态映射
```php
CASE
    WHEN o.status IS NULL THEN "未创建出库单"
    WHEN o.status = 0 THEN "草稿"
    WHEN o.status = 1 THEN "已提交"
    WHEN o.status = 2 THEN "已审核"
    WHEN o.status = 3 THEN "部分出库"
    WHEN o.status = 4 THEN "全部出库"
    WHEN o.status = 5 THEN "已取消"
    ELSE "未知状态"
END as warehouse_status
```

### 颜色映射
- `gray` - 未创建/草稿
- `orange` - 已提交
- `blue` - 已审核
- `green` - 部分出库/全部出库
- `red` - 已取消

## 关联查询逻辑修复

### 问题描述
API接口返回了与本发货单无关的出库单信息，显示了错误的仓库状态。

### 根本原因
原始关联条件：`o.ref_id = d.order_id AND o.ref_type = "customer_order"`
- 只匹配了订单ID和类型
- 没有匹配具体的发货单号
- 导致同一订单下的所有出库单都被关联上

### 修复方案
修正关联条件：`o.ref_id = d.order_id AND o.ref_type = "customer_order" AND o.ref_no LIKE CONCAT("%/", d.delivery_no)`

### 关联逻辑说明
出库单的 `ref_no` 字段格式：`销售订单号/发货单号`
- 例如：`XS20250813001/FH20250813001`
- 通过 `LIKE CONCAT("%/", d.delivery_no)` 精确匹配发货单号
- 确保每个发货单只关联自己对应的出库单

## 前端页面仓库状态列显示

### 修改文件
`app/customer/view/order/view.html` - 订单详情页面

### 修改内容

#### 1. 表格头部增加仓库状态列
```html
<th width="120">仓库状态</th>
```

#### 2. JavaScript数据处理
```javascript
// 仓库状态显示逻辑
var warehouseStatusText = '';
if (item.warehouse_status) {
    var colorClass = 'layui-bg-gray'; // 默认颜色
    switch(item.warehouse_status_color) {
        case 'gray': colorClass = 'layui-bg-gray'; break;
        case 'orange': colorClass = 'layui-bg-orange'; break;
        case 'blue': colorClass = 'layui-bg-blue'; break;
        case 'green': colorClass = 'layui-bg-green'; break;
        case 'red': colorClass = 'layui-bg-red'; break;
    }
    warehouseStatusText = '<span class="layui-badge ' + colorClass + '">' + item.warehouse_status + '</span>';
} else {
    warehouseStatusText = '<span class="layui-badge layui-bg-gray">未创建</span>';
}
```

#### 3. 表格行数据增加仓库状态列
```javascript
html += '<td>' + warehouseStatusText + '</td>';
```

### 显示效果
- **未创建出库单** → 灰色标签 "未创建"
- **草稿** → 灰色标签 "草稿"
- **已提交** → 橙色标签 "已提交"
- **已审核** → 蓝色标签 "已审核"
- **部分出库** → 绿色标签 "部分出库"
- **全部出库** → 绿色标签 "全部出库"
- **已取消** → 红色标签 "已取消"

## 完成发货方法重构

### 重构背景
原有的 `complete` 方法逻辑过于复杂，包含了大量的订单明细更新和复杂的状态判断逻辑。

### 重构目标
简化完成发货的逻辑，只关注核心功能：
1. 更新物流单号和发货日期
2. 更新发货单状态为已完成
3. 检查订单是否全部发货完成并更新订单状态

### 新的实现逻辑

#### 1. 参数验证
```php
$id = $param['id'] ?? '';
$logistics_no = $param['logistics_no'] ?? '';
$delivery_time = $param['delivery_time'] ?? '';
```

#### 2. 更新发货单信息
```php
Db::name('customer_order_delivery')
    ->where('id', $id)
    ->update([
        'status' => 2, // 已完成
        'logistics_no' => $logistics_no,
        'delivery_time' => $delivery_time,
        'handler_id' => $this->uid,
        'handler_name' => session('admin.nickname'),
        'handle_time' => date('Y-m-d H:i:s'),
        'update_time' => date('Y-m-d H:i:s')
    ]);
```

#### 3. 订单状态检查逻辑
```php
private function checkAndUpdateOrderStatus($orderId)
{
    // 获取订单所有商品的总数量
    $orderTotalQty = Db::name('customer_order_detail')
        ->where('order_id', $orderId)
        ->sum('quantity');

    // 获取所有已完成发货的数量
    $deliveredTotalQty = Db::name('customer_order_delivery_item')
        ->alias('di')
        ->join('customer_order_delivery d', 'di.delivery_id = d.id')
        ->where('di.order_id', $orderId)
        ->where('d.status', 2) // 只统计已完成的发货
        ->sum('di.delivery_qty');

    // 如果已发货数量 >= 订单总数量，则订单完成发货
    if ($deliveredTotalQty >= $orderTotalQty) {
        Db::name('customer_order')
            ->where('id', $orderId)
            ->update([
                'status' => 2, // 已发货
                'update_time' => date('Y-m-d H:i:s')
            ]);
    }
}
```

### 重构优势
1. **逻辑简化**：移除了复杂的订单明细更新逻辑
2. **性能提升**：减少了数据库操作次数
3. **维护性好**：代码结构清晰，易于理解和维护
4. **功能聚焦**：专注于核心的发货完成功能

### API接口
- **URL**: `POST /customer/delivery/complete`
- **参数**:
  - `id`: 发货单ID
  - `logistics_no`: 物流单号
  - `delivery_time`: 发货日期 (格式: YYYY-MM-DD)

### 库存锁定数据结构
```sql
oa_inventory_lock 表：
- ref_type: 'customer_order' (客户订单锁定)
- ref_id: 订单ID
- ref_no: 订单号
- status: 1=锁定中, 2=已使用, 3=已释放
```

### 实现步骤 ✅
1. ✅ 在 `createOutboundFromDelivery` 方法中添加库存锁定检查
2. ✅ 添加 `checkAndCreateInventoryLocks` 方法检查现有锁定
3. ✅ 添加 `createInventoryLock` 方法创建库存锁定记录
4. ✅ 集成到发货单保存流程中

### 新增的库存锁定逻辑

#### 1. 库存锁定检查方法
```php
private function checkAndCreateInventoryLocks($orderId, $deliveryId, $outboundItems)
{
    foreach ($outboundItems as $item) {
        // 检查现有锁定数量
        $existingLock = Db::name('inventory_lock')
            ->where('ref_type', 'customer_order')
            ->where('ref_id', $orderId)
            ->where('product_id', $item['product_id'])
            ->where('status', 1)
            ->sum('quantity') ?: 0;

        // 计算需要补充锁定的数量
        $needLockQty = $item['quantity'] - $existingLock;

        if ($needLockQty > 0) {
            $this->createInventoryLock($orderId, $deliveryId, $item, $needLockQty);
        }
    }
}
```

#### 2. 库存锁定创建方法
```php
private function createInventoryLock($orderId, $deliveryId, $item, $quantity)
{
    // 查找可用库存
    $availableStocks = Db::name('inventory_realtime')
        ->where('product_id', $item['product_id'])
        ->where('available_quantity', '>', 0)
        ->order('available_quantity desc')
        ->select();

    // 按库存分配锁定
    foreach ($availableStocks as $stock) {
        $lockQuantity = min($remainingQuantity, $stock['available_quantity']);

        // 创建锁定记录并更新实时库存
        // ...
    }
}
```

## 最终修复状态

### ✅ 已解决的所有错误
1. **`oa_inventory` 表不存在** → 使用 `oa_inventory_realtime`
2. **`oa_warehouse_pending_outbound` 表不存在** → 直接创建 `oa_outbound`
3. **生成出库单号失败** → 简化生成逻辑，参照领料单模式
4. **出库类型错误** → 改为 `'sales'`（销售出库）
5. **缺少客户信息** → 添加 `customer_id` 和关联销售订单
6. **业务单号优化** → `ref_no` 显示为 `销售订单号/发货单号` 格式
7. **日期格式错误** → 修复 `date()` 函数的时间戳参数类型检查
8. **状态显示错误** → 修复详情页面状态常量和按钮逻辑
9. **发货状态同步** → 根据出库状态动态显示发货状态
10. **撤销参数错误** → 修复前后端参数名不一致问题
11. **表名错误** → 修复join查询中错误的表名 `delivery` → `customer_order_delivery`
12. **库存解锁失败** → 修复锁定和解锁时ref_type不一致的问题
13. **`oa_material_requirement` 表不存在** → 移除物料需求表依赖，改为使用库存分配服务
14. **反审核被库存锁定阻止** → 移除库存锁定检查，反审核时自动清理锁定记录
15. **反审核未清理分配请求** → 修复状态过滤条件，使用数字状态 `[1,2]` 而非字符串
16. **重复审核创建重复分配请求** → 添加重复检查逻辑，存在时更新数量而非创建新记录
17. **BOM表不存在错误** → 修复所有BOM相关代码，使用新表 `oa_material_bom` 和 `oa_material_bom_detail`
18. **前端BOM清单显示错误** → 修复前端调用API时缺少订单数量参数，使用API返回的计算数据
19. **出库执行锁定处理错误** → 修复按实际出库数量减少锁定，支持部分发货场景

### ✅ 完整的库存锁定机制
1. **检查现有锁定** → `checkAndCreateInventoryLocks()`
2. **自动补充锁定** → `createInventoryLock()`
3. **智能库存分配** → 优先使用库存多的仓库
4. **状态同步** → 更新 `oa_inventory_realtime` 和 `oa_inventory_lock`

### 🔄 最终数据流转
```
客户发货单 → 检查/创建库存锁定 → 生成oa_outbound → 仓库执行出库 → 库存流水记录
```

### 🎯 最终显示效果

#### 发货单列表页面 (`/customer/delivery/index`)：
- **发货单号**：FH202508130001
- **订单编号**：XS202507070002
- **客户名称**：T95
- **出库状态**：已审核（蓝色标签）
- **出库单号**：CK202508132107（可点击跳转）
- **出库人**：赵员工1
- **出库时间**：2025-08-13 02:26:00
- **创建时间**：2025-08-13 10:28:27

#### 出库单列表页面 (`/warehouse/Outbound/index`)：
- **类型**：销售出库（蓝色标签）
- **客户**：正确显示客户名称
- **业务单号**：`销售: XS20250813001/FH20250813001`
  - 前半部分：销售订单号（便于追溯原始订单）
  - 后半部分：发货单号（便于关联具体发货指令）
- **状态**：已审核（可以直接执行出库）

#### 出库单详情页面 (`/warehouse/outbound/detail.html`)：
- **状态显示**：已审核（蓝色标签）
- **操作按钮**：执行出库（因为状态=2已审核）
- **状态流转**：草稿→已提交→已审核→部分出库→全部出库

## 发货单列表优化

### 新增的出库状态跟踪功能

#### 后端数据扩展
添加 `getOutboundStatusInfo()` 方法，为每个发货单获取：
- **出库单ID和单号**：便于跳转查看详情
- **出库状态**：实时显示仓库处理进度
- **出库人员**：显示具体的操作人员
- **出库时间**：显示处理的具体时间
- **进度描述**：用户友好的状态说明

#### 前端列表优化
精简列表显示结构：
- **发货单号**：发货指令编号
- **订单编号**：销售订单编号
- **客户名称**：客户信息
- **出库状态**：仓库出库单的状态（新增，带颜色标签）
- **出库单号**：可点击跳转到出库单详情（新增）
- **出库人**：仓库操作人员（新增）
- **出库时间**：仓库处理时间（新增）
- **创建时间**：发货单创建时间

移除的列：
- ~~发货状态~~：与出库状态重复，简化显示
- ~~创建人~~：不是关键信息，节省空间

#### 状态映射关系
```
出库单状态 → 发货状态显示 → 业务含义
none → 等待生成出库单 → 系统准备中
0 → 等待仓库操作 → 仓库准备中
1 → 等待仓库操作 → 等待审核
2 → 等待仓库操作 → 等待出库
3 → 正在出库 → 仓库执行中
4 → 出库完成 → 可以发货
5 → 已取消 → 出库取消
```

#### 撤销业务逻辑
- **允许撤销**：出库状态 ≤ 2（未开始实际出库操作）
- **禁止撤销**：出库状态 ≥ 3（已开始或完成出库）
- **撤销操作**：
  1. 更新发货单状态为"已取消"
  2. 删除关联的出库单及明细
  3. 解除所有库存锁定（更新锁定表状态为3，释放实时库存）
  4. 记录操作日志

## 🔄 销售订单审核机制简化

### 移除物料需求表依赖
- **移除**：`oa_material_requirement` 表的所有操作
- **改为**：使用 `InventoryAllocationService` 统一处理库存分配和锁定

### 新的审核流程
1. **库存检查**：检查每个产品的库存是否满足订单需求
2. **满足部分锁定**：有库存的部分直接锁定（`oa_inventory_lock`）
3. **不足部分分配**：库存不足的部分写入分配表（`oa_inventory_allocation_request`）
4. **状态管理**：根据分配结果设置订单状态（已审核/待补货）

## 🔄 库存锁定机制重构方案

### 当前问题分析
1. **重复锁定**：销售订单审核时锁定 + 发货单创建时再次锁定
2. **锁定不一致**：销售订单锁定用`customer_order`，发货单锁定用`customer_order_delivery`
3. **解锁困难**：无法准确找到对应的锁定记录

### 推荐方案：基于销售订单的统一锁定

#### 阶段1：销售订单审核时
```sql
-- 锁定全部订单库存
INSERT INTO oa_inventory_lock (
    product_id, warehouse_id, quantity,
    ref_type = 'customer_order',
    ref_id = {order_id},
    ref_no = {order_no},
    status = 1, -- 锁定中
    notes = '销售订单锁定'
);
```

#### 阶段2：发货单创建时
```sql
-- 不重新锁定，而是标记部分库存为"使用中"
UPDATE oa_inventory_lock SET
    status = 2, -- 部分使用
    notes = CONCAT(notes, '; 发货单使用：', {delivery_no}, '，数量：', {delivery_qty})
WHERE ref_type='customer_order' AND ref_id={order_id} AND product_id={product_id};

-- 如果需要精确追踪，可以拆分锁定记录
INSERT INTO oa_inventory_lock (
    product_id, warehouse_id, quantity = {delivery_qty},
    ref_type = 'customer_order',
    ref_id = {order_id},
    ref_no = {order_no},
    status = 2, -- 使用中
    notes = '发货单使用：' + {delivery_no}
);
```

#### 阶段3：发货单取消时
```sql
-- 恢复锁定状态或删除使用记录
UPDATE oa_inventory_lock SET
    status = 1, -- 重新锁定
    notes = CONCAT(notes, '; 发货单取消，恢复锁定')
WHERE ref_type='customer_order' AND ref_id={order_id}
  AND notes LIKE '%发货单使用：{delivery_no}%';
```

### 优势
1. **统一管理**：所有锁定都基于销售订单
2. **避免重复**：不会重复锁定同一库存
3. **精确追踪**：通过notes字段追踪具体用途
4. **支持多次发货**：一个订单可以分多次发货
5. **智能复用**：优先使用订单预锁定，不足时才额外锁定
6. **状态管理**：预锁定(1) → 使用中(2) → 已释放(3)

## 🔧 实际实现的优化方案

### 发货时的库存处理
```php
// 1. 优先使用订单预锁定库存
$orderLocks = 查找订单预锁定记录(ref_type='customer_order', ref_id=订单ID, status=1);

foreach ($orderLocks as $lock) {
    if (发货数量 == 锁定数量) {
        // 完全使用：更新状态为已使用
        UPDATE status=2, notes=追加发货单信息
    } else {
        // 部分使用：拆分锁定记录
        UPDATE 原记录 quantity=剩余数量
        INSERT 新记录 quantity=使用数量, status=2
    }
}

// 2. 预锁定不足时，额外锁定库存
if (还有剩余数量) {
    额外锁定可用库存(status=2直接标记为已使用)
}
```

### 取消时的库存处理
```php
// 查找所有相关锁定记录（通过notes匹配发货单号）
$lockRecords = 查找锁定记录(notes包含发货单号);

foreach ($lockRecords as $lock) {
    if ($lock['status'] == 2) {
        // 已使用记录：直接释放
        UPDATE status=3, notes=追加释放信息
    } else {
        // 预锁定记录：恢复原状态
        UPDATE 清理发货单相关信息，恢复预锁定状态
    }

    // 释放实时库存
    UPDATE inventory_realtime 减少locked_quantity，增加available_quantity
}
```

### 测试验证
现在可以测试发货功能：
- 发货单列表：`http://tc.xinqiyu.cn:8830/customer/delivery/index`
- 预期：显示完整的出库状态跟踪信息
- 功能：点击出库单号可跳转到仓库出库详情
- 状态：实时显示仓库处理进度和操作人员
