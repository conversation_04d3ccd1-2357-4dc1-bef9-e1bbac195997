<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 调拨单明细模型
 */
class InventoryTransferDetail extends Model
{
    // 设置表名
    protected $name = 'inventory_transfer_detail';
    
    // 不使用自动时间戳
    protected $autoWriteTimestamp = false;
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'transfer_id' => 'int',
        'product_id'  => 'int',
        'quantity'    => 'float',
        'unit'        => 'string',
        'cost_price'  => 'float',
        'amount'      => 'float',
        'notes'       => 'string',
    ];
    
    /**
     * 关联调拨单
     */
    public function transfer()
    {
        return $this->belongsTo('app\warehouse\model\InventoryTransfer', 'transfer_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\product\model\Product', 'product_id', 'id');
    }
    
    /**
     * 根据调拨单ID获取明细列表
     * 
     * @param int $transferId 调拨单ID
     * @return \think\Collection
     */
    public static function getDetailsByTransferId($transferId)
    {
        return self::where('transfer_id', $transferId)
            ->with(['product'])
            ->select();
    }
    
    /**
     * 批量创建调拨明细
     * 
     * @param int $transferId 调拨单ID
     * @param array $details 明细数据数组
     * @return bool
     */
    public static function batchCreate($transferId, $details)
    {
        $data = [];
        foreach ($details as $detail) {
            $data[] = [
                'transfer_id' => $transferId,
                'product_id' => $detail['product_id'],
                'quantity' => $detail['quantity'],
                'unit' => $detail['unit'] ?? '',
                'cost_price' => $detail['cost_price'] ?? 0,
                'amount' => $detail['quantity'] * ($detail['cost_price'] ?? 0),
                'notes' => $detail['notes'] ?? ''
            ];
        }
        
        return self::insertAll($data);
    }
    
    /**
     * 计算调拨明细总金额
     * 
     * @param int $transferId 调拨单ID
     * @return float
     */
    public static function getTotalAmount($transferId)
    {
        return self::where('transfer_id', $transferId)->sum('amount') ?: 0;
    }
    
    /**
     * 获取调拨明细统计信息
     * 
     * @param int $transferId 调拨单ID
     * @return array
     */
    public static function getTransferSummary($transferId)
    {
        $details = self::where('transfer_id', $transferId)->select();
        
        $totalQuantity = 0;
        $totalAmount = 0;
        $productCount = 0;
        
        foreach ($details as $detail) {
            $totalQuantity += $detail->quantity;
            $totalAmount += $detail->amount;
            $productCount++;
        }
        
        return [
            'product_count' => $productCount,
            'total_quantity' => $totalQuantity,
            'total_amount' => $totalAmount
        ];
    }
}
