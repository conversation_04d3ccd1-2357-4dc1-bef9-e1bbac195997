<?php
declare (strict_types = 1);

namespace app\Produce\controller;

use app\base\BaseController;
use app\Produce\model\DrawingModel;
use app\Produce\model\DrawingCategoryModel;
use think\facade\View;
use think\facade\Db;

class Drawing extends BaseController
{
    /**
     * 主页面
     */
    public function index()
    {
        return view();
    }
    
    /**
     * 获取图纸列表数据
     */
    public function getList()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 检查字段是否存在，避免SQL错误
            try {
                // 先检查表结构
                $fields = Db::getFields('oa_drawing');
                $hasProductId = isset($fields['product_id']);
                $hasProcessId = isset($fields['process_id']);
                
                if ($hasProductId && !empty($param['product_id']) && $param['product_id'] != '0') {
                    $where[] = ['product_id', '=', $param['product_id']];
                }
                
                if ($hasProcessId && !empty($param['process_id']) && $param['process_id'] != '0') {
                    $where[] = ['process_id', '=', $param['process_id']];
                }
                
                if (!empty($param['keywords'])) {
                    $where[] = ['name|product_name|material_name|drawing_no', 'like', '%' . $param['keywords'] . '%'];
                }
                
                // 确保 limit 是整数类型
                $limit = isset($param['limit']) ? (int)$param['limit'] : 15;
                
                $list = DrawingModel::where($where)
                    ->order('create_time desc')
                    ->paginate([
                        'list_rows' => $limit,
                        'page' => isset($param['page']) ? (int)$param['page'] : 1,
                        'query' => $param
                    ])
                    ->each(function ($item) {
                        $item['create_time_format'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                        $item['update_time_format'] = date('Y-m-d H:i:s', (int)$item['update_time']);
                        $item['file_size_format'] = $this->formatFileSize((int)$item['file_size']);
                    });
                
                return table_assign(0, '', $list);
                
            } catch (\Exception $e) {
                // 如果出现错误，返回空数据
                return json([
                    'code' => 0,
                    'msg' => '',
                    'count' => 0,
                    'data' => []
                ]);
            }
        } else {
            return view();
        }
    }
    
    /**
     * 添加/编辑页面
     */
    public function add()
    {
        $id = input('id/d', 0);
        if ($id > 0) {
            $info = DrawingModel::find($id);
            if (!$info) {
                $this->error('图纸不存在');
            }
            $this->assign('info', $info);
        }
        
        // 获取分类列表
        $categoryList = DrawingCategoryModel::where('status', 1)->select();
        $this->assign('categoryList', $categoryList);
        
        return view();
    }
    
    /**
     * 保存图纸数据
     */
    public function save()
    {
        $param = get_params();
        
        // 使用验证器验证数据
        $validate = new \app\Produce\validate\DrawingValidate();
        if (!$validate->check($param)) {
            return json(['code' => 1, 'msg' => $validate->getError()]);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 处理图纸数据
            $data = [
                'name' => $param['name'],
                'product_name' => $param['product_name'] ?? '',
                'material_name' => $param['material_name'] ?? '',
                'category_id' => (int)($param['category_id'] ?? 0),
                'remark' => $param['remark'] ?? '',
                'update_time' => time()
            ];
            
            // 检查字段是否存在再添加
            $fields = Db::getFields('oa_drawing');
            if (isset($fields['product_id'])) {
                $data['product_id'] = (int)($param['product_id'] ?? 0);
            }
            if (isset($fields['process_id'])) {
                $data['process_id'] = (int)($param['process_id'] ?? 0);
            }
            
            // 处理文件信息
            if (!empty($param['file_path'])) {
                $data['file_path'] = $param['file_path'];
                $data['file_name'] = $param['file_name'] ?? '';
                $data['file_size'] = $param['file_size'] ?? 0;
                $data['file_type'] = $param['file_type'] ?? '';
            }
            
            if(isset($param['id']) && $param['id'] > 0) {
                // 编辑图纸
                DrawingModel::where('id', $param['id'])->update($data);
            } else {
                // 新增图纸
                $data['create_time'] = time();
                $data['drawing_no'] = $this->generateDrawingNo();
                DrawingModel::create($data);
            }
            
            // 提交事务
            Db::commit();
            
            return json(['code' => 0, 'msg' => '保存成功']);
        } catch(\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '保存失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 查看图纸详情页面
     */
    public function view()
    {
        $id = input('id/d', 0);
        if ($id <= 0) {
            $this->error('参数错误');
        }
        
        $info = DrawingModel::find($id);
        if (!$info) {
            $this->error('图纸不存在');
        }
        
        $info['create_time_format'] = date('Y-m-d H:i:s', $info['create_time']);
        $info['update_time_format'] = date('Y-m-d H:i:s', $info['update_time']);
        $info['file_size_format'] = $this->formatFileSize($info['file_size']);
        
        $this->assign('info', $info);
        return view();
    }
    
    /**
     * 删除图纸
     */
    public function delete()
    {
        $id = input('id/d', 0);
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $info = DrawingModel::find($id);
        if (!$info) {
            return json(['code' => 1, 'msg' => '图纸不存在']);
        }
        
        try {
            // 删除文件
            if ($info['file_path'] && file_exists(public_path() . $info['file_path'])) {
                unlink(public_path() . $info['file_path']);
            }
            
            DrawingModel::destroy($id);
            
            return json(['code' => 0, 'msg' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 文件上传
     */
    public function upload()
    {
        $file = request()->file('file');
        if (!$file) {
            return json(['code' => 1, 'msg' => '请选择文件']);
        }
        
        try {
            $savename = \think\facade\Filesystem::disk('public')->putFile('drawing', $file);
            
            if ($savename) {
                $fileInfo = [
                    'name' => $file->getOriginalName(),
                    'path' => '/storage/' . $savename,
                    'size' => $file->getSize(),
                    'type' => $file->getOriginalExtension(),
                    'size_format' => $this->formatFileSize($file->getSize()),
                    'can_preview' => in_array(strtolower($file->getOriginalExtension()), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf']),
                    'url' => '/storage/' . $savename
                ];
                
                return json(['code' => 0, 'msg' => '上传成功', 'data' => $fileInfo]);
            } else {
                return json(['code' => 1, 'msg' => '上传失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '上传失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 生成图纸编号
     */
    private function generateDrawingNo()
    {
        $prefix = 'TZ' . date('Ymd');
        $count = DrawingModel::where('drawing_no', 'like', $prefix . '%')->count();
        return $prefix . str_pad($count + 1, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 格式化文件大小
     */
    private function formatFileSize($size)
    {
        if ($size == 0) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = floor(log($size, 1024));
        
        return round($size / pow(1024, $i), 2) . ' ' . $units[$i];
    }
}