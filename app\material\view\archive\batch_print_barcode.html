<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>批量打印二维码</title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body {
                margin: 0;
                padding: 0;
                background: white;
            }
            .print-container {
                background: white;
                padding: 0;
                border-radius: 0;
                box-shadow: none;
                max-width: none;
                margin: 0;
                width: 100%;
            }
            .labels-grid {
                gap: 15px;
                padding: 10px;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
            .barcode-label {
                width: 100%;
                height: 220px;
                border: 2px solid #333;
                padding: 15px;
                margin: 0;
                display: flex;
                background: white;
                page-break-inside: avoid;
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            .label-left {
                flex: 1;
                padding-right: 15px;
            }
            .label-right {
                width: 140px;
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .label-info {
                font-size: 14px;
                line-height: 1.8;
                margin-bottom: 4px;
                color: #333;
            }
            .label-info strong {
                display: inline-block;
                width: 80px;
                font-weight: bold;
                color: #000;
            }
            .qr-code {
                width: 120px;
                height: 120px;
                border: 1px solid #ddd;
                margin-bottom: 10px;
            }
            .page-info {
                text-align: center;
                font-size: 12px;
                color: #666;
                margin-top: 10px;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .print-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .labels-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: flex-start;
        }
        
        .barcode-label {
            width: 350px;
            height: 220px;
            border: 2px solid #333;
            padding: 15px;
            display: flex;
            background: white;
            page-break-inside: avoid;
            flex-shrink: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        .label-left {
            flex: 1;
            padding-right: 15px;
        }
        
        .label-right {
            width: 120px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .label-info {
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 3px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .label-info strong {
            display: inline-block;
            width: 60px;
            font-weight: bold;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        
        .page-info {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        .print-buttons {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .summary {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-buttons no-print">
            <div class="summary">共 {$materials|count} 个物料标签</div>
            <button class="btn" onclick="window.print()">打印全部</button>
            <button class="btn btn-secondary" onclick="window.close()">关闭</button>
        </div>

        <div class="labels-grid">
            {volist name="materials" id="material" key="index"}
            <div class="barcode-label">
                <div class="label-left">
                    <div class="label-info"><strong>物料名称:</strong> {$material.title}</div>
                    <div class="label-info"><strong>物料编号:</strong> {$material.material_code}</div>
                    <div class="label-info"><strong>物料规格:</strong> {$material.specs|default='-'}</div>
                    <div class="label-info"><strong>单位:</strong> {$material.unit|default='-'}</div>
                    <div class="label-info"><strong>打印时间:</strong> {$printTime}</div>
                    <div class="label-info"><strong>备注:</strong></div>
                </div>
                <div class="label-right">
                    <img src="{$material.qr_image_url}" alt="二维码" class="qr-code" />
                    <div class="page-info">{$index}-1</div>
                </div>
            </div>
            {/volist}
        </div>
    </div>
</body>
</html>
