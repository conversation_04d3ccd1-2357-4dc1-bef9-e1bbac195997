<?php
declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;
use app\engineering\model\Product as ProductModel;

/**
 * 物料API控制器
 */
class Product extends BaseController
{
    /**
     * 获取所有物料的库存信息
     */
    public function getMaterialInventory()
    {
        $param = get_params();
        $where = [];
        
        // 添加关键词搜索
        if (!empty($param['keywords'])) {
            $where[] = ['p.title|p.material_code', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 添加分类搜索
        if (!empty($param['cate_id'])) {
            // 获取所有子分类ID
            $categoryIds = [$param['cate_id']];
            $childCategories = Db::name('ProductCate')
                ->where('pid', $param['cate_id'])
                ->where('delete_time', 0)
                ->column('id');
            if (!empty($childCategories)) {
                $categoryIds = array_merge($categoryIds, $childCategories);
            }
            $where[] = ['p.cate_id', 'in', $categoryIds];
        }

        // 添加BOM筛选 - 使用子查询检查是否有BOM
        if (isset($param['has_bom']) && $param['has_bom'] !== '') {
            if ($param['has_bom'] == 1) {
                // 查询有BOM的产品
                $where[] = ['p.id', 'in', function($query) {
                    $query->name('bom_master')
                        ->where('delete_time', 0)
                        ->where('check_status', 2) // 已审核的BOM
                        ->field('product_id');
                }];
            } else {
                // 查询没有BOM的产品
                $where[] = ['p.id', 'not in', function($query) {
                    $query->name('bom_master')
                        ->where('delete_time', 0)
                        ->where('check_status', 2) // 已审核的BOM
                        ->field('product_id');
                }];
            }
        }
        
        // 添加仓库筛选
        if (!empty($param['warehouse_id'])) {
            $warehouse_id = $param['warehouse_id'];
        } else {
            $warehouse_id = 0; // 默认为0，表示所有仓库
        }

        // 查询物料库存信息
        $query = Db::name('Product')->alias('p')
            ->field([
                'p.id as material_id',
                'p.material_code',
                'p.title',
                'p.specs',
                'p.unit',
                'p.source_type',
                'IFNULL(SUM(i.quantity), 0) as total_inventory',
                'IFNULL(SUM(i.quantity - i.locked_quantity - i.reserved_quantity), 0) as available_inventory',
                '0 as in_transit' // 暂时设为0，后面再更新
            ])
            ->leftJoin('inventory i', 'p.id = i.product_id')
            ->where($where)
            ->where('p.delete_time', 0)
            ->group('p.id');

        if ($warehouse_id > 0) {
            $query->where('i.warehouse_id', $warehouse_id);
        }

        $count = $query->count();
        
        // 确保page和limit是整数
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 15;
        
        $list = $query
            ->page($page)
            ->limit($limit)
            ->select()
            ->each(function ($item) {
                // 查询在途库存（采购订单中未完全入库的数量）
                $inTransitQuantity = Db::name('purchase_order_detail')
                    ->alias('d')
                    ->join('purchase_order o', 'd.order_id = o.id')
                    ->where('d.product_id', $item['material_id'])
                    ->where('o.status', 'in', [2, 3]) // 已审核，部分入库的订单
                    ->where('d.received_quantity', '<', Db::raw('d.quantity')) // 未完全入库
                    ->sum(Db::raw('d.quantity - d.received_quantity'));
                
                $item['in_transit'] = $inTransitQuantity ?? 0;
                
                // 获取仓库明细
                $item['warehouse_details'] = Db::name('inventory')
                    ->alias('i')
                    ->join('warehouse w', 'i.warehouse_id = w.id')
                    ->field([
                        'i.warehouse_id', 
                        'w.name as warehouse_name',
                        'SUM(i.quantity) as total_inventory', 
                        'SUM(i.quantity - i.locked_quantity - i.reserved_quantity) as available_inventory'
                    ])
                    ->where('i.product_id', $item['material_id'])
                    ->group('i.warehouse_id')
                    ->select();
                
                // 检查是否有BOM
                $hasBom = Db::name('bom_master')
                    ->where('product_id', $item['material_id'])
                    ->where('delete_time', 0)
                    ->where('check_status', 2) // 已审核的BOM
                    ->count() > 0;
                    
                $item['has_bom'] = $hasBom ? 1 : 0;
                
                return $item;
            });

        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'total' => $count,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($count / $limit),
                'data' => $list
            ]
        ]);
    }

    /**
     * 获取物料的BOM详情
     */
    public function getBomDetail()
    {
        $param = get_params();
        
        if (empty($param['material_id'])) {
            return json(['code' => 1, 'msg' => '物料ID不能为空']);
        }
        
        // 查询有效的BOM ID
        $bomId = Db::name('bom_master')
            ->where('product_id', $param['material_id'])
            ->where('check_status', 2) // 已审核状态
            ->where('delete_time', 0)
            ->order('create_time', 'desc') // 取最新的BOM
            ->value('id');
            
        if (!$bomId) {
            return json(['code' => 1, 'msg' => '该物料没有有效的BOM']);
        }
        
        // 查询BOM清单
        $bomList = Db::name('bom_item')->alias('b')
            ->join('product p', 'b.material_id = p.id')
            ->field([
                'p.material_code',
                'p.title',
                'p.specs',
                'p.unit',
                'b.qty as quantity',
                '(SELECT SUM(i.quantity) FROM oa_inventory i WHERE i.product_id = p.id) as stock'
            ])
            ->where('b.bom_id', $bomId)
            ->where('b.delete_time', 0)
            ->select();
            
        return json(['code' => 0, 'msg' => 'success', 'data' => $bomList]);
    }
} 