{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <form class="layui-form" lay-filter="drawingForm">
        <input type="hidden" name="id" value="{$info.id|default=0}" />
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">* 工序名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" value="{$info.name|default=''}" placeholder="请输入工序名称" class="layui-input" lay-verify="required" maxlength="100" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">产品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="product_name" value="{$info.product_name|default=''}" placeholder="请输入产品名称" class="layui-input" maxlength="100" />
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">物料名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="material_name" value="{$info.material_name|default=''}" placeholder="请输入物料名称" class="layui-input" maxlength="100" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">分类</label>
                    <div class="layui-input-block">
                        <select name="category_id">
                            <option value="0">请选择分类</option>
                            {volist name="categoryList" id="category"}
                            <option value="{$category.id}" {if condition="$info.category_id == $category.id"}selected{/if}>{$category.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">图纸文件</label>
            <div class="layui-input-block">
                <div class="layui-upload">
                    <button type="button" class="layui-btn layui-btn-normal" id="uploadBtn">
                        <i class="layui-icon layui-icon-upload"></i> 选择文件
                    </button>
                    <div class="layui-upload-list">
                        <div id="uploadPreview">
                            {if condition="$info.file_path"}
                            <div class="file-item">
                                <i class="layui-icon layui-icon-file"></i>
                                <span class="file-name">{$info.file_name|default='图纸文件'}</span>
                                <span class="file-size">({$info.file_size_format|default=''})</span>
                                <a href="{$info.file_path}" target="_blank" class="layui-btn layui-btn-xs layui-btn-primary">预览</a>
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeFile()">删除</button>
                            </div>
                            {/if}
                        </div>
                        <div id="uploadProgress" style="display: none;">
                            <div class="layui-progress">
                                <div class="layui-progress-bar" lay-percent="0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">支持上传 PDF、DWG、DXF、JPG、PNG 等格式文件，大小不超过 50MB</div>
            </div>
        </div>

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea" maxlength="500">{$info.remark|default=''}</textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="drawingSubmit">保存</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'form', 'upload', 'element'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool, upload = layui.upload, element = layui.element;
        
        // 当前上传的文件信息
        var currentFile = null;
        
        // 文件上传
        var uploadIns = upload.render({
            elem: '#uploadBtn',
            url: '/Produce/drawing/upload',
            accept: 'file',
            acceptMime: '.pdf,.dwg,.dxf,.jpg,.jpeg,.png,.gif,.bmp',
            size: 51200, // 50MB
            progress: function(n, elem, e) {
                // 显示上传进度
                $('#uploadProgress').show();
                element.progress('uploadProgress', n + '%');
            },
            done: function(res, index, upload) {
                $('#uploadProgress').hide();
                if (res.code === 0) {
                    currentFile = res.data;
                    showFilePreview(res.data);
                    layer.msg('上传成功');
                } else {
                    layer.msg('上传失败：' + res.msg);
                }
            },
            error: function(index, upload) {
                $('#uploadProgress').hide();
                layer.msg('上传失败，请重试');
            }
        });
        
        // 显示文件预览
        function showFilePreview(fileInfo) {
            var html = '<div class="file-item">';
            html += '<i class="layui-icon layui-icon-file"></i>';
            html += '<span class="file-name">' + fileInfo.name + '</span>';
            html += '<span class="file-size">(' + fileInfo.size_format + ')</span>';
            if (fileInfo.can_preview) {
                html += '<a href="' + fileInfo.url + '" target="_blank" class="layui-btn layui-btn-xs layui-btn-primary">预览</a>';
            }
            html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removeFile()">删除</button>';
            html += '</div>';
            
            $('#uploadPreview').html(html);
        }
        
        // 删除文件
        window.removeFile = function() {
            layer.confirm('确定要删除这个文件吗？', {icon: 3, title: '提示'}, function(index) {
                $('#uploadPreview').empty();
                currentFile = null;
                layer.close(index);
            });
        };
        
        // 表单提交
        form.on('submit(drawingSubmit)', function(data) {
            // 添加文件信息到表单数据
            if (currentFile) {
                data.field.file_path = currentFile.path;
                data.field.file_name = currentFile.name;
                data.field.file_size = currentFile.size;
                data.field.file_type = currentFile.type;
            }
            
            let callback = function (e) {
                layer.msg(e.msg);
                if (e.code == 0) {
                    // 刷新父页面表格
                    if (parent.window.drawingTableIns) {
                        parent.window.drawingTableIns.reload();
                    }
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            }
            tool.post("/Produce/drawing/save", data.field, callback);
            return false;
        });
    }
</script>
{/block}

{block name="style"}
<style>
.file-item {
    padding: 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-item .layui-icon {
    font-size: 20px;
    color: #FF5722;
}

.file-name {
    flex: 1;
    font-weight: bold;
}

.file-size {
    color: #999;
    font-size: 12px;
}

.layui-upload-list {
    margin-top: 10px;
}

.layui-progress {
    margin: 10px 0;
}

/* 表单样式优化 */
.layui-form-label {
    width: 100px;
}

.layui-input-block {
    margin-left: 130px;
}

.layui-textarea {
    min-height: 80px;
}
</style>
{/block}