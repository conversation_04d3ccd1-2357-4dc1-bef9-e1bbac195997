<?php
/**
 * 库存调拨API控制器
 * 为移动端和第三方系统提供调拨管理接口
 */

namespace app\warehouse\controller\api;

use app\api\BaseController;
use app\warehouse\service\InventoryTransferService;
use think\Request;
use think\response\Json;

class InventoryTransferApi extends BaseController
{
    protected $transferService;

    public function initialize()
    {
        parent::initialize();
        $this->transferService = new InventoryTransferService();
    }

    /**
     * 获取调拨单列表
     * @param Request $request
     * @return Json
     */
    public function index(Request $request): Json
    {
        try {
            $params = $request->param();
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $status = $params['status'] ?? '';
            $fromWarehouseId = $params['from_warehouse_id'] ?? '';
            $toWarehouseId = $params['to_warehouse_id'] ?? '';
            $keywords = $params['keywords'] ?? '';

            $where = [];
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if (!empty($fromWarehouseId)) {
                $where[] = ['from_warehouse_id', '=', $fromWarehouseId];
            }
            if (!empty($toWarehouseId)) {
                $where[] = ['to_warehouse_id', '=', $toWarehouseId];
            }

            $result = $this->transferService->getTransferList($where, $page, $limit, $keywords);

            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取调拨单详情
     * @param Request $request
     * @return Json
     */
    public function detail(Request $request): Json
    {
        try {
            $id = $request->param('id');
            if (empty($id)) {
                return $this->error('参数错误');
            }

            $detail = $this->transferService->getTransferDetail($id);
            if (!$detail) {
                return $this->error('调拨单不存在');
            }

            return $this->success('获取成功', $detail);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建调拨单
     * @param Request $request
     * @return Json
     */
    public function create(Request $request): Json
    {
        try {
            $params = $request->param();
            
            // 验证必要参数
            $required = ['from_warehouse_id', 'to_warehouse_id', 'details'];
            foreach ($required as $field) {
                if (empty($params[$field])) {
                    return $this->error("参数{$field}不能为空");
                }
            }

            // 验证仓库不能相同
            if ($params['from_warehouse_id'] == $params['to_warehouse_id']) {
                return $this->error('源仓库和目标仓库不能相同');
            }

            // 验证明细数据
            if (!is_array($params['details']) || empty($params['details'])) {
                return $this->error('调拨明细不能为空');
            }

            foreach ($params['details'] as $detail) {
                if (empty($detail['product_id']) || empty($detail['quantity']) || $detail['quantity'] <= 0) {
                    return $this->error('调拨明细数据不完整或数量无效');
                }
            }

            $data = [
                'from_warehouse_id' => $params['from_warehouse_id'],
                'to_warehouse_id' => $params['to_warehouse_id'],
                'notes' => $params['notes'] ?? '',
                'details' => $params['details'],
                'creator_id' => $this->uid
            ];

            $result = $this->transferService->createTransfer($data);
            
            return $this->success('创建成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 审核调拨单
     * @param Request $request
     * @return Json
     */
    public function approve(Request $request): Json
    {
        try {
            $params = $request->param();
            $id = $params['id'] ?? '';
            $approveNotes = $params['approve_notes'] ?? '';
            
            if (empty($id)) {
                return $this->error('调拨单ID不能为空');
            }

            $result = $this->transferService->approveTransfer($id, $this->uid, $approveNotes);
            
            return $this->success('审核成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 执行调拨单
     * @param Request $request
     * @return Json
     */
    public function execute(Request $request): Json
    {
        try {
            $params = $request->param();
            $id = $params['id'] ?? '';
            $executeNotes = $params['execute_notes'] ?? '';
            
            if (empty($id)) {
                return $this->error('调拨单ID不能为空');
            }

            $result = $this->transferService->executeTransfer($id, $this->uid, $executeNotes);
            
            return $this->success('执行成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 取消调拨单
     * @param Request $request
     * @return Json
     */
    public function cancel(Request $request): Json
    {
        try {
            $params = $request->param();
            $id = $params['id'] ?? '';
            $cancelReason = $params['cancel_reason'] ?? '';
            
            if (empty($id)) {
                return $this->error('调拨单ID不能为空');
            }

            $result = $this->transferService->cancelTransfer($id, $this->uid, $cancelReason);
            
            return $this->success('取消成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量审核调拨单
     * @param Request $request
     * @return Json
     */
    public function batchApprove(Request $request): Json
    {
        try {
            $params = $request->param();
            $ids = $params['ids'] ?? [];
            $approveNotes = $params['approve_notes'] ?? '';
            
            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要审核的调拨单');
            }

            $result = $this->transferService->batchApproveTransfer($ids, $this->uid, $approveNotes);
            
            return $this->success('批量审核成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量执行调拨单
     * @param Request $request
     * @return Json
     */
    public function batchExecute(Request $request): Json
    {
        try {
            $params = $request->param();
            $ids = $params['ids'] ?? [];
            $executeNotes = $params['execute_notes'] ?? '';
            
            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要执行的调拨单');
            }

            $result = $this->transferService->batchExecuteTransfer($ids, $this->uid, $executeNotes);
            
            return $this->success('批量执行成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取调拨统计
     * @param Request $request
     * @return Json
     */
    public function statistics(Request $request): Json
    {
        try {
            $params = $request->param();
            $startDate = $params['start_date'] ?? '';
            $endDate = $params['end_date'] ?? '';
            $warehouseId = $params['warehouse_id'] ?? '';

            $result = $this->transferService->getTransferStatistics($startDate, $endDate, $warehouseId);
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取调拨趋势数据
     * @param Request $request
     * @return Json
     */
    public function trends(Request $request): Json
    {
        try {
            $params = $request->param();
            $period = $params['period'] ?? 'month'; // day, week, month, year
            $warehouseId = $params['warehouse_id'] ?? '';

            $result = $this->transferService->getTransferTrends($period, $warehouseId);
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取仓库间调拨流向分析
     * @param Request $request
     * @return Json
     */
    public function flowAnalysis(Request $request): Json
    {
        try {
            $params = $request->param();
            $startDate = $params['start_date'] ?? '';
            $endDate = $params['end_date'] ?? '';

            $result = $this->transferService->getTransferFlowAnalysis($startDate, $endDate);
            
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
