<?php
namespace app\customer\model;

use think\Model;

class Requirement extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'procurement_requirement';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;

        // 软删除
        use \think\model\concern\SoftDelete;
        protected $deleteTime = 'delete_time';
        protected $defaultSoftDelete = 0;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
   
 
} 