{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-page">
	<h3 class="pb-1">添加车辆信息</h3>
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">车辆名称<font>*</font></td>
			<td><input type="text" name="title" autocomplete="off" placeholder="请输入车辆名称" lay-verify="required" lay-reqText="请输入车辆名称" class="layui-input"></td>
			<td class="layui-td-gray">车牌号码<font>*</font></td>
			<td><input type="text" name="name" autocomplete="off" placeholder="请输入车牌号码" lay-verify="required" lay-reqText="请输入车牌号码" class="layui-input"></td>
			<td class="layui-td-gray-2" rowspan="5">
				车辆缩略图
				<button type="button" class="layui-btn layui-btn-sm" id="uploadImg">+ 上传图片</button>
			</td>
			<td rowspan="5" style="width:252px;">
				  <div id="demo1" style="width: 240px; height:136px; overflow: hidden;">
					<img src="" style="max-width: 100%; height:136px;" />
					<input type="hidden" name="thumb" value="">
				  </div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">购买日期<font>*</font></td>
			<td><input type="text" name="buy_time" value='' readonly placeholder="请选择" lay-verify="required" lay-reqText="请选择购买日期" class="layui-input tool-time" data-max="0"></td>
			<td class="layui-td-gray-2">购买价格<font>*</font></td>
			<td><input type="text" name="price" value='' placeholder="请输入购买价格" lay-verify="required|number" lay-reqText="请输入购买价格" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">车身颜色<font>*</font></td>
			<td><input type="text" name="color" value='' placeholder="请输入车身颜色" lay-verify="required" lay-reqText="请输入车身颜色" class="layui-input"></td>
			<td class="layui-td-gray">座位数<font>*</font></td>
			<td><input type="text" name="seats" autocomplete="off" placeholder="请输入座位数" lay-verify="required|number" lay-reqText="请输入座位数" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">发动机号<font>*</font></td>
			<td><input type="text" name="engine" autocomplete="off" placeholder="请输入发动机号" lay-verify="required" lay-reqText="请输入发动机号" class="layui-input"></td>
			<td class="layui-td-gray-2">车架号<font>*</font></td>
			<td><input type="text" name="vin" value='' placeholder="请输入车架号" lay-verify="required" lay-reqText="请输入车架号" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">车险到期日期<font>*</font></td>
			<td><input type="text" name="insure_time" value='' readonly placeholder="请选择" lay-verify="required" lay-reqText="请选择车险到期日期" class="layui-input tool-time"></td>
			<td class="layui-td-gray-2">车审到期日期<font>*</font></td>
			<td><input type="text" name="review_time" value='' readonly placeholder="请选择" lay-verify="required" lay-reqText="请选择车审到期日期" class="layui-input tool-time"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">车辆油耗<font>*</font></td>
			<td><input type="text" name="oil" autocomplete="off" placeholder="请输入油耗" lay-verify="required" lay-reqText="请输入油耗" class="layui-input"></td>
			<td class="layui-td-gray-2">开始里程数<font>*</font></td>
			<td><input type="text" name="mileage" value='' placeholder="请输入开始里程数" lay-verify="required|number" lay-reqText="请输入开始里程数" class="layui-input"></td>
			<td class="layui-td-gray-2">驾驶员</td>
			<td><input type="text" name="driver_name" value='' readonly placeholder="请选择"class="layui-input picker-admin"><input type="hidden" name="driver" value='0'></td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">相关附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					<input type="hidden" data-type="file" name="file_ids" value="">
				</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">备注信息</td>
			<td colspan="5">
				<textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
			</td>
		</tr>
	</table>
	<div class="pt-1">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','uploadPlus','oaPicker'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,uploadPlus= layui.uploadPlus;	
		//单图上传
		var photoUpload = new uploadPlus({
			'use':'single',
			'target':'uploadImg',
			'callback':function(res){
				layer.msg(res.msg);
				if (res.code == 0) {
					//上传成功
					$('#demo1 input').attr('value', res.data.id);
					$('#demo1 img').attr('src', res.data.filepath);
				}
			}
		});

		//相关附件上传
		var attachment = new uploadPlus();
		//监听提交
		form.on('submit(webform)', function(data){
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);				
				}
			}
			let clickbtn = $(this);
			tool.post("/adm/car/add", data.field, callback,clickbtn);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->