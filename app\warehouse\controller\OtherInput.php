<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\model\Warehouse as WarehouseModel;
use app\warehouse\model\Location as LocationModel;
use app\product\model\Product as ProductModel;
use app\warehouse\service\InventoryRealtimeService;
use think\facade\Db;
use think\facade\View;

class OtherInput extends BaseController
{
    /**
     * 入库单列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            if (!empty($param['keywords'])) {
                $where[] = ['i.input_no|i.related_bill_no', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['warehouse_id'])) {
                $where[] = ['i.warehouse_id', '=', $param['warehouse_id']];
            }
            if (isset($param['input_type']) && $param['input_type'] !== '') {
                $where[] = ['i.input_type', '=', $param['input_type']];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['i.status', '=', $param['status']];
            }
            if (!empty($param['start_date']) && !empty($param['end_date'])) {
                $where[] = ['i.input_date', 'between', [$param['start_date'], $param['end_date']]];
            }
            
            // 重构查询条件，基于库存流水表
            $transactionWhere = [
                ['it.transaction_type', '=', 'in'] // 只查询入库记录，显示所有类型的入库
            ];

            if (!empty($param['keywords'])) {
                $transactionWhere[] = ['it.transaction_no|it.ref_no|p.title|p.material_code', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['warehouse_id'])) {
                $transactionWhere[] = ['it.warehouse_id', '=', $param['warehouse_id']];
            }
            if (!empty($param['input_type']) && $param['input_type'] !== '') {
                // 映射原有的入库类型到新的ref_type
                $refTypeMap = [
                    1 => 'production_order',
                    2 => 'sample_input',
                    3 => 'return_input',
                    4 => 'adjust_input',
                    5 => 'transfer_input',
                    6 => 'gift_input',
                    7 => 'manual_input'
                ];
                $refType = $refTypeMap[$param['input_type']] ?? 'manual_input';
                $transactionWhere[] = ['it.ref_type', '=', $refType];
            }
            if (!empty($param['start_date']) && !empty($param['end_date'])) {
                $startTime = strtotime($param['start_date'] . ' 00:00:00');
                $endTime = strtotime($param['end_date'] . ' 23:59:59');
                $transactionWhere[] = ['it.create_time', 'between', [$startTime, $endTime]];
            }

            $list = Db::name('inventory_transaction')
                ->alias('it')
                ->leftJoin('product p', 'it.product_id = p.id')
                ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
                ->leftJoin('admin a', 'it.created_by = a.id')
                ->field([
                    'it.id',
                    'it.transaction_no',
                    'it.product_id',
                    'it.warehouse_id',
                    'it.quantity',
                    'it.ref_type',
                    'it.ref_id',
                    'it.ref_no',
                    'it.notes',
                    'it.create_time',
                    'p.title as product_name',
                    'p.material_code as product_code',
                    'p.unit as unit',
                    'p.reference_cost as cost_price',
                    'w.name as warehouse_name',
                    'a.name as operator_name'
                ])
                ->where($transactionWhere)
                ->order('it.create_time desc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 15,
                    'page' => isset($param['page']) ? (int)$param['page'] : 1,
                    'query' => $param
                ]);

            // 转换数据格式以保持前端兼容性
            $data = [];
            foreach ($list as $item) {
                // 保存原始时间戳
                $createTime = intval($item['create_time']);

                // 映射字段名
                $item['input_no'] = $item['transaction_no'];
                // 处理入库日期 - 如果时间戳太大，可能是毫秒级别的
                if ($createTime > 9999999999) { // 大于10位数，可能是毫秒时间戳
                    $createTime = intval($createTime / 1000);
                }
                $item['input_date'] = $createTime > 0 ? date('Y-m-d', $createTime) : date('Y-m-d');
                $item['related_bill_no'] = $item['ref_no'];

                // 映射入库类型 - 扩展映射以支持更多类型
                $typeMap = [
                    // 标准类型
                    'production_order' => 1,
                    'sample_input' => 2,
                    'return_input' => 3,
                    'adjust_input' => 4,
                    'transfer_input' => 5,
                    'gift_input' => 6,
                    'manual_input' => 7,
                    // 扩展类型
                    'customer_order' => 1,      // 客户订单 -> 生产入库
                    'purchase_receipt' => 1,    // 采购入库 -> 生产入库
                    'receipt' => 1,             // 入库单 -> 生产入库
                    'outbound' => 3,            // 出库单 -> 退货入库
                    'transfer' => 5,            // 调拨单 -> 调拨入库
                    'check' => 4,               // 盘点单 -> 盘盈入库
                    'inventory_check' => 4,     // 库存盘点 -> 盘盈入库
                    'inventory_transfer' => 5,  // 库存调拨 -> 调拨入库
                    'manual' => 7,              // 手动操作 -> 其他入库
                    'system' => 7,              // 系统操作 -> 其他入库
                    '' => 7
                ];

                $refType = $item['ref_type'] ?? '';
                $item['input_type'] = $typeMap[$refType] ?? 7;
                $item['input_type_text'] = $this->getInputTypeText($item['input_type']);

                // 如果ref_type为空，根据ref_no前缀智能判断类型
                if (empty($refType) && !empty($item['ref_no'])) {
                    if (strpos($item['ref_no'], 'SC') === 0) {
                        $item['input_type'] = 1; // 生产入库
                        $item['input_type_text'] = '生产入库';
                    } elseif (strpos($item['ref_no'], 'GR') === 0) {
                        $item['input_type'] = 1; // 采购收货入库
                        $item['input_type_text'] = '采购入库';
                    } elseif (strpos($item['ref_no'], 'PO') === 0) {
                        $item['input_type'] = 1; // 采购订单入库
                        $item['input_type_text'] = '采购入库';
                    } elseif (strpos($item['ref_no'], 'TR') === 0) {
                        $item['input_type'] = 5; // 调拨入库
                        $item['input_type_text'] = '调拨入库';
                    } elseif (strpos($item['ref_no'], 'RT') === 0) {
                        $item['input_type'] = 3; // 退货入库
                        $item['input_type_text'] = '退货入库';
                    }
                }

                // 固定状态为已入库
                $item['status'] = 3;
                $item['status_text'] = '已入库';

                // 格式化时间和其他字段
                $item['create_time'] = date('Y-m-d H:i:s', $createTime);
                $item['total_amount'] = $item['quantity'] * ($item['cost_price'] ?: 0);
                $item['total_quantity'] = $item['quantity'];

                // 确保所有字段都有默认值
                $item['product_name'] = $item['product_name'] ?: '未知产品';
                $item['product_code'] = $item['product_code'] ?: '';
                $item['warehouse_name'] = $item['warehouse_name'] ?: '未知仓库';
                $item['operator_name'] = $item['operator_name'] ?: '系统';
                $item['ref_no'] = $item['ref_no'] ?: '';
                $item['unit'] = $item['unit'] ?: '个';
                $item['input_type_text'] = $item['input_type_text'] ?: '其他入库';
                $item['input_date'] = $item['input_date'] ?: date('Y-m-d');

                // 将处理后的项目添加到数据数组
                $data[] = $item;
            }
            
            return json([
                'code' => 0,
                'msg' => '',
                'count' => $list->total(),
                'data' => $data
            ]);
        }
        
        // 获取仓库列表
        $warehouses = WarehouseModel::where('status', 1)->select();
        
        // 获取入库类型列表
        $inputTypes = [
            ['value' => 1, 'label' => '生产入库'],
            ['value' => 2, 'label' => '样品入库'],
            ['value' => 3, 'label' => '退货入库'],
            ['value' => 4, 'label' => '盘盈入库'],
            ['value' => 5, 'label' => '调拨入库'],
            ['value' => 6, 'label' => '赠送入库'],
            ['value' => 7, 'label' => '其他入库']
        ];
        
        // 获取状态列表
        $statusList = [
            ['value' => 1, 'label' => '草稿'],
            ['value' => 2, 'label' => '待审核'],
            ['value' => 3, 'label' => '已审核'],
            ['value' => 4, 'label' => '已取消']
        ];
        
        View::assign([
            'warehouses' => $warehouses,
            'input_types' => $inputTypes,
            'status_list' => $statusList
        ]);
        
        return View::fetch();
    }
    
    /**
     * 添加入库记录（重构版本）
     */
    public function add()
    {
        $id = input('id', 0, 'intval');

        if (request()->isAjax()) {
            $param = get_params();

            // 验证必要参数
            if (empty($param['warehouse_id'])) {
                return json(['code' => 1, 'msg' => '请选择仓库']);
            }

            if (empty($param['details']) || !is_array($param['details'])) {
                return json(['code' => 1, 'msg' => '请添加入库产品']);
            }

            // 开启事务
            Db::startTrans();
            try {
                $inventoryService = new InventoryRealtimeService();
                $successCount = 0;
                $transactionNos = [];

                // 处理每个产品的入库
                foreach ($param['details'] as $detail) {
                    if (empty($detail['product_id']) || empty($detail['quantity']) || $detail['quantity'] <= 0) {
                        continue;
                    }

                    // 获取产品信息
                    $product = ProductModel::find($detail['product_id']);
                    if (!$product) {
                        continue;
                    }

                    // 执行入库操作
                    $result = $inventoryService->increaseStock(
                        $detail['product_id'],
                        $param['warehouse_id'],
                        $detail['quantity'],
                        $product->unit ?? '',
                        $detail['price'] ?? $product->reference_cost ?? 0,
                        'manual_input', // 手工入库
                        0, // 无关联ID
                        $param['related_bill_no'] ?? '', // 关联单号
                        $param['notes'] ?? '手工入库',
                        $this->uid
                    );

                    if ($result) {
                        $successCount++;
                        $transactionNos[] = $result['transaction_no'] ?? '';
                    }
                }

                if ($successCount == 0) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '没有成功入库的产品']);
                }

                // 提交事务
                Db::commit();

                return json([
                    'code' => 0,
                    'msg' => "成功入库 {$successCount} 个产品",
                    'data' => [
                        'success_count' => $successCount,
                        'transaction_nos' => $transactionNos
                    ]
                ]);

            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '入库失败：' . $e->getMessage()]);
            }
        }

        // 显示添加页面
        $warehouses = WarehouseModel::where('status', 1)->select();

        View::assign([
            'warehouses' => $warehouses,
            'id' => $id
        ]);

        return View::fetch();
    }
    
    /**
     * 查看入库记录详情（重构版本）
     */
    public function detail()
    {
        $id = input('id', 0, 'intval');

        if (!$id) {
            $this->error('参数错误');
        }

        // 获取库存流水记录详情
        $detail = Db::name('inventory_transaction')
            ->alias('it')
            ->leftJoin('product p', 'it.product_id = p.id')
            ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
            ->leftJoin('admin a', 'it.created_by = a.id')
            ->field([
                'it.*',
                'p.title as product_name',
                'p.material_code as product_code',
                'p.specs as spec',
                'p.unit',
                'p.reference_cost as cost_price',
                'w.name as warehouse_name',
                'a.name as operator_name'
            ])
            ->where('it.id', $id)
            ->where('it.transaction_type', 'in')
            ->find();

        if (!$detail) {
            $this->error('入库记录不存在');
        }

        // 格式化数据以保持前端兼容性
        $detail['input_no'] = $detail['transaction_no'];
        $detail['input_date'] = date('Y-m-d', $detail['create_time']);
        $detail['related_bill_no'] = $detail['ref_no'];

        // 映射入库类型
        $typeMap = [
            'production_order' => 1,
            'sample_input' => 2,
            'return_input' => 3,
            'adjust_input' => 4,
            'transfer_input' => 5,
            'gift_input' => 6,
            'manual_input' => 7,
            '' => 7
        ];
        $detail['input_type'] = $typeMap[$detail['ref_type']] ?? 7;
        $detail['input_type_text'] = $this->getInputTypeText($detail['input_type']);

        // 固定状态
        $detail['status'] = 3;
        $detail['status_text'] = '已入库';

        // 格式化时间
        $detail['create_time'] = date('Y-m-d H:i:s', $detail['create_time']);

        // 计算金额
        $detail['total_amount'] = $detail['quantity'] * ($detail['cost_price'] ?: 0);

        // 创建明细数组（单条记录）
        $details = [[
            'product_id' => $detail['product_id'],
            'product_code' => $detail['product_code'],
            'product_name' => $detail['product_name'],
            'spec' => $detail['spec'] ?? '-',
            'unit' => $detail['unit'],
            'location_name' => $detail['location_name'] ?? '-',
            'batch_no' => $detail['batch_no'] ?? '-',
            'quantity' => $detail['quantity'],
            'price' => $detail['cost_price'] ?: 0,
            'amount' => $detail['total_amount'],
            'production_date' => $detail['production_date'] ?? '-',
            'expiry_date' => $detail['expiry_date'] ?? '-',
            'quality_status' => $detail['quality_status'] ?? 2,
            'notes' => $detail['notes'] ?? '-'
        ]];

        View::assign([
            'detail' => $detail,
            'detail_items' => $details
        ]);

        return View::fetch();
    }
    
    /**
     * 删除入库单
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            
            if (!$id) {
                return to_assign(1, '参数错误');
                
                
            }
            
            // 查询入库单
            $input = OtherInputModel::find($id);
            if (!$input) {
                return to_assign(1, '入库单不存在');
            }
            
            // 只有草稿和已取消状态的入库单可以删除
            if (!in_array($input->status, [OtherInputModel::STATUS_DRAFT, OtherInputModel::STATUS_CANCELED])) {
                return to_assign(1, '只有草稿和已取消状态的入库单可以删除');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 删除入库单明细
                OtherInputDetailModel::where('input_id', $id)->delete();
                
                // 删除入库单
                $input->delete();
                
                // 提交事务
                Db::commit();
                
                return to_assign(0, '删除成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '删除失败：' . $e->getMessage());
            }
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 提交入库单审核
     */
    public function submit()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            
            if (!$id) {
                return to_assign(1, '参数错误');
            }
            
            // 查询入库单
            $input = OtherInputModel::find($id);
            if (!$input) {
                return to_assign(1, '入库单不存在');
            }
            
            // 只有草稿状态的入库单可以提交审核
            if ($input->status != OtherInputModel::STATUS_DRAFT) {
                return to_assign(1, '只有草稿状态的入库单可以提交审核');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 更新入库单状态
                $input->status = OtherInputModel::STATUS_PENDING;
                $input->save();
                
                // 记录操作日志
                $input->addLog('submit', '提交入库单审核');
                
                // 提交事务
                Db::commit();
                
                return to_assign(0, '提交成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '提交失败：' . $e->getMessage());
            }
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 审核入库单
     */
    public function approve()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            
            if (!$id) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 查询入库单
            $input = OtherInputModel::find($id);
            if (!$input) {
                return json(['code' => 1, 'msg' => '入库单不存在']);
            }
            
          
            try {
                if ($input['related_bill_type']=='produce_order') {
                    // 操作订单
                        $quantity= (float) Db::name('warehouse_other_input_detail')->where('input_id',$input['id'])->value('quantity');
                        Db::name('produce_order')->where('order_no',$input['related_bill_no'])->inc('warehousing',$quantity)->update();

                        //判断如果全部入库，状态更改为完成
                        $status=5;
                        $produce_order=db::name('produce_order')->where('order_no',$input['related_bill_no'])->where('status',3)->find();
                        if (!empty($produce_order)) {
                            if ($produce_order['completed_qty']==$produce_order['warehousing']) {
                                Db::name('produce_order')->where('order_no',$input['related_bill_no'])->update(['status'=>5]);
                            }
                        }


                }

                
                // 审核入库单
                $result = $input->approve($this->uid);
                
                if ($result) {
                    return json(['code' => 0, 'msg' => '审核成功']);
                } else {
                    return json(['code' => 1, 'msg' => '审核失败']);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '审核失败：' . $e->getMessage()]);
            }
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 反审核入库单
     */
    public function unapprove()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            
            if (!$id) {
                return json(['code' => 1, 'msg' => '参数错误']);
            }
            
            // 查询入库单
            $input = OtherInputModel::find($id);
            if (!$input) {
                return json(['code' => 1, 'msg' => '入库单不存在']);
            }
            
            try {


                if ($input['related_bill_type']=='produce_order') {
                    // 操作订单
                        $quantity= (float) Db::name('warehouse_other_input_detail')->where('input_id',$input['id'])->value('quantity');
                        Db::name('produce_order')->where('order_no',$input['related_bill_no'])->dec('warehousing',$quantity)->update(['status'=>3]);
                }


                


                // 反审核入库单
                $result = $input->unapprove($this->uid);
                
                if ($result) {
                    return json(['code' => 0, 'msg' => '反审核成功']);
                    
                } else {
                    return json(['code' => 1, 'msg' => '反审核失败']);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '反审核失败：' . $e->getMessage()]); 
            }
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 取消入库单
     */
    public function cancel()
    {
        if (request()->isAjax()) {
            $id = input('id', 0, 'intval');
            
            if (!$id) {
                return to_assign(1, '参数错误');
            }
            
            // 查询入库单
            $input = OtherInputModel::find($id);
            if (!$input) {
                return to_assign(1, '入库单不存在');
            }
            
            try {
                // 取消入库单
                $result = $input->cancel($this->uid);
                
                if ($result) {
                    return to_assign(0, '取消成功');
                } else {
                    return to_assign(1, '取消失败');
                }
            } catch (\Exception $e) {
                return to_assign(1, '取消失败：' . $e->getMessage());
            }
        }
        
        return to_assign(1, '请求方式错误');
    }
    
    /**
     * 获取产品列表（AJAX）
     */
    public function getProducts()
    {
        if (request()->isAjax()) {
            $keyword = input('keyword', '', 'trim');
            
            $where = [];
            if (!empty($keyword)) {
                $where[] = ['title|material_code', 'like', '%' . $keyword . '%'];
            }
            
            $products = ProductModel::where($where)
                ->field('id, title, material_code, specs, unit')
                ->limit(20)
                ->select();
            
            return json(['code' => 0, 'data' => $products]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取库位列表（AJAX）
     */
    public function getLocations()
    {
        if (request()->isAjax()) {
            $warehouseId = input('warehouse_id', 0, 'intval');
            
            if (!$warehouseId) {
                return json(['code' => 1, 'msg' => '请选择仓库']);
            }
            
            $locations = LocationModel::where(['warehouse_id' => $warehouseId, 'status' => 1])
                ->field('id, code, name')
                ->select();
            
            return json(['code' => 0, 'data' => $locations]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取仓库列表（AJAX）
     */
    public function getWarehouses()
    {
        if (request()->isAjax()) {
            $warehouses = WarehouseModel::where('status', 1)
                ->field('id, name, code')
                ->select();
            
            return json(['code' => 0, 'data' => $warehouses]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 导出入库单
     */
    public function export()
    {
        $id = input('id', 0, 'intval');
        
        if (!$id) {
            $this->error('参数错误');
        }
        
        // 获取入库单详情
        $detail = OtherInputModel::find($id);
        if (!$detail) {
            $this->error('入库单不存在');
        }
        
        // 获取入库单明细
        $detailItems = OtherInputDetailModel::alias('d')
            ->leftJoin('warehouse_location l', 'l.id = d.location_id')
            ->field('d.*, l.name as location_name')
            ->where('d.input_id', $id)
            ->order('d.sort_order', 'asc')
            ->select();
        
        // 获取仓库名称
        $warehouse = WarehouseModel::find($detail['warehouse_id']);
        if ($warehouse) {
            $detail['warehouse_name'] = $warehouse->name;
        }
        
        // 获取创建人信息
        $creator = '';
        if ($detail['created_by']) {
            $creatorInfo = Db::name('admin')->field('name')->where('id', $detail['created_by'])->find();
            if ($creatorInfo) {
                $creator = $creatorInfo['name'];
            }
        }
        
        // 获取审核人信息
        $approver = '';
        if ($detail['approved_by']) {
            $approverInfo = Db::name('admin')->field('name')->where('id', $detail['approved_by'])->find();
            if ($approverInfo) {
                $approver = $approverInfo['name'];
            }
        }
        
        // 创建Excel对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置标题
        $sheet->setCellValue('A1', OtherInputModel::getTypeText($detail['input_type']) . '入库单');
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        
        // 设置基本信息
        $sheet->setCellValue('A2', '入库单号：');
        $sheet->setCellValue('B2', $detail['input_no']);
        $sheet->setCellValue('C2', '入库日期：');
        $sheet->setCellValue('D2', $detail['input_date']);
        $sheet->setCellValue('E2', '仓库：');
        $sheet->setCellValue('F2', $detail['warehouse_name'] ?? '');
        
        $sheet->setCellValue('A3', '状态：');
        $sheet->setCellValue('B3', OtherInputModel::getStatusText($detail['status']));
        $sheet->setCellValue('C3', '关联单据：');
        $sheet->setCellValue('D3', $detail['related_bill_no'] ?? '');
        $sheet->setCellValue('E3', '总金额：');
        $sheet->setCellValue('F3', number_format((float)$detail['total_amount'], 2) . ' 元');
        
        $sheet->setCellValue('A4', '备注：');
        $sheet->setCellValue('B4', $detail['notes'] ?? '');
        $sheet->mergeCells('B4:F4');
        
        $sheet->setCellValue('A5', '创建人：');
        $sheet->setCellValue('B5', $creator);
        $sheet->setCellValue('C5', '创建时间：');
        $sheet->setCellValue('D5', $detail['create_time'] ?? '');
        $sheet->setCellValue('E5', '审核人：');
        $sheet->setCellValue('F5', $approver);
        
        // 设置表头
        $headers = ['序号', '产品编码', '产品名称', '规格', '库位', '批次号', '数量', '单价', '金额', '质检状态', '备注'];
        $col = 'A';
        $row = 7;
        foreach ($headers as $header) {
            $sheet->setCellValue($col . $row, $header);
            $col++;
        }
        
        // 设置表头样式
        $sheet->getStyle('A7:K7')->getFont()->setBold(true);
        $sheet->getStyle('A7:K7')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('DDDDDD');
        
        // 填充明细数据
        $row = 8;
        $index = 1;
        foreach ($detailItems as $item) {
            $sheet->setCellValue('A' . $row, $index);
            $sheet->setCellValue('B' . $row, $item['product_code']);
            $sheet->setCellValue('C' . $row, $item['product_name']);
            $sheet->setCellValue('D' . $row, $item['spec']);
            $sheet->setCellValue('E' . $row, $item['location_name']);
            $sheet->setCellValue('F' . $row, $item['batch_no']);
            $sheet->setCellValue('G' . $row, $item['quantity']);
            $sheet->setCellValue('H' . $row, $item['price']);
            $sheet->setCellValue('I' . $row, $item['amount']);
            $sheet->setCellValue('J' . $row, OtherInputDetailModel::getQualityStatusText($item['quality_status']));
            $sheet->setCellValue('K' . $row, $item['notes']);
            
            $row++;
            $index++;
        }
        
        // 设置列宽
        $sheet->getColumnDimension('A')->setWidth(6);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(25);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(15);
        $sheet->getColumnDimension('G')->setWidth(10);
        $sheet->getColumnDimension('H')->setWidth(10);
        $sheet->getColumnDimension('I')->setWidth(10);
        $sheet->getColumnDimension('J')->setWidth(10);
        $sheet->getColumnDimension('K')->setWidth(20);
        
        // 生成文件
        $filename = OtherInputModel::getTypeText($detail['input_type']) . '入库单_' . $detail['input_no'] . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    /**
     * 获取入库类型文本
     */
    private function getInputTypeText($type)
    {
        $types = [
            1 => '采购/生产入库',
            2 => '样品入库',
            3 => '退货入库',
            4 => '盘盈入库',
            5 => '调拨入库',
            6 => '赠送入库',
            7 => '其他入库'
        ];

        return $types[$type] ?? '其他入库';
    }
}
