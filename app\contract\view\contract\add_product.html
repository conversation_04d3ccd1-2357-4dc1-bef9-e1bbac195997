<tr>
	<td colspan="6"><strong>产品信息</strong> <span class="layui-btn layui-btn-sm" type="button" id="productAdd">+ 添加产品</span></td>
</tr>
<tr>
	<td colspan="6">				
		<table id="productTable" class="layui-table layui-table-min" style="margin:0">
			<tr>
				<th width="200">产品名称</th>
				<th width="100">单位</th>
				<th width="100">规格</th>
				<th width="100">销售单价</th>
				<th width="100">数量</th>
				<th width="100">小计</th>
				<th>备注信息</th>
				<th width="60">操作</th>
			</tr>
			<tr class="product-tr">
				{eq name="is_product" value="1"}
				<td><input type="text" name="product_title[]" value="" class="layui-input product-picker" readonly lay-verify="required" lay-reqText="请完善产品名称"><input type="hidden" name="product_id[]" value="0"></td>
				{else/}
				<td><input type="text" name="product_title[]" value="" class="layui-input" lay-verify="required" lay-reqText="请完善产品名称"><input type="hidden" name="product_id[]" value="0"></td>
				{/eq}
				<td><input type="text" name="product_unit[]" value="" class="layui-input" lay-verify="required" lay-reqText="请输入单位"></td>
				<td><input type="text" name="product_specs[]" value="" class="layui-input" lay-verify="required" lay-reqText="请输入产品规格"></td>
				<td><input type="text" name="product_price[]" value="0.00" class="layui-input input-num" lay-verify="required|number" lay-reqText="请输入销售单价"></td>
				<td><input type="text" name="product_num[]" value="1" class="layui-input input-num" lay-verify="required|number" lay-reqText="请输入产品数量"></td>
				<td><input type="text" name="product_subtotal[]" value="0.00" class="layui-input layui-input-readonly" readonly></td>
				<td><input type="text" name="product_remark[]" value="" class="layui-input"></td>
				<td><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a></td>
			</tr>
		</table>
	</td>
</tr>
<script>
const is_product={$is_product};
function product_fun(layui){
	let form = layui.form,oaPicker = layui.oaPicker;
	
	//选择物品弹窗        
	$('body').on('click','.product-picker',function () {
		let that = $(this),ids = [],titles=[],sale_price=[],unit=[],specs=[],map = {},types = 'product',type = 1;
		let callback = function(data){
			for ( var i = 0; i <data.length; i++){
				ids.push(data[i].id);
				titles.push(data[i].title);
				unit.push(data[i].unit);
				specs.push(data[i].specs);
				sale_price.push(data[i].sale_price);
			}
			that.val(titles.join(','));
			that.next().val(ids.join(','));
			that.parents('.product-tr').find('input').eq(2).val(unit.join(','));
			that.parents('.product-tr').find('input').eq(3).val(specs.join(','));
			that.parents('.product-tr').find('input').eq(4).val(sale_price.join(','));
			that.parents('.product-tr').find('input').eq(6).val(sale_price.join(','));
			count_cost();
		}
		oaPicker.picker(types,type,callback,map);
	});
	
	//添加产品信息表格
	$('#productAdd').on('click',function(){
		var html = '',product_title=' class="layui-input"';
		if(is_product==1){
			product_title=' class="layui-input product-picker" readonly';
		}
		html += '<tr class="product-tr">\
					<td><input type="text" name="product_title[]" value="" '+product_title+' lay-verify="required" lay-reqText="请完善产品名称"><input type="hidden" name="product_id[]" value="0"></td>\
					<td><input type="text" name="product_unit[]" value="" class="layui-input" lay-verify="required" lay-reqText="请输入单位"></td>\
					<td><input type="text" name="product_specs[]" value="" class="layui-input" lay-verify="required" lay-reqText="请输入产品规格"></td>\
					<td><input type="text" name="product_price[]" value="0.00" class="layui-input input-num" lay-verify="required|number" lay-reqText="请输入销售单价"></td>\
					<td><input type="text" name="product_num[]" value="1" class="layui-input input-num" lay-verify="required|number" lay-reqText="请输入产品数量"></td>\
					<td><input type="text" name="product_subtotal[]" value="0.00" class="layui-input layui-input-readonly" readonly></td>\
					<td><input type="text" name="product_remark[]" value="" class="layui-input"></td>\
					<td><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a></td>\
				</tr>';
		$('#productTable').append(html).find('.tr-none').remove();
		form.render();
	});	

	$('#productTable').on('click', '[lay-event="del"]', function() {
		if($('.product-tr').length<2){
			layer.msg('至少保留一个产品选项');
			return false;
		}
		$(this).parents('.product-tr').remove();
		count_cost();
	});	
	
	//计算价格
	$('#productTable').on('input', '.input-num', function() {
		var inputs = $(this).parents('.product-tr').find("input");
		var product_price = inputs.eq(4).val();
		var product_num = inputs.eq(5).val();
		var product_amount = (product_price*product_num).toFixed(2);			  
		if(isNaN(product_amount)){ 
			product_amount = 0.00;
		}
		inputs.eq(6).val(product_amount);
		count_cost();
	});
	 
	//计算总价
	function count_cost(){
		var product_subtotal = $('#productTable').find('[name="product_subtotal[]"]');
		var total = 0;
		for (var m = 0; m < product_subtotal.length; m++) {
			total+=$(product_subtotal[m]).val()*1000;
		}
		if(isNaN(total)){ 
			total = 0;
		}
		$('[name="cost"]').val((total/1000).toFixed(2));
	}
}
</script>
