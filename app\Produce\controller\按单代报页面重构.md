# 按单代报页面重构

## 需求背景
用户希望按单代报页面显示该订单的所有生产工艺流程，就像截图中的工序列表那样，并支持对每个工序进行报工操作。

## 实现思路

### 1. 数据源调整
- 从显示所有工艺流程改为显示订单的具体工序
- 使用 `oa_produce_order_process` 表获取订单工序实例
- 显示工序的详细信息：名称、类型、状态、计划时间等

### 2. 页面布局重构
- 保持与报工页面相同的布局风格
- 顶部显示订单基本信息
- 主体显示工序列表表格
- 每个工序提供报工按钮

### 3. 工序列表功能
- **工序信息展示**：工序名称、类型、状态、计价方式
- **时间信息**：计划开始时间、计划结束时间
- **状态管理**：显示工序当前状态（未开始/进行中/已完成/暂停）
- **报工操作**：每个工序独立的报工按钮
- **批量选择**：支持选择多个工序进行批量操作

### 4. 报工弹窗功能
- 弹窗式报工界面，避免页面跳转
- 包含完成数量、合格数量、工时、报工人员等字段
- 日期选择器，默认当前日期
- 表单验证和提交

## 技术实现

### 1. 前端组件
```javascript
// 使用layui table组件
table.render({
    elem: '#table_delegate',
    toolbar: '#toolbarDemo',
    data: reportData,
    cols: [...]
});
```

### 2. 模板系统
- 工艺流程选择模板：`#processSelectTpl`
- 工序选择模板：`#stepSelectTpl`
- 报工人员选择模板：`#workerSelectTpl`
- 各种输入框模板：数量、工时、日期、备注
- 操作按钮模板：`#operationTpl`

### 3. 数据交互
- 工艺流程变更时Ajax加载工序：`/Produce/order/getProcessSteps`
- 表单提交：`/Produce/order/delegate`
- 数据验证：前端验证必填字段完整性

### 4. 样式适配
- 使用系统标准样式类：`p-page`、`layui-card`、`gg-form-bar`
- 保持与报工页面一致的视觉效果
- 响应式布局适配

## 关键功能点

### 1. 动态添加行
```javascript
function addReportRow() {
    var newRow = {
        id: 'new_' + reportIndex++,
        process_id: '',
        step_id: '',
        // ... 其他字段
    };
    reportData.push(newRow);
    delegateTable.reload({data: reportData});
}
```

### 2. 工艺流程联动
```javascript
form.on('select', function(data) {
    if ($(elem).attr('lay-filter').indexOf('processSelect_') === 0) {
        var rowIndex = $(elem).attr('lay-filter').replace('processSelect_', '');
        loadSteps(value, rowIndex);
    }
});
```

### 3. 数据收集与提交
```javascript
function submitDelegate() {
    var tableData = table.cache.table_delegate;
    // 遍历表格行，收集实际输入值
    // 验证数据完整性
    // Ajax提交到后端
}
```

## 界面优化 (第二版)

### 优化内容
1. **移除不必要的按钮**：
   - 移除顶部的"提交代报"按钮
   - 移除工序列表的"选择"列
   - 简化操作流程

2. **界面美化**：
   - 工序状态使用彩色徽章显示
   - 调整列宽度，优化显示效果
   - 按钮文字改为"代报工"更符合业务场景

3. **功能简化**：
   - 移除批量选择功能
   - 专注于单个工序的代报操作
   - 保持界面简洁清晰

## 弹窗优化 (第三版)

### 优化内容
1. **工序信息优化**：
   - 工序名称改为只显示，不可编辑
   - 弹窗标题显示具体工序名称
   - 明确代报的目标工序

2. **表单布局优化**：
   - 调整字段排列，提高填写效率
   - 报工日期移到第二行，与工时并列
   - 压缩弹窗高度，减少滚动

3. **用户体验提升**：
   - 点击工序代报工按钮，直接针对该工序
   - 无需再选择或填写工序信息
   - 操作更直观，减少用户困惑

## 预期效果
1. ✅ 清晰的工序列表展示
2. ✅ 直观的工序状态显示
3. ✅ 简单的单工序代报操作
4. ✅ 弹窗式报工界面
5. ✅ 与系统整体风格保持一致

## 数据保存修复 (第四版)

### 问题分析
1. **数据提交成功但未保存**：代报工返回成功，但`oa_production_work_report`表中没有数据
2. **订单生产差异化**：不同订单有不同的工艺流程、工序配置
3. **数据结构不匹配**：原代码使用通用工艺流程，未考虑订单工序实例

### 修复内容
1. **数据源调整**：
   - 从`oa_engineering_process`改为`oa_produce_order_process`
   - 使用订单工序实例数据，体现订单生产差异化
   - 确保每个订单的工序配置独立

2. **字段映射修复**：
   - `process_id`：使用订单工序实例ID
   - `step_id`：使用工序序号
   - `work_time`：小时转换为分钟存储
   - `worker_name`：从admin表获取用户昵称

3. **状态更新**：
   - 保存报工记录后更新订单工序状态
   - 累加完成数量、合格数量、实际工时
   - 工序状态改为"进行中"

### 技术要点
```php
// 获取订单工序实例（体现差异化）
$orderProcess = Db::name('produce_order_process')
    ->where('id', $reportData['process_id'])
    ->where('order_id', $data['order_id'])
    ->find();

// 更新工序状态
Db::name('produce_order_process')
    ->where('id', $orderProcess['id'])
    ->update([
        'status' => 1, // 进行中
        'completed_qty' => Db::raw('completed_qty + ' . $quantity),
        // ...
    ]);
```

## 计划时间显示修复 (第五版)

### 问题分析
计划开始时间和计划结束时间显示为空，因为订单工序实例表中的时间字段没有数据。

### 修复方案
1. **数据源扩展**：
   - 订单查询时关联生产计划表(`oa_production_plan`)
   - 获取生产计划的`plan_start_date`和`plan_end_date`

2. **时间显示优先级**：
   ```
   工序计划时间 > 生产计划时间 > 订单交期时间
   ```

3. **代码实现**：
   ```php
   // 关联生产计划表
   $order = Db::name('produce_order')
       ->alias('o')
       ->leftJoin('production_plan p', 'o.plan_id = p.id')
       ->field('o.*, p.plan_start_date, p.plan_end_date')
       ->find();

   // 时间优先级处理
   if (!$plannedStartTime && !empty($order['plan_start_date'])) {
       $plannedStartTime = strtotime($order['plan_start_date']);
   }
   ```

### 效果
- ✅ 计划开始时间正确显示
- ✅ 计划结束时间正确显示
- ✅ 体现订单的实际排产计划
- ✅ 支持多层级时间回退机制

## 逾期状态和报工统计 (第六版)

### 新增功能
1. **逾期状态计算**：
   - 基于计划开始时间和计划结束时间计算
   - 实时判断工序是否逾期
   - 彩色徽章显示状态

2. **报工数据统计**：
   - 显示每个工序的累计报工数量
   - 显示合格数量和不合格数量
   - 数据来源于`oa_production_work_report`表

### 逾期状态逻辑
```php
if ($plannedEndTime && $process['status'] != 2) { // 未完成的工序
    if ($currentTime > $plannedEndTime) {
        $process['overdue_text'] = '逾期';        // 红色
        $process['overdue_class'] = 'layui-bg-red';
    } elseif ($currentTime > $plannedStartTime) {
        $process['overdue_text'] = '进行中';      // 蓝色
        $process['overdue_class'] = 'layui-bg-blue';
    } else {
        $process['overdue_text'] = '正常';        // 绿色
        $process['overdue_class'] = 'layui-bg-green';
    }
}
```

### 报工统计逻辑
```php
$reports = Db::name('production_work_report')
    ->where('order_id', $orderId)
    ->where('process_id', 'in', $processIds)
    ->where('status', 1) // 有效状态
    ->field('process_id, SUM(quantity) as total_quantity, SUM(qualified_qty) as total_qualified, SUM(unqualified_qty) as total_unqualified')
    ->group('process_id')
    ->select();
```

### 界面改进
1. **新增列**：
   - 逾期状态列：显示正常/进行中/逾期/已完成
   - 报工数列：显示累计报工数量
   - 合格数列：显示累计合格数量
   - 不合格数列：显示累计不合格数量

2. **视觉优化**：
   - 逾期状态使用彩色徽章
   - 报工数据使用不同颜色区分
   - 调整列宽度适应新内容

## 报工数量验证 (第七版)

### 核心业务逻辑
实现工序间的数量流转控制，确保生产数据的准确性和合理性。

### 验证规则
1. **第一道工序**：
   ```
   可报工量 = 订单总量 - 当前工序已报工量
   ```

2. **后续工序**：
   ```
   可报工量 = 上一道工序合格数量 - 当前工序已报工量
   ```

3. **多次报工支持**：
   - 累计计算已报工数量
   - 支持分批次报工
   - 防止超量报工

### 技术实现
1. **验证方法**：
   ```php
   private function validateReportQuantity($orderId, $currentProcess, $reportQuantity)
   {
       // 获取工序顺序
       $allProcesses = Db::name('produce_order_process')
           ->where('order_id', $orderId)
           ->order('step_no asc')
           ->select();

       // 计算可报工量
       if ($currentStepIndex == 0) {
           // 第一道工序逻辑
       } else {
           // 后续工序逻辑
       }
   }
   ```

2. **前端显示**：
   - 弹窗显示当前可报工数量
   - 实时获取最新可报工量
   - 输入框设置最大值限制

3. **错误提示**：
   ```
   工序【切割】报工数量不能超过50件
   （上一道工序【备料】合格数量100件 - 当前工序已报工50件）
   ```

### 业务价值
1. **数据准确性**：防止虚假报工和数据错误
2. **流程控制**：确保工序间的逻辑关系
3. **质量管控**：基于合格数量进行流转
4. **多次报工**：支持分批生产的实际需求

### 用户体验
1. **智能提示**：显示可报工数量
2. **实时验证**：提交前后端双重验证
3. **友好错误**：详细的错误提示信息
4. **操作便捷**：自动设置输入框限制

## 生产计划进度修复 (第八版)

### 问题分析
生产计划页面显示的进度百分比都是0%，但实际上已经有报工数据。原因是生产计划的进度没有根据报工数据自动更新。

### 修复方案
1. **实时进度计算**：
   - 在`getScheduledPlans`方法中添加实时进度计算
   - 根据报工数据计算每个订单的实际完成进度
   - 自动更新数据库中的进度字段

2. **进度计算逻辑**：
   ```php
   private function calculateOrderProgress($orderId, $orderQuantity)
   {
       // 获取所有工序
       $processes = Db::name('produce_order_process')
           ->where('order_id', $orderId)
           ->order('step_no asc')
           ->select();

       // 平均权重分配
       $weightPerProcess = 100 / count($processes);

       foreach ($processes as $process) {
           // 获取工序报工数据
           $qualifiedQty = 工序合格数量;
           $processProgress = min(100, ($qualifiedQty / $orderQuantity) * 100);
           $totalProgress += ($processProgress / 100) * $weightPerProcess;
       }
   }
   ```

3. **自动同步机制**：
   - 检测实际进度与数据库进度的差异
   - 差异超过0.1%时自动更新数据库
   - 确保甘特图显示最新进度

### 技术实现
1. **数据源整合**：
   - 从`oa_production_work_report`表获取报工数据
   - 按工序汇总合格数量
   - 计算每个工序的完成率

2. **权重分配策略**：
   - 所有工序平均分配权重
   - 每个工序权重 = 100% / 工序总数
   - 支持后续扩展为自定义权重

3. **性能优化**：
   - 只在进度有变化时更新数据库
   - 避免频繁的数据库写操作

### 业务价值
1. **实时监控**：生产计划进度实时反映生产状况
2. **决策支持**：准确的进度数据支持生产调度
3. **可视化管理**：甘特图直观显示生产进度
4. **数据一致性**：报工数据与计划进度保持同步

## 生产计划悬停详情 (第九版)

### 功能描述
在生产计划甘特图中，鼠标悬停在订单条上时，显示该订单当天各个工序的详细报工情况。

### 技术实现
1. **后端接口**：
   ```php
   public function getOrderProcessDetails()
   {
       // 获取指定订单在指定日期的工序报工详情
       // 包含每个工序的当日报工数量、合格数量、工时等
   }
   ```

2. **前端交互**：
   ```javascript
   // 甘特条添加悬停事件
   onmouseover="window.showProcessTooltip(event, orderId, date)"
   onmouseout="window.hideProcessTooltip()"
   ```

3. **数据结构**：
   ```json
   {
     "order": {"order_no": "...", "product_name": "...", "quantity": 100},
     "date": "2025-08-11",
     "processes": [
       {
         "process_name": "切割",
         "daily_quantity": 50,
         "daily_qualified": 48,
         "daily_work_time": 4.5,
         "status": "进行中"
       }
     ]
   }
   ```

### 显示内容
1. **订单信息**：订单号、产品名称、订单数量
2. **日期信息**：当前查看的报工日期
3. **工序详情**：
   - 工序名称和序号
   - 工序状态（未开始/进行中/已完成）
   - 当日报工数量
   - 当日合格数量
   - 当日工时
4. **汇总信息**：当日总报工数量和合格数量

### 用户体验
1. **即时显示**：鼠标悬停立即显示详情
2. **美观界面**：专业的提示框样式
3. **信息丰富**：一目了然的工序进度
4. **操作便捷**：无需点击即可查看详情

### 业务价值
1. **快速查看**：无需跳转页面即可了解详情
2. **进度监控**：实时掌握各工序执行情况
3. **决策支持**：基于详细数据进行生产调度
4. **效率提升**：减少查询操作，提高工作效率

## 测试要点
- [x] 页面正常加载和渲染
- [x] 工序列表正确显示
- [x] 工序状态彩色显示
- [x] 单个工序代报功能
- [x] 数据正确保存到数据库
- [x] 工序状态实时更新
- [x] 计划时间正确显示
- [x] 逾期状态正确计算和显示
- [x] 报工统计数据正确显示
- [x] 报工数量验证功能
- [x] 可报工数量实时显示
- [x] 多次报工累计计算
- [x] 生产计划进度实时更新
- [x] 甘特图悬停显示工序详情
