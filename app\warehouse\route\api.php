<?php
/**
 * 仓库管理API路由配置
 */

use think\facade\Route;

// 实时库存API路由组
Route::group('api/inventory', function () {
    // 库存列表
    Route::get('/', 'api.InventoryRealtimeApi/index');
    
    // 库存详情
    Route::get('/:id', 'api.InventoryRealtimeApi/detail');
    
    // 库存操作
    Route::post('/inbound', 'api.InventoryRealtimeApi/inbound');
    Route::post('/outbound', 'api.InventoryRealtimeApi/outbound');
    Route::post('/lock', 'api.InventoryRealtimeApi/lock');
    Route::post('/unlock', 'api.InventoryRealtimeApi/unlock');
    Route::post('/use-locked', 'api.InventoryRealtimeApi/useLocked');
    
    // 库存检查
    Route::post('/check-stock', 'api.InventoryRealtimeApi/checkStock');
    
    // 库存统计
    Route::get('/statistics', 'api.InventoryRealtimeApi/statistics');
    Route::get('/alerts', 'api.InventoryRealtimeApi/alerts');
})->middleware(['auth:api', 'permission:warehouse']);

// 调拨管理API路由组
Route::group('api/transfer', function () {
    // 调拨单列表
    Route::get('/', 'api.InventoryTransferApi/index');
    
    // 调拨单详情
    Route::get('/:id', 'api.InventoryTransferApi/detail');
    
    // 调拨单操作
    Route::post('/', 'api.InventoryTransferApi/create');
    Route::post('/:id/approve', 'api.InventoryTransferApi/approve');
    Route::post('/:id/execute', 'api.InventoryTransferApi/execute');
    Route::post('/:id/cancel', 'api.InventoryTransferApi/cancel');
    
    // 批量操作
    Route::post('/batch-approve', 'api.InventoryTransferApi/batchApprove');
    Route::post('/batch-execute', 'api.InventoryTransferApi/batchExecute');
    
    // 统计分析
    Route::get('/statistics', 'api.InventoryTransferApi/statistics');
    Route::get('/trends', 'api.InventoryTransferApi/trends');
    Route::get('/flow-analysis', 'api.InventoryTransferApi/flowAnalysis');
})->middleware(['auth:api', 'permission:warehouse']);

// 库存锁定API路由组
Route::group('api/inventory-lock', function () {
    // 锁定列表
    Route::get('/', 'api.InventoryLockApi/index');
    
    // 锁定详情
    Route::get('/:id', 'api.InventoryLockApi/detail');
    
    // 锁定操作
    Route::post('/lock', 'api.InventoryLockApi/lock');
    Route::post('/:id/release', 'api.InventoryLockApi/release');
    Route::post('/:id/use', 'api.InventoryLockApi/use');
    
    // 批量操作
    Route::post('/batch-release', 'api.InventoryLockApi/batchRelease');
    
    // 锁定统计
    Route::get('/statistics', 'api.InventoryLockApi/statistics');
})->middleware(['auth:api', 'permission:warehouse']);

// 库存事务API路由组
Route::group('api/inventory-transaction', function () {
    // 事务列表
    Route::get('/', 'api.InventoryTransactionApi/index');
    
    // 事务详情
    Route::get('/:id', 'api.InventoryTransactionApi/detail');
    
    // 事务统计
    Route::get('/statistics', 'api.InventoryTransactionApi/statistics');
    Route::get('/trends', 'api.InventoryTransactionApi/trends');
})->middleware(['auth:api', 'permission:warehouse']);

// 仓库基础数据API路由组
Route::group('api/warehouse', function () {
    // 仓库列表
    Route::get('/', 'api.WarehouseApi/index');
    
    // 仓库详情
    Route::get('/:id', 'api.WarehouseApi/detail');
    
    // 仓库库存汇总
    Route::get('/:id/inventory-summary', 'api.WarehouseApi/inventorySummary');
})->middleware(['auth:api', 'permission:warehouse']);

// 产品基础数据API路由组
Route::group('api/product', function () {
    // 产品列表
    Route::get('/', 'api.ProductApi/index');
    
    // 产品详情
    Route::get('/:id', 'api.ProductApi/detail');
    
    // 产品库存汇总
    Route::get('/:id/inventory-summary', 'api.ProductApi/inventorySummary');
})->middleware(['auth:api', 'permission:warehouse']);

// 移动端专用API路由组
Route::group('api/mobile', function () {
    // 移动端首页数据
    Route::get('/dashboard', 'api.MobileApi/dashboard');
    
    // 快速入库
    Route::post('/quick-inbound', 'api.MobileApi/quickInbound');
    
    // 快速出库
    Route::post('/quick-outbound', 'api.MobileApi/quickOutbound');
    
    // 扫码查询库存
    Route::get('/scan-inventory', 'api.MobileApi/scanInventory');
    
    // 库存盘点
    Route::post('/inventory-check', 'api.MobileApi/inventoryCheck');
    
    // 获取待处理任务
    Route::get('/pending-tasks', 'api.MobileApi/pendingTasks');
})->middleware(['auth:api', 'permission:warehouse']);

// 第三方系统集成API路由组
Route::group('api/integration', function () {
    // 同步库存数据
    Route::post('/sync-inventory', 'api.IntegrationApi/syncInventory');
    
    // 推送库存变动
    Route::post('/push-inventory-change', 'api.IntegrationApi/pushInventoryChange');
    
    // 获取库存快照
    Route::get('/inventory-snapshot', 'api.IntegrationApi/inventorySnapshot');
    
    // 批量库存操作
    Route::post('/batch-inventory-operation', 'api.IntegrationApi/batchInventoryOperation');
})->middleware(['auth:api', 'permission:warehouse', 'throttle:100,1']);

// 报表API路由组
Route::group('api/report', function () {
    // 库存报表
    Route::get('/inventory', 'api.ReportApi/inventoryReport');
    
    // 调拨报表
    Route::get('/transfer', 'api.ReportApi/transferReport');
    
    // 库存变动报表
    Route::get('/inventory-change', 'api.ReportApi/inventoryChangeReport');
    
    // 库存周转率报表
    Route::get('/inventory-turnover', 'api.ReportApi/inventoryTurnoverReport');
    
    // 库存预警报表
    Route::get('/inventory-alert', 'api.ReportApi/inventoryAlertReport');
})->middleware(['auth:api', 'permission:warehouse']);

// WebHook路由组（用于接收外部系统推送）
Route::group('webhook/warehouse', function () {
    // 接收ERP系统库存变动
    Route::post('/erp-inventory-change', 'webhook.WarehouseWebhook/erpInventoryChange');
    
    // 接收WMS系统库存同步
    Route::post('/wms-inventory-sync', 'webhook.WarehouseWebhook/wmsInventorySync');
    
    // 接收采购系统入库通知
    Route::post('/purchase-inbound', 'webhook.WarehouseWebhook/purchaseInbound');
    
    // 接收生产系统完工入库
    Route::post('/production-inbound', 'webhook.WarehouseWebhook/productionInbound');
})->middleware(['webhook:signature']);
