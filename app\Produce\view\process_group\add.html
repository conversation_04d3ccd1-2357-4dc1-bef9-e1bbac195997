{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb" lay-separator=">">
            <a href="/Produce/processGroup/index">工作组管理</a>
            <a><cite>{if condition="$id > 0"}编辑工作组{else /}添加工作组{/if}</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="groupForm">
            {if condition="$id > 0"}
            <input type="hidden" name="id" value="{$detail.id|default=0}" />
            {/if}
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工作组名称 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="{$detail.name|default=''}" placeholder="请输入工作组名称" 
                                   class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">排序</label>
                        <div class="layui-input-block">
                            <input type="number" name="sort" value="{$detail.sort|default=0}" placeholder="请输入排序值" 
                                   class="layui-input" min="0">
                            <div class="layui-form-mid layui-word-aux">数值越小排序越靠前</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" 
                           {if condition="!isset($detail.status) || $detail.status == 1"}checked{/if}>
                    <input type="radio" name="status" value="0" title="禁用" 
                           {if condition="isset($detail.status) && $detail.status == 0"}checked{/if}>
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">工作组描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入工作组描述" class="layui-textarea" 
                              maxlength="500">{$detail.description|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        <span id="descLength">0</span> / 500
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="groupSubmit">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 监听描述字数统计
        $('[name="description"]').on('input', function(){
            var length = $(this).val().length;
            $('#descLength').text(length);
        });
        
        // 初始化字数统计
        var initLength = $('[name="description"]').val().length;
        $('#descLength').text(initLength);
        
        // 表单提交
        layui.form.on('submit(groupSubmit)', function(data){
            let callback = function (e) {
                layer.msg(e.msg);
                if (e.code == 0) {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            }
            tool.post("/Produce/processGroup/add", data.field, callback);
            return false;
        });
    }
</script>
{/block}