<?php
namespace app\customer\controller;

use app\base\BaseController;
use think\Controller;
use think\facade\Db;
use think\facade\Request;
use app\engineering\model\Product as ProductModel;
use app\customer\model\Order as OrderModel;
use app\customer\model\OrderDetail as OrderDetailModel;
use app\api\service\InventoryReserveService;
use app\api\model\EditLog;
use app\warehouse\model\Inventory;
use think\exception\ValidateException;
use think\facade\View;

class Order extends BaseController
{
      /**
     * 订单列表
     */
    public function index()
    {
        if(Request::isAjax()){
            $where = [];
            $page = input('page', 1);
            $limit = input('limit', 20);
            
            // 搜索条件
            $order_no = input('order_no', '');
            if($order_no){
                // 构建子查询，查找包含指定产品的订单ID
                $subQuery = Db::name('customer_order_detail')
                    ->where('product_name|material_code', 'like', "%{$order_no}%")
                    ->field('order_id')
                    ->buildSql();
                
                // 使用whereOr来组合订单号和产品信息的查询
                $where[] = ['', 'exp', Db::raw("(order_no LIKE '%{$order_no}%' OR id IN {$subQuery})")];
            }
            
            $customer_id = input('customer_id', 0);
            if($customer_id){
                $where[] = ['customer_id', '=', $customer_id];
            }
            
            $status = input('status', '');
            if($status !== ''){
                $where[] = ['status', '=', $status];
            }
            
            $order_type = input('order_type', '');
            if($order_type !== ''){
                $where[] = ['order_type', '=', $order_type];
            }
            $uid = $this->uid;
          
            //是否是客户管理员
            
            //是否是客户管理员
					$auth = isAuth($uid,'customer_admin','conf_1');
               
					if($auth == 1){
						$whereOr[] = ['did','=',$uid];
						$whereOr[] = ['', 'exp', Db::raw("FIND_IN_SET('{$uid}',share_ids)")];
						$dids_a = get_leader_departments($uid);
						$dids_b = get_role_departments($uid);
						$dids = array_merge($dids_a, $dids_b);
						if(!empty($dids)){
							$whereOr[] = ['did','in',$dids];
						}
					}
					else{
                        
                        if ($this->did==5){
                            $where[] = ['admin_id','=',$uid];
                        }
						
                       
					}

                    

                  //  $where[] = ['admin_id','=',$uid];
            //权限查询结束
         

            $list = OrderModel::with(['customer', 'adduser'])
                ->where($where)
                ->order('id desc')
                ->page($page, $limit)
                ->select()
                ->each(function($item) {
                    // 添加开票状态和金额信息
                    $item->invoice_amount = $item->getInvoiceAmount();
                    $item->invoice_status_text = $item->invoice_status_text;
                    $item->invoice_status_class = $item->invoice_status_class;
                    return $item;
                })
                ->toArray();

                
          
            
            $count = OrderModel::where($where)->count();
            
            return json(['code' => 0, 'msg' => '', 'count' => $count, 'data' => $list]);
        }

        $orderDetailModel = new OrderDetailModel();
        $orderDetailModel->checkInventory(211);
        View::assign('is_leader', isLeader($this->uid));
        
        
        return view();
    }
    
    /**
     * 添加订单
     */
    public function add()
    {
        if(Request::isPost()){
            $param = get_params();
            $isSubmit = isset($param['isSubmit']) ? $param['isSubmit'] : 0;
            $details = isset($param['details']) ? $param['details'] : [];
  
            // 验证 Token
            try {
                $check = validate()->rule(['__token__' => 'require|token'])->check(['__token__' => $param['__token__'] ?? '']);
                if (!$check) {
                  //  return json(['code' => 1, 'msg' => '请勿重复提交表单']);
                }
            } catch (\Exception $e) {
                  //   return json(['code' => 1, 'msg' => '请勿重复提交表单：' . $e->getMessage()]);
            }
            

            if (empty($details)) {
                return to_assign(1, '订单明细不能为空');
            }
          
          
            Db::startTrans();
            try {
                // 生成订单号 order_date
                $param['create_user_id'] = session('admin.id'); 
                $param['order_no'] = $this->generateOrderNo();

                // 计算订单金额
                $total_amount = 0;
                $tax_amount = 0;
                foreach($param['details'] as &$item){
                    // 将 price 字段赋值给 unit_price 字段，保持兼容性
                    if (isset($item['price']) && !isset($item['unit_price'])) {
                        $item['unit_price'] = $item['price'];
                    }
                    $item['amount'] = $item['quantity'] * $item['unit_price'];
                    $item['tax_amount'] = $item['amount'] * ($item['tax_rate'] / 100);
                    $total_amount += $item['amount']+$item['tax_amount'];
                    //$tax_amount += $item['tax_amount']
                }
                

                $datastt = [
                    'order_no' => $param['order_no'],
                    'customer_id' => $param['customer_id'],
                    'order_type' => $param['order_type'],
                    'tax_amount' => $tax_amount,
                    'tax_rate' => $param['tax_rate'],
                    'total_amount' => $total_amount,
                    'delivery_date' =>strtotime($param['delivery_date']),
                    'remark' => $param['description'],
                    'create_user_id' => $this->uid,
                    'admin_id'=>$this->uid,
                    //'check_status' => 2,
                    'create_time' => time(),
                    'glf'=>$param['glf'],
                    'yunfei'=>$param['yunfei'],
                    'did' => $this->did,
                ];
       // 设置是否提交状态
        $orderData['isSubmit'] = $isSubmit;
            //添加审批流程
            // 设置审批流程相关数据
            $datastt['check_flow_id'] = 15; // 审核流程id固定为14
            $datastt['check_step_sort'] = 0; // 当前审核步骤排序值，初始为0
            $datastt['check_uids'] = ''; // 当前步骤审核人ids，初始为空，提交时会更新
            $datastt['check_last_uid'] = 0; // 上一步审核人id，初始为0
            $datastt['check_history_uids'] = ''; // 所有审核过的审核人id集合，初始为空
            
            // 如果是直接提交，获取第一步审核人
            if ($isSubmit == 1) {
                // 获取审批流程的第一步信息
                $step = Db::name('FlowStep')
                    ->where('flow_id', 14) // 流程ID为14
                    ->where('sort', 1) // 第一步
                    ->find();
                
                if ($step) {
                    $datastt['check_step_sort'] = 1; // 设置为第一步
                    $datastt['check_uids'] = $step['check_uids']; // 设置第一步审核人
                }
            }
            //添加审批流结束
            
                // 使用模型方式保存数据
                $orderModel = new OrderModel();
                if (!$orderModel->save($datastt)) {
                    throw new \Exception('订单保存失败');
                }
                $order_id = $orderModel->id;
                if (empty($order_id)) {
                    throw new \Exception('获取订单ID失败');
                }
                // 保存订单明细
                foreach ($param['details'] as $item2) {

                      // 提取附件ID
                            if (isset($item2['attachments']) && !empty($item2['attachments'])) {
                                $attachmentIds = [];
                                foreach ($item2['attachments'] as $attachment) {
                                    if (isset($attachment['id'])) {
                                        $attachmentIds[] = $attachment['id'];
                                    }
                                }
                                // 将附件ID转换为逗号分隔的字符串
                                $item2['attachment_id'] = !empty($attachmentIds) ? implode(',', $attachmentIds) : '';
                            } elseif (isset($item2['attachment_id'])) {
                                // 保持现有的attachment_id
                                $item2['attachment_id'] = $item2['attachment_id'];
                            } else {
                                $item2['attachment_id'] = '';
                            }


                        $itemData = [
                            'order_id' => $order_id,
                            'product_id' => $item2['product_id'],   
                            'material_code' => $item2['material_code'],
                            'product_name' => $item2['product_name'],
                            'quantity' => $item2['quantity'],
                            'unit_price' => $item2['price'],
                            'amount' => $item2['amount'],
                            'tax_rate' => $item2['tax_rate'],
                            'tax_amount' => $item2['tax_amount'],
                            'create_time' => time(),
                            'attachment' => $item2['attachment_id'],
                            // 添加父子商品关系字段
                            'parent_product_id' => isset($item2['parent_product_id']) ? $item2['parent_product_id'] : 0,
                            'is_child' => isset($item2['is_child']) ? 1 : 0,
                        ];

                       
                        $orderDetailModel = new OrderDetailModel();
                        if (!$orderDetailModel->save($itemData)) {
                            throw new \Exception('订单详情保存失败');
                        }
                }  
                
                // 添加日志
                add_log('add', $order_id, $datastt);
                //添加编辑日志
                $log=new EditLog();
			    $log->add('customer_order',$order_id);

                // 检查订单物料需求
                $this->checkOrderMaterialRequirements($order_id);
                
 
                Db::commit();
                return json(['code' => 0, 'msg' => '添加成功', 'data' => ['order_id' => $order_id]]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '添加失败：'.$e->getMessage(), 'data' => null]);
            }
        
        }else{
            return view();
        }
        
        
    }
    
    /**
     * 编辑订单
     */
    public function edit($id)
    {
        if(Request::isPost()){
            $data = input('post.');
            
            // 数据验证
            $validate = validate('Order');
            if(!$validate->check($data)){
                return json(['code' => 1, 'msg' => $validate->getError()]);
            }
            
            // 检查订单状态
            $order = OrderModel::where('id', $id)->find();
            if(!$order){
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }
            
            if($order['check_status'] > 0){
                return json(['code' => 1, 'msg' => '已审核的订单不能修改','data' => null]);
            }
            
            Db::startTrans();
            try {
                // 格式化交货日期
                if (isset($data['delivery_date']) && !is_numeric($data['delivery_date'])) {
                    $data['delivery_date'] = strtotime($data['delivery_date']);
                }
                
                $data['update_time'] = time();
                
                // 计算订单金额
                $total_amount = 0;
                $tax_amount = 0;
                

                // 处理订单明细数据
                // 检查是否使用details字段（自动保存提交的格式）
                if (isset($data['details']) && !empty($data['details']) && empty($data['items'])) {
                    $data['items'] = $data['details'];
                }


                // 检查items是否存在
                if (empty($data['items'])) {
                    throw new \Exception('订单明细不能为空');
                }
                
                foreach($data['items'] as &$item){
                    // 将 price 字段赋值给 unit_price 字段，保持兼容性
                    if (isset($item['price']) && !isset($item['unit_price'])) {
                        $item['unit_price'] = $item['price'];
                    }
                    // 确保数值类型正确
                    $item['quantity'] = floatval($item['quantity']);
                    $item['unit_price'] = floatval($item['unit_price']);
                    $item['tax_rate'] = floatval($item['tax_rate']);
                    // 计算金额
                    $item['amount'] = $item['quantity'] * $item['unit_price'];
                    $item['tax_amount'] = $item['amount'] * ($item['tax_rate'] / 100);
                    // 累加总金额
                    $total_amount += $item['amount'];
                    $tax_amount += $item['tax_amount'];
                }
                
                // 更新订单主表数据
                $orderData = [
                    'id' => $id,
                    'customer_id' => $data['customer_id'],
                    'order_type' => $data['order_type'],
                    'delivery_date' => $data['delivery_date'],
                    'tax_rate' => $data['tax_rate'],
                    'total_amount' => $total_amount,
                    'tax_amount' => $tax_amount,
                    'remark' => $data['description'] ?? '',
                    'glf' => $data['glf'] ?? 0,
                    'yunfei' => $data['yunfei'] ?? 0,
                    'update_time' => time()
                ];
                
                // 更新订单主表
                OrderModel::update($orderData);
                
                // 删除原订单明细
               // OrderDetailModel::where('order_id', $id)->delete(true); // 真实删除

               // 删除原订单明细 - 修复删除不彻底的问题
                try {
                    // 先尝试强制删除（真实删除）
                    Db::name('customer_order_detail')->where('order_id', $id)->delete(true);
                } catch (\Exception $e) {
                    // 如果强制删除失败，则使用直接执行SQL的方式删除
                    Db::execute("DELETE FROM customer_order_detail WHERE order_id = ?", [$id]);
                }
                
                // 保存新订单明细
                foreach($data['items'] as $itemmm){

                       // 提取附件ID
                       if (isset($itemmm['attachments']) && !empty($itemmm['attachments'])) {
                        $attachmentIds = [];
                        foreach ($itemmm['attachments'] as $attachment) {
                            if (isset($attachment['id'])) {
                                $attachmentIds[] = $attachment['id'];
                            }
                        }
                        // 将附件ID转换为逗号分隔的字符串
                        $itemmm['attachment_id'] = !empty($attachmentIds) ? implode(',', $attachmentIds) : '';
                    } elseif (isset($itemmm['attachment_id'])) {
                        // 保持现有的attachment_id
                        $itemmm['attachment_id'] = $itemmm['attachment_id'];
                    } else {
                        $itemmm['attachment_id'] = '';
                    }


                    $itemData = [
                        'order_id' => $id,
                        'product_id' => $itemmm['product_id'],
                        'material_code' => $itemmm['material_code'],
                        'product_name' => $itemmm['product_name'],
                        'quantity' => $itemmm['quantity'],
                        'unit_price' => $itemmm['unit_price'],
                        'amount' => $itemmm['amount'],
                        'tax_rate' => $itemmm['tax_rate'],
                        'tax_amount' => $itemmm['tax_amount'],
                        'create_time' => time(),
                        'update_time' => time(),
                        'attachment' => $itemmm['attachment_id'],
                        // 添加父子商品关系字段
                        'parent_product_id' => isset($itemmm['parent_product_id']) ? $itemmm['parent_product_id'] : 0,
                        'is_child' => isset($itemmm['is_child']) ? 1 : 0,
                    ];
                    
                    $orderDetailModel = new OrderDetailModel();
                    $orderDetailModel->save($itemData);
                }
                
                // 添加日志
                add_log('edit', $id, $data);
                //添加编辑日志
                $log=new EditLog();
			    $log->add('customer_order',$id);
                
                // 检查订单物料需求
                $this->checkOrderMaterialRequirements($id);
                
                // 提交事务
                Db::commit();
                return json(['code' => 0, 'msg' => '修改成功','data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '修改失败：'.$e->getMessage(),'data' => null]);
            }
        }
        
        $order = OrderModel::where('id', $id)->find();
        if (!$order) {
            return $this->error('订单不存在');
        }
        
        // 格式化日期
        if (isset($order['delivery_date']) && $order['delivery_date']) {
            $order['delivery_date'] = date('Y-m-d', $order['delivery_date']);
        }
        
        // 获取订单明细，并转为数组
        $items = OrderDetailModel::where('order_id', $id)->select()->toArray();
        foreach ($items as &$item) { 
            // 处理附件逻辑
            $attachmentIds = !empty($item['attachment']) ? explode(',', $item['attachment']) : [];
            $attachmentList = [];
        
            if (!empty($attachmentIds)) {
                $fileList = Db::name('file')
                    ->whereIn('id', $attachmentIds)
                    ->field('id, name, filepath as url, fileext')
                    ->select()
                    ->toArray();
        
                foreach ($fileList as $file) {
                    $attachmentList[] = [
                        'id' => $file['id'],
                        'name' => $file['name'],
                        'url' => $file['url'],
                        'fileext' => $file['fileext']
                    ];
                }
            }
        
            $item['attachments'] = $attachmentList; 
            $product = Db::name('product')->where('id', $item['product_id'])->find();
            $item['product_specs'] = $product['specs'];
            $item['unit'] = $product['unit'];


        }
        unset($item); 
       // print_r(json_encode($items));
        
        return view('', ['order' => $order, 'items' => $items]);
    }
    
    /**
     * 删除订单
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return json(['code' => 1, 'msg' => '参数错误：缺少id', 'data' => null]);
            }
            // 检查订单状态
            $order = OrderModel::where('id', $id)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }
            
            if ($order['check_status'] > 0) {
                return json(['code' => 1, 'msg' => '已审核的订单不能删除', 'data' => null]);
            }
            
            $orderDetails = OrderDetailModel::where('order_id', $id)->select();
            foreach ($orderDetails as $detail) {
                if ($detail['status'] != 0) {
                    return json(['code' => 1, 'msg' => '订单明细中存在无法删除的项目', 'data' => null]);
                }
            }
            
            Db::startTrans();
            try {
                $order->delete();
                $orderData = $order->toArray();
                foreach ($orderDetails as $detail) {
                    $detail->delete();
                }
                
                // 添加日志
                add_log('delete', $id, $orderData);
                Db::commit();
                return json(['code' => 0, 'msg' => '删除成功', 'data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage(), 'data' => null]);
            }
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误', 'data' => null]);
    }
    
    /**
     * 审核订单
     */
    public function check($id)
    {
        if(Request::isPost()){
            $data = input('post.');
            
            // 检查订单状态
            $order = Db::name('order')->where('id', $id)->find();
            if($order['status'] > 0){
                return json(['code' => 1, 'msg' => '订单已审核']);
            }
            
            // 检查账期订单的信用额度
            if($order['order_type'] == 2){
                $customer = Db::name('customer')->where('id', $order['customer_id'])->find();
                if($customer['credit_limit'] < $order['total_amount']){
                    return json(['code' => 1, 'msg' => '客户信用额度不足']);
                }
            }
            
            // 检查订单状态，避免重复审核
            if ($order['check_status'] == 1) {
                return json(['code' => 1, 'msg' => '订单已审核，请勿重复操作']);
            }

            // 获取订单详情
            $orderDetails = Db::name('order_detail')->where('order_id', $id)->select();
            if (empty($orderDetails)) {
                return json(['code' => 1, 'msg' => '订单详情不存在']);
            }

            Db::startTrans();
            try {
                // 使用新的库存分配服务
                $allocationService = new \app\warehouse\service\InventoryAllocationService();

                // 🔧 修复：检查是否已有分配记录，如果有则先清理（防止重复审核）
                // 但要允许同一产品的多次分配需求（因为订单明细中可能有重复产品）
                $existingCount = Db::name('inventory_allocation_request')
                    ->where('ref_type', 'customer_order')
                    ->where('ref_id', $id)
                    ->where('status', 'in', [1, 2, 3])
                    ->count();

                if ($existingCount > 0) {
                    // 先清理已有的分配记录
                    $allocationService->cleanupBusinessAllocation('customer_order', $id, '重复审核前清理');
                }
                $allocationResults = [];
                $hasPartialAllocation = false;

                // 🔧 优化：合并相同产品的数量，避免重复记录
                $productRequests = [];
                foreach ($orderDetails as $detail) {
                    $warehouseId = $detail['warehouse_id'] ?? 1;
                    $key = $detail['product_id'] . '_' . $warehouseId;

                    if (!isset($productRequests[$key])) {
                        $productRequests[$key] = [
                            'product_id' => $detail['product_id'],
                            'warehouse_id' => $warehouseId,
                            'quantity' => 0,
                            'ref_type' => 'customer_order',
                            'ref_id' => $id,
                            'ref_no' => $order['order_no'],
                            'notes' => '销售订单审核锁定',
                            'created_by' => session('admin.id')
                        ];
                    }

                    // 累加相同产品的数量
                    $productRequests[$key]['quantity'] += $detail['quantity'];
                }

                // 对合并后的产品需求进行分配
                foreach ($productRequests as $request) {
                    $result = $allocationService->allocateAndLock($request);
                    $allocationResults[] = $result;

                    // 检查是否有部分分配或待分配的情况
                    if ($result['status'] === 'partial' || $result['status'] === 'pending') {
                        $hasPartialAllocation = true;
                    }
                }

                // 根据分配结果确定订单状态
                $orderStatus = $hasPartialAllocation ? 3 : 1; // 3=待补货, 1=已审核

                $update = [
                    'status' => $orderStatus,
                    'check_status' => 1,
                    'check_user_id' => session('admin.id'),
                    'check_time' => time(),
                    'update_time' => time()
                ];

                // 更新订单状态
                Db::name('order')->where('id', $id)->update($update);

                Db::commit();

                $message = $hasPartialAllocation ? '审核成功，部分商品库存不足已加入待补货队列' : '审核成功';
                return json(['code' => 0, 'msg' => $message, 'data' => $allocationResults]);

            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '审核失败：'.$e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求']);
    }
    
    /**
     * 获取新的表单令牌
     */
    public function get_token()
    {
        $token = token();
        return json(['code' => 0, 'msg' => 'success', 'data' => $token]);
    }
    
    /**
     * 生成订单号
     */
    private function generateOrderNo()
    {
        $prefix = 'XS' . date('Ymd');
        $maxRetries = 3; // 最大重试次数
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            // 开启事务
            Db::startTrans();
            try {
                // 使用FOR UPDATE锁定查询结果
                $maxNumber = Db::name('customer_order')
                    ->where('order_no', 'like', $prefix . '%')
                    ->order('id', 'desc')
                    ->lock(true)
                    ->value('order_no');
                
                if ($maxNumber) {
                    // 提取最后4位数字并加1
                    $lastNumber = intval(substr($maxNumber, -4));
                    $newNumber = $lastNumber + 1;
                } else {
                    // 如果没有记录，从0001开始
                    $newNumber = 1;
                }
                
                // 格式化为4位数字，不足补0
                $newPlanNo = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
                
                // 直接尝试插入一条测试记录
                Db::name('customer_order')->insert([
                    'order_no' => $newPlanNo,
                    'status' => -1, // 使用特殊状态标记为临时记录
                    'create_time' => time()
                ]);
                
                // 如果插入成功，删除测试记录并提交事务
                Db::name('customer_order')->where('order_no', $newPlanNo)->where('status', -1)->delete();
                Db::commit();
                
                return $newPlanNo;
            } catch (\Exception $e) {
                Db::rollback();
                $retryCount++;
                
                if ($retryCount >= $maxRetries) {
                    throw new \Exception('生成编号失败，请重试');
                }
                
                // 短暂延迟后重试
                usleep(100000); // 延迟100毫秒
            }
        }
        
        throw new \Exception('生成编号失败，请重试');
    }
    
    /**
     * 锁定库存
     */
    protected function lockInventory($order_id)
    {
        $order = Db::name('order')->where('id', $order_id)->find();
        $items = Db::name('order_detail')->where('order_id', $order_id)->select();
        
        foreach($items as $item){
            // 获取可用库存
            $inventory = Db::name('inventory')
                ->where('product_id', $item['product_id'])
                ->where('status', 1)
                ->order('create_time asc')
                ->select();
                
            $remain_quantity = $item['quantity'];
            
            foreach($inventory as $stock){
                if($remain_quantity <= 0) break;
                
                $lock_quantity = min($stock['quantity'], $remain_quantity);
                
                // 锁定库存
                Db::name('inventory_lock')->insert([
                    'order_id' => $order_id,
                    'inventory_id' => $stock['id'],
                    'product_id' => $item['product_id'],
                    'quantity' => $lock_quantity,
                    'create_time' => time()
                ]);
                
                $remain_quantity -= $lock_quantity;
            }
            
            // 库存不足，标记为待补货
            if($remain_quantity > 0){
                Db::name('order')->where('id', $order_id)->update(['status' => 3]);
                break;
            }
        }
    }

    /**
     * 选择商品页面
     */
    public function selectProduct()
    {
        // 获取产品分类
        $cate = Db::name('ProductCate')
            ->where('status', 1)
            ->where('delete_time', 0)
            ->field('id, pid, title, code, sort')
            ->order('sort asc, create_time asc')
            ->select()
            ->toArray();
            
        // 生成树形结构
        $cates = generateTree($cate);
        
        return view('selectProduct', [
            'cates' => $cates
        ]);
    }

    /**
     * 选择子商品页面
     */
    public function selectChildProduct()
    {
        // 获取父商品ID，如果有的话
        $parent_id = input('parent_id', 0);
        
        // 获取产品分类
        $cate = Db::name('ProductCate')
            ->where('status', 1)
            ->where('delete_time', 0)
            ->field('id, pid, title, code, sort')
            ->order('sort asc, create_time asc')
            ->select()
            ->toArray();
            
        // 生成树形结构
        $cates = generateTree($cate);
        
        return view('selectChildProduct', [
            'cates' => $cates,
            'parent_id' => $parent_id
        ]);
    }
    
    //获取产品列表
    public function get_product_datalist()
    {
        $param = get_params();
        $where = [];
        
        // 处理搜索参数
        if (!empty($param['keywords'])) {
            // 保留对keywords参数的支持（向后兼容）
            $where[] = ['p.title|p.material_code', 'like', '%' . $param['keywords'] . '%'];
        } else {
            // 新增对code和name参数的支持
            if (!empty($param['code'])) {
                $where[] = ['p.material_code', 'like', '%' . $param['code'] . '%'];
            }
            if (!empty($param['name'])) {
                $where[] = ['p.title', 'like', '%' . $param['name'] . '%'];
            }
        }
        
        // 处理其他过滤条件
        if (isset($param['status']) && $param['status'] !== '') {
            $where[] = ['p.status', '=', $param['status']];
        }
        if (!empty($param['cate_id'])) {
            $cate_id_array = get_cate_son('ProductCate', $param['cate_id']);
            $where[] = ['p.cate_id', 'in', $cate_id_array];
        }
        $where[] = ['p.delete_time', '=', 0];
        
        $cp = new ProductModel();
        $list = $cp->datalist($where, $param);
        return table_assign(0, '', $list);
    }

    /**
     * 查看订单详情
     */
    public function view($id)
    {
        // 根据权限检查是否有查看该订单的权限
        $uid = $this->uid;
        
        
        $order = OrderModel::where('id', $id)->find();

        
        if (!$order) {
            return $this->error('订单不存在');
        }
        
        // 权限检查
        $auth = isAuth($uid, 'customer_admin', 'conf_1');
     
        // 获取订单详情
        $order = OrderModel::with(['customer', 'adduser', 'checkuser'])->find($id);
        if (!$order) {
            return $this->error('订单不存在');
        }
        
        // 格式化日期
        $order['delivery_date'] =  date('Y-m-d', $order['delivery_date']);
        $order['create_time_format'] =  $order['create_time'];
        $order['check_time_format'] =  '';
        
        // 状态文本
        $statusTexts = [
            0 => '未审核',
            1 => '待审核',
            2 => '已审核',
            3 => '已拒绝',
        ];
        $order['status_text'] = isset($statusTexts[$order['check_status']]) ? $statusTexts[$order['check_status']] : '未知';
        
        // 状态样式
        $statusClasses = [
            0 => 'layui-bg-orange',
            1 => 'layui-bg-blue',
            2 => 'layui-bg-green', 
            3 => 'layui-bg-red',
            4 => 'layui-bg-green',
            5 => 'layui-bg-gray'
        ];
        $order['status_class'] = isset($statusClasses[$order['status']]) ? $statusClasses[$order['status']] : 'layui-bg-gray';
        
        // 订单类型文本
        $order['order_type_text'] = $order['order_type'] == 1 ? '现金订单' : '账期订单';
        
        // 获取订单明细
        $items = OrderDetailModel::with('product')->where('order_id', $id)->select()->toArray();
        
        // 重组订单明细数据，按照父子关系排序
        $organizedItems = [];
        $childItems = [];
        $parentProducts = []; // 保存父商品信息，用于后续给子商品添加父商品名称
        
        // 先分离父商品和子商品
        foreach ($items as $item) {
            // 如果是子商品（有parent_product_id），加入子商品数组
            if (!empty($item['parent_product_id'])) {
                if (!isset($childItems[$item['parent_product_id']])) {
                    $childItems[$item['parent_product_id']] = [];
                }
                $item['is_child'] = true; // 标记为子商品
                $childItems[$item['parent_product_id']][] = $item;
            } else {
                // 父商品直接加入结果数组
                $item['is_child'] = false; // 标记为父商品
                $item['has_children'] = false; // 初始化是否有子商品标记
                $organizedItems[] = $item;
                // 保存父商品信息，便于后续查找
                $parentProducts[$item['product_id']] = $item;
            }
        }
        
        // 将子商品插入到其父商品之后
        $finalItems = [];
        foreach ($organizedItems as $parent) {
            // 检查是否有子商品
            $hasChildren = isset($childItems[$parent['product_id']]) && !empty($childItems[$parent['product_id']]);
            
            // 更新父商品信息
            $parent['has_children'] = $hasChildren;
            $parent['child_count'] = $hasChildren ? count($childItems[$parent['product_id']]) : 0;
            
            $finalItems[] = $parent;
            
            // 如果有子商品，则在父商品后添加
            if ($hasChildren) {
                foreach ($childItems[$parent['product_id']] as $child) {
                    // 为子商品添加父商品的名称，方便前端显示
                    $child['parent_product_name'] = $parent['product_name'];
                    
                    // 计算倍率：子商品数量 / 父商品数量
                    $parentQuantity = floatval($parent['quantity']);
                    $childQuantity = floatval($child['quantity']);
                    if ($parentQuantity > 0) {
                        $child['ratio'] = $childQuantity / $parentQuantity;
                    } else {
                        $child['ratio'] = 0;
                    }
                    
                    $finalItems[] = $child;
                }
            }
        }
     
        // 计算合计金额
        $totalAmount = 0;
        $totalTax = 0;
        $totalWithTax = 0;
        foreach ($finalItems as &$item) {
            $totalAmount += floatval($item['amount'] ?? 0);
            $totalTax += floatval($item['tax_amount'] ?? 0);
            $totalWithTax += floatval($item['amount'] ?? 0) + floatval($item['tax_amount'] ?? 0);
            
            // 计算可用库存
            $inventory = get_inventory_stock($item['product_id']);
            
            $item['stock'] = $inventory;
            // 获取BOM表
            // 检查产品是否有有效的BOM
            $item['has_bom'] = has_product_bom($item['product_id']) ? '有' : '无';
            $item['source_type'] = $item['product']['source_type']==1?'自产':'外购';
        }
        
        // 如果订单主表没有合计金额，使用计算的值
        if (empty($order['total_amount'])) {
            $order['total_amount'] = number_format($totalAmount, 2);
        }
        
        if (empty($order['tax_amount'])) {
            $order['tax_amount'] = number_format($totalTax, 2);
        }
        
        // 构建审批按钮HTML
        $approvalButtons = '';
        
        return view('', [
            'order' => $order, 
            'items' => $finalItems, // 使用重组后的明细数据
            'totalAmount' => number_format($totalAmount, 2),
            'totalTax' => number_format($totalTax, 2),
            'is_leader' => isLeader($this->uid),
            'approvalButtons' => $approvalButtons
        ]);
        
    }
    public function Delivery_without_payment(){
         
        $data = get_params();
        $order = OrderModel::where('id', $data['id'])->find();
        $order['delivery_flag'] = 1;
        $order->save();
        $log=new EditLog();
        $log->add_log('customer_order',$order['id'],'重要操作:【 同意 】未收款发货 。备注：'.$data['remark'].' 。操作人:'.get_admin($this->uid)['name']);


        return json(['code' => 0, 'msg' => '同意未收款发货','data'=>null]);
    }

    /**
     * 检查是否可以反审核
     */
    public function checkReverseAudit()
    {
        $id = input('id');

        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误', 'data' => []]);
        }

        // 1. 基础检查
        $checkResult = $this->checkReverseAuditConditions($id);
        if ($checkResult['code'] != 0) {
            return json($checkResult);
        }

        // 2. 关联业务检查
        $businessCheckResult = $this->checkRelatedBusiness($id);
        if ($businessCheckResult['code'] != 0) {
            return json($businessCheckResult);
        }

        return json(['code' => 0, 'msg' => '可以反审核', 'data' => []]);
    }

    /**
     * 反审核订单
     */
    public function reverseAudit()
    {
        $id = input('id');

        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误', 'data' => []]);
        }

        // 1. 基础检查
        $checkResult = $this->checkReverseAuditConditions($id);
        if ($checkResult['code'] != 0) {
            return json($checkResult);
        }

        // 2. 关联业务检查
        $businessCheckResult = $this->checkRelatedBusiness($id);
        if ($businessCheckResult['code'] != 0) {
            return json($businessCheckResult);
        }

        // 3. 执行反审核
        return $this->executeReverseAudit($id);
    }

    /**
     * 检查反审核基础条件
     */
    private function checkReverseAuditConditions($orderId)
    {
        // 检查功能是否启用
        if (!config('reverse_audit.enabled', true)) {
            return ['code' => 1, 'msg' => '反审核功能已禁用', 'data' => []];
        }

        $order = Db::name('customer_order')->find($orderId);

        if (!$order) {
            return ['code' => 1, 'msg' => '订单不存在', 'data' => []];
        }

        // 检查订单状态
        if ($order['check_status'] != 2) {
            return ['code' => 1, 'msg' => '只能反审核已审核的订单', 'data' => []];
        }

        // 检查权限
        $permissionResult = $this->checkReverseAuditPermission($order);
        if ($permissionResult['code'] != 0) {
            return $permissionResult;
        }

        // 检查时间限制
        $timeResult = $this->checkTimeLimit($order);
        if ($timeResult['code'] != 0) {
            return $timeResult;
        }

        return ['code' => 0, 'msg' => '基础检查通过', 'data' => []];
    }

    /**
     * 检查反审核权限
     */
    private function checkReverseAuditPermission($order)
    {
        $currentUserId = $this->uid;
        $userRole = session('admin.role_id');
        $config = config('reverse_audit.permissions', []);

        // 系统管理员权限
        $adminRoleIds = $config['admin_role_ids'] ?? [1];
        if (in_array($userRole, $adminRoleIds)) {
            return ['code' => 0, 'msg' => '管理员权限', 'data' => []];
        }

        // 订单创建者权限
        if ($config['self_only'] ?? true) {
            if ($order['admin_id'] == $currentUserId) {
                return ['code' => 0, 'msg' => '创建者权限', 'data' => []];
            } else {
                return ['code' => 1, 'msg' => '只能反审核自己创建的订单', 'data' => []];
            }
        }

        // 部门主管权限（可选实现）
        if ($config['department_manager'] ?? false) {
            if ($this->isDepartmentManager($currentUserId, $order['admin_id'])) {
                return ['code' => 0, 'msg' => '部门主管权限', 'data' => []];
            }
        }

        return ['code' => 1, 'msg' => '无权限反审核此订单', 'data' => []];
    }

    /**
     * 检查时间限制
     */
    private function checkTimeLimit($order)
    {
        $timeLimit = config('reverse_audit.time_limit', 0);

        if ($timeLimit > 0) {
            $checkTime = $order['check_time'];

            if (time() - $checkTime > $timeLimit) {
                return [
                    'code' => 1,
                    'msg' => '超过反审核时间限制（' . ($timeLimit / 3600) . '小时）',
                    'data' => []
                ];
            }
        }

        return ['code' => 0, 'msg' => '时间检查通过', 'data' => []];
    }

    /**
     * 检查是否为部门主管（可选实现）
     */
    private function isDepartmentManager($managerId, $employeeId)
    {
        // 这里可以实现部门主管检查逻辑
        // 例如：检查managerId是否为employeeId所在部门的主管
        return false;
    }

    /**
     * 检查关联业务是否允许反审核
     */
    private function checkRelatedBusiness($orderId)
    {
        $config = config('reverse_audit.checks', []);
        $blockingReasons = [];

        // 物料需求检查已移除，改为使用库存分配表检查

        // 检查采购订单
        if ($config['purchase_order'] ?? true) {
            $purchaseCount = Db::name('purchase_order')
                ->where('source_order_id', $orderId)
                ->where('source_type', 'customer_order')
                ->where('status', '>', 0)
                ->count();
            if ($purchaseCount > 0) {
                $blockingReasons[] = "存在{$purchaseCount}个关联的采购订单";
            }
        }

        // 检查生产订单
        if ($config['produce_order'] ?? true) {
            $produceCount = Db::name('purchase_requirement_order')
                ->alias('pro')
                ->join('produce_order po', 'pro.purchase_id = po.id')
                ->where('pro.order_id', $orderId)
                ->where('po.status', '>', 0)
                ->count();
            if ($produceCount > 0) {
                $blockingReasons[] = "存在{$produceCount}个关联的生产订单";
            }
        }

        // 库存锁定记录不阻止反审核，反审核时会自动清理

        // 检查发货记录
        if ($config['delivery'] ?? true) {
            $deliveryCount = Db::name('customer_order_delivery')
                ->where('order_id', $orderId)
                ->where('status', '>', 0)
                ->count();
            if ($deliveryCount > 0) {
                $blockingReasons[] = "存在{$deliveryCount}条发货记录";
            }
        }

        // 检查付款记录
        if ($config['payment'] ?? true) {
            $paymentCount = Db::name('customer_order_payment')
                ->where('order_id', $orderId)
                ->count();
            if ($paymentCount > 0) {
                $blockingReasons[] = "存在{$paymentCount}条付款记录";
            }
        }

        // 检查发票记录
        if ($config['invoice'] ?? false) {
            $invoiceCount = Db::name('invoice_order')
                ->where('order_id', $orderId)
                ->count();
            if ($invoiceCount > 0) {
                $blockingReasons[] = "存在{$invoiceCount}条发票记录";
            }
        }

       
        if (!empty($blockingReasons)) {
            return [
                'code' => 1,
                'msg' => '无法反审核，原因：' . implode('；', $blockingReasons),
                'data' => $blockingReasons
            ];
        }

        return ['code' => 0, 'msg' => '检查通过', 'data' => []];
    }

    /**
     * 执行反审核操作
     */
    private function executeReverseAudit($orderId)
    {
        $config = config('reverse_audit.cleanup', []);

        Db::startTrans();
        try {
            // 1. 更新订单状态
            Db::name('customer_order')->where('id', $orderId)->update([
                'status' => 0,           // 回到草稿状态
                'check_status' => 0,     // 未审核
                'check_time' => 0,       // 清空审核时间
                'check_user_id' => 0,    // 清空审核人
                'update_time' => time()
            ]);

            // 2. 清理库存分配请求记录（仅清理未处理的）
            if ($config['clean_allocation_request'] ?? true) {
                $deletedCount = Db::name('inventory_allocation_request')
                    ->where('ref_type', 'customer_order')
                    ->where('ref_id', $orderId)
                    ->where('status', 'in', [1, 2]) // 1=待分配, 2=部分分配
                    ->delete();

                \think\facade\Log::info('反审核清理分配请求记录', [
                    'order_id' => $orderId,
                    'deleted_count' => $deletedCount
                ]);
            }

            // 3. 释放库存锁定
            if ($config['release_inventory_lock'] ?? true) {
                $this->releaseInventoryLocks($orderId);
            }

            // 4. 记录操作日志
            if (config('reverse_audit.logging.enabled', true)) {
                $this->addOrderLog($orderId, '订单反审核', '订单已反审核，回到草稿状态');
            }

            Db::commit();
            return json(['code' => 0, 'msg' => '反审核成功', 'data' => []]);

        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '反审核失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 释放订单相关的库存锁定
     */
    private function releaseInventoryLocks($orderId)
    {
        $locks = Db::name('inventory_lock')
            ->where('ref_type', 'customer_order')
            ->where('ref_id', $orderId)
            ->where('status', 1)
            ->select();

        foreach ($locks as $lock) {
            // 释放锁定库存
            Db::name('inventory_lock')
                ->where('id', $lock['id'])
                ->update([
                    'status' => 3, // 已释放
                    'update_time' => time()
                ]);

            // 更新实时库存
            Db::name('inventory_realtime')
                ->where('product_id', $lock['product_id'])
                ->where('warehouse_id', $lock['warehouse_id'])
                ->inc('available_quantity', $lock['quantity'])
                ->dec('locked_quantity', $lock['quantity'])
                ->update(['update_time' => time()]);

            \think\facade\Log::info('反审核释放库存锁定', [
                'order_id' => $orderId,
                'lock_id' => $lock['id'],
                'product_id' => $lock['product_id'],
                'warehouse_id' => $lock['warehouse_id'],
                'quantity' => $lock['quantity']
            ]);
        }
    }

    /**
     * 添加订单操作日志
     */
    private function addOrderLog($orderId, $action, $content)
    {
        try {
            // 使用系统标准的 oa_edit_log 表
            Db::name('edit_log')->insert([
                'name' => 'customer_order',
                'field' => $action,
                'action_id' => $orderId,
                'admin_id' => $this->uid,
                'old_content' => '',
                'new_content' => $content,
                'create_time' => time()
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 简单审核（自审核）
     */
    public function simpleCheck()
    {
        $id = input('id');

        if($id){
            $order = OrderModel::find($id);
            if(!$order){
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }

            // 检查权限 - 只能审核自己创建的订单
            if($order->admin_id != $this->uid){
                return json(['code' => 1, 'msg' => '只能审核自己创建的订单', 'data' => null]);
            }

            // 检查订单状态
            if($order->check_status != 0){
                return json(['code' => 1, 'msg' => '订单已审核，无法重复审核', 'data' => null]);
            }

            Db::startTrans();
            try {
                // 获取订单详情进行库存检查和需求分析
                $orderDetails = Db::name('customer_order_detail')->where('order_id', $id)->select();

                // 初始化服务
                $allocationService = new \app\warehouse\service\InventoryAllocationService();
                $allocationResults = [];
                $hasPartialAllocation = false;

                // 🔧 优化：合并相同产品的数量，避免重复记录
                $productRequests = [];
                foreach ($orderDetails as $detail) {
                    $warehouseId = $detail['warehouse_id'] ?? 1;
                    $key = $detail['product_id'] . '_' . $warehouseId;

                    if (!isset($productRequests[$key])) {
                        $productRequests[$key] = [
                            'product_id' => $detail['product_id'],
                            'warehouse_id' => $warehouseId,
                            'quantity' => 0,
                            'ref_type' => 'customer_order',
                            'ref_id' => $id,
                            'ref_no' => $order['order_no'],
                            'notes' => '销售订单自审核锁定',
                            'created_by' => $this->uid,
                            'detail_ids' => [] // 记录相关的明细ID
                        ];
                    }

                    // 累加相同产品的数量并记录明细ID
                    $productRequests[$key]['quantity'] += $detail['quantity'];
                    $productRequests[$key]['detail_ids'][] = $detail['id'];
                }

                // 对每个订单明细更新库存状态
                foreach ($orderDetails as $detail) {
                    $productId = $detail['product_id'];
                    $requiredQty = $detail['quantity'];
                    $warehouseId = $detail['warehouse_id'] ?? 1;

                    // 使用公共库存查询服务获取产品可用库存
                    $inventoryStatus = \app\common\service\InventoryQueryService::getProductAvailableQuantity($productId, $warehouseId);
                    $availableQty = $inventoryStatus['available_quantity'];
                    $totalQty = $inventoryStatus['total_quantity'];
                    $lockedQty = $inventoryStatus['locked_quantity'];

                    $requiredQty = floatval($requiredQty);

                    // 调试日志
                    trace("库存检查 - 产品ID: {$productId}, 仓库ID: {$warehouseId}, 需求数量: {$requiredQty}, 总库存: {$totalQty}, 锁定: {$lockedQty}, 可用库存: {$availableQty}", 'info');

                    // 判断库存状态和缺口数量
                    if ($availableQty >= $requiredQty) {
                        // 库存充足
                        $inventoryStatusCode = 1; // 库存
                        $gap = 0;
                        trace("库存充足 - 产品ID: {$productId}, 状态: 库存, 缺口: 0", 'info');
                    } else {
                        // 库存不足或无库存
                        $inventoryStatusCode = 2; // 缺货
                        $gap = $requiredQty - $availableQty;
                        if ($gap < 0) $gap = 0;
                        trace("库存不足 - 产品ID: {$productId}, 状态: 缺货, 缺口: {$gap}", 'info');
                    }

                    // 更新订单明细的库存状态
                    Db::name('customer_order_detail')->where('id', $detail['id'])->update([
                        'inventory_status' => $inventoryStatusCode,
                        'gap' => $gap,
                        'update_time' => time()
                    ]);
                }

                // 对合并后的产品需求进行分配
                foreach ($productRequests as $request) {
                    $result = $allocationService->allocateAndLock($request);
                    $allocationResults[] = $result;

                    // 检查是否有部分分配或待分配的情况
                    if ($result['status'] === 'partial' || $result['status'] === 'pending') {
                        $hasPartialAllocation = true;
                    }
                }

                // 根据分配结果确定订单状态
                $orderStatus = $hasPartialAllocation ? 3 : 1; // 3=待补货, 1=已审核  

                // 更新订单状态为已审核
                $update = [
                    'status' => $orderStatus,
                    'check_status' => 2,
                    'check_time' => time(),
                    'check_user_id' => $this->uid,
                    'update_time' => time()
                ];

                // 更新订单状态
                Db::name('customer_order')->where('id', $id)->update($update);

                // 记录操作日志
                $log = new EditLog();
                $log->add_log('customer_order', $id, '订单自审核通过。操作人：' . get_admin($this->uid)['name']);

                Db::commit();
                return json(['code' => 0, 'msg' => '审核成功', 'data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '审核失败：' . $e->getMessage(), 'data' => null]);
            }
        }

        return json(['code' => 1, 'msg' => '非法请求', 'data' => null]);
    }

    /**
     * 获取关联单据
     */
    public function getRelatedDocuments()
    {
        $id = input('id');

        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误', 'data' => []]);
        }

        try {
            $relatedDocuments = [];

            // 1. 获取采购订单

            $purchaseOrders = Db::name('purchase_order')
                ->alias('po')
                ->leftJoin('admin a', 'po.created_by = a.id')
                ->where('po.source_order_id', $id)
                ->where('po.source_type', 'customer_order')
                ->field('po.id, po.order_no, po.status, po.create_time, a.nickname as creator_name')
                ->select();

            foreach ($purchaseOrders as $item) {
                $relatedDocuments[] = [
                    'id' => $item['id'],
                    'type' => 'purchase_order',
                    'type_text' => '采购订单',
                    'document_no' => $item['order_no'],
                    'status' => $item['status'],
                    'product_name' => '-',
                    'creator_name' => $item['creator_name'],
                    'create_time' => $item['create_time']
                ];
            }

            // 2. 库存调拨单 - 暂时跳过，因为调拨单表没有与订单的直接关联字段
            // 如果需要关联，可以通过调拨单明细中的产品与订单明细中的产品进行间接关联

            // 2. 获取发货单
            $deliveries = Db::name('customer_order_delivery')
                ->alias('cd')
                ->leftJoin('admin a', 'cd.creator_id = a.id')
                ->where('cd.order_id', $id)
                ->field('cd.id, cd.delivery_no, cd.status, cd.create_time, a.nickname as creator_name')
                ->select();

            foreach ($deliveries as $item) {
                $relatedDocuments[] = [
                    'id' => $item['id'],
                    'type' => 'delivery',
                    'type_text' => '发货单',
                    'document_no' => $item['delivery_no'],
                    'status' => $item['status'],
                    'product_name' => '-',
                    'creator_name' => $item['creator_name'],
                    'create_time' => $item['create_time']
                ];
            }

            // 4. 获取发票 - 暂时跳过，需要确认发票表的正确表名
            // 可能的表名：invoice, customer_invoice, oa_invoice 等

            // 按创建时间倒序排列
            usort($relatedDocuments, function($a, $b) {
                return $b['create_time'] - $a['create_time'];
            });

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $relatedDocuments]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 订单反审功能
     * 将已审核的订单状态恢复为未审核状态
     */
    public function revertAudit()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $reason = isset($param['reason']) ? trim($param['reason']) : '';
            
            if (empty($id)) {
                return json(['code' => 1, 'msg' => '参数错误：缺少id', 'data' => null]);
            }
            
            if (empty($reason)) {
                return json(['code' => 1, 'msg' => '请输入反审原因', 'data' => null]);
            }
            
            // 检查订单状态
            $order = OrderModel::where('id', $id)->find();
            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }
            
            // 只有已审核的订单才能反审
            if ($order['check_status'] != 2) {
              //  return json(['code' => 1, 'msg' => '只有已审核的订单才能反审', 'data' => null]);
            }
            
            Db::startTrans();
            try {
                // 更新订单状态为未审核
                $update = [
                    'status' => 0,
                    'check_status' => 0,
                    'update_time' => time()
                ];
                
                // 更新订单状态
                Db::name('customer_order')->where('id', $id)->update($update);
                
                // 解除库存锁定
               // Db::name('inventory_lock')->where('order_id', $id)->delete();
                
                // 记录操作日志
                $log = new EditLog();
                $log->add_log('customer_order', $id, '订单反审操作。原因：' . $reason . '。操作人：' . get_admin($this->uid)['name']);
                
                Db::commit();
                return json(['code' => 0, 'msg' => '反审成功', 'data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '反审失败：' . $e->getMessage(), 'data' => null]);
            }
        }
        
        return json(['code' => 1, 'msg' => '非法请求', 'data' => null]);
    }


    protected function checkOrderMaterialRequirements($orderId)
    {
        // 初始化结果数组
        $result = [
            'success' => true,
            'message' => '检查完成',
            'requirements' => []
        ];
        
        try {
            // 获取订单明细
            $orderDetails = Db::name('customer_order_detail')
                ->where('order_id', $orderId)
                ->where('delete_time', 0)
                ->select()
                ->toArray();

            if (empty($orderDetails)) {
                return $result;
            }
            
            // 遍历订单明细
            foreach ($orderDetails as $detail) {
                $productId = $detail['product_id'];
                $quantity = $detail['quantity'];
                $detailID = $detail['id'];
                
                // 获取产品信息
                $product = Db::name('product')
                    ->where('id', $productId)
                    ->find();
                
                if (!$product) {
                    continue;
                }
                
                // 检查产品是否有 BOM
                $bom = get_product_bom($productId);

                if ($bom) {
                    // 产品有 BOM，检查 BOM 中的物料库存
                    $bomItems = get_bom_items($bom['id']);
                    
                    foreach ($bomItems as $bomItem) {
                        $materialId = $bomItem['material_id'];
                        // 兼容新旧BOM表的字段名
                        $bomQuantity = $bomItem['quantity'] ?? $bomItem['qty'] ?? 0;
                        $requiredQuantity = $bomQuantity * $quantity;

                        // 获取物料库存信息 - 使用新的库存表
                        $inventory = get_inventory_stock($materialId);
                        
                        // 获取物料在途数量
                        $inTransitQuantity = Inventory::getInTransitQuantity($materialId);
                        
                        // 计算总可用库存
                        $totalAvailable = floatval($inventory) + floatval($inTransitQuantity);
                        $requiredQuantity = floatval($requiredQuantity);
                        
                        // 记录BOM物料需求信息（仅用于显示）
                        $result['requirements'][] = [
                            'product_id' => $productId,
                            'product_name' => $product['title'],
                            'material_id' => $materialId,
                            'material_name' => Db::name('product')->where('id', $materialId)->value('title'),
                            'required_quantity' => $requiredQuantity,
                            'available_quantity' => $totalAvailable,
                            'gap' => max(0, $requiredQuantity - $totalAvailable)
                        ];
                    }
                } else {
                    // 产品没有 BOM，直接检查产品库存
                    $inventory = Db::name('inventory')
                        ->where('product_id', $productId)
                        ->sum('quantity');

                    // 获取产品在途数量
                    $inTransitQuantity = Inventory::getInTransitQuantity($productId);

                    // 计算总可用库存
                    $totalAvailable = floatval($inventory) + floatval($inTransitQuantity);
                    $quantity = floatval($quantity);

                    // 记录产品需求信息（仅用于显示）
                    $result['requirements'][] = [
                        'product_id' => $productId,
                        'required_quantity' => $quantity,
                        'available_quantity' => $totalAvailable,
                        'gap' => max(0, $quantity - $totalAvailable)
                    ];
                }
            }
            
            // 如果有需求记录，更新结果消息
            if (!empty($result['requirements'])) {
                $result['message'] = '发现' . count($result['requirements']) . '个物料需求已记录';
            }
            
            return $result;
            
        } catch (\Exception $e) {
            trace('检查订单物料需求失败：' . $e->getMessage(), 'error');
            return [
                'success' => false,
                'message' => '检查订单物料需求失败：' . $e->getMessage(),
                'requirements' => []
            ];
        }
    }
    //销售订单财务审批
    public function cwlist()
    { 
  if(Request::isAjax()){
            $where = [];
            $page = input('page', 1);
            $limit = input('limit', 20);
            
            // 搜索条件
            $order_no = input('order_no', '');
            if($order_no){
                // 构建子查询，查找包含指定产品的订单ID
                $subQuery = Db::name('customer_order_detail')
                    ->where('product_name|material_code', 'like', "%{$order_no}%")
                    ->field('order_id')
                    ->buildSql();
                
                // 使用whereOr来组合订单号和产品信息的查询
                $where[] = ['', 'exp', Db::raw("(order_no LIKE '%{$order_no}%' OR id IN {$subQuery})")];
            }
            
            $customer_id = input('customer_id', 0);
            if($customer_id){
                $where[] = ['customer_id', '=', $customer_id];
            }
            
            $status = input('status', '');
            if($status !== ''){
                $where[] = ['status', '=', $status];
            }
            
            $order_type = input('order_type', '');
            if($order_type !== ''){
                $where[] = ['order_type', '=', $order_type];
            }
            $uid = $this->uid;

              $where[] = ['check_status', '=', 2];
          
        
         

            $list = OrderModel::with(['customer', 'adduser'])
                ->where($where)
                ->order('id desc')
                ->page($page, $limit)
                ->select()
                ->each(function($item) {
                    // 添加开票状态和金额信息
                    $item->invoice_amount = $item->getInvoiceAmount();
                    $item->invoice_status_text = $item->invoice_status_text;
                    $item->invoice_status_class = $item->invoice_status_class;
                    $item->cw_status_text = $item->cw_status_text;
                    return $item;
                })
                ->toArray();

                
          
            
            $count = OrderModel::where($where)->count();
            
            return json(['code' => 0, 'msg' => '', 'count' => $count, 'data' => $list]);
        }

        $orderDetailModel = new OrderDetailModel();
        $orderDetailModel->checkInventory(211);
        View::assign('is_leader', isLeader($this->uid));
        
        
        return view();

    }

    /**
     * 财务审核
     */
    public function cwCheck()
    {
        $id = input('id');

        if($id){
            $order = OrderModel::find($id);
            if(!$order){
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }

            // 检查订单状态 - 只能审核已通过业务审核的订单
            if($order->check_status != 2){
                return json(['code' => 1, 'msg' => '订单未通过业务审核，无法进行财务审核', 'data' => null]);
            }

            // 检查财务状态
            if($order->cw != 0){
                return json(['code' => 1, 'msg' => '订单已进行财务审核，无法重复审核', 'data' => null]);
            }

            Db::startTrans();
            try {
                // 更新财务审核状态
                $update = [
                    'cw' => 1, // 财务审核通过
                    'update_time' => time()
                ];

                // 更新订单状态
                Db::name('customer_order')->where('id', $id)->update($update);

                // 记录操作日志
                $log = new EditLog();
                $log->add_log('customer_order', $id, '财务审核通过。操作人：' . get_admin($this->uid)['name']);

                Db::commit();
                return json(['code' => 0, 'msg' => '财务审核成功', 'data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '财务审核失败：' . $e->getMessage(), 'data' => null]);
            }
        }

        return json(['code' => 1, 'msg' => '参数错误', 'data' => null]);
    }

    /**
     * 财务反审核
     */
    public function cwReverseCheck()
    {
        $id = input('id');

        if($id){
            $order = OrderModel::find($id);
            if(!$order){
                return json(['code' => 1, 'msg' => '订单不存在', 'data' => null]);
            }

            // 检查财务状态
            if($order->cw != 1){
                return json(['code' => 1, 'msg' => '订单未通过财务审核，无法反审核', 'data' => null]);
            }

              // 1. 基础检查
        $checkResult = $this->checkReverseAuditConditions($id);
        if ($checkResult['code'] != 0) {
            return json($checkResult);
        }

        // 2. 关联业务检查
        $businessCheckResult = $this->checkRelatedBusiness($id);
        if ($businessCheckResult['code'] != 0) {
            return json($businessCheckResult);
        }


        

            Db::startTrans();
            try {
                // 更新财务审核状态
                $update = [
                    'cw' => 0, // 恢复到待处理状态
                    'update_time' => time()
                ];

                // 更新订单状态
                Db::name('customer_order')->where('id', $id)->update($update);

                // 记录操作日志
                $log = new EditLog();
                $log->add_log('customer_order', $id, '财务反审核。操作人：' . get_admin($this->uid)['name']);

                Db::commit();
                return json(['code' => 0, 'msg' => '财务反审核成功', 'data' => null]);
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 1, 'msg' => '财务反审核失败：' . $e->getMessage(), 'data' => null]);
            }
        }

        return json(['code' => 1, 'msg' => '参数错误', 'data' => null]);
    }



}