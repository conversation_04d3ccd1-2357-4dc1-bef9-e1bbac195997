{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>库存流水统计</h3>
        </div>
        <div class="layui-card-body">
            <!-- 统计概览 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">今日流水数</div>
                            <div class="text-primary" style="font-size:24px;font-weight:bold;" id="today_count">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">本月流水数</div>
                            <div class="text-success" style="font-size:24px;font-weight:bold;" id="month_count">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">总入库量</div>
                            <div class="text-info" style="font-size:24px;font-weight:bold;" id="total_inbound">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">总出库量</div>
                            <div class="text-warning" style="font-size:24px;font-weight:bold;" id="total_outbound">0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tab切换 -->
            <div class="layui-tab layui-tab-brief" lay-filter="statistics_tab" style="margin-top:20px;">
                <ul class="layui-tab-title">
                    <li class="layui-this">按仓库统计</li>
                    <li>按产品统计</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 按仓库统计 -->
                    <div class="layui-tab-item layui-show">
                        <table class="layui-hide" id="table_warehouse_stats" lay-filter="table_warehouse_stats"></table>
                    </div>
                    <!-- 按产品统计 -->
                    <div class="layui-tab-item">
                        <table class="layui-hide" id="table_product_stats" lay-filter="table_product_stats"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool;
        
        // 加载统计数据
        loadStatistics();
        
        // 按仓库统计表格
        var warehouseTable = table.render({
            elem: "#table_warehouse_stats"
            ,title: "按仓库统计"
            ,url: "/warehouse/InventoryTransaction/statistics"
            ,page: false
            ,cellMinWidth: 80
            ,height: 400
            ,parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.warehouse_stats ? res.data.warehouse_stats.length : 0,
                    "data": res.data.warehouse_stats || []
                };
            }
            ,cols: [[ //表头
                {
                    field: 'warehouse_name',
                    title: '仓库名称',
                    align: 'left',
                    width: 200
                },{ 
                    field: 'transaction_count', 
                    title: '流水笔数', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#1E9FFF;font-weight:bold;">' + d.transaction_count + '</span>';
                    }
                },{ 
                    field: 'total_inbound', 
                    title: '总入库量', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#5FB878;font-weight:bold;">' + (d.total_inbound || 0) + '</span>';
                    }
                },{ 
                    field: 'total_outbound', 
                    title: '总出库量', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#FF5722;font-weight:bold;">' + (d.total_outbound || 0) + '</span>';
                    }
                },{ 
                    field: 'net_change', 
                    title: '净变化', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        var netChange = (d.total_inbound || 0) - (d.total_outbound || 0);
                        var color = netChange >= 0 ? '#5FB878' : '#FF5722';
                        var sign = netChange >= 0 ? '+' : '';
                        return '<span style="color:' + color + ';font-weight:bold;">' + sign + netChange + '</span>';
                    }
                }
            ]]
        });
        
        // 按产品统计表格
        var productTable = table.render({
            elem: "#table_product_stats"
            ,title: "按产品统计"
            ,url: "/warehouse/InventoryTransaction/statistics"
            ,page: false
            ,cellMinWidth: 80
            ,height: 400
            ,parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.product_stats ? res.data.product_stats.length : 0,
                    "data": res.data.product_stats || []
                };
            }
            ,cols: [[ //表头
                {
                    field: 'product_name',
                    title: '产品名称',
                    align: 'left',
                    width: 200,
                    templet: function (d) {
                        var html = '<div style="line-height:18px;">';
                        html += '<div style="font-weight:bold;">' + (d.product_name || '') + '</div>';
                        html += '<div style="color:#999;font-size:12px;">' + (d.material_code || '') + '</div>';
                        html += '</div>';
                        return html;
                    }
                },{ 
                    field: 'transaction_count', 
                    title: '流水笔数', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#1E9FFF;font-weight:bold;">' + d.transaction_count + '</span>';
                    }
                },{ 
                    field: 'total_inbound', 
                    title: '总入库量', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#5FB878;font-weight:bold;">' + (d.total_inbound || 0) + '</span>';
                    }
                },{ 
                    field: 'total_outbound', 
                    title: '总出库量', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#FF5722;font-weight:bold;">' + (d.total_outbound || 0) + '</span>';
                    }
                },{ 
                    field: 'net_change', 
                    title: '净变化', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        var netChange = (d.total_inbound || 0) - (d.total_outbound || 0);
                        var color = netChange >= 0 ? '#5FB878' : '#FF5722';
                        var sign = netChange >= 0 ? '+' : '';
                        return '<span style="color:' + color + ';font-weight:bold;">' + sign + netChange + '</span>';
                    }
                }
            ]]
        });
        
        // Tab切换事件
        element.on('tab(statistics_tab)', function(data){
            if (data.index === 0) {
                warehouseTable.reload();
            } else if (data.index === 1) {
                productTable.reload();
            }
        });
        
        // 加载统计概览数据
        function loadStatistics() {
            // 这里应该调用实际的统计接口
            // 暂时使用模拟数据
            $('#today_count').text('156');
            $('#month_count').text('4,523');
            $('#total_inbound').text('12,345');
            $('#total_outbound').text('9,876');
        }
    }
</script>
{/block}
