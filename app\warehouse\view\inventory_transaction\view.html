{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>库存流水详情</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>流水号</strong></td>
                                <td>{$info.transaction_no}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>产品信息</strong></td>
                                <td>
                                    <div>{$info.product_name}</div>
                                    <div class="text-muted" style="font-size:12px;">编码：{$info.material_code}</div>
                                    {if $info.specs}
                                    <div class="text-muted" style="font-size:12px;">规格：{$info.specs}</div>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>仓库</strong></td>
                                <td>{$info.warehouse_name}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>流水类型</strong></td>
                                <td>
                                    {switch name="info.transaction_type"}
                                        {case value="inbound"}
                                            <span class="layui-badge layui-bg-green">入库</span>
                                        {/case}
                                        {case value="outbound"}
                                            <span class="layui-badge layui-bg-red">出库</span>
                                        {/case}
                                        {case value="transfer_in"}
                                            <span class="layui-badge layui-bg-blue">调入</span>
                                        {/case}
                                        {case value="transfer_out"}
                                            <span class="layui-badge layui-bg-orange">调出</span>
                                        {/case}
                                        {case value="adjust_increase"}
                                            <span class="layui-badge layui-bg-cyan">盘盈</span>
                                        {/case}
                                        {case value="adjust_decrease"}
                                            <span class="layui-badge layui-bg-gray">盘亏</span>
                                        {/case}
                                        {case value="lock"}
                                            <span class="layui-badge layui-bg-orange">锁定</span>
                                        {/case}
                                        {case value="unlock"}
                                            <span class="layui-badge layui-bg-green">解锁</span>
                                        {/case}
                                        {default /}
                                            <span class="layui-badge">{$info.transaction_type_text}</span>
                                    {/switch}
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>数量变动</strong></td>
                                <td>
                                    {php}
                                        $sign = '';
                                        $color = '#333';
                                        if (strpos($info['transaction_type'], 'inbound') !== false || 
                                            strpos($info['transaction_type'], 'transfer_in') !== false || 
                                            strpos($info['transaction_type'], 'increase') !== false) {
                                            $sign = '+';
                                            $color = '#5FB878';
                                        } else if (strpos($info['transaction_type'], 'outbound') !== false || 
                                                   strpos($info['transaction_type'], 'transfer_out') !== false || 
                                                   strpos($info['transaction_type'], 'decrease') !== false) {
                                            $sign = '-';
                                            $color = '#FF5722';
                                        }
                                    {/php}
                                    <span style="color:{$color};font-size:16px;font-weight:bold;">
                                        {$sign}{$info.quantity} {$info.unit}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>变动前数量</strong></td>
                                <td>
                                    <span style="font-size:14px;">
                                        {$info.before_quantity} {$info.unit}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120" style="background-color: #f8f9fa;"><strong>变动后数量</strong></td>
                                <td>
                                    <span style="font-size:14px;">
                                        {$info.after_quantity} {$info.unit}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>关联类型</strong></td>
                                <td>{$info.ref_type_text}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>关联单号</strong></td>
                                <td>{$info.ref_no|default='无'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>操作人</strong></td>
                                <td>{$info.creator_name|default='系统'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>创建时间</strong></td>
                                <td>{$info.create_time|date='Y-m-d H:i:s'}</td>
                            </tr>
                            <tr>
                                <td style="background-color: #f8f9fa;"><strong>备注</strong></td>
                                <td>
                                    {if $info.notes}
                                        <div style="max-height:100px;overflow-y:auto;">{$info.notes}</div>
                                    {else}
                                        <span class="text-muted">无</span>
                                    {/if}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 关联信息 -->
            {if $info.ref_id > 0}
            <div class="layui-card" style="margin-top:20px;">
                <div class="layui-card-header">
                    <h4>关联信息</h4>
                </div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-md12">
                            <table class="layui-table" lay-skin="nob">
                                <tbody>
                                    <tr>
                                        <td width="120" style="background-color: #f8f9fa;"><strong>关联ID</strong></td>
                                        <td>{$info.ref_id}</td>
                                        <td width="120" style="background-color: #f8f9fa;"><strong>关联单号</strong></td>
                                        <td>{$info.ref_no}</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #f8f9fa;"><strong>关联类型</strong></td>
                                        <td colspan="3">{$info.ref_type_text}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            {/if}
            
            <!-- 操作按钮 -->
            <div class="layui-form-item" style="margin-top:20px;">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 这里可以添加其他交互功能
        console.log('库存流水详情页面加载完成');
    }
</script>
{/block}
