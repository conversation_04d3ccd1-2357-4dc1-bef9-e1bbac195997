{extend name="../../base/view/common/base" /}

<!-- 主体 -->
{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">订单工序管理</li>
            </ul>
        </div>
    </div>
    
    <!-- 订单基本信息 -->
    <div class="layui-card" style="margin-bottom: 15px;">
        <div class="layui-card-header">
            <h3 style="margin: 0; color: #333;">订单信息</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>订单编号：</label>
                        <span>{$order.order_no}</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>产品名称：</label>
                        <span>{$order.product_name}</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>订单数量：</label>
                        <span>{$order.quantity}</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>完成数量：</label>
                        <span>{$order.completed_qty|default=0}</span>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space15" style="margin-top: 10px;">
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>总工序数：</label>
                        <span>{$order.total_processes|default=0}</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>完成工序：</label>
                        <span>{$order.completed_processes|default=0}</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>整体进度：</label>
                        <span>{$order.progress|default=0}%</span>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="info-item">
                        <label>订单状态：</label>
                        <span class="status-badge status-{$order.status}">{$order.status_name|default='未知'}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工序列表 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h3 style="margin: 0; color: #333;">工序列表</h3>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
        <div class="layui-card-body" style="padding: 0;">
            <table class="layui-table" lay-skin="line" style="margin: 0;">
                <thead>
                    <tr>
                        <th width="60">序号</th>
                        <th width="100">工序编码</th>
                        <th>工序名称</th>
                        <th width="100">工序类型</th>
                        <th width="100">加工类型</th>
                        <th width="80">状态</th>
                        <th width="100">已完成数量</th>
                        <th width="100">合格数量</th>
                        <th width="100">标准工时</th>
                        <th width="100">实际工时</th>
                        <th width="150">计划开始时间</th>
                        <th width="150">实际开始时间</th>
                        <th width="150">实际结束时间</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {if !empty($processes)}
                    {foreach $processes as $process}
                    <tr data-process-id="{$process.id}">
                        <td style="text-align: center;">{$process.step_no}</td>
                        <td>{$process.process_code}</td>
                        <td>{$process.process_name}</td>
                        <td>{$process.process_type}</td>
                        <td>{$process.processing_type}</td>
                        <td style="text-align: center;">
                            <span class="layui-badge process-status-{$process.status}">{$process.status_text}</span>
                        </td>
                        <td style="text-align: center;">{$process.completed_qty}</td>
                        <td style="text-align: center;">{$process.qualified_qty}</td>
                        <td style="text-align: center;">{$process.standard_time}</td>
                        <td style="text-align: center;">{$process.actual_work_time}</td>
                        <td style="text-align: center;">{$process.planned_start_time_format}</td>
                        <td style="text-align: center;">{$process.actual_start_time_format}</td>
                        <td style="text-align: center;">{$process.actual_end_time_format}</td>
                        <td style="text-align: center;">
                            <div class="layui-btn-group">
                                {if $process.status == 0}
                                <button class="layui-btn layui-btn-xs layui-btn-normal start-process" data-id="{$process.id}">开始</button>
                                {elseif $process.status == 1}
                                <button class="layui-btn layui-btn-xs layui-btn-warm pause-process" data-id="{$process.id}">暂停</button>
                                <button class="layui-btn layui-btn-xs layui-btn-normal complete-process" data-id="{$process.id}">完成</button>
                                {elseif $process.status == 3}
                                <button class="layui-btn layui-btn-xs layui-btn-normal resume-process" data-id="{$process.id}">继续</button>
                                {/if}
                                <button class="layui-btn layui-btn-xs layui-btn-primary view-detail" data-id="{$process.id}">详情</button>
                            </div>
                        </td>
                    </tr>
                    {/foreach}
                    {else}
                    <tr>
                        <td colspan="14" style="text-align: center; color: #999; padding: 40px;">
                            <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; display: block; margin-bottom: 10px;"></i>
                            暂无工序数据
                        </td>
                    </tr>
                    {/if}
                </tbody>
            </table>
        </div>
    </div>
</div>
{/block}

<!-- 样式 -->
{block name="style"}
<style>
.info-item {
    margin-bottom: 8px;
}

.info-item label {
    font-weight: bold;
    color: #666;
    margin-right: 8px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    color: white;
}

.status-0 { background-color: #ccc; }
.status-1 { background-color: #ff9800; }
.status-2 { background-color: #4caf50; }
.status-3 { background-color: #f44336; }
.status-4 { background-color: #9e9e9e; }

.process-status-0 { background-color: #ccc; }
.process-status-1 { background-color: #ff9800; }
.process-status-2 { background-color: #4caf50; }
.process-status-3 { background-color: #f44336; }

.layui-table tbody tr:hover {
    background-color: #f8f8f8;
}

.layui-btn-group .layui-btn {
    margin-right: 5px;
}

.layui-btn-group .layui-btn:last-child {
    margin-right: 0;
}
</style>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var tool = layui.tool, $ = layui.jquery, layer = layui.layer;

    // 刷新按钮
    $('#refresh-btn').on('click', function() {
        location.reload();
    });

    // 开始工序
    $('.start-process').on('click', function() {
        var processId = $(this).data('id');
        updateProcessStatus(processId, 1, '确定要开始这个工序吗？');
    });

    // 暂停工序
    $('.pause-process').on('click', function() {
        var processId = $(this).data('id');
        updateProcessStatus(processId, 3, '确定要暂停这个工序吗？');
    });

    // 继续工序
    $('.resume-process').on('click', function() {
        var processId = $(this).data('id');
        updateProcessStatus(processId, 1, '确定要继续这个工序吗？');
    });

    // 完成工序
    $('.complete-process').on('click', function() {
        var processId = $(this).data('id');
        updateProcessStatus(processId, 2, '确定要完成这个工序吗？');
    });

    // 查看详情
    $('.view-detail').on('click', function() {
        var processId = $(this).data('id');
        layer.msg('工序详情功能开发中...');
    });

    // 更新工序状态
    function updateProcessStatus(processId, status, confirmMsg) {
        layer.confirm(confirmMsg, {icon: 3, title: '提示'}, function(index) {
            var data = {
                process_id: processId,
                status: status
            };

            tool.post('/Produce/order/updateProcessStatus', data, function(res) {
                layer.msg(res.msg);
                if (res.code === 0) {
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            });
            
            layer.close(index);
        });
    }
}
</script>
{/block}
