<?php
namespace app\Produce\controller;

use app\common\controller\BaseController;
use think\facade\Db;
use think\facade\Log;

/**
 * 生产计划排产模块测试控制器
 */
class ProductionPlanTest extends BaseController
{
    /**
     * 测试数据库表结构
     */
    public function testTables()
    {
        try {
            $results = [];
            
            // 测试生产计划表
            $planTableExists = $this->checkTableExists('production_plan');
            $results['production_plan'] = $planTableExists;
            
            // 测试日生产统计表
            $statsTableExists = $this->checkTableExists('daily_production_stats');
            $results['daily_production_stats'] = $statsTableExists;
            
            // 测试排产日志表
            $logTableExists = $this->checkTableExists('production_plan_log');
            $results['production_plan_log'] = $logTableExists;
            
            // 测试生产订单表字段
            $orderFields = $this->checkOrderTableFields();
            $results['order_table_fields'] = $orderFields;
            
            return json([
                'code' => 0,
                'msg' => '数据库表结构检查完成',
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => '检查失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 检查表是否存在
     */
    private function checkTableExists($tableName)
    {
        try {
            $fullTableName = config('database.connections.mysql.prefix') . $tableName;
            $result = Db::query("SHOW TABLES LIKE '{$fullTableName}'");
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查生产订单表字段
     */
    private function checkOrderTableFields()
    {
        try {
            $requiredFields = ['plan_id', 'plan_status', 'scheduled_date', 'estimated_days'];
            $existingFields = [];
            
            $columns = Db::query("SHOW COLUMNS FROM " . config('database.connections.mysql.prefix') . "produce_order");
            $fieldNames = array_column($columns, 'Field');
            
            foreach ($requiredFields as $field) {
                $existingFields[$field] = in_array($field, $fieldNames);
            }
            
            return $existingFields;
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 创建测试数据
     */
    public function createTestData()
    {
        try {
            // 检查是否已有测试数据
            $existingPlans = Db::name('production_plan')->count();
            if ($existingPlans > 0) {
                return json([
                    'code' => 1,
                    'msg' => '已存在排产数据，请先清理后再创建测试数据'
                ]);
            }
            
            // 获取一些生产订单作为测试
            $orders = Db::name('produce_order')
                ->where('plan_status', 0)
                ->limit(5)
                ->select();
                
            if (empty($orders)) {
                return json([
                    'code' => 1,
                    'msg' => '没有可用的生产订单创建测试数据'
                ]);
            }
            
            $createdCount = 0;
            $startDate = date('Y-m-d', strtotime('+1 day'));
            
            foreach ($orders as $index => $order) {
                $planStartDate = date('Y-m-d', strtotime($startDate . ' +' . $index . ' days'));
                $planEndDate = date('Y-m-d', strtotime($planStartDate . ' +1 day'));
                
                // 创建排产计划
                $planId = Db::name('production_plan')->insertGetId([
                    'order_id' => $order['id'],
                    'plan_start_date' => $planStartDate,
                    'plan_end_date' => $planEndDate,
                    'plan_days' => 2,
                    'priority' => rand(1, 5),
                    'progress' => rand(0, 100),
                    'status' => rand(0, 2),
                    'auto_scheduled' => 1,
                    'notes' => '测试排产数据',
                    'create_time' => time(),
                    'create_uid' => 1,
                    'create_name' => '测试用户'
                ]);
                
                // 更新订单状态
                Db::name('produce_order')->where('id', $order['id'])->update([
                    'plan_id' => $planId,
                    'plan_status' => 1,
                    'scheduled_date' => $planStartDate,
                    'estimated_days' => 2,
                    'update_time' => time()
                ]);
                
                $createdCount++;
            }
            
            return json([
                'code' => 0,
                'msg' => "成功创建 {$createdCount} 条测试排产数据"
            ]);
            
        } catch (\Exception $e) {
            Log::error('创建测试数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json([
                'code' => 1,
                'msg' => '创建失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanTestData()
    {
        try {
            Db::startTrans();
            
            // 获取测试排产数据
            $testPlans = Db::name('production_plan')
                ->where('notes', 'like', '%测试%')
                ->select();
            
            $cleanedCount = 0;
            
            foreach ($testPlans as $plan) {
                // 恢复订单状态
                Db::name('produce_order')->where('id', $plan['order_id'])->update([
                    'plan_id' => 0,
                    'plan_status' => 0,
                    'scheduled_date' => null,
                    'estimated_days' => 1,
                    'update_time' => time()
                ]);
                
                // 删除排产计划
                Db::name('production_plan')->where('id', $plan['id'])->delete();
                
                $cleanedCount++;
            }
            
            // 清理日志
            Db::name('production_plan_log')
                ->where('operator_name', '测试用户')
                ->delete();
            
            Db::commit();
            
            return json([
                'code' => 0,
                'msg' => "成功清理 {$cleanedCount} 条测试数据"
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('清理测试数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return json([
                'code' => 1,
                'msg' => '清理失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 测试API接口
     */
    public function testApi()
    {
        try {
            $results = [];
            
            // 测试获取待排产订单
            $unscheduledOrders = app('app\Produce\controller\ProductionPlan')->getUnscheduledOrders();
            $results['unscheduled_orders'] = json_decode($unscheduledOrders->getContent(), true);
            
            // 测试获取已排产计划
            $scheduledPlans = app('app\Produce\controller\ProductionPlan')->getScheduledPlans();
            $results['scheduled_plans'] = json_decode($scheduledPlans->getContent(), true);
            
            // 测试获取每日统计
            $dailyStats = app('app\Produce\controller\ProductionPlan')->getDailyStats();
            $results['daily_stats'] = json_decode($dailyStats->getContent(), true);
            
            return json([
                'code' => 0,
                'msg' => 'API接口测试完成',
                'data' => $results
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => 'API测试失败：' . $e->getMessage()
            ]);
        }
    }
}
