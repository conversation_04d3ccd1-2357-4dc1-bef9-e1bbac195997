<?php
namespace app\api\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use app\api\service\InventoryCleanService;

class CleanExpiredInventory extends Command
{
    protected function configure()
    {
        $this->setName('clean:expired_inventory')
            ->setDescription('清理过期的库存记录（包括锁定和预占）');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始清理过期库存记录...');
        
        try {
            // 使用服务类而不是控制器
            $service = new InventoryCleanService();
            
            // 调用清理方法
            $result = $service->cleanExpiredRecords();
            
            if ($result['success']) {
                $output->writeln('清理成功:');
                $output->writeln('- 过期库存锁定: ' . ($result['data']['expired_locks'] ?? 0) . ' 条记录');
                $output->writeln('- 过期库存预占: ' . ($result['data']['expired_reserves'] ?? 0) . ' 条记录');
                $output->writeln('- 执行时间: ' . ($result['data']['execution_time'] ?? 0) . ' 秒');
            } else {
                $output->writeln('清理过程中出现错误:');
                if (isset($result['errors']) && is_array($result['errors'])) {
                    foreach ($result['errors'] as $error) {
                        $output->writeln('- ' . $error);
                    }
                } else {
                    $output->writeln('- ' . ($result['message'] ?? '未知错误'));
                }
            }
        } catch (\Exception $e) {
            $output->writeln('执行清理命令异常: ' . $e->getMessage());
            $output->writeln('异常位置: ' . $e->getFile() . ':' . $e->getLine());
        }
        
        $output->writeln('清理过程完成');
    }
} 