{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-page">
	<h3 class="pb-1">编辑车辆费用记录</h3>
	<table class="layui-table">
		<tr>
			<td class="layui-td-gray">车辆名称</td>
			<td colspan="3">{$detail.car}</td>
			<td class="layui-td-gray">费用类型<font>*</font></td>
			<td>
				<select name="types" lay-verify="required" lay-reqText="选择费用类型">
				<option value="">选择费用类型</option>
				{volist name=":get_base_data('basicAdm')" id="vo"}
					<option value="{$vo.id}" {eq name="$vo.id" value="$detail.types"} selected{/eq}>{$vo.title}</option>
				{/volist}
			</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">费用主题<font>*</font></td>
			<td colspan="5">
				<input type="text" name="title" value="{$detail.title}" autocomplete="off" lay-verify="required" lay-reqText="请完善费用主题" placeholder="请完善费用主题" class="layui-input">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">费用日期</td>
			<td><input type="text" name="fee_time" readonly value="{$detail.fee_time|date='Y-m-d'}" autocomplete="off" readonly placeholder="请选择费用日期" class="layui-input tool-time"></td>
			<td class="layui-td-gray">费用金额<font>*</font></td>
			<td><input type="text" name="amount" value="{$detail.amount}" autocomplete="off" placeholder="请输入费用金额" lay-verify="required|number" lay-reqText="请输入费用金额" class="layui-input"></td>
			<td class="layui-td-gray">经手人</td>
			<td>
				<input type="text" name="handled_name" value="{$detail.handled_name}" autocomplete="off" readonly lay-verify="required" lay-reqText="请选择经手人" class="layui-input picker-admin">
				<input type="hidden" name="handled" value="{$detail.handled}">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">相关附件</div>
				<div class="layui-input-inline">
					<button type="button" class="layui-btn layui-btn-xs" id="uploadBtn"><i class="layui-icon"></i></button>
				</div>
			</td>
			<td colspan="5" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					<input type="hidden" data-type="file" name="file_ids" value="{$detail.file_ids}">
					{notempty name="$detail.fileArray"}
					{volist name="$detail.fileArray" id="vo"}
					<div class="layui-col-md4" id="fileItem{$vo.id}">{:file_card($vo)}</div>
					{/volist}
					{/notempty}
				</div>
			</td>
		</tr> 
		<tr>
			<td class="layui-td-gray" style="vertical-align:top;">费用内容</td>
			<td colspan="5">
				<textarea name="content" placeholder="请输入费用内容" class="layui-textarea">{$detail.content|default=''}</textarea>
			</td>
		</tr>
	</table>
	<div class="pt-2">
		<input type="hidden" name="id" value="{$detail.id}">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','uploadPlus','oaPicker'];
function gouguInit() {
	var form = layui.form,tool=layui.tool,uploadPlus = layui.uploadPlus;
	//相关附件上传
	var attachment = new uploadPlus();
	
	//监听提交
	form.on('submit(webform)', function(data){
		let callback = function (e) {
			layer.msg(e.msg);
			if (e.code == 0) {
				tool.sideClose(1000,'feeTable');				
			}
		}
		let clickbtn = $(this);
		tool.post("/adm/car/fee_add", data.field, callback,clickbtn);
		return false;
	});
}
</script>
{/block}
<!-- /脚本 -->