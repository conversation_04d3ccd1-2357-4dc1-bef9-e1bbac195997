{extend name="../../base/view/common/base" /}
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">单位管理</h3>
	<div class="layui-form-item">
		<div class="layui-bg-orange" style="padding: 10px; margin-bottom: 15px; border-radius: 4px;">
			<p style="margin: 0; color: #fff;">
				<i class="layui-icon layui-icon-tips"></i>
				1. 单位启用后，单位名称不可由大改小<br>
				2. 单位修改后不改小时，数量字段会自动进行单位换算后进入实际进行重新
			</p>
		</div>
	</div>
	{if condition="$id eq 0"}
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">单位名称<font>*</font></td>
			<td>
				<input type="text" name="name" value="" lay-verify="required" lay-reqText="请输入单位名称" placeholder="请输入单位名称" class="layui-input" maxlength="20">
				<div class="layui-word-aux">0 / 20</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">单位精度<font>*</font></td>
			<td>
				<input type="number" name="precision" value="0" lay-verify="required|number" lay-reqText="请输入单位精度" placeholder="0" class="layui-input" min="0" max="6">
			</td>
			<td class="layui-td-gray">含入类型<font>*</font></td>
			<td>
				<select name="type" lay-verify="required" lay-reqText="请选择含入类型">
					<option value="">请选择含入类型</option>
					<option value="四舍五入">四舍五入</option>
					<option value="进位">进位</option>
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">备注</td>
			<td colspan="3">
				<textarea name="remark" placeholder="请输入备注" class="layui-textarea" style="height: 120px;" maxlength="100"></textarea>
				<div class="layui-word-aux">0 / 100</div>
			</td>
		</tr>
	</table>
	{else/}
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">单位名称<font>*</font></td>
			<td>
				<input type="text" name="name" value="{$detail.name}" lay-verify="required" lay-reqText="请输入单位名称" placeholder="请输入单位名称" class="layui-input" maxlength="20">
				<div class="layui-word-aux">0 / 20</div>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">单位精度<font>*</font></td>
			<td>
				<input type="number" name="precision" value="{$detail.precision}" lay-verify="required|number" lay-reqText="请输入单位精度" placeholder="0" class="layui-input" min="0" max="6">
			</td>
			<td class="layui-td-gray">含入类型<font>*</font></td>
			<td>
				<select name="type" lay-verify="required" lay-reqText="请选择含入类型">
					<option value="">请选择含入类型</option>
					<option value="四舍五入" {if condition="$detail.type eq '四舍五入'"}selected{/if}>四舍五入</option>
					<option value="进位" {if condition="$detail.type eq '进位'"}selected{/if}>进位</option>
				</select>
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">备注</td>
			<td colspan="3">
				<textarea name="remark" placeholder="请输入备注" class="layui-textarea" style="height: 120px;" maxlength="100">{$detail.remark}</textarea>
				<div class="layui-word-aux">0 / 100</div>
			</td>
		</tr>
	</table>
	{/if}
	<div class="pt-4">
		<input type="hidden" value="{$id}" name="id">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">保存</button>
		<button type="button" class="layui-btn layui-btn-primary" onclick="var index = parent.layer.getFrameIndex(window.name); parent.layer.close(index);">取消</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
	var form = layui.form, tool = layui.tool;
	
	// 字符计数
	function updateWordCount(input, maxLength) {
		var currentLength = input.val().length;
		input.siblings('.layui-word-aux').text(currentLength + ' / ' + maxLength);
	}
	
	// 监听输入框变化
	$('input[name="name"]').on('input', function() {
		updateWordCount($(this), 20);
	});
	
	$('textarea[name="remark"]').on('input', function() {
		updateWordCount($(this), 100);
	});
	
	// 初始化字符计数
	updateWordCount($('input[name="name"]'), 20);
	updateWordCount($('textarea[name="remark"]'), 100);
	
	// 监听表单提交
	form.on('submit(webform)', function(data){
		$.ajax({
			url: "/metadata/unit/add",
			type: 'post',
			data: data.field,
			success: function(e){
				layer.msg(e.msg);
				if (e.code == 0) {
					// 刷新父页面表格
					parent.layui.pageTable.reload();
					// 关闭弹窗
					setTimeout(function(){
						var index = parent.layer.getFrameIndex(window.name);
						parent.layer.close(index);
					}, 1000);
				}
			}
		});
		return false;
	});
}		
</script>
{/block}
<!-- /脚本 -->