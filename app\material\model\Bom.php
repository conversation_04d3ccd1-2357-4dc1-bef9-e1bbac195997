<?php

namespace app\material\model;

use think\Model;

class Bom extends Model
{
    protected $table = 'oa_material_bom';
    
    // 设置字段信息
    protected $schema = [
        'id' => 'int',
        'bom_code' => 'string',
        'bom_name' => 'string',
        'product_id' => 'int',
        'product_code' => 'string',
        'product_name' => 'string',
        'customer_id' => 'int',
        'customer_name' => 'string',
        'version' => 'string',
        'status' => 'int',
        'remark' => 'text',
        'create_time' => 'int',
        'update_time' => 'int',
        'delete_time' => 'int',
        'admin_id' => 'int'
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = false;

    

    /**
     * 关联BOM物料清单
     */
    public function bomItems()
    {
        return $this->hasMany(BomItem::class, 'bom_id', 'id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '草稿',
            1 => '已审核',
            2 => '已停用'
        ];
        return $status[$data['status']] ?? '未知';
    }

    /**
     * 获取产品分类文本
     */
    public function getProductCategoryTextAttr($value, $data)
    {
        $categories = [
            '测试001' => '测试001',
            '产品' => '产品',
            '成品' => '成品',
            '铝制品' => '铝制品',
            '龙凤' => '龙凤'
        ];
        return $categories[$data['product_category']] ?? $data['product_category'];
    }

    /**
     * 获取单据状态文本
     */
    public function getUnitStatusTextAttr($value, $data)
    {
        $unitStatus = [
            '已审核' => '已审核'
        ];
        return $unitStatus[$data['unit_status']] ?? $data['unit_status'];
    }

    /**
     * 创建时间格式化
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['create_time']);
    }

    /**
     * 更新时间格式化
     */
    public function getUpdateTimeTextAttr($value, $data)
    {
        return date('Y-m-d H:i:s', $data['update_time']);
    }
}