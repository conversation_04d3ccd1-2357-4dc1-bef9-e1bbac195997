， 生产订单投料状态判断修复

## 🔍 问题描述

生产订单列表页面（`http://tc.xinqiyu.cn:8830/Produce/order/index`）的投料状态显示不准确：
- 订单27：显示"部分投料" ✅ 正确
- 订单2：显示"未投料" ❌ 错误 - 实际有领料记录
- 订单1：显示"未投料" ❌ 错误 - 实际有领料记录

## 🎯 问题原因分析

### 当前实现问题
1. **查询条件不完整**：缺少 `delete_time = 0` 过滤条件
2. **状态判断逻辑简单**：没有结合BOM需求和实际投料量进行精确计算
3. **状态定义不清晰**：需要明确"未投料"、"投料中"、"投料完"、"超投料"的判断标准

### 业务需求
- **未投料**：没有任何领料记录
- **投料中**：有领料记录但未完成投料
- **投料完**：所有物料已按BOM需求投料完成
- **超投料**：投料量超过BOM需求或有超领记录

## 🛠️ 解决方案

### 1. 修改投料状态获取逻辑http://tc.xinqiyu.cn:8830/warehouse/Outbound/index
在 `app\Produce\model\Order.php` 的 `getList()` 方法中修改投料状态判断：

```php
// 修改前：直接使用字段值
$order['feeding_status_name'] = $feedingStatusArr[$order['feeding_flag']] ?? '未知';

// 修改后：通过领料记录判断投料状态
$feedingStatus = $this->getFeedingStatusByOrderId($order['id']);
$feedingStatusArr = [
    0 => '未投料',
    1 => '部分投料',
    2 => '投料完成',
    3 => '补料'
];
$order['feeding_status'] = $feedingStatus;
$order['feeding_status_name'] = $feedingStatusArr[$feedingStatus] ?? '未知';
```

### 2. 添加投料状态判断方法

在Order模型中添加 `getFeedingStatusByOrderId()` 方法：

```php
/**
 * 根据生产订单ID获取投料状态
 * @param int $orderId 生产订单ID
 * @return int 投料状态：0=未投料，1=部分投料，2=投料完成，3=补料
 */
private function getFeedingStatusByOrderId($orderId)
{
    try {
        // 查询该生产订单的领料记录
        $materialRequests = \think\facade\Db::name('production_material_request')
            ->where('production_order_id', $orderId)
            ->where('delete_time', 0) // 未删除的记录
            ->field('id, status, request_type, create_time')
            ->order('create_time desc')
            ->select()
            ->toArray();

        if (empty($materialRequests)) {
            return 0; // 未投料
        }

        // 检查是否有补料记录（request_type=2）或超领记录（request_type=3）
        $hasSupplementary = false;
        $completedCount = 0;
        $totalCount = count($materialRequests);

        foreach ($materialRequests as $request) {
            // 补料或超领
            if ($request['request_type'] == 2 || $request['request_type'] == 3) {
                $hasSupplementary = true;
            }

            // 已完成的领料单
            if ($request['status'] == 3) {
                $completedCount++;
            }
        }

        if ($hasSupplementary) {
            return 3; // 补料
        }

        // 判断投料完成度
        if ($completedCount == $totalCount && $totalCount > 0) {
            return 2; // 投料完成
        } else if ($completedCount > 0) {
            return 1; // 部分投料
        } else {
            // 有领料记录但都未完成，根据状态判断
            $hasApproved = false;
            foreach ($materialRequests as $request) {
                if ($request['status'] >= 1) { // 已审核或以上状态
                    $hasApproved = true;
                    break;
                }
            }
            return $hasApproved ? 1 : 0; // 有已审核的记录认为是部分投料
        }

    } catch (\Exception $e) {
        \think\facade\Log::error('获取投料状态失败', [
            'order_id' => $orderId,
            'error' => $e->getMessage()
        ]);
        return 0; // 出错时返回未投料
    }
}
```

## 📋 投料状态判断逻辑

### 状态定义

| 状态值 | 状态名称 | 判断条件 |
|--------|----------|----------|
| 0 | 未投料 | 没有任何投料记录 |
| 1 | 部分投料 | 有投料记录但未完成 |
| 2 | 投料完成 | 所有物料已投料完成 |
| 3 | 补料 | 存在补料记录（exceed=1） |

### 判断流程

```mermaid
flowchart TD
    A[开始判断投料状态] --> B[查询领料记录]
    B --> C{是否有领料记录?}
    C -->|否| D[返回: 未投料]
    C -->|是| E{是否有补料/超领记录?}
    E -->|是| F[返回: 补料]
    E -->|否| G[统计已完成领料单]
    G --> H{所有领料单都已完成?}
    H -->|是| I[返回: 投料完成]
    H -->|否| J{有已完成的领料单?}
    J -->|是| K[返回: 部分投料]
    J -->|否| L{有已审核的领料单?}
    L -->|是| M[返回: 部分投料]
    L -->|否| N[返回: 未投料]
```

### 数据表关联

#### production_material_request 表（生产领料单表）
```sql
-- 关键字段
`production_order_id` int(11) -- 生产订单ID
`status` tinyint(1) -- 领料状态：0=待审核，1=已审核，2=已出库，3=已完成，4=已取消
`request_type` tinyint(1) -- 领料类型：1=正常领料，2=补料，3=超领
`delete_time` int(11) -- 删除时间：0=未删除
```

## 🔧 技术实现细节

### 1. 查询优化

```php
// 只查询必要字段，提高查询效率
->field('id, status, exceed, create_time')

// 按时间倒序，优先获取最新记录
->order('create_time desc')

// 排除草稿状态的记录
->where('status', '>', 0)
```

### 2. 异常处理

```php
try {
    // 投料状态查询逻辑
} catch (\Exception $e) {
    \think\facade\Log::error('获取投料状态失败', [
        'order_id' => $orderId,
        'error' => $e->getMessage()
    ]);
    return 0; // 出错时返回未投料
}
```

### 3. 补料识别

```php
// 检查是否有补料记录
$hasSupplementary = false;
foreach ($feedingRecords as $record) {
    if ($record['exceed'] == 1) {
        $hasSupplementary = true;
        break;
    }
}
```

## 🎨 前端显示效果

### 投料状态列

| 显示文本 | 颜色标识 | 对应状态值 |
|----------|----------|------------|
| 未投料 | 灰色 | 0 |
| 部分投料 | 蓝色 | 1 |
| 投料完成 | 绿色 | 2 |
| 补料 | 橙色 | 3 |

## 🧪 测试验证

### 测试场景

#### 1. 未投料状态测试
- **条件**：生产订单没有任何投料记录
- **预期**：显示"未投料"

#### 2. 部分投料状态测试
- **条件**：生产订单有投料记录但未完成
- **预期**：显示"部分投料"

#### 3. 补料状态测试
- **条件**：生产订单存在补料记录（exceed=1）
- **预期**：显示"补料"

#### 4. 异常处理测试
- **条件**：查询过程中发生异常
- **预期**：显示"未投料"并记录错误日志

### 验证步骤

1. 访问生产订单列表页面
2. 检查投料状态列的显示
3. 对比数据库中的实际投料记录
4. 验证状态判断的准确性

## 🎯 修复效果

### 修复前
- ❌ 直接使用数据库字段，可能不准确
- ❌ 无法实时反映投料记录变化
- ❌ 补料状态识别不准确

### 修复后
- ✅ 基于实际投料记录判断状态
- ✅ 实时反映投料情况变化
- ✅ 准确识别补料状态
- ✅ 完善的异常处理机制

## 🚀 扩展优化建议

### 1. 性能优化
- 考虑添加缓存机制减少数据库查询
- 批量查询多个订单的投料状态

### 2. 业务逻辑完善
- 根据BOM需求量和实际投料量精确计算完成度
- 支持更细粒度的投料状态判断

### 3. 用户体验
- 添加投料进度百分比显示
- 提供投料详情的快速查看功能

修复完成后，生产订单列表将准确显示基于实际投料记录的投料状态，提供更可靠的生产管理信息。
