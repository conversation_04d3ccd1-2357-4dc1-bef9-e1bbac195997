<?php

namespace app\common\database\seeds;

use think\migration\Seeder;

class LogisticsSeeder extends Seeder
{
    /**
     * 执行数据填充
     */
    public function run()
    {
        $data = [
            [
                'name' => '顺丰速运',
                'code' => 'SF',
                'contact' => '95338',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '中通快递',
                'code' => 'ZTO',
                'contact' => '95311',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '申通快递',
                'code' => 'STO',
                'contact' => '95543',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '圆通速递',
                'code' => 'YTO',
                'contact' => '95554',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '韵达速递',
                'code' => 'YD',
                'contact' => '95546',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '京东物流',
                'code' => 'JD',
                'contact' => '950616',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => 'EMS',
                'code' => 'EMS',
                'contact' => '11183',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '百世快递',
                'code' => 'HTKY',
                'contact' => '95320',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
            [
                'name' => '德邦快递',
                'code' => 'DBL',
                'contact' => '95353',
                'status' => 1,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->table('logistics')->insert($data)->save();
    }
} 