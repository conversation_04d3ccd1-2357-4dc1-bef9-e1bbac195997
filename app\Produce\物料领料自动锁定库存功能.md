# 物料领料自动锁定库存功能

## 🔍 问题描述

在创建物料领料单时，如果锁定库存不够，系统直接报错：
```
{"code":1,"msg":"物料【659上壳】没有足够的锁定库存，请先进行库存分配"}
```

## 🎯 需求分析

用户希望当锁定库存不够时，系统能够：
1. **检查当前实际库存是否满足需求**
2. **如果实际库存满足，自动执行剩余数量的锁定和出库操作**
3. **如果实际库存也不满足，才报错提示库存不足**

## 🛠️ 解决方案

### 1. 修改领料单创建逻辑

在 `app\Produce\controller\MaterialRequest.php` 的 `save()` 方法中修改库存检查逻辑：

```php
// 修改前：直接报错
$lockedStocks = $this->getLockedStockByMaterial($material['material_id'], $param['production_order_id'], $requestQuantity);

if (empty($lockedStocks)) {
    throw new \Exception("物料【{$material['material_name']}】没有足够的锁定库存，请先进行库存分配");
}

// 修改后：尝试自动锁定
$lockedStocks = $this->getLockedStockByMaterial($material['material_id'], $param['production_order_id'], $requestQuantity);

// 如果锁定库存不够，检查实际库存并尝试自动锁定
if (empty($lockedStocks)) {
    $autoLockResult = $this->autoLockInventoryForMaterial($material['material_id'], $param['production_order_id'], $requestQuantity);
    if ($autoLockResult['success']) {
        $lockedStocks = $autoLockResult['locked_stocks'];
        Log::info('自动锁定库存成功', [
            'material_id' => $material['material_id'],
            'material_name' => $material['material_name'],
            'request_quantity' => $requestQuantity,
            'locked_stocks' => $lockedStocks
        ]);
    } else {
        throw new \Exception("物料【{$material['material_name']}】库存不足，当前可用库存：{$autoLockResult['available_quantity']}，需求数量：{$requestQuantity}");
    }
}
```

### 2. 添加自动锁定库存方法

```php
/**
 * 自动锁定物料库存
 * @param int $materialId 物料ID
 * @param int $productionOrderId 生产订单ID
 * @param float $requestQuantity 需求数量
 * @return array 锁定结果
 */
private function autoLockInventoryForMaterial($materialId, $productionOrderId, $requestQuantity)
{
    try {
        // 查询该物料的可用库存
        $availableStocks = Db::name('inventory_realtime')
            ->alias('ir')
            ->join('warehouse w', 'ir.warehouse_id = w.id')
            ->where('ir.product_id', $materialId)
            ->where('ir.available_quantity', '>', 0)
            ->field('ir.id, ir.warehouse_id, w.name as warehouse_name, ir.available_quantity')
            ->order('ir.available_quantity desc') // 优先使用库存多的仓库
            ->select()
            ->toArray();

        if (empty($availableStocks)) {
            return [
                'success' => false,
                'available_quantity' => 0,
                'message' => '没有可用库存'
            ];
        }

        // 计算总可用库存
        $totalAvailable = array_sum(array_column($availableStocks, 'available_quantity'));
        
        if ($totalAvailable < $requestQuantity) {
            return [
                'success' => false,
                'available_quantity' => $totalAvailable,
                'message' => '可用库存不足'
            ];
        }

        // 按需求数量分配并锁定库存
        $lockedStocks = [];
        $remainingQuantity = $requestQuantity;

        foreach ($availableStocks as $stock) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $lockQuantity = min($remainingQuantity, $stock['available_quantity']);
            
            // 创建库存锁定记录
            $lockData = [
                'product_id' => $materialId,
                'warehouse_id' => $stock['warehouse_id'],
                'quantity' => $lockQuantity,
                'ref_type' => 'production_order',
                'ref_id' => $productionOrderId,
                'status' => 1, // 已锁定
                'lock_time' => time(),
                'created_by' => $this->uid,
                'create_time' => time(),
                'update_time' => time()
            ];

            $lockId = Db::name('inventory_lock')->insertGetId($lockData);

            // 更新实时库存（减少可用库存，增加锁定库存）
            Db::name('inventory_realtime')
                ->where('id', $stock['id'])
                ->dec('available_quantity', $lockQuantity)
                ->inc('locked_quantity', $lockQuantity)
                ->update();

            $lockedStocks[] = [
                'lock_id' => $lockId,
                'warehouse_id' => $stock['warehouse_id'],
                'warehouse_name' => $stock['warehouse_name'],
                'quantity' => $lockQuantity
            ];

            $remainingQuantity -= $lockQuantity;
        }

        return [
            'success' => true,
            'locked_stocks' => $lockedStocks,
            'message' => '自动锁定成功'
        ];

    } catch (\Exception $e) {
        Log::error('自动锁定库存失败', [
            'material_id' => $materialId,
            'production_order_id' => $productionOrderId,
            'request_quantity' => $requestQuantity,
            'error' => $e->getMessage()
        ]);

        return [
            'success' => false,
            'available_quantity' => 0,
            'message' => '自动锁定失败：' . $e->getMessage()
        ];
    }
}
```

## 📋 功能流程

### 自动锁定流程图

```mermaid
flowchart TD
    A[创建领料单] --> B[检查锁定库存]
    B --> C{锁定库存是否足够?}
    C -->|是| D[使用现有锁定库存]
    C -->|否| E[查询可用库存]
    E --> F{可用库存是否足够?}
    F -->|否| G[报错：库存不足]
    F -->|是| H[自动锁定库存]
    H --> I[更新库存状态]
    I --> J[创建锁定记录]
    J --> K[继续创建领料单]
    D --> K
    K --> L[创建出库单]
    L --> M[完成]
```

### 库存状态变化

#### 1. 锁定前状态
```
inventory_realtime 表：
- available_quantity: 100 (可用库存)
- locked_quantity: 0 (锁定库存)
```

#### 2. 锁定后状态
```
inventory_realtime 表：
- available_quantity: 70 (可用库存减少30)
- locked_quantity: 30 (锁定库存增加30)

inventory_lock 表：
- 新增锁定记录，quantity: 30
```

## 🔧 技术实现细节

### 1. 库存查询策略

```php
// 优先使用库存多的仓库，减少跨仓库操作
->order('ir.available_quantity desc')

// 只查询有可用库存的记录
->where('ir.available_quantity', '>', 0)
```

### 2. 分配算法

```php
// 按需求数量逐个仓库分配
foreach ($availableStocks as $stock) {
    $lockQuantity = min($remainingQuantity, $stock['available_quantity']);
    // 锁定当前仓库的部分或全部库存
    $remainingQuantity -= $lockQuantity;
}
```

### 3. 事务安全

```php
// 在事务中执行锁定操作，确保数据一致性
Db::startTrans();
try {
    // 创建锁定记录
    $lockId = Db::name('inventory_lock')->insertGetId($lockData);
    
    // 更新库存状态
    Db::name('inventory_realtime')
        ->where('id', $stock['id'])
        ->dec('available_quantity', $lockQuantity)
        ->inc('locked_quantity', $lockQuantity)
        ->update();
        
    Db::commit();
} catch (\Exception $e) {
    Db::rollback();
    throw $e;
}
```

## 🎯 修复效果

### 修复前
- ❌ 锁定库存不够直接报错
- ❌ 需要手动进行库存分配
- ❌ 用户体验不佳

### 修复后
- ✅ 自动检查可用库存
- ✅ 自动锁定满足需求的库存
- ✅ 智能的库存分配策略
- ✅ 详细的错误提示信息

## 🧪 测试场景

### 1. 自动锁定成功
- **条件**：锁定库存不够，但可用库存充足
- **预期**：自动锁定库存，成功创建领料单

### 2. 库存不足
- **条件**：锁定库存和可用库存都不够
- **预期**：报错并显示当前可用库存数量

### 3. 跨仓库锁定
- **条件**：单个仓库库存不够，需要多个仓库
- **预期**：自动分配多个仓库的库存

修复完成后，物料领料功能将更加智能和用户友好！
