# 库存锁定机制使用说明

## 概述

新的库存锁定机制取代了原有的预占机制，提供更简洁、高效的库存管理方式。系统在业务审核后直接锁定相关物料，支持多模块调用，具有良好的复用性。

## 主要特性

1. **直接锁定**：业务审核后直接锁定库存，无需预占步骤
2. **自动释放**：支持过期自动释放和业务完成后释放
3. **多模块支持**：提供统一的服务接口，支持多个业务模块调用
4. **状态管理**：完整的锁定状态跟踪（锁定、使用、释放、过期）
5. **日志记录**：详细的操作日志，便于追踪和审计

## 数据库变更

### 删除预占相关表
提供了三个文件来处理数据库迁移：

1. **delete_inventory_reserve.sql**: 完整版迁移脚本，包含动态检查
2. **delete_inventory_reserve_simple.sql**: 简化版迁移脚本（推荐使用）
3. **check_database_migration.php**: 迁移完成情况检查脚本

主要变更：
- 删除 `oa_inventory_reserve` 表
- 删除 `oa_inventory_reserve_log` 表  
- 创建/更新 `oa_inventory_lock` 表结构
- 创建 `oa_inventory_lock_log` 日志表
- 删除库存表中的预占相关字段（如果存在）

## 核心服务类

### InventoryLockService

位置：`app/warehouse/service/InventoryLockService.php`

#### 主要方法

1. **锁定库存**
```php
$lockService = new InventoryLockService();
$result = $lockService->lockInventory([
    'product_id' => 1,
    'quantity' => 100,
    'warehouse_id' => 1,
    'location_id' => 1,
    'batch_no' => 'BATCH001',
    'ref_type' => 'customer_order',
    'ref_id' => 123,
    'ref_no' => 'SO2024001',
    'notes' => '客户订单锁定',
    'expire_time' => strtotime('+7 days'),
    'auto_release' => 1,
    'created_by' => 1
]);
```

2. **批量锁定库存**
```php
$lockItems = [
    [
        'product_id' => 1,
        'quantity' => 50,
        'warehouse_id' => 1,
        'ref_type' => 'customer_order',
        'ref_id' => 123,
        'ref_no' => 'SO2024001',
        'created_by' => 1
    ],
    [
        'product_id' => 2,
        'quantity' => 30,
        'warehouse_id' => 1,
        'ref_type' => 'customer_order',
        'ref_id' => 123,
        'ref_no' => 'SO2024001',
        'created_by' => 1
    ]
];

$result = $lockService->batchLockInventory($lockItems);
```

3. **使用锁定库存（出库时）**
```php
$lockIds = [1, 2, 3]; // 锁定记录ID数组
$result = $lockService->useLockedInventory($lockIds, [
    'operation_by' => 1,
    'notes' => '出库使用锁定库存'
]);
```

4. **释放锁定库存**
```php
$lockIds = [1, 2, 3];
$result = $lockService->releaseLockedInventory($lockIds, [
    'operation_by' => 1,
    'notes' => '订单取消，释放锁定'
]);
```

5. **根据业务单据释放锁定**
```php
$result = $lockService->releaseByReference('customer_order', 123, [
    'operation_by' => 1,
    'notes' => '订单取消'
]);
```

6. **获取产品库存状态**
```php
$status = $lockService->getProductInventoryStatus(1, 1);
// 返回：
// [
//     'product_id' => 1,
//     'warehouse_id' => 1,
//     'total_quantity' => 1000,
//     'locked_quantity' => 200,
//     'available_quantity' => 800,
//     'status' => 'sufficient'
// ]
```

## 业务模块集成示例

### 客户订单模块

```php
// 订单审核通过后锁定库存
public function auditOrder($orderId)
{
    $order = CustomerOrder::find($orderId);
    $orderDetails = $order->details;
    
    $lockService = new InventoryLockService();
    $lockItems = [];
    
    foreach ($orderDetails as $detail) {
        $lockItems[] = [
            'product_id' => $detail->product_id,
            'quantity' => $detail->quantity,
            'warehouse_id' => $detail->warehouse_id,
            'ref_type' => 'customer_order',
            'ref_id' => $orderId,
            'ref_no' => $order->order_no,
            'notes' => '客户订单审核锁定',
            'expire_time' => strtotime('+30 days'),
            'created_by' => session('admin.id')
        ];
    }
    
    $result = $lockService->batchLockInventory($lockItems);
    
    if ($result['code'] == 0) {
        // 更新订单状态
        $order->status = 'approved';
        $order->save();
    }
    
    return $result;
}

// 订单出库时使用锁定库存
public function deliverOrder($orderId)
{
    // 查找该订单的锁定记录
    $lockIds = Db::name('inventory_lock')
        ->where('ref_type', 'customer_order')
        ->where('ref_id', $orderId)
        ->where('status', 1)
        ->column('id');
    
    if (!empty($lockIds)) {
        $lockService = new InventoryLockService();
        $result = $lockService->useLockedInventory($lockIds, [
            'operation_by' => session('admin.id')
        ]);
        
        return $result;
    }
}

// 订单取消时释放锁定
public function cancelOrder($orderId)
{
    $lockService = new InventoryLockService();
    $result = $lockService->releaseByReference('customer_order', $orderId, [
        'operation_by' => session('admin.id'),
        'notes' => '订单取消释放锁定'
    ]);
    
    return $result;
}
```

### 生产订单模块

```php
// 生产订单审核后锁定原料
public function auditProductionOrder($orderId)
{
    $order = ProductionOrder::find($orderId);
    $materials = $order->materials; // 生产所需原料
    
    $lockService = new InventoryLockService();
    $lockItems = [];
    
    foreach ($materials as $material) {
        $lockItems[] = [
            'product_id' => $material->product_id,
            'quantity' => $material->quantity,
            'warehouse_id' => $material->warehouse_id,
            'ref_type' => 'production_order',
            'ref_id' => $orderId,
            'ref_no' => $order->order_no,
            'notes' => '生产订单原料锁定',
            'created_by' => session('admin.id')
        ];
    }
    
    return $lockService->batchLockInventory($lockItems);
}
```

## API接口

### 获取产品库存状态
```
GET /warehouse/inventory/getProductInventory
参数：
- product_id: 产品ID
- warehouse_id: 仓库ID（可选）

返回：
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "product_id": 1,
        "warehouse_id": 1,
        "total_quantity": 1000,
        "locked_quantity": 200,
        "available_quantity": 800,
        "status": "sufficient"
    }
}
```

### 锁定库存
```
POST /warehouse/inventory/lockInventory
参数：
- product_id: 产品ID
- quantity: 锁定数量
- warehouse_id: 仓库ID
- location_id: 库位ID（可选）
- batch_no: 批次号（可选）
- ref_type: 关联类型
- ref_id: 关联ID
- ref_no: 关联单号（可选）
- notes: 备注（可选）
- expire_time: 过期时间（可选）
- auto_release: 是否自动释放（可选，默认1）

返回：
{
    "code": 0,
    "msg": "锁定成功",
    "data": {
        "lock_id": 123
    }
}
```

## 定时任务

### 处理过期锁定
建议设置定时任务，定期处理过期的锁定记录：

```php
// 在定时任务中调用
$lockService = new InventoryLockService();
$result = $lockService->handleExpiredLocks();
```

## 锁定类型说明

- `customer_order`: 客户订单锁定
- `production_order`: 生产订单锁定
- `transfer_order`: 调拨单锁定
- `sample_order`: 样品单锁定
- `manual_lock`: 手动锁定
- `quality_check`: 质检锁定

## 状态说明

- `0`: 已释放
- `1`: 已锁定
- `2`: 已使用
- `3`: 已过期

## 注意事项

1. **库存检查**：锁定前会自动检查库存是否充足
2. **事务处理**：所有锁定操作都在事务中执行，确保数据一致性
3. **日志记录**：所有操作都会记录详细日志
4. **过期处理**：建议设置定时任务处理过期锁定
5. **权限控制**：根据业务需要控制锁定和释放权限

## 迁移步骤

### 1. 备份数据
```bash
# 备份相关数据表
mysqldump -u用户名 -p密码 数据库名 oa_inventory_reserve oa_inventory_reserve_log oa_inventory oa_inventory_lock > backup_before_migration.sql
```

### 2. 执行数据库迁移

#### 方式一：使用简化版SQL脚本（推荐）
```bash
mysql -u用户名 -p密码 数据库名 < delete_inventory_reserve_simple.sql
```

#### 方式二：手动执行SQL语句
```sql
-- 删除预占表
DROP TABLE IF EXISTS `oa_inventory_reserve`;
DROP TABLE IF EXISTS `oa_inventory_reserve_log`;

-- 创建锁定表（参考 delete_inventory_reserve_simple.sql）
-- ...
```

### 3. 检查迁移结果
```bash
php check_database_migration.php
```

### 4. 手动处理（如果需要）
根据检查脚本的结果，手动删除库存表中的预占字段：
```sql
-- 如果存在这些字段，请删除
ALTER TABLE `oa_inventory` DROP COLUMN `reserved_quantity`;
ALTER TABLE `oa_inventory` DROP COLUMN `allocated_quantity`;
```

### 5. 更新代码
- 将使用 `InventoryReservationService` 的代码改为使用 `InventoryLockService`
- 删除预占相关的代码文件
- 更新业务逻辑，改为审核后直接锁定

### 6. 测试验证
- 测试库存锁定功能
- 测试库存释放功能
- 测试各个业务场景的集成
- 验证库存数据的准确性

### 7. 上线部署
确认测试无误后部署到生产环境

## 数据库迁移说明

### SQL脚本说明
- `delete_inventory_reserve.sql`: 完整版迁移脚本，包含动态SQL检查
- `delete_inventory_reserve_simple.sql`: 简化版迁移脚本，避免复杂语法
- `check_database_migration.php`: PHP检查脚本，验证迁移完成情况

### 可能遇到的问题
1. **语法错误**: 如果遇到 `IF NOT EXISTS` 语法错误，请使用简化版SQL脚本
2. **字段已存在**: 如果提示字段已存在，可以忽略或手动检查
3. **权限问题**: 确保数据库用户有足够的权限执行DDL操作

## 常见问题

### Q: 如何处理部分锁定？
A: 可以通过多次调用锁定接口实现部分锁定，每次锁定不同的数量。

### Q: 锁定记录可以修改吗？
A: 锁定记录创建后不建议修改，如需调整可以释放后重新锁定。

### Q: 如何查看某个产品的所有锁定记录？
A: 使用 `getProductLockDetails()` 方法或查询锁定记录列表页面。

### Q: 锁定过期后会自动释放吗？
A: 如果设置了 `auto_release=1` 且配置了定时任务，会自动释放过期锁定。