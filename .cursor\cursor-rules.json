{"version": "1.0", "name": "Cursor Rules Configuration", "description": "Custom rules configuration for Cursor IDE to enhance productivity and code quality", "rules": {"codeQuality": {"formatting": {"indentSize": 2, "tabWidth": 2, "useTabs": false, "maxLineLength": 100, "trailingComma": "es5", "bracketSpacing": true, "semi": true, "singleQuote": true, "jsxSingleQuote": false, "arrowParens": "always", "endOfLine": "lf"}, "naming": {"variableNaming": "camelCase", "functionNaming": "camelCase", "classNaming": "PascalCase", "constantNaming": "UPPER_SNAKE_CASE", "privatePrefix": "_", "minNameLength": 2, "maxNameLength": 30, "forbiddenNames": ["temp", "foo", "bar"]}, "complexity": {"maxFunctionLength": 50, "maxParameters": 4, "maxNestingDepth": 3, "maxComplexity": 15, "maxFileLength": 400}, "patterns": {"requireExplicitTypes": true, "noImplicitAny": true, "noConsole": "warning", "noUnusedVariables": "error", "noMagicNumbers": true, "requireTryCatch": true, "preferEarlyReturn": true, "requireJSDoc": "functions"}}, "aiAssistance": {"completion": {"triggerCharCount": 3, "autoSuggest": true, "suggestAfterDelay": 500, "acceptWithTab": true, "maxSuggestionLength": 50, "contextLines": 10, "autoDismissDelay": 10000}, "generation": {"style": "concise", "includeComments": true, "respectNamingConventions": true, "insertImports": true, "preferredPatterns": ["async/await", "functional", "typed"], "avoidPatterns": ["callback-heavy", "deeply-nested"]}, "prompts": {"customPrompts": [{"name": "Document Function", "shortcut": "Alt+D", "template": "Generate JSDoc documentation for this function"}, {"name": "Optimize Code", "shortcut": "Alt+O", "template": "Optimize this code for performance while maintaining readability"}, {"name": "Add Tests", "shortcut": "Alt+T", "template": "Generate unit tests for this function using the project's testing framework"}, {"name": "Refa<PERSON>", "shortcut": "Alt+R", "template": "Refactor this code to improve readability and maintainability"}]}}, "productivity": {"autoSave": {"enabled": true, "delayMs": 1000, "focusChange": true, "windowChange": true}, "fileTemplates": [{"name": "React Component", "extension": ".tsx", "path": "src/components", "template": "import React from 'react';\n\ninterface {{NAME}}Props {\n  // Props definition\n}\n\nexport const {{NAME}}: React.FC<{{NAME}}Props> = (props) => {\n  return (\n    <div>\n      {/* Component content */}\n    </div>\n  );\n};\n"}, {"name": "API Endpoint", "extension": ".ts", "path": "src/api", "template": "import { Request, Response } from 'express';\n\nexport const {{camelCase NAME}} = async (req: Request, res: Response): Promise<void> => {\n  try {\n    // Endpoint logic\n    res.status(200).json({ success: true });\n  } catch (error) {\n    res.status(500).json({ success: false, error: error.message });\n  }\n};\n"}], "snippets": [{"trigger": "trycatch", "scope": "javascript,typescript", "content": "try {\n  $1\n} catch (error) {\n  $2\n}"}, {"trigger": "usestate", "scope": "javascriptreact,typescriptreact", "content": "const [$1, set${1/(.*)/${1:/capitalize}/}] = useState($2);"}], "shortcuts": {"aiComplete": "Alt+\\", "aiExplain": "Ctrl+Shift+E", "aiRefactor": "Ctrl+Shift+R", "aiFixErrors": "Alt+F", "toggleFocusMode": "Ctrl+Alt+F", "jumpToDefinition": "F12"}}, "collaboration": {"commitMessage": {"format": "<type>(<scope>): <subject>", "types": ["feat", "fix", "docs", "style", "refactor", "test", "chore"], "maxLength": 72, "requireTicketNumber": true, "ticketRegex": "(PROJ-\\d+)"}, "review": {"requiredApprovals": 1, "blockingLabels": ["wip", "on-hold"], "autoAssign": true, "codeOwners": "CODEOWNERS", "reminderAfterHours": 24}, "validation": {"runTests": true, "runLinters": true, "checkFormatting": true, "preventDirectPushToMain": true, "enforceConventionalCommits": true}}}, "custom": {"projectTechStack": {"zh": "后端：PHP 8+，ThinkPHP 8 框架，MySQL 数据库，主要依赖 topthink/framework、topthink/think-filesystem、topthink/think-multi-app、topthink/think-view、topthink/think-captcha、topthink/think-helper、overtrue/pinyin、phpmailer/phpmailer、firebase/php-jwt、phpoffice/phpspreadsheet、phpoffice/phpword 等。前端：以 LayUI、Bootstrap 为主，部分页面自定义组件，使用 jQuery，页面模板为多端适配（PC/H5），静态资源管理采用模块化加载（如 layui.define），UI 组件和交互逻辑主要集中在 public/static/assets/gougu/ 及 mbui/ 目录。整体为典型的前后端一体化架构，接口部分支持 JWT 鉴权。", "en": "Backend: PHP 8+, ThinkPHP 8 framework, MySQL database. Main dependencies: topthink/framework, topthink/think-filesystem, topthink/think-multi-app, topthink/think-view, topthink/think-captcha, topthink/think-helper, overtrue/pinyin, phpmailer/phpmailer, firebase/php-jwt, phpoffice/phpspreadsheet, phpoffice/phpword, etc. Frontend: Primarily LayUI and Bootstrap, with custom components on some pages, using jQuery. Page templates are multi-terminal adaptive (PC/H5). Static resources are managed via modular loading (e.g., layui.define). UI components and interaction logic are mainly in public/static/assets/gougu/ and mbui/ directories. The overall architecture is a typical monolithic (integrated) system, with API endpoints supporting JWT authentication."}}}