# 出库单前端显示修复说明

## 问题描述
出库单列表页面显示不正确，主要原因是前端代码还在使用旧的表名和字段结构，而数据库已经更新为新的`oa_outbound`表结构。

## 修复内容

### 1. 模型常量更新

#### 出库类型常量 (字符串类型)
```php
// 修改前：数字类型
const TYPE_SALES = 1;       // 销售出库
const TYPE_PRODUCTION = 2;  // 生产领料
const TYPE_SAMPLE = 3;      // 样品出库

// 修改后：字符串类型
const TYPE_SALES = 'sales';         // 销售出库
const TYPE_PRODUCTION = 'production'; // 生产出库
const TYPE_TRANSFER = 'transfer';   // 调拨出库
const TYPE_RETURN = 'return';       // 退货出库
const TYPE_OTHER = 'other';         // 其他出库
```

#### 状态常量更新
```php
// 修改前：简化状态
const STATUS_DRAFT = 1;     // 草稿
const STATUS_PENDING = 2;   // 已出库
const STATUS_CANCELED = 3;  // 已取消

// 修改后：完整状态流程
const STATUS_DRAFT = 0;         // 草稿
const STATUS_SUBMITTED = 1;     // 已提交
const STATUS_APPROVED = 2;      // 已审核
const STATUS_PARTIAL = 3;       // 部分出库
const STATUS_COMPLETED = 4;     // 全部出库
const STATUS_CANCELED = 5;      // 已取消
```

### 2. 控制器查询更新

#### 出库类型筛选修复
```php
// 修改前：强制转换为整数
$where[] = ['o.outbound_type', '=', intval($param['outbound_type'])];

// 修改后：保持字符串类型
$where[] = ['o.outbound_type', '=', $param['outbound_type']];
```

#### 查询字段扩展
```php
// 新增字段
->leftJoin('warehouse w', 'o.warehouse_id = w.id')
->field('o.id, o.outbound_no, o.outbound_type, o.outbound_date, o.customer_id, 
        o.total_quantity, o.total_amount, o.status, o.priority, o.ref_type, o.ref_no,
        o.created_by, o.create_time, o.update_time,
        c.name as customer_name, w.name as warehouse_name,
        FROM_UNIXTIME(o.create_time, "%Y-%m-%d %H:%i") as create_time_formatted')
```

#### 类型和状态选项更新
```php
// 出库类型选项
$outboundTypes = [
    ['value' => OutboundModel::TYPE_SALES, 'label' => '销售出库'],
    ['value' => OutboundModel::TYPE_PRODUCTION, 'label' => '生产出库'],
    ['value' => OutboundModel::TYPE_TRANSFER, 'label' => '调拨出库'],
    ['value' => OutboundModel::TYPE_RETURN, 'label' => '退货出库'],
    ['value' => OutboundModel::TYPE_OTHER, 'label' => '其他出库'],
];

// 状态选项
$statusList = [
    ['value' => 0, 'label' => '草稿'],
    ['value' => 1, 'label' => '已提交'],
    ['value' => 2, 'label' => '已审核'],
    ['value' => 3, 'label' => '部分出库'],
    ['value' => 4, 'label' => '全部出库'],
    ['value' => 5, 'label' => '已取消'],
];
```

### 3. 前端视图更新

#### 状态筛选选项
```html
<!-- 修改前 -->
<select name="status">
    <option value="">全部</option>
    <option value="1">草稿</option>
    <option value="2">已出库</option>
    <option value="3">已取消</option>
</select>

<!-- 修改后 -->
<select name="status">
    <option value="">全部</option>
    <option value="0">草稿</option>
    <option value="1">已提交</option>
    <option value="2">已审核</option>
    <option value="3">部分出库</option>
    <option value="4">全部出库</option>
    <option value="5">已取消</option>
</select>
```

### 4. 新增功能

#### 优先级显示
```php
// 新增优先级文字转换方法
public static function getPriorityText($priority)
{
    $priorities = [
        1 => '普通',
        2 => '紧急',
        3 => '特急'
    ];
    
    return $priorities[$priority] ?? '普通';
}

// 在控制器中添加优先级文字
$item['priority_text'] = OutboundModel::getPriorityText($item['priority']);
```

## 新表结构字段说明

### 主要新增字段
- `outbound_type` - 出库类型（字符串）
- `ref_type` - 关联业务类型
- `ref_id` - 关联业务ID
- `ref_no` - 关联业务单号
- `department_id` - 部门ID（生产出库使用）
- `to_warehouse_id` - 目标仓库ID（调拨使用）
- `outbound_date` - 出库日期
- `total_quantity` - 总出库数量
- `priority` - 优先级
- `approved_by` - 审核人ID
- `approved_time` - 审核时间
- `submitted_by` - 提交人ID
- `submitted_at` - 提交时间
- `outbound_by` - 出库操作人ID
- `outbound_time` - 出库时间

### 字段映射关系
| 旧字段 | 新字段 | 说明 |
|--------|--------|------|
| outbound_type (int) | outbound_type (varchar) | 类型改为字符串 |
| - | ref_type | 新增关联业务类型 |
| - | ref_id | 新增关联业务ID |
| - | ref_no | 新增关联业务单号 |
| - | priority | 新增优先级 |
| - | total_quantity | 新增总数量 |

## 业务流程状态

### 完整的出库流程
1. **草稿 (0)** - 初始创建状态
2. **已提交 (1)** - 提交审核
3. **已审核 (2)** - 审核通过，可以出库
4. **部分出库 (3)** - 部分商品已出库
5. **全部出库 (4)** - 所有商品已出库完成
6. **已取消 (5)** - 出库单取消

### 出库类型业务场景
- **sales** - 销售出库：客户订单发货
- **production** - 生产出库：生产领料
- **transfer** - 调拨出库：仓库间调拨
- **return** - 退货出库：退货处理
- **other** - 其他出库：其他业务需求

### 优先级管理
- **普通 (1)** - 正常业务优先级
- **紧急 (2)** - 紧急业务，优先处理
- **特急 (3)** - 特别紧急，最高优先级

## 注意事项

### 1. 数据兼容性
- 确保旧数据正确迁移到新表结构
- 检查所有使用出库表的模块
- 更新相关的查询和统计逻辑

### 2. 前端显示
- 确保所有字段正确显示
- 更新表格列定义
- 检查筛选和搜索功能

### 3. 业务逻辑
- 确保状态流转正确
- 检查权限控制
- 验证数据完整性

### 4. 其他模块影响
需要检查的相关模块：
- 销售出库
- 库存管理
- 报表统计
- 数据导出

通过这些修复，出库单列表页面现在可以正确显示新表结构的数据了。
