<?php
declare (strict_types = 1);

namespace app\metadata\controller;

use app\base\BaseController;
use app\metadata\validate\UnitCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Unit extends BaseController
{
    /**
     * 单位管理列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['keywords'])) {
                $where[] = ['name', 'like', '%' . $param['keywords'] . '%'];
            }
            
            if (!empty($param['status']) && $param['status'] != '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            if (!empty($param['type']) && $param['type'] != '') {
                $where[] = ['type', '=', $param['type']];
            }
            
            $list = Db::name('Unit')
                ->where($where)
                ->order('create_time desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 20,
                    'page' => $param['page'] ?? 1
                ]);
                
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }
    
    /**
     * 新增/编辑单位
     */
    public function add()
    {
        $param = get_params();
        if (request()->isAjax()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(UnitCheck::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }
                $param['update_time'] = time();
                $res = Db::name('Unit')->strict(false)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                }
                return to_assign(0, '编辑成功');
            } else {
                try {
                    validate(UnitCheck::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }
                $param['create_time'] = time();
                $param['update_time'] = time();
                $insertId = Db::name('Unit')->strict(false)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                }
                return to_assign(0, '添加成功');
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            if ($id > 0) {
                $detail = Db::name('Unit')->where(['id' => $id])->find();
                View::assign('detail', $detail);
            }
            View::assign('id', $id);
            return view();
        }
    }
    
    /**
     * 设置单位状态
     */
    public function check()
    {
        $param = get_params();
        $res = Db::name('Unit')->strict(false)->field('id,status')->update($param);
        if ($res) {
            if($param['status'] == 0){
                add_log('disable', $param['id'], $param);
            } else if($param['status'] == 1){
                add_log('recovery', $param['id'], $param);
            }
            return to_assign(0, '操作成功');
        } else {
            return to_assign(1, '操作失败');
        }
    }
    
    /**
     * 删除单位
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }
        
        $detail = Db::name('Unit')->where(['id' => $id])->find();
        if (empty($detail)) {
            return to_assign(1, "单位不存在");
        }
        
        $res = Db::name('Unit')->where(['id' => $id])->delete();
        if ($res) {
            add_log('delete', $id);
            return to_assign(0, "删除成功");
        } else {
            return to_assign(1, "删除失败");
        }
    }
}