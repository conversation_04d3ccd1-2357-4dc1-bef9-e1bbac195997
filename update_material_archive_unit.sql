-- 更新物料档案表的基本单位字段
-- 将base_unit字段从varchar改为int类型，存储单位ID

-- 1. 首先备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS `oa_material_archive_unit_backup` AS 
SELECT id, material_code, material_name, base_unit 
FROM `oa_material_archive` 
WHERE base_unit IS NOT NULL AND base_unit != '';

-- 2. 清空base_unit字段的数据（因为要改变数据类型）
UPDATE `oa_material_archive` SET base_unit = NULL;

-- 3. 修改字段类型
ALTER TABLE `oa_material_archive` 
MODIFY COLUMN `base_unit` int(11) DEFAULT NULL COMMENT '基本单位ID';

-- 4. 添加外键索引（可选，提高查询性能）
ALTER TABLE `oa_material_archive` 
ADD INDEX `idx_base_unit` (`base_unit`);

-- 5. 如果需要，可以根据单位名称更新为单位ID
-- 示例：将原来的单位名称映射为单位ID
-- UPDATE `oa_material_archive` a 
-- JOIN `oa_unit` u ON u.name = '平米'
-- SET a.base_unit = u.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_unit_backup` WHERE base_unit = '平米');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_unit` u ON u.name = '个'
-- SET a.base_unit = u.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_unit_backup` WHERE base_unit = '个');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_unit` u ON u.name = '千克'
-- SET a.base_unit = u.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_unit_backup` WHERE base_unit = '千克');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_unit` u ON u.name = '米'
-- SET a.base_unit = u.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_unit_backup` WHERE base_unit = '米');

-- UPDATE `oa_material_archive` a 
-- JOIN `oa_unit` u ON u.name = '套'
-- SET a.base_unit = u.id 
-- WHERE a.id IN (SELECT id FROM `oa_material_archive_unit_backup` WHERE base_unit = '套');

-- 6. 验证更新结果
-- SELECT a.id, a.material_code, a.material_name, a.base_unit, u.name as unit_name, u.precision, u.type
-- FROM `oa_material_archive` a 
-- LEFT JOIN `oa_unit` u ON a.base_unit = u.id
-- WHERE a.base_unit IS NOT NULL;
