# 新出库表结构适配说明

## 表结构变更

### 旧表结构
- `oa_warehouse_outbound` - 出库单主表
- `oa_warehouse_outbound_detail` - 出库单明细表

### 新表结构
- `oa_outbound` - 出库单主表
- `oa_outbound_detail` - 出库单明细表

## 字段映射关系

### 主表字段映射 (oa_outbound)

| 旧字段 | 新字段 | 说明 |
|--------|--------|------|
| warehouse_name | - | 删除，通过warehouse_id关联获取 |
| production_order_id | - | 删除，通过ref_id存储 |
| production_order_no | ref_no | 关联业务单号 |
| total_items | - | 删除，可通过明细统计 |
| created_name | - | 删除，通过created_by关联获取 |
| - | outbound_type | 新增，出库类型 |
| - | department_id | 新增，部门ID |
| - | outbound_date | 新增，出库日期 |
| - | total_amount | 新增，总金额 |
| - | priority | 新增，优先级 |

### 明细表字段映射 (oa_outbound_detail)

| 旧字段 | 新字段 | 说明 |
|--------|--------|------|
| outbound_no | - | 删除，通过outbound_id关联 |
| product_specs | specification | 字段名变更 |
| warehouse_id | - | 删除，通过主表获取 |
| warehouse_name | - | 删除，通过主表获取 |
| lock_id | notes | 移到备注字段记录 |
| - | ref_detail_id | 新增，关联业务明细ID |
| - | unit_price | 新增，单价 |
| - | total_amount | 新增，总金额 |
| - | batch_no | 新增，批次号 |
| - | production_date | 新增，生产日期 |
| - | expiry_date | 新增，过期日期 |
| - | location | 新增，库位 |
| - | quality_status | 新增，质量状态 |
| - | actual_quantity | 新增，实际出库数量 |
| - | shortage_quantity | 新增，缺货数量 |

## 代码适配修改

### 1. 主表创建逻辑
```php
// 修改前
$outboundData = [
    'outbound_no' => $outboundNo,
    'warehouse_id' => $warehouseGroup['warehouse_id'],
    'warehouse_name' => $warehouseGroup['warehouse_name'],
    'outbound_type' => 3, // 数字类型
    'production_order_id' => $order['id'],
    'production_order_no' => $order['order_no'],
    'total_items' => count($warehouseGroup['materials']),
    'created_name' => session('admin.name'),
];

// 修改后
$outboundData = [
    'outbound_no' => $outboundNo,
    'outbound_type' => 'production', // 字符串类型
    'ref_type' => 'production_material_request',
    'ref_id' => $requestId,
    'ref_no' => $requestNo,
    'warehouse_id' => $warehouseGroup['warehouse_id'],
    'department_id' => 0,
    'outbound_date' => date('Y-m-d'),
    'total_quantity' => array_sum(array_column($warehouseGroup['materials'], 'quantity')),
    'total_amount' => 0.00,
    'status' => 2, // 已审核状态
    'priority' => 1, // 普通优先级
];
```

### 2. 明细表创建逻辑
```php
// 修改前
$detailData = [
    'outbound_id' => $outboundId,
    'outbound_no' => $outboundNo,
    'product_id' => $material['material_id'],
    'product_code' => $material['material_code'],
    'product_specs' => $material['material_specs'],
    'warehouse_id' => $warehouseGroup['warehouse_id'],
    'warehouse_name' => $warehouseGroup['warehouse_name'],
    'lock_id' => $material['lock_id'],
];

// 修改后
$detailData = [
    'outbound_id' => $outboundId,
    'ref_detail_id' => 0,
    'product_id' => $material['material_id'],
    'product_name' => $material['material_name'],
    'product_code' => $material['material_code'],
    'specification' => $material['material_specs'] ?? '',
    'unit' => $material['unit'],
    'quantity' => $material['quantity'],
    'unit_price' => 0.00,
    'total_amount' => 0.00,
    'batch_no' => '',
    'location' => '',
    'quality_status' => 1, // 合格
    'actual_quantity' => 0.00,
    'shortage_quantity' => 0.00,
    'notes' => '锁定记录ID:' . $material['lock_id'],
];
```

### 3. 表名修改
```php
// 修改前
Db::name('warehouse_outbound')->insertGetId($outboundData);
Db::name('warehouse_outbound_detail')->insert($detailData);

// 修改后
Db::name('outbound')->insertGetId($outboundData);
Db::name('outbound_detail')->insert($detailData);
```

## 出库类型定义

### 新的出库类型 (outbound_type)
- `sales` - 销售出库
- `production` - 生产出库
- `transfer` - 调拨出库
- `return` - 退货出库
- `other` - 其他出库

### 关联业务类型 (ref_type)
- `customer_order` - 客户订单
- `production_order` - 生产订单
- `production_material_request` - 生产领料单
- `transfer_order` - 调拨订单
- `return_order` - 退货订单

## 状态定义

### 出库单状态 (status)
- `0` - 草稿
- `1` - 已提交
- `2` - 已审核
- `3` - 部分出库
- `4` - 全部出库
- `5` - 已取消

### 优先级 (priority)
- `1` - 普通
- `2` - 紧急
- `3` - 特急

### 质量状态 (quality_status)
- `1` - 合格
- `2` - 不合格
- `3` - 待检

## 业务逻辑调整

### 1. 生产领料出库
```php
// 出库类型设为 'production'
// 关联类型设为 'production_material_request'
// 状态直接设为已审核 (2)
// 锁定记录ID记录在明细的notes字段中
```

### 2. 数据完整性
```php
// 必填字段确保有值
'outbound_date' => date('Y-m-d'),
'total_quantity' => 计算总数量,
'total_amount' => 0.00, // 生产领料通常不涉及金额
'priority' => 1, // 默认普通优先级
```

### 3. 关联关系
```php
// 主表关联
'ref_type' => 'production_material_request',
'ref_id' => $requestId,
'ref_no' => $requestNo,

// 明细关联
'ref_detail_id' => 0, // 可以关联到领料单明细ID
'notes' => '锁定记录ID:' . $material['lock_id'],
```

## 注意事项

### 1. 数据迁移
如果有旧数据需要迁移，需要：
- 转换出库类型为字符串格式
- 调整字段映射关系
- 补充新增的必填字段

### 2. 其他模块适配
需要检查其他使用出库表的模块：
- 销售出库
- 调拨出库
- 库存查询
- 报表统计

### 3. 前端页面调整
可能需要调整：
- 出库单列表页面
- 出库单详情页面
- 相关查询和统计页面

通过这些适配修改，生产领料功能现在可以正确使用新的出库表结构了。
