<?php
// +----------------------------------------------------------------------
// | 工艺报工控制器
// +----------------------------------------------------------------------
namespace app\engineering\controller;

use app\BaseController;
use think\facade\View;
use think\facade\Db;

class Report extends BaseController
{
    /**
     * 报工列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            if (!empty($param['keywords'])) {
                $where[] = ['code|worker_name', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['process_id'])) {
                $where[] = ['process_id', '=', $param['process_id']];
            }
            if (!empty($param['product_id'])) {
                $where[] = ['product_id', '=', $param['product_id']];
            }
            if (!empty($param['status'])) {
                $where[] = ['status', '=', $param['status']];
            }
            if (!empty($param['date_range'])) {
                $dateRange = explode(' - ', $param['date_range']);
                $where[] = ['report_date', '>=', $dateRange[0]];
                $where[] = ['report_date', '<=', $dateRange[1]];
            }
            
            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $list = Db::name('engineering_report')
                ->where($where)
                ->order('id desc')
                ->paginate($rows, false, ['query' => $param]);
            
            // 获取关联数据
            foreach ($list as &$item) {
                // 获取工艺信息
                if (!empty($item['process_id'])) {
                    $process = Db::name('engineering_process')->where('id', $item['process_id'])->find();
                    $item['process_name'] = $process ? $process['name'] : '';
                }
                
                // 获取产品信息
                if (!empty($item['product_id'])) {
                    $product = Db::name('product')->where('id', $item['product_id'])->find();
                    $item['product_name'] = $product ? $product['name'] : '';
                }
            }
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }

    /**
     * 添加报工
     */
    public function add()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            // 生成报工单号
            if (empty($param['code'])) {
                $param['code'] = 'RG' . date('YmdHis') . rand(1000, 9999);
            }
            
            // 设置报工日期
            if (empty($param['report_date'])) {
                $param['report_date'] = date('Y-m-d');
            }
            
            // 设置工序信息
            if (!empty($param['process_id'])) {
                $process = Db::name('engineering_process')->where('id', $param['process_id'])->find();
                if ($process) {
                    // 获取工序信息
                    $steps = json_decode($process['steps'], true);
                    if (!empty($steps) && is_array($steps)) {
                        foreach ($steps as $step) {
                            if ($step['order'] == $param['step_id']) {
                                $param['step_name'] = $step['name'];
                                break;
                            }
                        }
                    }
                    
                    // 设置产品ID
                    $param['product_id'] = $process['product_id'];
                }
            }
            
            // 设置操作人信息
            $param['worker_id'] = get_login_admin('id');
            $param['worker_name'] = get_login_admin('name');
            $param['create_time'] = time();
            
            $result = Db::name('engineering_report')->strict(false)->insertGetId($param);
            if ($result) {
                return to_assign(0, '添加成功');
            } else {
                return to_assign(1, '添加失败');
            }
        } else {
            // 获取工艺列表
            $processes = Db::name('engineering_process')->where('status', 1)->select();
            View::assign('processes', $processes);
            return view();
        }
    }

    /**
     * 编辑报工
     */
    public function edit()
    {
        $param = get_params();
        
        if (request()->isAjax()) {
            // 检查状态，已审核的不允许修改
            $check = Db::name('engineering_report')->where('id', $param['id'])->find();
            if ($check['status'] == 1) {
                return to_assign(1, '已审核的报工单不允许修改');
            }
            
            // 设置工序信息
            if (!empty($param['process_id']) && !empty($param['step_id'])) {
                $process = Db::name('engineering_process')->where('id', $param['process_id'])->find();
                if ($process) {
                    // 获取工序信息
                    $steps = json_decode($process['steps'], true);
                    if (!empty($steps) && is_array($steps)) {
                        foreach ($steps as $step) {
                            if ($step['order'] == $param['step_id']) {
                                $param['step_name'] = $step['name'];
                                break;
                            }
                        }
                    }
                    
                    // 设置产品ID
                    $param['product_id'] = $process['product_id'];
                }
            }
            
            $param['update_time'] = time();
            
            $result = Db::name('engineering_report')
                ->strict(false)
                ->where('id', $param['id'])
                ->update($param);
                
            if ($result) {
                return to_assign(0, '修改成功');
            } else {
                return to_assign(1, '修改失败');
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = Db::name('engineering_report')->where('id', $id)->find();
            
            // 获取工艺列表
            $processes = Db::name('engineering_process')->where('status', 1)->select();
            
            // 获取当前工艺的工序
            $steps = [];
            if (!empty($detail['process_id'])) {
                $process = Db::name('engineering_process')->where('id', $detail['process_id'])->find();
                if ($process && !empty($process['steps'])) {
                    $steps = json_decode($process['steps'], true);
                }
            }
            
            View::assign('detail', $detail);
            View::assign('processes', $processes);
            View::assign('steps', $steps);
            
            return view();
        }
    }

    /**
     * 查看报工详情
     */
    public function detail()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = Db::name('engineering_report')->where('id', $id)->find();
        
        // 获取工艺信息
        if (!empty($detail['process_id'])) {
            $process = Db::name('engineering_process')->where('id', $detail['process_id'])->find();
            View::assign('process', $process);
            
            // 获取工序信息
            if ($process && !empty($process['steps'])) {
                $steps = json_decode($process['steps'], true);
                if (!empty($steps) && is_array($steps)) {
                    foreach ($steps as $step) {
                        if ($step['order'] == $detail['step_id']) {
                            View::assign('step', $step);
                            break;
                        }
                    }
                }
            }
        }
        
        // 获取产品信息
        if (!empty($detail['product_id'])) {
            $product = Db::name('product')->where('id', $detail['product_id'])->find();
            View::assign('product', $product);
        }
        
        View::assign('detail', $detail);
        return view();
    }

    /**
     * 删除报工
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        
        // 检查状态，已审核的不允许删除
        $check = Db::name('engineering_report')->where('id', $id)->find();
        if ($check['status'] == 1) {
            return to_assign(1, '已审核的报工单不允许删除');
        }
        
        $result = Db::name('engineering_report')->where('id', $id)->delete();
        if ($result) {
            return to_assign(0, '删除成功');
        } else {
            return to_assign(1, '删除失败');
        }
    }

    /**
     * 审核报工
     */
    public function audit()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        
        $result = Db::name('engineering_report')
            ->where('id', $id)
            ->update(['status' => 1, 'update_time' => time()]);
            
        if ($result) {
            return to_assign(0, '审核成功');
        } else {
            return to_assign(1, '审核失败');
        }
    }
    
    /**
     * 获取工艺的工序列表
     */
    public function getSteps()
    {
        $param = get_params();
        $process_id = isset($param['process_id']) ? $param['process_id'] : 0;
        
        $process = Db::name('engineering_process')->where('id', $process_id)->find();
        $steps = [];
        
        if ($process && !empty($process['steps'])) {
            $steps = json_decode($process['steps'], true);
        }
        
        return to_assign(0, '', $steps);
    }
} 