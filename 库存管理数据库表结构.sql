-- 库存管理系统数据库表结构
-- 创建时间: 2025-08-02

-- 1. 实时库存表
CREATE TABLE `oa_inventory_realtime` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总库存数量',
  `available_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '可用数量',
  `locked_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '锁定数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_inventory` (`product_id`,`warehouse_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时库存表';

-- 2. 库存锁定表
CREATE TABLE `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '锁定数量',
  `ref_type` varchar(50) NOT NULL COMMENT '关联类型(order,production,transfer等)',
  `ref_id` int(11) NOT NULL COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1锁定中,2已使用,3已释放',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_ref` (`ref_type`,`ref_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表';

-- 3. 库存流水表
CREATE TABLE `oa_inventory_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
  `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
  `ref_id` int(11) DEFAULT 0 COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transaction_no` (`transaction_no`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_type` (`transaction_type`),
  KEY `idx_ref` (`ref_type`,`ref_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';

-- 4. 仓库间调拨单表
CREATE TABLE `oa_inventory_transfer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_no` varchar(50) NOT NULL COMMENT '调拨单号',
  `from_warehouse_id` int(11) NOT NULL COMMENT '源仓库ID',
  `to_warehouse_id` int(11) NOT NULL COMMENT '目标仓库ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待审核,1已审核,2已完成,3已取消',
  `total_amount` decimal(10,2) DEFAULT 0 COMMENT '调拨总金额',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `approved_by` int(11) DEFAULT 0 COMMENT '审核人',
  `approved_time` int(11) DEFAULT 0 COMMENT '审核时间',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transfer_no` (`transfer_no`),
  KEY `idx_from_warehouse` (`from_warehouse_id`),
  KEY `idx_to_warehouse` (`to_warehouse_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库间调拨单表';

-- 5. 调拨单明细表
CREATE TABLE `oa_inventory_transfer_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL COMMENT '调拨单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '调拨数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `amount` decimal(10,2) DEFAULT 0 COMMENT '金额',
  `notes` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_transfer` (`transfer_id`),
  KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调拨单明细表';

-- 6. 库存盘点单表
CREATE TABLE `oa_inventory_check` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_no` varchar(50) NOT NULL COMMENT '盘点单号',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `check_date` int(11) NOT NULL COMMENT '盘点日期',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待盘点,1盘点中,2已完成,3已取消',
  `total_products` int(11) DEFAULT 0 COMMENT '盘点产品总数',
  `checked_products` int(11) DEFAULT 0 COMMENT '已盘点产品数',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `approved_by` int(11) DEFAULT 0 COMMENT '审核人',
  `approved_time` int(11) DEFAULT 0 COMMENT '审核时间',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_check_no` (`check_no`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点单表';

-- 7. 盘点单明细表
CREATE TABLE `oa_inventory_check_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_id` int(11) NOT NULL COMMENT '盘点单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `book_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '账面数量',
  `actual_quantity` decimal(10,2) DEFAULT NULL COMMENT '实盘数量',
  `diff_quantity` decimal(10,2) DEFAULT 0 COMMENT '差异数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待盘点,1已盘点',
  `notes` text COMMENT '备注',
  `checked_by` int(11) DEFAULT 0 COMMENT '盘点人',
  `checked_time` int(11) DEFAULT 0 COMMENT '盘点时间',
  PRIMARY KEY (`id`),
  KEY `idx_check` (`check_id`),
  KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点单明细表';
