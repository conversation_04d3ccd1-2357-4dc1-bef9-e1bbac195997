{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.material-select-container {
    padding: 15px;
}
.search-form {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f8f8;
    border-radius: 5px;
}
</style>
{/block}

<!-- 主体 -->
{block name="body"}
<div class="material-select-container">
    <!-- 搜索表单 -->
    <form class="layui-form search-form" lay-filter="searchForm">
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">物料分类</label>
                    <div class="layui-input-block">
                        <select name="material_category">
                            <option value="">请选择分类</option>
                            <option value="物料测试01">物料测试01</option>
                            <option value="物料">物料</option>
                            <option value="毛坯">毛坯</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">供应商名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="supplier_name" placeholder="请输入供应商名称" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">物料编码/名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="material_name" placeholder="请输入物料编码或名称" class="layui-input" />
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-form-item">
                    <label class="layui-form-label">型号</label>
                    <div class="layui-input-block">
                        <input type="text" name="model" placeholder="请输入型号" class="layui-input" />
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-normal" lay-submit lay-filter="search">搜索</button>
                <button type="reset" class="layui-btn layui-btn-primary">列表字段</button>
            </div>
        </div>
    </form>
    
    <!-- 物料列表 -->
    <table class="layui-hide" id="materialTable" lay-filter="materialTable"></table>
</div>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool','tablePlus','form'];
function gouguInit() {
    var table = layui.tablePlus, form = layui.form, tool = layui.tool;
    // 渲染表格
    var materialTable = table.render({
        elem: '#materialTable'
        ,url: '/material/bom/getMaterials'
        ,page: true
        ,limit: 10
        ,height: 'full-200'
        ,cols: [[
            {type: 'checkbox', fixed: 'left'}
            ,{field: 'id', title: 'ID', width: 80, hide: true}
            ,{field: 'image', title: '物料图片', width: 80, align: 'center', templet: function(d) {
                return '<img src="/static/images/default.png" width="40" height="40">';
            }}
            ,{field: 'name', title: '物料名称', width: 200}
            ,{field: 'material_code', title: '物料编号', width: 150}
            ,{field: 'specifications', title: '规格/型号', width: 460}
            ,{field: 'category_name', title: '类别', width: 100}
            ,{field: 'unit', title: '基本单位', width: 80}
            ,{field: 'type', title: '是否已', width: 80, templet: function(d) {
                return d.type == 0 ? '成品' : '半成品';
            }}
        ]]
    });
    
    // 搜索
    form.on('submit(search)', function(data) {
        materialTable.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
    
    // 获取选中数据的方法，供父页面调用
    window.getSelectedData = function() {
        var checkStatus = table.checkStatus('materialTable');
        return checkStatus.data;
    };
}
</script>
{/block}
<!-- /脚本 -->