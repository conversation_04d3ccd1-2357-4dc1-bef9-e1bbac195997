{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-page">
	<h3 class="pb-3">新增联系人</h3>
    <table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray-2">供应商名称</td>
			<td colspan="5">{$supplier_name}</td>
		</tr>
		<tr>
			<td class="layui-td-gray">联系人姓名<font>*</font></td>
			<td><input type="text" name="name" autocomplete="off" lay-verify="required" lay-reqText="请输入联系人姓名" placeholder="请输入联系人姓名" class="layui-input"></td>
			<td class="layui-td-gray">联系电话<font>*</font></td>
			<td><input type="text" name="mobile" autocomplete="off" lay-verify="required" lay-reqText="请输入联系人电话" placeholder="请输入联系人电话" class="layui-input"></td>
			<td class="layui-td-gray">性别<font>*</font></td>
			<td>
				<input type="radio" name="sex" value="1" title="男">
				<input type="radio" name="sex" value="2" title="女">
			</td>
		</tr>
		<tr>
			<td class="layui-td-gray">微 信 号</td>
			<td><input type="text" name="wechat" autocomplete="off" placeholder="请输入联系人微信号" class="layui-input"></td>
			<td class="layui-td-gray">QQ号码</td>
			<td><input type="text" name="qq" autocomplete="off" placeholder="请输入联系人QQ号码" class="layui-input"></td>
			<td class="layui-td-gray">电子邮箱</td>
			<td><input type="text" name="email" autocomplete="off" placeholder="请输入联系人电子邮箱" class="layui-input"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">称谓</td>
			<td><input type="text" name="nickname" autocomplete="off" placeholder="请输入联系人的称谓" class="layui-input"></td>
			<td class="layui-td-gray">部门</td>
			<td><input type="text" name="department" autocomplete="off" placeholder="请输入联系人所在部门" class="layui-input"></td>
			<td class="layui-td-gray">职务</td>
			<td><input type="text" name="position" autocomplete="off" placeholder="请输入联系人担任的职务" class="layui-input"></td>
		</tr>
    </table>
    <div class="pt-3">
		<input type="hidden" name="sid" value="{$supplier_id}">
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
    </div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool;
		//监听提交
		form.on('submit(webform)', function (data) {
			if(!data.field.sex){
				layer.msg('请选择性别');
				return false;
			}
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			let clickbtn = $(this);
			tool.post("/contract/supplier/contact_add", data.field, callback,clickbtn);
			return false;
		});

	}
</script>
{/block}
<!-- /脚本 -->