{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-breadcrumb" lay-separator=">">
            <a href="/Produce/process/index">工序管理</a>
            <a><cite>{if condition="$id > 0"}编辑工序{else /}添加工序{/if}</cite></a>
        </span>
    </div>
    <div class="layui-card-body">
        <form class="layui-form" action="" lay-filter="processForm">
            {if condition="$id > 0"}
            <input type="hidden" name="id" value="{$detail.id|default=0}" />
            {/if}
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工序编号 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="text" name="code" value="{$detail.code|default=''}" placeholder="请输入工序编号" 
                                   class="layui-input" lay-verify="required" {if condition="$id > 0"}readonly{/if}>
                            <div class="layui-form-mid layui-word-aux">
                                <input type="checkbox" name="use_system_code" value="1" title="使用系统编号" lay-filter="use_system_code" {if condition="empty($detail.code)"}checked{/if}>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工序名称 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="{$detail.name|default=''}" placeholder="请输入工序名称" 
                                   class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">工作组 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <select name="group_id" lay-verify="required">
                                <option value="">请选择工作组</option>
                                {volist name="groups" id="group"}
                                <option value="{$group.id}" {if condition="isset($detail.group_id) && $detail.group_id == $group.id"}selected{/if}>
                                    {$group.name}
                                </option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">计价方式 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="radio" name="pricing_method" value="1" title="按件计价" 
                                   {if condition="!isset($detail.pricing_method) || $detail.pricing_method == 1"}checked{/if}>
                            <input type="radio" name="pricing_method" value="2" title="按时计价" 
                                   {if condition="isset($detail.pricing_method) && $detail.pricing_method == 2"}checked{/if}>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">报工用户</label>
                        <div class="layui-input-block">
                            <input type="text" name="report_user_names" placeholder="请选择报工用户" readonly class="layui-input picker-admin" data-type="2" value="{$detail.report_user_names|default=''}">
                            <input type="hidden" name="report_user" class="layui-input" value="{$detail.report_user|default=''}">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">数量单比 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="number" name="quantity_ratio" value="{$detail.quantity_ratio|default=1}" 
                                   placeholder="请输入数量单比" class="layui-input" lay-verify="required" step="0.01" min="0.01">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">标准单价 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <input type="number" name="standard_price" value="{$detail.standard_price|default=0}" 
                                   placeholder="请输入标准单价" class="layui-input" lay-verify="required" step="0.01" min="0">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">标准效率 <span class="text-danger">*</span></label>
                        <div class="layui-input-block">
                            <div class="layui-input-wrap">
                                <input type="number" name="efficiency_value" value="{$detail.efficiency|default=0}" 
                                       placeholder="单位时间产出数量" class="layui-input" lay-verify="required" step="0.01" min="0.01">
                                <div class="layui-input-suffix">
                                    <span>/</span>
                                    <select name="efficiency_unit" style="width: 60px; border: none;">
                                        <option value="hour" {if condition="!isset($detail.efficiency_unit) || $detail.efficiency_unit == 'hour'"}selected{/if}>小时</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-mid layui-word-aux">标准效率：0 / 1小时</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">工序序号</label>
                <div class="layui-input-block">
                    <input type="text" name="serial_number" value="{$detail.serial_number|default=''}" 
                           placeholder="请输入工序序号" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">工序描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入工序描述" class="layui-textarea" 
                              maxlength="300">{$detail.description|default=''}</textarea>
                    <div class="layui-form-mid layui-word-aux">
                        <span id="descLength">0</span> / 300
                    </div>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="processSubmit">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','oaPicker'];
    function gouguInit() {
        var tool = layui.tool, oaPicker = layui.oaPicker;
        
        // 初始化工序编号输入框状态
        function initCodeInput() {
            var isChecked = $('[name="use_system_code"]').is(':checked');
            var isEdit = $('[name="id"]').val() > 0;
            
            if (isEdit) {
                // 编辑模式下，工序编号不可修改
                $('[name="code"]').prop('readonly', true);
            } else {
                // 新增模式下，根据复选框状态设置
                if (isChecked) {
                    $('[name="code"]').val('').prop('readonly', true).attr('placeholder', '系统自动生成').removeAttr('lay-verify');
                } else {
                    $('[name="code"]').prop('readonly', false).attr('placeholder', '请输入工序编号').attr('lay-verify', 'required');
                }
            }
        }
        
        // 初始化状态
        initCodeInput();
        
        // 监听使用系统编号复选框
        layui.form.on('checkbox(use_system_code)', function(data){
            var isEdit = $('[name="id"]').val() > 0;
            
            if (!isEdit) {
                if(data.elem.checked){
                    $('[name="code"]').val('').prop('readonly', true).attr('placeholder', '系统自动生成').removeAttr('lay-verify');
                } else {
                    $('[name="code"]').prop('readonly', false).attr('placeholder', '请输入工序编号').attr('lay-verify', 'required');
                }
            }
        });
        
        // 监听效率值变化，更新显示
        $('[name="efficiency_value"]').on('input', function(){
            var value = $(this).val() || 0;
            var unit = $('[name="efficiency_unit"]').val();
            var unitText = unit == 'hour' ? '小时' : '分钟';
            $('.layui-word-aux').text('标准效率：' + value + ' / 1' + unitText);
        });
        
        // 监听描述字数统计
        $('[name="description"]').on('input', function(){
            var length = $(this).val().length;
            $('#descLength').text(length);
        });
        
        // 初始化字数统计
        var initLength = $('[name="description"]').val().length;
        $('#descLength').text(initLength);
        
        // 表单提交
        layui.form.on('submit(processSubmit)', function(data){
            // 处理效率字段
            var efficiencyValue = data.field.efficiency_value || 0;
            data.field.efficiency = efficiencyValue;
            
            // 如果选择使用系统编号，清空编号字段
            if($('[name="use_system_code"]').is(':checked')){
                data.field.code = '';
            }
            
            let callback = function (e) {
                layer.msg(e.msg);
                if (e.code == 0) {
                    parent.layui.pageTable.reload();
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            }
            tool.post("/Produce/process/add", data.field, callback);
            return false;
        });
    }
</script>
{/block}