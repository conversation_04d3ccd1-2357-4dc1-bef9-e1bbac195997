{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar border-x" id="searchForm">
        <div class="layui-input-inline" style="width:180px;">
            <input type="text" name="order_no" placeholder="订单编号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:180px;">
            <select name="customer_id">
                <option value="">选择客户</option>
                {volist name=":get_customer_list()" id="vo"}
                <option value="{$vo.id}">{$vo.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:120px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search">
                <i class="layui-icon layui-icon-search mr-1"></i>搜索
            </button>
            <button type="reset" class="layui-btn layui-btn-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="orderTable" lay-filter="orderTable"></table>
</div>

<script type="text/html" id="actionBar">
  <div class="layui-btn-group">
    {{# if(d.has_shippable){ }}
    <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="select">选择</button>
    {{# } else { }}
    <button class="layui-btn layui-btn-disabled layui-btn-xs">无可发货商品</button>
    {{# } }}
  </div>
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus', 'form'];
    
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool;
        
        // 初始化表格
        table.render({
            elem: "#orderTable"
            ,title: "订单列表"
            ,url: "/customer/delivery/getOrderList"
            ,page: true
            ,limit: 10
            ,cellMinWidth: 80
            ,height: 'full-90'
            ,cols: [[ //表头
                {field: 'id', title: 'ID', width: 60, align: 'center'},
                {field: 'order_no', title: '订单编号', width: 160, align: 'center'},
                {field: 'customer.name', title: '客户名称', width: 200, templet: function(d) {
                    return d.customer ? d.customer.name : '';
                }},
                {field: 'status', title: '状态', width: 100, align: 'center', templet: function(d) {
                    var statusMap = {
                        0: '待审核',
                        1: '已审核',
                        2: '已发货',
                        3: '待补货',
                        4: '已完成',
                        5: '已取消'
                    };
                    var classMap = {
                        0: 'layui-bg-orange',
                        1: 'layui-bg-blue',
                        2: 'layui-bg-green',
                        3: 'layui-bg-red',
                        4: 'layui-bg-green',
                        5: 'layui-bg-gray'
                    };
                    return '<span class="' + classMap[d.status] + '">' + statusMap[d.status] + '</span>';
                }},
                {field: 'total_amount', title: '订单金额', width: 120, align: 'right'},
                {field: 'create_time', title: '创建时间', width: 160, align: 'center'},
                {field: 'right', title: '操作', width: 150, align: 'center', toolbar: '#actionBar', fixed: 'right'}
            ]]
        });
        
        // 行工具条
        table.on('tool(orderTable)', function(obj){
            var data = obj.data;
            
            if(obj.event === 'select'){
                // 跳转到发货指令创建页面
                var index = parent.layer.getFrameIndex(window.name);
                parent.window.location.href = '/customer/delivery/add?order_id=' + data.id;
                parent.layer.close(index);
            }
        });
        
        // 表单搜索
        form.on('submit(table-search)', function(data){
            table.reload('orderTable', {
                where: data.field,
                page: {curr: 1}
            });
            return false;
        });
    }
</script>
{/block} 