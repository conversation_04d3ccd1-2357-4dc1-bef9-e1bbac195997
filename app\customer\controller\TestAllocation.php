<?php
namespace app\customer\controller;

use think\Controller;
use think\Db;

class TestAllocation extends Controller
{
    /**
     * 测试查询分配需求数据
     */
    public function index()
    {
        // 查询所有分配需求
        $requests = Db::name('inventory_allocation_request')
            ->where('status', 'in', [1, 2])
            ->order('id desc')
            ->limit(10)
            ->select();
        
        // 按产品ID统计等待分配数量
        $productStats = Db::name('inventory_allocation_request')
            ->where('status', 'in', [1, 2])
            ->field('product_id, SUM(quantity - allocated_quantity) as pending_qty')
            ->group('product_id')
            ->select();
        
        // 查询产品信息
        $products = [];
        foreach ($productStats as $stat) {
            $product = Db::name('product')->where('id', $stat['product_id'])->find();
            if ($product) {
                $products[] = [
                    'product_id' => $stat['product_id'],
                    'product_name' => $product['title'],
                    'pending_qty' => $stat['pending_qty']
                ];
            }
        }
        
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'total_requests' => count($requests),
                'requests' => $requests,
                'product_stats' => $products
            ]
        ]);
    }
    
    /**
     * 查询特定产品的分配需求
     */
    public function product($productId = 2157)
    {
        // 查询该产品的所有分配需求
        $requests = Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->select();
        
        // 查询等待分配的数量
        $pending = Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->where('status', 'in', [1, 2])
            ->field('SUM(quantity - allocated_quantity) as pending_qty')
            ->find();
        
        // 查询库存信息
        $inventory = Db::name('inventory_realtime')
            ->where('product_id', $productId)
            ->field('
                SUM(quantity) as total_stock,
                SUM(locked_quantity) as locked_stock,
                SUM(available_quantity) as available_stock
            ')
            ->find();
        
        return json([
            'code' => 0,
            'msg' => 'success',
            'data' => [
                'product_id' => $productId,
                'inventory' => $inventory,
                'pending_allocation' => $pending['pending_qty'] ?? 0,
                'all_requests' => $requests,
                'pending_requests' => array_filter($requests, function($r) {
                    return in_array($r['status'], [1, 2]);
                })
            ]
        ]);
    }
}
