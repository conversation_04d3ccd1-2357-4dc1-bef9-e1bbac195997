<?php
namespace app\customer\model;

use think\Model;

class DeliveryDetail extends Model
{
    //无用
    // 设置表名
    protected $name = 'customer_delivery_detail';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 关联发货单
     */
    public function delivery()
    {
        return $this->belongsTo('Delivery', 'delivery_id', 'id');
    }
    
    /**
     * 关联订单明细
     */
    public function orderDetail()
    {
        return $this->belongsTo('OrderDetail', 'order_detail_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\\engineering\\model\\Product', 'product_id', 'id');
    }
} 