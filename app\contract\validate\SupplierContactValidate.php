<?php


namespace app\contract\validate;

use think\Validate;

class SupplierContactValidate extends Validate
{
    protected $rule = [
        'name' => 'require',
        'mobile' => 'require',
        'id' => 'require',
    ];

    protected $message = [
        'name.require' => '联系人姓名不能为空',
        'mobile' => '手机号码不能为空',
        'id.require' => '缺少更新条件',
    ];

    protected $scene = [
        'add' => ['name','mobile'],
        'edit' => ['id', 'name','mobile'],
    ];
}
