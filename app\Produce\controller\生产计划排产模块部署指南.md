# 生产计划排产模块部署指南

## 🚀 部署步骤

### 1. 数据库初始化

#### 执行SQL脚本
```bash
# 在数据库中执行以下SQL文件
mysql -u username -p database_name < database/migrations/production_plan_tables.sql
```

#### 或者手动执行SQL语句
```sql
-- 1. 创建生产计划表
CREATE TABLE `oa_production_plan` (
  -- 表结构见 database/migrations/production_plan_tables.sql
);

-- 2. 创建日生产统计表
CREATE TABLE `oa_daily_production_stats` (
  -- 表结构见 database/migrations/production_plan_tables.sql
);

-- 3. 创建排产日志表
CREATE TABLE `oa_production_plan_log` (
  -- 表结构见 database/migrations/production_plan_tables.sql
);

-- 4. 为生产订单表添加排产字段
ALTER TABLE `oa_produce_order` 
ADD COLUMN `plan_id` int(11) DEFAULT 0 COMMENT '排产计划ID',
ADD COLUMN `plan_status` tinyint(1) DEFAULT 0 COMMENT '排产状态',
ADD COLUMN `scheduled_date` date DEFAULT NULL COMMENT '排产日期',
ADD COLUMN `estimated_days` int(11) DEFAULT 1 COMMENT '预计生产天数';
```

### 2. 文件部署

#### 后端文件
```
app/Produce/controller/ProductionPlan.php          # 排产控制器
app/Produce/controller/ProductionPlanTest.php      # 测试控制器
app/Produce/controller/Order.php                   # 生产订单控制器（已更新）
```

#### 前端文件
```
app/Produce/view/production_plan/index.html        # 排产主页面
```

#### 文档文件
```
app/Produce/controller/生产计划排产模块实现文档.md
app/Produce/controller/生产计划排产模块部署指南.md
database/migrations/production_plan_tables.sql
```

### 3. 权限配置

#### 菜单配置
在系统菜单管理中添加以下菜单项：

```
生产管理
├── 生产订单
├── 生产计划排产  ← 新增
│   ├── 排产管理     /Produce/ProductionPlan/index
│   └── 排产统计     /Produce/ProductionPlan/stats
└── 生产报工
```

#### 权限节点
```
Produce/ProductionPlan/index              # 排产主页面
Produce/ProductionPlan/getUnscheduledOrders  # 获取待排产订单
Produce/ProductionPlan/getScheduledPlans     # 获取已排产计划
Produce/ProductionPlan/savePlan              # 保存排产计划
Produce/ProductionPlan/autoSchedule          # 自动排产
Produce/ProductionPlan/updateProgress        # 更新进度
Produce/ProductionPlan/getDailyStats         # 获取统计
Produce/ProductionPlan/deletePlan            # 删除计划
Produce/ProductionPlan/batchSchedule         # 批量排产
```

### 4. 测试验证

#### 数据库表结构测试
```bash
# 访问测试接口检查表结构
GET /Produce/ProductionPlanTest/testTables
```

#### 创建测试数据
```bash
# 创建测试排产数据
POST /Produce/ProductionPlanTest/createTestData
```

#### API接口测试
```bash
# 测试所有API接口
GET /Produce/ProductionPlanTest/testApi
```

#### 清理测试数据
```bash
# 清理测试数据
POST /Produce/ProductionPlanTest/cleanTestData
```

### 5. 功能验证

#### 基础功能测试
1. **访问排产页面**
   ```
   http://your-domain/Produce/ProductionPlan/index
   ```

2. **检查页面加载**
   - 左侧待排产订单列表是否正常显示
   - 右侧甘特图是否正常渲染
   - 工具栏功能按钮是否正常

3. **测试排产功能**
   - 拖拽订单到甘特图进行手动排产
   - 点击"自动排产"按钮测试自动排产
   - 选择多个订单测试批量排产

4. **测试数据同步**
   - 排产后检查生产订单状态是否同步更新
   - 更新进度后检查甘特图是否实时刷新

#### 集成功能测试
1. **与生产订单集成**
   - 在生产订单页面点击"生成计划"按钮
   - 检查订单状态变更是否正确同步

2. **与生产领料集成**
   - 在排产页面点击"领料"按钮
   - 检查是否正确跳转到领料页面

## ⚙️ 配置说明

### 1. 系统参数配置

#### 排产参数
```php
// 在配置文件中添加排产相关参数
'production_plan' => [
    'max_daily_orders' => 5,        // 每日最大订单数
    'default_production_days' => 1,  // 默认生产天数
    'auto_schedule_enabled' => true, // 是否启用自动排产
    'weekend_schedule' => false,     // 是否允许周末排产
    'max_search_days' => 60,        // 最大搜索天数
],
```

#### 优先级权重配置
```php
'priority_weights' => [
    'urgency' => 0.4,      // 紧急度权重 40%
    'priority' => 0.3,     // 优先级权重 30%
    'quantity' => 0.3,     // 数量权重 30%
],
```

### 2. 前端配置

#### 甘特图配置
```javascript
// 在前端页面中配置甘特图参数
const ganttConfig = {
    defaultView: 'day',           // 默认视图：day/week/month
    cellWidth: 80,                // 单元格宽度
    rowHeight: 40,                // 行高
    showWeekends: false,          // 是否显示周末
    enableDrag: true,             // 是否启用拖拽
    autoRefresh: 30000,           // 自动刷新间隔（毫秒）
};
```

## 🔧 故障排除

### 1. 常见问题

#### 数据库相关
**问题**：表不存在错误
**解决**：检查数据库表是否正确创建，表前缀是否正确

**问题**：字段不存在错误
**解决**：检查生产订单表是否添加了排产相关字段

#### 权限相关
**问题**：页面访问403错误
**解决**：检查用户是否有相应的菜单和节点权限

**问题**：API接口调用失败
**解决**：检查接口权限配置是否正确

#### 功能相关
**问题**：甘特图不显示
**解决**：检查前端JavaScript是否正确加载，数据接口是否正常

**问题**：拖拽排产无效
**解决**：检查拖拽事件是否正确绑定，排产接口是否正常

### 2. 调试方法

#### 开启调试模式
```php
// 在配置文件中开启调试
'app_debug' => true,
'log' => [
    'level' => ['error', 'warning', 'info', 'debug'],
],
```

#### 查看日志
```bash
# 查看应用日志
tail -f runtime/log/202X/XX/XX.log

# 查看错误日志
tail -f runtime/log/error.log
```

#### 数据库调试
```php
// 在控制器中添加SQL调试
Db::listen(function ($sql, $time, $explain) {
    Log::info('SQL调试', [
        'sql' => $sql,
        'time' => $time,
        'explain' => $explain
    ]);
});
```

## 📈 性能优化

### 1. 数据库优化

#### 索引优化
```sql
-- 为常用查询字段添加索引
ALTER TABLE `oa_production_plan` ADD INDEX `idx_date_status` (`plan_start_date`, `status`);
ALTER TABLE `oa_produce_order` ADD INDEX `idx_plan_status` (`plan_status`, `status`);
```

#### 查询优化
```php
// 使用合适的查询条件和限制
$orders = Db::name('produce_order')
    ->where('plan_status', 0)
    ->where('status', 'in', [0, 1])
    ->limit(100)  // 限制查询数量
    ->select();
```

### 2. 前端优化

#### 数据分页
```javascript
// 对大量数据进行分页处理
const pageSize = 20;
const currentPage = 1;
```

#### 缓存机制
```javascript
// 缓存甘特图数据，避免频繁请求
let ganttDataCache = null;
let cacheTime = 0;
const cacheExpire = 5 * 60 * 1000; // 5分钟缓存
```

## 🔄 维护说明

### 1. 定期维护

#### 数据清理
```sql
-- 清理过期的排产日志（保留3个月）
DELETE FROM oa_production_plan_log 
WHERE create_time < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 MONTH));

-- 清理过期的统计数据（保留1年）
DELETE FROM oa_daily_production_stats 
WHERE date < DATE_SUB(NOW(), INTERVAL 1 YEAR);
```

#### 性能监控
```php
// 监控排产算法执行时间
$startTime = microtime(true);
$this->autoSchedule();
$endTime = microtime(true);
$executionTime = $endTime - $startTime;

Log::info('排产算法执行时间', ['time' => $executionTime]);
```

### 2. 备份策略

#### 数据备份
```bash
# 备份排产相关表
mysqldump -u username -p database_name \
  oa_production_plan \
  oa_daily_production_stats \
  oa_production_plan_log \
  > production_plan_backup.sql
```

#### 配置备份
```bash
# 备份相关配置文件
cp app/Produce/controller/ProductionPlan.php backup/
cp app/Produce/view/production_plan/index.html backup/
```

## ✅ 部署检查清单

- [ ] 数据库表创建完成
- [ ] 生产订单表字段添加完成
- [ ] 控制器文件部署完成
- [ ] 前端页面文件部署完成
- [ ] 菜单权限配置完成
- [ ] 接口权限配置完成
- [ ] 基础功能测试通过
- [ ] 集成功能测试通过
- [ ] 性能测试通过
- [ ] 用户培训完成

部署完成后，生产计划排产模块即可正常使用！
