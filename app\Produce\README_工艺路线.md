# 工艺路线管理功能

## 功能概述

工艺路线管理是生产管理系统的核心功能之一，用于定义和管理产品的生产工艺流程。通过工艺路线模板，可以标准化生产流程，提高生产效率和产品质量。

## 主要功能

### 1. 工艺路线模板管理
- **新增工艺路线**：创建新的工艺路线模板
- **编辑工艺路线**：修改现有工艺路线模板
- **复制工艺路线**：基于现有模板快速创建新模板
- **删除工艺路线**：删除不再使用的工艺路线模板
- **查看详情**：查看工艺路线的详细信息和流程图

### 2. 工艺步骤管理
- **步骤定义**：定义每个工艺步骤的详细信息
- **步骤排序**：支持工艺步骤的上移、下移操作
- **步骤类型**：支持数据记录和状态记录两种类型
- **时间设置**：设置每个步骤的完成所需时间
- **加工类型**：区分自制和外协加工
- **检验方式**：设置免检、抽检、全检等检验方式

### 3. 工艺流程可视化
- **流程图展示**：以可视化方式展示工艺流程
- **步骤详情**：显示每个步骤的详细参数
- **响应式设计**：支持移动端查看

## 数据库设计

### 工艺路线模板表 (oa_process_template)
```sql
CREATE TABLE `oa_process_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_no` varchar(50) NOT NULL DEFAULT '' COMMENT '工艺模板编号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工艺名称',
  `remark` text COMMENT '备注',
  `steps` text COMMENT '工艺步骤JSON数据',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺路线模板表';
```

### 工艺步骤数据结构
```json
[
  {
    "step": 1,
    "name": "下料",
    "type": "数据记录",
    "completion_time": 0,
    "time_unit": "天",
    "processing_type": "自制",
    "inspection_method": "免检",
    "description": "原材料下料工序"
  }
]
```

## 文件结构

```
app/Produce/
├── controller/
│   └── ProcessTemplate.php          # 工艺路线控制器
├── model/
│   └── ProcessTemplateModel.php     # 工艺路线模型
├── validate/
│   └── ProcessTemplateValidate.php  # 数据验证器
├── view/processTemplate/
│   ├── index.html                   # 工艺路线列表页面
│   ├── add.html                     # 添加/编辑工艺路线页面
│   └── view.html                    # 工艺路线详情页面
└── sql/
    └── process_template.sql         # 数据库表结构
```

## 使用说明

### 1. 访问工艺路线管理
访问地址：`/Produce/processTemplate/index`

### 2. 创建工艺路线
1. 点击"新增"按钮
2. 填写工艺名称和备注
3. 添加工艺步骤：
   - 选择工序名称
   - 设置工序类型（数据记录/状态记录）
   - 设置完成时间
   - 选择加工类型（自制/外协）
   - 选择检验方式（免检/抽检/全检）
   - 填写工序描述
4. 可以通过上移/下移按钮调整步骤顺序
5. 点击"保存"完成创建

### 3. 编辑工艺路线
1. 在列表页面点击"编辑"按钮
2. 修改工艺信息和步骤
3. 保存修改

### 4. 复制工艺路线
1. 在列表页面点击"复制"按钮
2. 系统会创建一个名为"原名称_副本"的新工艺路线
3. 可以进一步编辑复制的工艺路线

## 技术特点

### 1. 模块化设计
- 控制器、模型、视图分离
- 独立的验证器处理数据验证
- 可复用的组件设计

### 2. 数据验证
- 前端JavaScript验证
- 后端PHP验证器双重验证
- 工艺步骤数据完整性检查

### 3. 用户体验
- 响应式界面设计
- 拖拽排序功能
- 实时数据验证
- 友好的错误提示

### 4. 扩展性
- 支持自定义工序类型
- 支持扩展检验方式
- 预留接口对接其他系统

## 注意事项

1. **数据完整性**：删除工艺路线前会检查是否被生产订单使用
2. **编号唯一性**：工艺模板编号自动生成，确保唯一性
3. **步骤顺序**：工艺步骤按照定义顺序执行，请合理安排
4. **权限控制**：建议配置相应的用户权限控制访问

## 后续扩展

1. **工艺路线版本管理**：支持工艺路线的版本控制
2. **工艺参数管理**：支持更详细的工艺参数设置
3. **工艺路线导入导出**：支持Excel格式的批量导入导出
4. **工艺路线统计分析**：提供工艺路线使用情况的统计报表