# 物料需求创建人显示修复

## 🔍 问题描述

物料需求页面（`http://tc.xinqiyu.cn:8830/Produce/MaterialRequest/index`）的创建人字段需要关联admin表去查询姓名，目前显示不正确。

## 🎯 问题原因分析

### 1. 列表页面查询问题
- 查询时没有关联 `admin` 表获取创建人姓名
- 只查询了 `production_material_request` 表本身的字段

### 2. 详情页面查询问题  
- 详情页面同样没有关联 `admin` 表
- 无法显示正确的创建人和审核人姓名

### 3. 创建时数据问题
- 创建领料单时 `created_name` 字段被错误设置为 `'0'`
- 应该设置为当前用户的真实姓名

## 🛠️ 解决方案

### 1. 修复列表页面查询

在 `app\Produce\controller\MaterialRequest.php` 的 `index()` 方法中添加关联查询：

```php
$list = Db::name('production_material_request')
    ->alias('r')
    ->leftJoin('admin a', 'r.created_by = a.id')  // 关联admin表
    ->where($where)
    ->field([
        'r.*',
        'IFNULL(a.name, r.created_name) as created_name',  // 优先使用admin表的name
        'CASE r.status
            WHEN 0 THEN "待审核"
            WHEN 1 THEN "已审核"
            WHEN 2 THEN "已出库"
            WHEN 3 THEN "已完成"
            WHEN 4 THEN "已取消"
            ELSE "未知"
        END as status_text',
        'CASE r.request_type
            WHEN 1 THEN "正常领料"
            WHEN 2 THEN "补料"
            WHEN 3 THEN "超领"
            ELSE "未知"
        END as request_type_text'
    ])
    ->order('r.create_time desc')
    ->paginate([...]);
```

### 2. 修复详情页面查询

在 `view()` 方法中添加创建人和审核人的关联查询：

```php
// 获取主表信息，关联创建人和审核人信息
$request = Db::name('production_material_request')
    ->alias('r')
    ->leftJoin('admin a1', 'r.created_by = a1.id')      // 关联创建人
    ->leftJoin('admin a2', 'r.approved_by = a2.id')     // 关联审核人
    ->where('r.id', $id)
    ->field([
        'r.*',
        'IFNULL(a1.name, r.created_name) as created_name',    // 创建人姓名
        'IFNULL(a2.name, r.approved_name) as approved_name'   // 审核人姓名
    ])
    ->find();
```

### 3. 修复创建时的数据设置

在 `save()` 方法中正确设置创建人姓名：

```php
// 修改前
'created_by' => $this->uid,
'created_name' => '0',  // 错误的设置

// 修改后
'created_by' => $this->uid,
'created_name' => get_admin($this->uid)['name'] ?? '',  // 获取真实姓名
```

## 📋 数据库表结构

### production_material_request 表相关字段

```sql
`created_by` int(11) NOT NULL COMMENT '创建人ID',
`created_name` varchar(50) NOT NULL COMMENT '创建人姓名',
`approved_by` int(11) DEFAULT 0 COMMENT '审核人ID',
`approved_name` varchar(50) DEFAULT '' COMMENT '审核人姓名',
```

### admin 表相关字段

```sql
`id` int(11) NOT NULL AUTO_INCREMENT,
`name` varchar(50) NOT NULL COMMENT '真实姓名',
`username` varchar(50) NOT NULL COMMENT '用户名',
```

## 🔧 技术实现细节

### 1. 关联查询策略

```php
// 使用 LEFT JOIN 确保即使admin记录不存在也能显示数据
->leftJoin('admin a', 'r.created_by = a.id')

// 使用 IFNULL 函数提供备用值
'IFNULL(a.name, r.created_name) as created_name'
```

### 2. 多重关联

```php
// 同时关联创建人和审核人
->leftJoin('admin a1', 'r.created_by = a1.id')      // 创建人
->leftJoin('admin a2', 'r.approved_by = a2.id')     // 审核人
```

### 3. 使用系统函数

```php
// 使用系统提供的 get_admin() 函数获取用户信息
'created_name' => get_admin($this->uid)['name'] ?? ''
```

## 🎨 前端显示

### 列表页面字段配置

```javascript
{field: 'created_name', title: '创建人', width: 100}
```

### 显示效果

- **修复前**：显示 `0` 或空白
- **修复后**：显示真实的用户姓名，如 `张三`、`李四`

## 🧪 测试验证

### 测试场景

#### 1. 列表页面测试
- 访问物料需求列表页面
- 验证创建人列显示正确的用户姓名

#### 2. 详情页面测试  
- 点击查看领料单详情
- 验证创建人和审核人信息正确显示

#### 3. 新建领料单测试
- 创建新的领料单
- 验证创建人姓名正确保存和显示

#### 4. 历史数据测试
- 查看历史创建的领料单
- 验证通过关联查询能正确显示创建人

### 验证步骤

1. 访问 `http://tc.xinqiyu.cn:8830/Produce/MaterialRequest/index`
2. 检查列表中的"创建人"列是否显示正确的姓名
3. 点击"查看"按钮进入详情页面
4. 验证详情页面中创建人和审核人信息
5. 创建新的领料单，验证创建人信息

## 🎯 修复效果

### 修复前
- ❌ 创建人显示为 `0` 或空白
- ❌ 详情页面无法显示正确的用户信息
- ❌ 新建时创建人姓名保存错误

### 修复后
- ✅ 创建人显示真实的用户姓名
- ✅ 详情页面正确显示创建人和审核人
- ✅ 新建时正确保存创建人姓名
- ✅ 通过关联查询确保数据准确性

## 🚀 扩展优化

### 1. 性能优化
- 考虑添加适当的数据库索引
- 对于大量数据可以考虑缓存用户信息

### 2. 数据一致性
- 定期检查和修复历史数据中的用户姓名
- 考虑添加数据同步机制

### 3. 用户体验
- 在用户姓名旁显示部门信息
- 添加用户头像显示

修复完成后，物料需求页面将正确显示创建人姓名，提供更好的用户体验和数据追溯能力。
