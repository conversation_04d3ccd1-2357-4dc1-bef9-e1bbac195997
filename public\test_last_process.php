<?php
/**
 * 测试最后工序判断逻辑
 */

// 引入框架
require_once __DIR__ . '/../vendor/autoload.php';

// 启动应用
$app = new \think\App();
$app->initialize();

use think\facade\Db;

echo "<pre>";
echo "=== 测试最后工序判断逻辑 ===\n\n";

try {
    $orderId = 27;
    $stepId = 3;
    
    echo "测试订单ID: {$orderId}\n";
    echo "测试工序ID: {$stepId}\n\n";
    
    // 1. 获取订单的所有工序
    $orderProcesses = Db::name('produce_order_process')
        ->where('order_id', $orderId)
        ->order('step_no asc')
        ->select()
        ->toArray();
    
    echo "订单工序列表：\n";
    foreach ($orderProcesses as $process) {
        echo "  step_no: {$process['step_no']}, process_name: {$process['process_name']}\n";
    }
    echo "\n";
    
    if (empty($orderProcesses)) {
        echo "❌ 未找到订单工序信息\n";
        exit;
    }
    
    // 2. 查找当前工序和最大工序号
    $currentStepIndex = -1;
    $currentStep = null;
    $maxStepNo = 0;
    
    foreach ($orderProcesses as $index => $orderProcess) {
        // 更新最大工序号
        if ($orderProcess['step_no'] > $maxStepNo) {
            $maxStepNo = $orderProcess['step_no'];
        }
        
        // 查找当前报工的工序
        if ($orderProcess['step_no'] == $stepId) {
            $currentStepIndex = $index;
            $currentStep = $orderProcess;
        }
    }
    
    echo "分析结果：\n";
    echo "  最大工序号: {$maxStepNo}\n";
    echo "  当前工序索引: {$currentStepIndex}\n";
    
    if ($currentStep) {
        echo "  当前工序号: {$currentStep['step_no']}\n";
        echo "  当前工序名: {$currentStep['process_name']}\n";
    } else {
        echo "  ❌ 未找到当前工序信息\n";
        exit;
    }
    
    // 3. 判断是否为最后工序
    $isLastProcess = 0;
    if ($currentStep['step_no'] == $maxStepNo) {
        $isLastProcess = 1;
    }
    
    echo "  是否最后工序: " . ($isLastProcess ? "是" : "否") . " (值: {$isLastProcess})\n\n";
    
    // 4. 模拟更新completed_qty
    if ($isLastProcess == 1) {
        echo "🎯 这是最后工序，应该更新completed_qty\n";
        
        // 查询当前completed_qty
        $order = Db::name('produce_order')->where('id', $orderId)->find();
        echo "  当前completed_qty: {$order['completed_qty']}\n";
        
        // 模拟增加10个合格品
        $qualifiedQty = 10;
        echo "  模拟增加合格品: {$qualifiedQty}\n";
        echo "  预期completed_qty: " . ($order['completed_qty'] + $qualifiedQty) . "\n";
        
        // 实际更新（注释掉，避免真实更新）
        /*
        $affected = Db::name('produce_order')
            ->where('id', $orderId)
            ->setInc('completed_qty', $qualifiedQty);
        echo "  更新结果: 影响行数 {$affected}\n";
        */
        
    } else {
        echo "ℹ️ 这不是最后工序，不会更新completed_qty\n";
    }
    
    echo "\n✅ 测试完成\n";
    
} catch (\Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}

echo "</pre>";
?>
