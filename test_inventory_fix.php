<?php
/**
 * 测试库存修复是否有效
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

echo "=== 库存系统修复测试 ===\n\n";

try {
    // 1. 测试 get_inventory_stock 函数
    echo "1. 测试 get_inventory_stock 函数:\n";
    
    // 假设产品ID为1
    $productId = 1;
    $stock = get_inventory_stock($productId);
    echo "   产品ID {$productId} 的库存: {$stock}\n";
    
    // 2. 测试新库存表是否存在
    echo "\n2. 测试新库存表:\n";
    try {
        $count = Db::name('oa_inventory_realtime')->count();
        echo "   ✅ oa_inventory_realtime 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        echo "   ❌ oa_inventory_realtime 表不存在: " . $e->getMessage() . "\n";
    }
    
    // 3. 测试库存预占表
    echo "\n3. 测试库存预占表:\n";
    try {
        $count = Db::name('oa_inventory_reserve')->count();
        echo "   ✅ oa_inventory_reserve 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        echo "   ❌ oa_inventory_reserve 表不存在: " . $e->getMessage() . "\n";
    }
    
    // 4. 测试库存事务表
    echo "\n4. 测试库存事务表:\n";
    try {
        $count = Db::name('oa_inventory_transaction')->count();
        echo "   ✅ oa_inventory_transaction 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        echo "   ❌ oa_inventory_transaction 表不存在: " . $e->getMessage() . "\n";
    }
    
    // 5. 测试仓库表兼容性
    echo "\n5. 测试仓库表兼容性:\n";
    try {
        $count = Db::name('oa_warehouse')->count();
        echo "   ✅ oa_warehouse 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        try {
            $count = Db::name('warehouse')->count();
            echo "   ⚠️  使用旧表 warehouse，记录数: {$count}\n";
        } catch (\Exception $e2) {
            echo "   ❌ 仓库表都不存在\n";
        }
    }
    
    // 6. 测试产品表兼容性
    echo "\n6. 测试产品表兼容性:\n";
    try {
        $count = Db::name('oa_product')->count();
        echo "   ✅ oa_product 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        try {
            $count = Db::name('product')->count();
            echo "   ⚠️  使用旧表 product，记录数: {$count}\n";
        } catch (\Exception $e2) {
            echo "   ❌ 产品表都不存在\n";
        }
    }

    // 7. 测试BOM表兼容性
    echo "\n7. 测试BOM表兼容性:\n";
    try {
        $count = Db::name('oa_material_bom')->count();
        echo "   ✅ oa_material_bom 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        try {
            $count = Db::name('bom_master')->count();
            echo "   ⚠️  使用旧表 bom_master，记录数: {$count}\n";
        } catch (\Exception $e2) {
            echo "   ❌ BOM表都不存在\n";
        }
    }

    // 8. 测试BOM明细表兼容性
    echo "\n8. 测试BOM明细表兼容性:\n";
    try {
        $count = Db::name('oa_material_bom_detail')->count();
        echo "   ✅ oa_material_bom_detail 表存在，记录数: {$count}\n";
    } catch (\Exception $e) {
        try {
            $count = Db::name('bom_item')->count();
            echo "   ⚠️  使用旧表 bom_item，记录数: {$count}\n";
        } catch (\Exception $e2) {
            echo "   ❌ BOM明细表都不存在\n";
        }
    }

    // 9. 测试BOM兼容函数
    echo "\n9. 测试BOM兼容函数:\n";
    if (function_exists('has_product_bom')) {
        echo "   ✅ has_product_bom() 函数已定义\n";
    } else {
        echo "   ❌ has_product_bom() 函数未定义\n";
    }

    if (function_exists('get_product_bom')) {
        echo "   ✅ get_product_bom() 函数已定义\n";
    } else {
        echo "   ❌ get_product_bom() 函数未定义\n";
    }

    if (function_exists('get_bom_items')) {
        echo "   ✅ get_bom_items() 函数已定义\n";
    } else {
        echo "   ❌ get_bom_items() 函数未定义\n";
    }

    echo "\n=== 测试完成 ===\n";
    
} catch (\Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}
