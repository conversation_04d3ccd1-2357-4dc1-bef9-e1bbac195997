# 锁定库存数据分析报告

## 📊 基于截图数据的分析

### 1. 实时库存数据（第一张截图）

从实时库存页面可以看到以下关键数据：

| 产品ID | 产品名称 | 物料编码 | 总库存 | 可用库存 | 锁定库存 | 状态 |
|--------|----------|----------|--------|----------|----------|------|
| 140 | 生产门窗 | SC20250810000031 | M3*8螺丝-公制 | 100.00 | 100.00 | 0 | 116 |
| 126 | 生产门窗 | SC20250810000031 | 8J下架 | 100.00 | 100.00 | 0 | 100 |

**关键观察**：
- 产品140：总库存100，可用库存100，锁定库存0，但右侧显示"116"
- 产品126：总库存100，可用库存100，锁定库存0，但右侧显示"100"

### 2. 库存锁定记录数据（第二张截图）

从库存锁定记录页面可以看到：

| ID | 产品名称 | 数量 | 关联类型 | 关联单号 | 状态 |
|----|----------|------|----------|----------|------|
| 42 | M3*8螺丝-公制 | 100.00个 | production_order | SC20250810000031 | 锁定中 |
| 41 | M3*8螺丝-公制 | 100.00个 | production_order | SC20250810000031 | 锁定中 |

**关键观察**：
- M3*8螺丝-公制产品有两条锁定记录，每条100个，总计200个
- 都是针对生产订单SC20250810000031
- 状态都是"锁定中"

### 3. 产品库存数据（第三张截图）

从产品库存页面可以看到：

| 产品ID | 产品名称 | 物料编码 | 总库存 | 可用库存 | 锁定库存 |
|--------|----------|----------|--------|----------|----------|
| 2169 | 滑轨 | WL20250810000031 | 200.00 | 100.00 | 100.00 |
| 2166 | 6.3X65不锈钢螺丝 | 1.0650810.0503.002 | 11.00 | 11.00 | 0.00 |
| 2164 | M3*8螺丝-公制 | 1.0651.010 | 1000.00 | 800.00 | 200.00 |

**关键观察**：
- 产品2164（M3*8螺丝-公制）：总库存1000，可用库存800，锁定库存200
- 这与锁定记录表中的数据一致（两条100的锁定记录 = 200总锁定）

## 🔍 数据一致性分析

### 问题发现

**实时库存表与锁定记录表数据不一致**：

1. **M3*8螺丝-公制产品**：
   - 实时库存表显示：锁定库存 = 0
   - 锁定记录表显示：有2条锁定记录，总计200个
   - 产品库存表显示：锁定库存 = 200
   - **结论**：实时库存表的锁定数量错误

2. **数据不一致的原因**：
   - 可能是锁定操作时没有正确更新实时库存表
   - 或者释放锁定时没有正确同步数据
   - 或者存在直接操作数据库的情况

### 正确答案

**基于多个模块数据对比，正确的锁定库存应该是：**

- **M3*8螺丝-公制**：锁定库存应该是 **200个**（不是实时库存表显示的0）
- **滑轨**：锁定库存应该是 **100个**（产品库存表显示正确）

### 验证方法

可以通过以下SQL查询验证：

```sql
-- 查询M3*8螺丝-公制的锁定记录
SELECT 
    product_id,
    SUM(quantity) as total_locked,
    COUNT(*) as lock_count
FROM oa_inventory_lock 
WHERE product_id IN (140, 2164) -- M3*8螺丝-公制的不同ID
  AND status = 1 -- 锁定中状态
GROUP BY product_id;

-- 查询实时库存表中的锁定数量
SELECT 
    product_id,
    locked_quantity
FROM oa_inventory_realtime 
WHERE product_id IN (140, 2164);
```

## 🛠️ 修复建议

### 1. 立即修复数据
```sql
-- 更新实时库存表中的锁定数量
UPDATE oa_inventory_realtime ir 
SET locked_quantity = (
    SELECT COALESCE(SUM(quantity), 0) 
    FROM oa_inventory_lock il 
    WHERE il.product_id = ir.product_id 
      AND il.warehouse_id = ir.warehouse_id 
      AND il.status = 1
),
available_quantity = quantity - (
    SELECT COALESCE(SUM(quantity), 0) 
    FROM oa_inventory_lock il 
    WHERE il.product_id = ir.product_id 
      AND il.warehouse_id = ir.warehouse_id 
      AND il.status = 1
)
WHERE EXISTS (
    SELECT 1 FROM oa_inventory_lock il 
    WHERE il.product_id = ir.product_id 
      AND il.warehouse_id = ir.warehouse_id 
      AND il.status = 1
);
```

### 2. 预防措施
1. **强制使用服务类**：禁止直接操作锁定记录表
2. **添加数据校验**：定期运行一致性检查
3. **完善日志记录**：记录所有锁定操作的详细日志
4. **事务完整性**：确保所有锁定操作在事务中完成

## 📋 结论

**实时库存中的锁定库存数据是错误的**。根据锁定记录表和产品库存表的数据对比：

- ❌ 实时库存表显示的锁定数量不准确
- ✅ 锁定记录表中的数据是准确的
- ✅ 产品库存表中的锁定数量与锁定记录表一致

**建议立即运行数据修复脚本，并加强锁定操作的规范性管理。**
