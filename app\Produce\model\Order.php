<?php
namespace app\Produce\model;
use think\Model;
use think\facade\Db;
use app\purchase\model\OrderDetail as OrderDetailModel;
use app\Produce\model\FirstArticleInspection;
use think\facade\View;


class Order extends Model
{
    // 设置当前模型对应的数据表名称
    protected $name = 'produce_order';
  
    // 自定义选项
    protected static $statusArr = [
        0 => '待排产',
        1 => '已排产',
        2 => '生产中',
        3 => '完成生产',
        4 => '取消',
        5 => '完成-全部入库'
    ];

    protected static $priorityArr = [
        1 => '低',
        2 => '中',
        3 => '高'
    ];

    protected static $sourceArr = [
        0 => '手动录入',
        1 => '系统导入'
    ];

    /**
     * 获取状态数组
     */
    public static function getStatusArr()
    {
        return self::$statusArr;
    }

    /**
     * 获取优先级数组
     */
    public static function getPriorityArr()
    {
        return self::$priorityArr;
    }

    /**
     * 获取来源数组
     */
    public static function getSourceArr()
    {
        return self::$sourceArr;
    }

    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\material\model\Archive', 'product_id', 'id');
    }
    /**
     * 关联BOM（已废弃，使用新的BOM表结构）
     */
    // public function BomMaster()
    // {
    //     return $this->belongsTo('app\engineering\model\BomMaster', 'bom_id', 'id');
    // }
     
    
    public function admin()
    {
        return $this->belongsTo('app\user\model\Admin', 'create_uid', 'id');
    }

    /**
     * 关联订单明细
     */
    public function details()
    {
        return $this->hasMany('app\Produce\model\OrderDetail', 'order_id', 'id');
    }

    /**
     * 关联销售订单
     */
    public function customerOrder()
    {
        return $this->belongsTo('app\customer\model\Order', 'customer_order_id', 'id');
    }
    /**
     * 获取状态名称
     */
    public function getStatusNameAttr($value, $data)
    {
        return isset(self::$statusArr[$data['status']]) ? self::$statusArr[$data['status']] : '';
    }

    /**
     * 获取优先级名称
     */
    public function getPriorityNameAttr($value, $data)
    {
        return isset(self::$priorityArr[$data['priority']]) ? self::$priorityArr[$data['priority']] : '';
    }

    /**
     * 获取来源名称
     */
    public function getSourceNameAttr($value, $data)
    {
        return isset(self::$sourceArr[$data['source']]) ? self::$sourceArr[$data['source']] : '';
    }

    /**
     * 格式化交期时间
     */
    public function getDeliveryDateFormatAttr($value, $data)
    {
        return !empty($data['delivery_date']) ? date('Y-m-d', $data['delivery_date']) : '';
    }

    /**
     * 获取订单列表
     */
    public function getList($param = [], $page = 1, $limit = 10)
    {
        // 构建查询条件
        $map = [];
 
        // 订单状态
        if (isset($param['status']) && $param['status'] !== '') {
            $map[] = ['status', '=', $param['status']];
        }

        // 优先级
        if (isset($param['priority']) && $param['priority'] !== '') {
            $map[] = ['priority', '=', $param['priority']];
        }

        // 产品名称/订单编号
        if (isset($param['keywords']) && $param['keywords'] !== '') {
            $map[] = ['order_no|product_name', 'like', '%' . $param['keywords'] . '%'];
        }

        // 查询订单列表，包含排产信息
        $list = $this->alias('o')
            ->leftJoin('production_plan p', 'o.plan_id = p.id')
            ->field('o.*, p.plan_start_date, p.plan_end_date, p.progress as plan_progress, p.status as plan_status')
            ->where($map)
            ->page($page, $limit)
            ->order('o.id DESC, o.create_time DESC')
            ->select();
        // 获取状态中文名称
        foreach ($list as &$order) {
            $order['status_name'] = self::$statusArr[$order['status']];
            $order['priority_name'] = self::$priorityArr[$order['priority']];
            $order['delivery_date_format'] = date('Y-m-d', $order['delivery_date']);
            $order['source_name'] = self::$sourceArr[$order['source']];
            $order['first_article_status_name'] = self::$firstArticleStatusArr[$order['first_article_status']];

            // 处理排产状态信息
            $planStatusArr = [
                0 => '未排产',
                1 => '已排产',
                2 => '生产中',
                3 => '已完成'
            ];
            $order['plan_status_name'] = $planStatusArr[$order['plan_status']] ?? '未知';

            // 格式化排产日期
            $order['plan_start_date_format'] = $order['plan_start_date'] ? $order['plan_start_date'] : '';
            $order['plan_end_date_format'] = $order['plan_end_date'] ? $order['plan_end_date'] : '';
            $order['scheduled_date_format'] = $order['scheduled_date'] ? $order['scheduled_date'] : '';

            // 排产状态标识
            $order['is_scheduled'] = $order['plan_status'] > 0 ? 1 : 0;

            // 通过投料记录判断投料状态
            $feedingStatus = $this->getFeedingStatusByOrderId($order['id']);
            $feedingStatusArr = [
                0 => '未投料',
                1 => '投料中',
                2 => '投料完',
                3 => '超投料'
            ];
            $order['feeding_status'] = $feedingStatus;
            $order['feeding_status_name'] = $feedingStatusArr[$feedingStatus] ?? '未知';

            // 动态计算生产工序数（总工序数/已完成工序数）
            $processStats = $this->calculateProcessStats($order['id']);
            $order['completed_processes'] = $processStats['completed'];
            $order['total_processes'] = $processStats['total'];
            $order['process_info'] = $processStats['completed'] . '/' . $processStats['total'];

            // 获取最近报工工序
            $order['latest_work_process'] = $this->getLatestWorkProcess($order['id']);

            // 计算已入库数量
            $order['warehousing'] = $this->getWarehousingQuantity($order['id']);
        }

        // 获取总数
        $count = $this->where($map)->count();

        return ['data' => $list, 'count' => $count];
    }

    /**
     * 根据生产订单ID获取投料状态
     * @param int $orderId 生产订单ID
     * @return int 投料状态：0=未投料，1=投料中，2=投料完，3=超投料
     */
    private function getFeedingStatusByOrderId($orderId)
    {
        try {
            // 1. 查询该生产订单的领料记录
            $materialRequests = \think\facade\Db::name('production_material_request')
                ->where('production_order_id', $orderId)
                ->where('delete_time', 0)  // 排除已删除的记录
                ->field('id, status, request_type, create_time')
                ->order('create_time desc')
                ->select()
                ->toArray();

            \think\facade\Log::info('查询投料状态', [
                'order_id' => $orderId,
                'material_requests_count' => count($materialRequests),
                'material_requests' => $materialRequests
            ]);

            if (empty($materialRequests)) {
                return 0; // 未投料
            }

            // 2. 查询BOM需求和实际投料情况
            $bomRequirements = $this->getBomRequirements($orderId);
            $actualFeeding = $this->getActualFeeding($orderId);

            \think\facade\Log::info('BOM需求和实际投料', [
                'order_id' => $orderId,
                'bom_requirements' => $bomRequirements,
                'actual_feeding' => $actualFeeding
            ]);

            // 3. 统计领料单状态
            $hasCompletedRequest = false;
            $hasApprovedRequest = false;

            foreach ($materialRequests as $request) {
                // 已完成的领料单
                if ($request['status'] == 3) {
                    $hasCompletedRequest = true;
                }

                // 已审核的领料单
                if ($request['status'] >= 1) {
                    $hasApprovedRequest = true;
                }
            }

            // 4. 判断投料状态 - 优先按实际投料量判断
            if (!empty($bomRequirements) && !empty($actualFeeding)) {
                // 有BOM需求和实际投料记录，按投料量精确判断
                $isExceed = false;
                $totalBomQty = 0;
                $totalFedQty = 0;

                // 按物料逐一比较
                foreach ($bomRequirements as $bomItem) {
                    $materialId = $bomItem['material_id'];
                    $requiredQty = $bomItem['required_qty'];
                    $totalBomQty += $requiredQty;

                    // 查找对应的实际投料量
                    $fedQty = 0;
                    foreach ($actualFeeding as $fedItem) {
                        if ($fedItem['material_id'] == $materialId) {
                            $fedQty = floatval($fedItem['fed_qty']);
                            break;
                        }
                    }
                    $totalFedQty += $fedQty;

                    // 检查是否超领（允许5%的误差）
                    if ($fedQty > $requiredQty * 1.05) {
                        $isExceed = true;
                    }
                }

                if ($isExceed) {
                    return 3; // 超投料
                } else if ($totalFedQty >= $totalBomQty * 0.95) { // 95%以上认为投料完成
                    return 2; // 投料完
                } else if ($totalFedQty > 0) {
                    return 1; // 投料中
                } else {
                    return $hasApprovedRequest ? 1 : 0;
                }
            } else if (!empty($bomRequirements)) {
                // 有BOM但无实际投料记录
                return $hasApprovedRequest ? 1 : 0;
            } else {
                // 没有BOM，检查领料明细是否有超领情况
                $hasExceedFeeding = $this->checkExceedFeedingFromDetails($orderId);
                if ($hasExceedFeeding) {
                    return 3; // 超投料
                } else if ($hasCompletedRequest) {
                    return 2; // 投料完
                } else if ($hasApprovedRequest) {
                    return 1; // 投料中
                } else {
                    return 0; // 未投料
                }
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('获取投料状态失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return 0; // 出错时返回未投料
        }
    }

    /**
     * 获取BOM需求
     */
    private function getBomRequirements($orderId)
    {
        try {
            $orderInfo = \think\facade\Db::name('produce_order')
                ->where('id', $orderId)
                ->field('bom_id, quantity')
                ->find();

            if (!$orderInfo || !$orderInfo['bom_id']) {
                return [];
            }

            $bomDetails = \think\facade\Db::name('bom_detail')
                ->where('bom_id', $orderInfo['bom_id'])
                ->where('delete_time', 0)
                ->field('material_id, quantity as unit_qty')
                ->select()
                ->toArray();

            $requirements = [];
            foreach ($bomDetails as $detail) {
                $requirements[] = [
                    'material_id' => $detail['material_id'],
                    'required_qty' => $detail['unit_qty'] * $orderInfo['quantity']
                ];
            }

            return $requirements;
        } catch (\Exception $e) {
            \think\facade\Log::error('获取BOM需求失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取实际投料情况
     */
    private function getActualFeeding($orderId)
    {
        try {
            $feeding = \think\facade\Db::name('production_feeding_detail')
                ->alias('pfd')
                ->leftJoin('production_feeding pf', 'pfd.feeding_id = pf.id')
                ->where('pf.production_order_id', $orderId)
                ->where('pf.delete_time', 0)
                ->field('pfd.material_id, SUM(pfd.actual_quantity) as fed_qty')
                ->group('pfd.material_id')
                ->select()
                ->toArray();

            return $feeding;
        } catch (\Exception $e) {
            \think\facade\Log::error('获取实际投料失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 检查领料明细中是否有超领情况（针对无BOM的订单）
     */
    private function checkExceedFeedingFromDetails($orderId)
    {
        try {
            // 按物料汇总所有领料单的实际数量
            $materialSummary = \think\facade\Db::name('production_material_request_detail')
                ->alias('pmrd')
                ->leftJoin('production_material_request pmr', 'pmrd.request_id = pmr.id')
                ->where('pmr.production_order_id', $orderId)
                ->where('pmr.delete_time', 0)
                ->field('pmrd.material_id, pmrd.material_name,
                         MAX(pmrd.standard_quantity) as standard_quantity,
                         SUM(pmrd.actual_quantity) as total_actual_quantity,
                         MAX(pmrd.is_excess) as has_excess_flag')
                ->group('pmrd.material_id')
                ->select()
                ->toArray();

            \think\facade\Log::error('检查超领情况汇总数据', [
                'order_id' => $orderId,
                'materials_count' => count($materialSummary),
                'summary' => $materialSummary
            ]);

            foreach ($materialSummary as $material) {
                $materialId = $material['material_id'];
                $materialName = $material['material_name'];
                $standardQty = floatval($material['standard_quantity']);
                $totalActualQty = floatval($material['total_actual_quantity']);
                $hasExcessFlag = intval($material['has_excess_flag']);

                \think\facade\Log::error('检查物料累计超领情况', [
                    'material_id' => $materialId,
                    'material_name' => $materialName,
                    'standard_qty' => $standardQty,
                    'total_actual_qty' => $totalActualQty,
                    'has_excess_flag' => $hasExcessFlag,
                    'is_exceed' => $totalActualQty > $standardQty * 1.05
                ]);

                // 检查是否有超领标记
                if ($hasExcessFlag == 1) {
                    \think\facade\Log::error('发现超领标记', ['material_id' => $materialId]);
                    return true;
                }

                // 如果累计实际数量超过标准数量的105%，认为是超领
                if ($standardQty > 0 && $totalActualQty > $standardQty * 1.05) {
                    \think\facade\Log::error('发现累计数量超领', [
                        'material_id' => $materialId,
                        'material_name' => $materialName,
                        'standard_qty' => $standardQty,
                        'total_actual_qty' => $totalActualQty,
                        'exceed_amount' => $totalActualQty - $standardQty
                    ]);
                    return true;
                }
            }

            \think\facade\Log::info('未发现超领情况', ['order_id' => $orderId]);
            return false;
        } catch (\Exception $e) {
            \think\facade\Log::error('检查超领情况失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 计算订单工序统计
     */
    private function calculateProcessStats($orderId)
    {
        try {
            // 获取订单的总工序数
            $totalProcesses = \think\facade\Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->count();

            if ($totalProcesses == 0) {
                return ['total' => 0, 'completed' => 0];
            }

            // 计算已完成的工序数（基于报工记录）
            $completedProcesses = 0;

            // 获取所有工序
            $processes = \think\facade\Db::name('produce_order_process')
                ->where('order_id', $orderId)
                ->field('id, step_no, process_name')
                ->order('step_no asc')
                ->select()
                ->toArray();

            // 获取订单信息
            $order = \think\facade\Db::name('produce_order')
                ->where('id', $orderId)
                ->field('quantity')
                ->find();

            $orderQuantity = $order['quantity'] ?? 0;

            foreach ($processes as $process) {
                // 查询该工序的报工数量
                $reportedQty = \think\facade\Db::name('production_work_report')
                    ->where('order_id', $orderId)
                    ->where('step_id', $process['step_no'])
                    ->where('status', 1) // 有效的报工记录
                    ->sum('qualified_qty') ?: 0;

                // 如果报工数量达到订单数量的80%，认为该工序已完成
                if ($reportedQty >= $orderQuantity * 0.8) {
                    $completedProcesses++;
                }
            }

            return [
                'total' => $totalProcesses,
                'completed' => $completedProcesses
            ];

        } catch (\Exception $e) {
            return ['total' => 0, 'completed' => 0];
        }
    }

    /**
     * 获取最近报工工序
     */
    private function getLatestWorkProcess($orderId)
    {
        try {
            // 查询最近的报工记录
            $latestWork = \think\facade\Db::name('production_work_report')
                ->where('order_id', $orderId)
                ->where('status', 1) // 有效的报工记录
                ->order('create_time DESC')
                ->field('step_id, step_name, create_time')
                ->find();

            if ($latestWork) {
                return $latestWork['step_name'] ?: $latestWork['step_id'];
            }

            return '-';
        } catch (\Exception $e) {
            return '-';
        }
    }

    /**
     * 获取订单已入库数量
     * @param int $orderId 订单ID
     * @return float 已入库数量
     */
    private function getWarehousingQuantity($orderId)
    {
        try {
            // 从库存流水表查询已入库数量
            $warehousingQty = \think\facade\Db::name('inventory_transaction')
                ->where('ref_type', 'production_order')
                ->where('ref_id', $orderId)
                ->where('transaction_type', 'in')
                ->sum('quantity') ?: 0;

            return floatval($warehousingQty);
        } catch (\Exception $e) {
            \think\facade\Log::error('获取入库数量失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 添加或编辑订单 （废弃）
     */
    public function saveOrder0000($data)
    {
        // 交期时间转时间戳
        if (isset($data['delivery_date']) && !empty($data['delivery_date'])) {
            $data['delivery_date'] = strtotime($data['delivery_date']);
        }

        // 更新或添加
        if (isset($data['id']) && $data['id'] > 0) {
            // 先获取原始数据
            $old = $this->where('id', $data['id'])->find();
            if (!$old) {
                return ['code' => 1, 'msg' => '订单不存在'];
            }
            
            // 记录更新者信息
            $data['update_uid'] = session('admin.id');
            $data['update_name'] = session('admin.name');
            $data['update_time'] = time();
            
            // 更新数据
            $result = $this->where('id', $data['id'])->update($data);
            
            // 记录日志
            $this->recordLog($data['id'], $old->toArray(), $data, '更新订单');
            
            return ['code' => 0, 'msg' => '更新成功', 'data' => $result];
        } else {
            // 生成订单编号
            $data['order_no'] = $this->generateOrderNo();
            
            // 记录创建者信息
            
            $data['create_uid'] = session('tc_admin');
            //$data['create_name'] = session('gougu.name');
            $data['create_time'] = time();
            
            // 添加数据
            $this->save($data);
            
            // 记录日志
            $this->recordLog($this->id, [], $data, '创建订单');
            
            return ['code' => 0, 'msg' => '添加成功', 'data' => $this->id];
        }
    }

    /**
     * 生成订单编号
     */
    private function generateOrderNo()
    {
        $prefix = 'PO'; // 订单前缀
        $date = date('Ymd');
        $maxId = $this->where('order_no', 'like', $prefix . $date . '%')->max('id');
        $serialNo = $maxId ? intval(substr($this->where('id', $maxId)->value('order_no'), -4)) + 1 : 1;
        return $prefix . $date . str_pad($serialNo, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 记录订单日志
     */
    private function recordLog($orderId, $oldData, $newData, $action = '')
    {
        $log = [
            'order_id' => $orderId,
            'order_no' => isset($newData['order_no']) ? $newData['order_no'] : (isset($oldData['order_no']) ? $oldData['order_no'] : ''),
            'action' => $action,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'remark' => $action,
            'uid' => session('tc_admin'),
            'create_time' => time()
        ];
        
        return OrderLog::create($log);
    }

    /**
     * 更新订单状态
     */
    public function updateStatus($id, $status)
    {
        // 获取原始数据
        $old = $this->where('id', $id)->find();
        if (!$old) {
            return ['code' => 1, 'msg' => '订单不存在'];
        }
       
        //print_r(session('tc_admin'));
      
        // 更新数据 uid
        $data = [
            'id' => $id,
            'status' => $status,
            'update_uid' => session('tc_admin'),
            'update_time' => time()
        ];
        
        $result = $this->where('id', $id)->update($data);
       
        
        // 记录日志
        $action = '更新订单状态：' . (isset(self::$statusArr[$old['status']]) ? self::$statusArr[$old['status']] : '') 
                . ' -> ' . (isset(self::$statusArr[$status]) ? self::$statusArr[$status] : '');
        $this->recordLog($id, $old->toArray(), $data, $action);
        
        return ['code' => 0, 'msg' => '状态更新成功', 'data' => $result];
    }

    /**
     * 删除订单（软删除）
     */
    public function deleteOrder($id)
    {
        // 获取原始数据
        $old = $this->where('id', $id)->find();
        if (!$old) {
            return ['code' => 1, 'msg' => '订单不存在'];
        }
        
        // 只有待排产和已取消的订单可以删除
        if (!in_array($old['status'], [0, 4])) {
            return ['code' => 1, 'msg' => '只有待排产和已取消的订单可以删除'];
        }
        
        // 软删除
        $data = [
            'id' => $id,
            'delete_time' => time(),
            'update_uid' => session('admin.id'),
            'update_name' => session('admin.name'),
            'update_time' => time()
        ];
        
        $result = $this->where('id', $id)->update($data);
        
        // 记录日志
        $this->recordLog($id, $old->toArray(), $data, '删除订单');
        
        return ['code' => 0, 'msg' => '删除成功', 'data' => $result];
    }

    // 添加首件状态数组
    protected static $firstArticleStatusArr = [
        0 => '未完成',
        1 => '部分完成',
        2 => '全部完成'
    ];
    
    /**
     * 获取首件状态数组
     */
    public static function getFirstArticleStatusArr()
    {
        return self::$firstArticleStatusArr;
    }
    
    /**
     * 获取首件状态名称
     */
    public function getFirstArticleStatusNameAttr($value, $data)
    {
        return isset(self::$firstArticleStatusArr[$data['first_article_status']]) ? self::$firstArticleStatusArr[$data['first_article_status']] : '';
    }
    
    /**
     * 保存订单时新增首件相关处理
     */
    public function saveOrder($data)
    {
        // 交期时间转时间戳
        if (isset($data['delivery_date']) && !empty($data['delivery_date'])) {
            $data['delivery_date'] = strtotime($data['delivery_date']);
        }
        
        // 处理首件检验设置
        if (isset($data['first_article_required'])) {
            $data['first_article_required'] = intval($data['first_article_required']);
        }
        
        // if (isset($data['first_article_qty'])) {
        //     $data['first_article_qty'] = intval($data['first_article_qty']);
        // }

        // 更新或添加
        if (isset($data['id']) && $data['id'] > 0) {
            // 先获取原始数据
            $old = $this->where('id', $data['id'])->find();
            if (!$old) {
                return ['code' => 1, 'msg' => '订单不存在'];
            }
            
            // 记录更新者信息
            $data['update_uid'] = session('admin.id');
            $data['update_name'] = session('admin.name');
            $data['update_time'] = time();
            
            // 更新数据
            $result = $this->where('id', $data['id'])->update($data);
            
            // 记录日志
            $this->recordLog($data['id'], $old->toArray(), $data, '更新订单');
            
            return ['code' => 0, 'msg' => '更新成功', 'data' => $result];
        } else {
            // 生成订单编号
            $data['order_no'] = $this->generateOrderNo();
            
            // 记录创建者信息
            $data['create_uid'] = session('tc_admin');
            $data['create_time'] = time();
            
            // 默认首件检验状态
            $data['first_article_status'] = 0;
            
            // 添加数据
            $this->save($data);
            
            // 记录日志
            $this->recordLog($this->id, [], $data, '创建订单');
            
            return ['code' => 0, 'msg' => '添加成功', 'data' => $this->id];
        }
    }
    
    /**
     * 获取订单首件检验状态
     */
    public function getFirstArticleStatus($orderId)
    {
        // 获取订单信息
        $order = $this->find($orderId);
        if (!$order) {
            return false;
        }
        
        // 如果不需要首件检验，直接返回已完成
        if ($order['first_article_required'] == 0) {
            return [
                'required' => false,
                'status' => 2,
                'status_name' => self::$firstArticleStatusArr[2]
            ];
        }
        
        // 获取工艺流程的工序数
        $process = Db::name('engineering_process')->where('id', $order['process_id'])->find();
        $steps = [];
        if (!empty($process['steps'])) {
            $stepsData = json_decode($process['steps'], true);
            if (is_array($stepsData)) {
                foreach ($stepsData as $step) {
                    if (isset($step['order']) && isset($step['name'])) {
                        $steps[] = $step;
                    }
                }
                
                // 按工序顺序排序
                usort($steps, function($a, $b) {
                    return $a['order'] <=> $b['order'];
                });
            }


            // return [
            //     'required' => true,
            //     'status' => $order['first_article_status'],
            //     'status_name' => self::$firstArticleStatusArr[$order['first_article_status']],
            //     'steps' => [],
            //     'inspections' => []
            // ];
        }
       
       // print_r($steps);
       // $steps = json_decode($process['steps'], true);
        
        // 获取各工序的首件检验状态
        $inspections = Db::name('produce_first_article_inspection')
            ->alias('pfai')
            ->leftJoin('admin a', 'pfai.inspector_id = a.id')
            ->where('pfai.order_id', $orderId)
            ->field('pfai.id, pfai.step_id, pfai.step_name, pfai.status, a.name as inspector_name, pfai.inspection_date')
            ->select()
            ->toArray();
            
        $inspectionMap = [];
        foreach ($inspections as $inspection) {
            $inspectionMap[$inspection['step_id']] = $inspection;
        }
       
        // 处理各工序检验状态
        $stepsStatus = [];
        $statusArr = FirstArticleInspection::getStatusArr();

         foreach ($steps as $step) {
            $status = isset($inspectionMap[$step['order']]) ? $inspectionMap[$step['order']]['status'] : 0;
            $stepsStatus[] = [
                'step_id' => $step['order'],
                'step_name' => $step['name'],
                'status' => $status,
                'status_name' => $statusArr[$status],
                'inspection_id' => isset($inspectionMap[$step['order']]) ? $inspectionMap[$step['order']]['id'] : 0,
                'inspector_name' => isset($inspectionMap[$step['order']]) ? $inspectionMap[$step['order']]['inspector_name'] : '',
                'inspection_date' => isset($inspectionMap[$step['order']]) ? $inspectionMap[$step['order']]['inspection_date'] : ''
            ];
        }
        
        return [
            'required' => true,
            'status' => $order['first_article_status'],
            'status_name' => self::$firstArticleStatusArr[$order['first_article_status']],
            'steps' => $stepsStatus,
            'inspections' => $inspections
        ];
    }
} 