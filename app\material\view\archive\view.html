{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
.p-4 {
    padding: 15px;
}
.p-page {
    padding: 15px;
}
.layui-td-gray {
    background-color: #f8f8f8;
    font-weight: bold;
}
.info-value {
    padding: 8px 12px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    min-height: 38px;
    line-height: 22px;
}
</style>
{/block}

{block name="body"}
<div class="layui-tab layui-tab-brief" lay-filter="materialViewTab">
    <ul class="layui-tab-title">
        <li class="layui-this">基础资料</li>
        <li>质检信息</li>
        <li>价格信息</li>
        <li>价格管理</li>
        <li>工艺管理</li>
        <li>BOM管理</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基础资料 -->
        <div class="layui-tab-item layui-show">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">物料编号</td>
                        <td><div class="info-value">{$detail.material_code}</div></td>
                        <td class="layui-td-gray">物料名称</td>
                        <td><div class="info-value">{$detail.title}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料分类</td>
                        <td><div class="info-value">{$detail.category_name|default='未分类'}</div></td>
                        <td class="layui-td-gray">基本单位</td>
                        <td><div class="info-value">{$detail.unit|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">规格型号</td>
                        <td><div class="info-value">{$detail.specs|default='-'}</div></td>
                        <td class="layui-td-gray">物料类型</td>
                        <td><div class="info-value">{if condition="$detail.source_type == 1"}外购{elseif condition="$detail.source_type == 2"}自制{else/}-{/if}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">状态</td>
                        <td><div class="info-value">{if condition="$detail.status == 1"}启用{else/}禁用{/if}</div></td>
                        <td class="layui-td-gray">生产厂家</td>
                        <td><div class="info-value">{$detail.producer|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">采购周期(天)</td>
                        <td><div class="info-value">{$detail.purchase_cycle|default='-'}</div></td>
                        <td class="layui-td-gray">库存数量</td>
                        <td><div class="info-value">{$detail.stock|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">默认仓库</td>
                        <td><div class="info-value">{$detail.default_warehouse|default='-'}</div></td>
                        <td class="layui-td-gray">最小订购量</td>
                        <td><div class="info-value">{$detail.min_order_qty|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最小包装量</td>
                        <td><div class="info-value">{$detail.min_package_qty|default='-'}</div></td>
                        <td class="layui-td-gray">物料等级</td>
                        <td><div class="info-value">{$detail.material_level|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料来源</td>
                        <td><div class="info-value">{$detail.material_source|default='-'}</div></td>
                        <td class="layui-td-gray">类别</td>
                        <td><div class="info-value">{$detail.category|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">型号</td>
                        <td><div class="info-value">{$detail.model|default='-'}</div></td>
                        <td class="layui-td-gray">颜色</td>
                        <td><div class="info-value">{$detail.color|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">物料描述</td>
                        <td colspan="3"><div class="info-value">{$detail.description|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">备注信息</td>
                        <td colspan="3"><div class="info-value">{$detail.remark|default='-'}</div></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 质检信息 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">质量管理</td>
                        <td><div class="info-value">{if condition="$detail.quality_management == 1"}是{else/}否{/if}</div></td>
                        <td class="layui-td-gray">质检免检</td>
                        <td><div class="info-value">{if condition="$detail.quality_exempt == 1"}是{else/}否{/if}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">检验项目</td>
                        <td colspan="3">
                            <div class="info-value">
                                {if condition="$detail.quality_management == 1 && $detail.quality_exempt == 0"}
                                    {if condition="!empty($detail.quality_settings)"}
                                        {php}
                                            $quality_settings = json_decode($detail['quality_settings'], true);
                                            if (is_array($quality_settings) && !empty($quality_settings)) {
                                                $quality_map = [
                                                    'purchase_inspect' => '采购入库检',
                                                    'outsource_inspect' => '外协入库检',
                                                    'production_inspect' => '生产入库检',
                                                    'sales_inspect' => '销售出库检'
                                                ];
                                                $quality_names = [];
                                                foreach ($quality_settings as $setting) {
                                                    if (isset($quality_map[$setting])) {
                                                        $quality_names[] = $quality_map[$setting];
                                                    }
                                                }
                                                echo !empty($quality_names) ? implode('、', $quality_names) : '无';
                                            } else {
                                                echo '无';
                                            }
                                        {/php}
                                    {else/}
                                        无
                                    {/if}
                                {elseif condition="$detail.quality_exempt == 1"/}
                                    免检
                                {else/}
                                    无
                                {/if}
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
        </div>
        
        <!-- 价格信息 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <table class="layui-table layui-table-form">
                    <tr>
                        <td class="layui-td-gray">参考成本</td>
                        <td><div class="info-value">{$detail.reference_cost|default='-'}</div></td>
                        <td class="layui-td-gray">销售价格</td>
                        <td><div class="info-value">{$detail.sales_price|default='-'}</div></td>
                    </tr>
                    <tr>
                        <td class="layui-td-gray">最低销售价</td>
                        <td><div class="info-value">{$detail.min_sales_price|default='-'}</div></td>
                        <td class="layui-td-gray">最高销售价</td>
                        <td><div class="info-value">{$detail.max_sales_price|default='-'}</div></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 价格管理 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <div class="layui-tab layui-tab-brief" lay-filter="priceTab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">供应商价格</li>
                        <li>外协价格</li>
                        <li>附件管理</li>
                    </ul>
                    <div class="layui-tab-content">
                        <!-- 供应商价格 -->
                        <div class="layui-tab-item layui-show">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>供应商</th>
                                        <th>优先级</th>
                                        <th>最小数量</th>
                                        <th>最大数量</th>
                                        <th>含税价格</th>
                                        <th>税率(%)</th>
                                        <th>不含税价格</th>
                                    </tr>
                                </thead>
                                <tbody id="supplierPriceList">
                                    {if condition="!empty($supplierPrices)"}
                                        {volist name="supplierPrices" id="price"}
                                        <tr>
                                            <td>{$price.supplier_name|default='-'}</td>
                                            <td>{$price.priority|default='-'}</td>
                                            <td>{$price.min_qty|default='-'}</td>
                                            <td>{$price.max_qty|default='-'}</td>
                                            <td>{$price.tax_price|default='-'}</td>
                                            <td>{$price.tax_rate|default='-'}</td>
                                            <td>{$price.no_tax_price|default='-'}</td>
                                        </tr>
                                        {/volist}
                                    {else/}
                                        <tr><td colspan="7" style="text-align:center;">暂无数据</td></tr>
                                    {/if}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 外协价格 -->
                        <div class="layui-tab-item">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>外协商</th>
                                        <th>优先级</th>
                                        <th>最小数量</th>
                                        <th>最大数量</th>
                                        <th>含税价格</th>
                                        <th>税率(%)</th>
                                        <th>不含税价格</th>
                                    </tr>
                                </thead>
                                <tbody id="outsourcePriceList">
                                    {if condition="!empty($outsourcePrices)"}
                                        {volist name="outsourcePrices" id="price"}
                                        <tr>
                                            <td>{$price.supplier_name|default='-'}</td>
                                            <td>{$price.priority|default='-'}</td>
                                            <td>{$price.min_qty|default='-'}</td>
                                            <td>{$price.max_qty|default='-'}</td>
                                            <td>{$price.tax_price|default='-'}</td>
                                            <td>{$price.tax_rate|default='-'}</td>
                                            <td>{$price.no_tax_price|default='-'}</td>
                                        </tr>
                                        {/volist}
                                    {else/}
                                        <tr><td colspan="7" style="text-align:center;">暂无数据</td></tr>
                                    {/if}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 附件管理 -->
                        <div class="layui-tab-item">
                            <div class="info-value">
                                {if condition="!empty($detail.attachments)"}
                                    {php}
                                        $attachments = json_decode($detail['attachments'], true);
                                        if (is_array($attachments) && !empty($attachments)) {
                                            foreach ($attachments as $attachment) {
                                                echo '<p><a href="' . $attachment['url'] . '" target="_blank">' . $attachment['name'] . '</a></p>';
                                            }
                                        } else {
                                            echo '暂无附件';
                                        }
                                    {/php}
                                {else/}
                                    暂无附件
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工艺管理 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <div style="margin-bottom: 15px;">
                    <span style="color: #1E9FFF; font-weight: bold;">
                        选择的工艺路线
                    </span>
                </div>

                <div id="process-content">
                    <div id="process-loading" style="text-align: center; color: #999; padding: 50px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 加载工艺信息中...
                    </div>

                    <div id="process-empty" style="text-align: center; color: #999; padding: 50px; display: none;">
                        <i class="layui-icon layui-icon-template" style="font-size: 48px; color: #d2d2d2;"></i>
                        <div style="margin-top: 15px; font-size: 16px;">暂无工艺数据</div>
                        <div style="margin-top: 10px; color: #999;">该产品还没有关联工艺路线</div>
                    </div>

                    <div id="process-info" style="display: none;">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <strong id="process-title">工艺信息</strong>
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md6">
                                        <div><strong>工艺编号：</strong><span id="process-code">-</span></div>
                                        <div style="margin-top: 8px;"><strong>工艺名称：</strong><span id="process-name">-</span></div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <div><strong>工序数量：</strong><span id="process-step-count">-</span></div>
                                        <div style="margin-top: 8px;"><strong>备注信息：</strong><span id="process-remark">-</span></div>
                                    </div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <strong>工序列表：</strong>
                                    <div id="process-steps" style="margin-top: 10px;">
                                        <!-- 工序列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- BOM管理 -->
        <div class="layui-tab-item">
            <div class="p-page">
                <div style="margin-bottom: 15px;">
                    <span style="color: #1E9FFF; font-weight: bold;">
                        产品BOM信息
                    </span>
                </div>

                <div id="bom-content">
                    <div id="bom-loading" style="text-align: center; color: #999; padding: 50px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 检查BOM信息中...
                    </div>

                    <div id="bom-empty" style="text-align: center; color: #999; padding: 50px; display: none;">
                        <i class="layui-icon layui-icon-template" style="font-size: 48px; color: #d2d2d2;"></i>
                        <div style="margin-top: 15px; font-size: 16px;">暂无BOM数据</div>
                        <div style="margin-top: 10px; color: #999;">该产品还没有创建BOM表</div>
                    </div>

                    <div id="bom-info" style="display: none;">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <strong id="bom-title">BOM信息</strong>
                                <span class="layui-badge layui-bg-green" id="bom-status" style="margin-left: 10px;">启用</span>
                            </div>
                            <div class="layui-card-body">
                                <div class="layui-row layui-col-space15">
                                    <div class="layui-col-md6">
                                        <div><strong>BOM编号：</strong><span id="bom-code">-</span></div>
                                        <div style="margin-top: 8px;"><strong>BOM名称：</strong><span id="bom-name">-</span></div>
                                    </div>
                                    <div class="layui-col-md6">
                                        <div><strong>版本号：</strong><span id="bom-version">-</span></div>
                                        <div style="margin-top: 8px;"><strong>创建时间：</strong><span id="bom-create-time">-</span></div>
                                    </div>
                                </div>
                                <div style="margin-top: 15px;">
                                    <strong>物料清单：</strong>
                                    <div id="bom-materials" style="margin-top: 10px;">
                                        <!-- BOM物料列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    layui.use(['element'], function () {
        var element = layui.element;

        // 监听tab切换
        element.on('tab(materialViewTab)', function(data){
            console.log('切换到：', data.index);
        });

        // 监听价格管理内部tab切换
        element.on('tab(priceTab)', function(data){
            console.log('价格管理切换到：', data.index);
        });

        // 监听主标签页切换
        element.on('tab(materialViewTab)', function(data){
            console.log('切换到标签页：', data.index);

            // 当切换到工艺管理标签页时（索引4）
            if (data.index === 4) {
                initProcessView();
            }

            // 当切换到BOM管理标签页时（索引5）
            if (data.index === 5) {
                initBomView();
            }
        });

        // 供应商价格和外协价格数据已在服务端加载
    });

    // 工艺管理相关功能
    var processInitialized = false;

    function initProcessView() {
        if (processInitialized) return;
        processInitialized = true;

        var productId = getProductId();
        if (productId) {
            loadProcessInfo(productId);
        } else {
            showProcessEmpty();
        }
    }

    function loadProcessInfo(productId) {
        // 从产品信息中获取工艺模板ID
        var processTemplateId = '{$detail.process_template_id|default="0"}';

        if (processTemplateId && processTemplateId != '0') {
            // 加载工艺模板详情
            $.get('/material/archive/getProcessTemplates', function(res) {
                if (res.code === 0 && res.data) {
                    var processTemplate = res.data.find(function(template) {
                        return template.id == processTemplateId;
                    });

                    if (processTemplate) {
                        showProcessInfo(processTemplate);
                    } else {
                        showProcessEmpty();
                    }
                } else {
                    showProcessEmpty();
                }
            }).fail(function() {
                showProcessError();
            });
        } else {
            showProcessEmpty();
        }
    }

    function showProcessInfo(processData) {
        $('#process-loading').hide();
        $('#process-empty').hide();
        $('#process-info').show();

        // 填充工艺信息
        $('#process-title').text('工艺信息 - ' + processData.name);
        $('#process-code').text(processData.template_no || '-');
        $('#process-name').text(processData.name || '-');
        $('#process-step-count').text(processData.step_count_text || '暂无工序');
        $('#process-remark').text(processData.remark || '-');

        // 显示工序列表
        showProcessSteps(processData.steps);
    }

    function showProcessSteps(stepsJson) {
        var html = '';
        try {
            var steps = JSON.parse(stepsJson || '[]');
            if (steps.length === 0) {
                html = '<div style="text-align: center; color: #999; padding: 20px;">暂无工序数据</div>';
            } else {
                html += '<table class="layui-table" lay-size="sm">';
                html += '<thead><tr><th>序号</th><th>工序名称</th><th>工序类型</th><th>加工方式</th><th>检验方式</th><th>完成时间</th><th>说明</th></tr></thead>';
                html += '<tbody>';
                steps.forEach(function(step, index) {
                    html += '<tr>';
                    html += '<td>' + (step.step || (index + 1)) + '</td>';
                    html += '<td>' + (step.name || '-') + '</td>';
                    html += '<td>' + (step.type || '-') + '</td>';
                    html += '<td>' + (step.processing_type || '-') + '</td>';
                    html += '<td>' + (step.inspection_method || '-') + '</td>';
                    html += '<td>' + (step.completion_time || '0') + (step.time_unit || '天') + '</td>';
                    html += '<td>' + (step.description || '-') + '</td>';
                    html += '</tr>';
                });
                html += '</tbody></table>';
            }
        } catch (e) {
            html = '<div style="text-align: center; color: #ff5722; padding: 20px;">工序数据格式错误</div>';
        }
        $('#process-steps').html(html);
    }

    function showProcessEmpty() {
        $('#process-loading').hide();
        $('#process-info').hide();
        $('#process-empty').show();
    }

    function showProcessError() {
        $('#process-loading').hide();
        $('#process-info').hide();
        $('#process-empty').show();
        $('#process-empty').html('<div style="text-align: center; color: #ff5722; padding: 50px;"><i class="layui-icon layui-icon-close" style="font-size: 48px;"></i><div style="margin-top: 15px;">加载工艺信息失败</div></div>');
    }

    // BOM管理相关功能
    var bomInitialized = false;

    function initBomView() {
        if (bomInitialized) return;
        bomInitialized = true;

        var productId = getProductId();
        if (productId) {
            checkProductBom(productId);
        } else {
            showBomEmpty();
        }
    }

    function checkProductBom(productId) {
        $.get('/material/bom/checkProductBom', {product_id: productId}, function(res) {
            if (res.code === 1 && res.data && res.data.bom_id) {
                // 产品已有BOM，加载BOM信息
                loadBomInfo(res.data.bom_id);
            } else {
                // 产品没有BOM，显示空状态
                showBomEmpty();
            }
        }).fail(function() {
            showBomError();
        });
    }

    function loadBomInfo(bomId) {
        $.get('/material/bom/getBomDetail', {id: bomId}, function(res) {
            if (res.code === 0 && res.data) {
                showBomInfo(res.data);
            } else {
                showBomError();
            }
        }).fail(function() {
            showBomError();
        });
    }

    function showBomInfo(bomData) {
        $('#bom-loading').hide();
        $('#bom-empty').hide();
        $('#bom-info').show();

        // 填充BOM信息
        $('#bom-title').text('BOM信息 - ' + bomData.bom_name);
        $('#bom-code').text(bomData.bom_code || '-');
        $('#bom-name').text(bomData.bom_name || '-');
        $('#bom-version').text(bomData.version || '1.0');
        $('#bom-create-time').text(bomData.create_time_format || '-');

        // 设置状态
        var statusBadge = $('#bom-status');
        if (bomData.status == 1) {
            statusBadge.removeClass('layui-bg-gray').addClass('layui-bg-green').text('启用');
        } else {
            statusBadge.removeClass('layui-bg-green').addClass('layui-bg-gray').text('禁用');
        }

        // 显示物料清单
        showBomMaterials(bomData.materials || []);
    }

    function showBomMaterials(materials) {
        var html = '';
        if (materials.length === 0) {
            html = '<div style="text-align: center; color: #999; padding: 20px;">暂无物料数据</div>';
        } else {
            html += '<table class="layui-table" lay-size="sm">';
            html += '<thead><tr><th>序号</th><th>物料名称</th><th>物料编码</th><th>规格</th><th>单位</th><th>用量</th></tr></thead>';
            html += '<tbody>';
            materials.forEach(function(material, index) {
                html += '<tr>';
                html += '<td>' + (index + 1) + '</td>';
                html += '<td>' + (material.material_name || '-') + '</td>';
                html += '<td>' + (material.material_code || '-') + '</td>';
                html += '<td>' + (material.specs || '-') + '</td>';
                html += '<td>' + (material.unit || '-') + '</td>';
                html += '<td>' + (material.quantity || '0') + '</td>';
                html += '</tr>';
            });
            html += '</tbody></table>';
        }
        $('#bom-materials').html(html);
    }

    function showBomEmpty() {
        $('#bom-loading').hide();
        $('#bom-info').hide();
        $('#bom-empty').show();
    }

    function showBomError() {
        $('#bom-loading').hide();
        $('#bom-info').hide();
        $('#bom-empty').show();
        $('#bom-empty').html('<div style="text-align: center; color: #ff5722; padding: 50px;"><i class="layui-icon layui-icon-close" style="font-size: 48px;"></i><div style="margin-top: 15px;">加载BOM信息失败</div></div>');
    }

    function getProductId() {
        var urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }
}


</script>
{/block}
