# 库存管理系统设计方案

## 1. 项目概述

### 1.1 项目背景
基于现有ERP系统，新建一个简化的库存管理模块，控制器位于 `app/warehouse` 目录，视图参考 `Produce/order/index` 模版。

### 1.2 功能范围
- ✅ 实时库存管理（多仓库支持）
- ✅ 库存锁定机制（仅锁定和释放）
- ✅ 库存流水记录
- ✅ 仓库间调拨
- ✅ 库存盘点
- ❌ 不考虑批次管理
- ❌ 不需要预占机制
- ❌ 不需要过期库存提醒
- ❌ 不支持库位级别调拨

## 2. 系统架构

### 2.1 技术架构
```
Controller层 (app/warehouse/controller/)
    ↓
Service层 (app/warehouse/service/)
    ↓
Model层 (app/warehouse/model/)
    ↓
Database层 (MySQL数据库表)
```

### 2.2 核心模块
- **InventoryRealtime** - 实时库存管理
- **InventoryLock** - 库存锁定管理
- **InventoryTransaction** - 库存流水记录
- **InventoryTransfer** - 仓库间调拨
- **InventoryCheck** - 库存盘点 ✅

## 3. 数据库设计

### 3.1 实时库存表 (oa_inventory_realtime)
```sql
CREATE TABLE `oa_inventory_realtime` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总库存数量',
  `available_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '可用数量',
  `locked_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '锁定数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_inventory` (`product_id`,`warehouse_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时库存表';
```

### 3.2 库存锁定表 (oa_inventory_lock)
```sql
CREATE TABLE `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '锁定数量',
  `ref_type` varchar(50) NOT NULL COMMENT '关联类型(order,production,transfer等)',
  `ref_id` int(11) NOT NULL COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1锁定中,2已使用,3已释放',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_ref` (`ref_type`,`ref_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表';
```

### 3.3 库存流水表 (oa_inventory_transaction)
```sql
CREATE TABLE `oa_inventory_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
  `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
  `ref_id` int(11) DEFAULT 0 COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transaction_no` (`transaction_no`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_type` (`transaction_type`),
  KEY `idx_ref` (`ref_type`,`ref_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';
```

### 3.4 仓库间调拨单表 (oa_inventory_transfer)
```sql
CREATE TABLE `oa_inventory_transfer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_no` varchar(50) NOT NULL COMMENT '调拨单号',
  `from_warehouse_id` int(11) NOT NULL COMMENT '源仓库ID',
  `to_warehouse_id` int(11) NOT NULL COMMENT '目标仓库ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待审核,1已审核,2已完成,3已取消',
  `total_amount` decimal(10,2) DEFAULT 0 COMMENT '调拨总金额',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `approved_by` int(11) DEFAULT 0 COMMENT '审核人',
  `approved_time` int(11) DEFAULT 0 COMMENT '审核时间',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transfer_no` (`transfer_no`),
  KEY `idx_from_warehouse` (`from_warehouse_id`),
  KEY `idx_to_warehouse` (`to_warehouse_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库间调拨单表';
```

### 3.5 调拨单明细表 (oa_inventory_transfer_detail)
```sql
CREATE TABLE `oa_inventory_transfer_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL COMMENT '调拨单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '调拨数量',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
  `amount` decimal(10,2) DEFAULT 0 COMMENT '金额',
  `notes` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_transfer` (`transfer_id`),
  KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调拨单明细表';
```

## 4. 核心业务逻辑

### 4.1 库存锁定机制
- **锁定**：订单审核通过后锁定库存，减少可用数量
- **释放**：订单取消或完成后释放锁定的库存
- **使用**：实际出库时将锁定状态改为已使用

### 4.2 库存操作流程
1. **入库**：增加总库存和可用库存，记录流水
2. **出库**：减少总库存和可用库存，记录流水
3. **仓库间调拨**：源仓库减少，目标仓库增加，记录流水
4. **盘点**：调整库存数量，记录差异流水
5. **锁定**：减少可用库存，增加锁定库存，记录流水
6. **释放锁定**：增加可用库存，减少锁定库存，记录流水

### 4.3 数据一致性保证
- 使用数据库事务确保操作原子性
- 库存数量关系：`总库存 = 可用库存 + 锁定库存`
- 所有库存变动必须记录流水

## 5. 控制器设计

### 5.1 文件结构
```
app/warehouse/controller/
├── InventoryRealtime.php      # 实时库存管理 ✅
├── InventoryLock.php          # 库存锁定管理 ✅
├── InventoryTransaction.php   # 库存流水查询 ✅
├── InventoryTransfer.php      # 仓库间调拨 ✅
└── InventoryCheck.php         # 库存盘点 ✅
```

### 5.2 核心接口
- **库存查询**：按产品、仓库查询库存状态
- **库存锁定**：锁定、释放、使用操作
- **仓库间调拨**：创建调拨单、审核、执行调拨
- **库存盘点**：盘点单创建、执行、审核
- **库存流水**：查询库存变动历史

## 6. 前端界面设计

### 6.1 库存总览页面
参考 `Produce/order/index` 模版设计：
- **Tab切换**：全部库存、正常库存、锁定库存、低库存预警
- **搜索筛选**：产品名称/编码、仓库、数量范围
- **表格展示**：产品信息、总库存、可用库存、锁定库存、仓库分布
- **操作按钮**：查看详情、仓库调拨、手动盘点、库存锁定

### 6.2 调拨管理页面
- **Tab切换**：全部调拨、待审核、已完成、已取消
- **搜索筛选**：调拨单号、源仓库、目标仓库、时间范围
- **表格展示**：调拨单号、源仓库、目标仓库、状态、创建时间
- **操作按钮**：查看详情、审核、执行、取消

## 7. 服务层设计

### 7.1 库存服务类 (InventoryRealtimeService)
```php
- checkStock($productId, $warehouseId, $quantity)     // 检查库存是否充足
- increaseStock($productId, $warehouseId, $quantity)  // 增加库存
- decreaseStock($productId, $warehouseId, $quantity)  // 减少库存
- getInventoryStatus($productId, $warehouseId)        // 获取库存状态
- transferStock($productId, $fromWarehouse, $toWarehouse, $quantity) // 调拨库存
```

### 7.2 库存锁定服务类 (InventoryLockService)
```php
- lockInventory($productId, $warehouseId, $quantity, $refType, $refId) // 锁定库存
- unlockInventory($lockId)                            // 释放锁定
- useLock($lockId)                                    // 使用锁定库存
- getLocksByRef($refType, $refId)                     // 获取关联的锁定记录
```

## 8. 业务流程示例

### 8.1 生产订单锁库流程
1. 生产订单审核通过 → 调用锁定服务锁定原料库存
2. 投料时 → 将锁定状态改为已使用，实际减少库存
3. 订单取消 → 释放锁定的库存

### 8.2 仓库间调拨流程
1. 创建调拨单 → 锁定源仓库库存
2. 调拨单审核 → 执行库存转移
3. 源仓库减少库存 → 目标仓库增加库存 → 记录流水

## 9. 安全性和性能

### 9.1 安全性
- **并发控制**：使用数据库事务确保数据一致性
- **权限控制**：操作权限按角色分配
- **操作日志**：完整记录库存操作历史
- **数据校验**：严格的数据验证和业务规则检查

### 9.2 性能优化
- **索引优化**：为常用查询字段建立索引
- **分页查询**：避免大数据量查询性能问题
- **事务优化**：合理控制事务范围，避免长事务

## 10. 开发计划

### 10.1 开发阶段
1. **数据库设计** - 创建表结构和索引
2. **模型层开发** - 创建数据模型和基础操作
3. **服务层开发** - 实现核心业务逻辑
4. **控制器开发** - 实现API接口
5. **视图层开发** - 创建前端界面
6. **测试和优化** - 功能测试和性能优化

### 10.2 预期工期
- 数据库设计：1天
- 后端开发：3-4天
- 前端开发：2-3天
- 测试优化：1-2天
- **总计：7-10天**

## 11. 注意事项

1. **数据一致性**：所有库存操作必须在事务中进行
2. **并发处理**：考虑多用户同时操作的并发问题
3. **错误处理**：完善的异常处理和错误提示
4. **日志记录**：详细记录所有库存变动操作
5. **权限控制**：不同角色的操作权限限制
6. **性能监控**：关注查询性能，及时优化慢查询

---

*本方案基于现有ERP系统架构设计，专注于核心库存管理功能，去除了复杂的批次管理和预占机制，确保系统简洁高效。*

## 12. 最新优化记录

### 12.1 仓库间调拨功能优化（2025-08-02）

**优化背景**：
用户反馈调拨功能缺少必要的业务验证，存在以下问题：
1. 可以在未选择源仓库的情况下选择产品
2. 没有库存充足性验证
3. 用户体验不够友好

**优化内容**：

#### 12.1.1 前端交互限制
- **源仓库优先**：初始状态禁用产品选择，必须先选择源仓库
- **状态联动**：源仓库变化时清空所有产品选择并重新验证
- **视觉反馈**：禁用状态的按钮和输入框有明显的视觉区分

#### 12.1.2 产品搜索优化
- **实时搜索**：支持按产品名称或编号进行模糊搜索
- **库存显示**：搜索结果实时显示当前仓库的可用库存
- **状态区分**：
  - 绿色：有库存可调拨
  - 红色：无库存，禁止选择
  - 黄色：库存状态未知

#### 12.1.3 库存验证机制
- **前端验证**：
  - 产品选择时检查库存状态
  - 数量输入时设置最大值限制
  - 失焦时验证是否超过库存
- **后端验证**：
  - 提交时再次验证所有产品库存
  - 返回具体的错误信息（产品名称+数量）

#### 12.1.4 用户体验提升
- **智能提示**：数量输入框显示"最大可调拨: X"
- **错误提示**：明确的错误信息，指出具体问题
- **操作引导**：通过禁用状态引导用户正确操作流程

**技术实现**：
- 前端：JavaScript + Layui框架
- 后端：ThinkPHP控制器验证
- 数据库：实时库存表查询

**文件修改**：
- `app/warehouse/view/inventory_transfer/add.html` - 前端交互优化
- `app/warehouse/controller/InventoryTransfer.php` - 后端验证逻辑

### 12.2 库存盘点功能完成（2025-08-02）

**功能概述**：
完成了完整的库存盘点管理功能，包括盘点单创建、盘点执行、库存调整等核心流程。

**主要功能**：

#### 12.2.1 盘点单管理
- **创建盘点单**：支持选择仓库和产品，自动生成盘点单号
- **盘点状态管理**：待盘点 → 盘点中 → 已完成 → 已取消
- **盘点进度跟踪**：显示总产品数和已盘点产品数

#### 12.2.2 盘点执行
- **实时盘点**：支持逐个产品录入实际库存数量
- **差异计算**：自动计算系统库存与实际库存的差异
- **批量保存**：支持批量保存盘点结果

#### 12.2.3 库存调整
- **自动调整**：完成盘点后自动调整系统库存
- **流水记录**：记录所有库存调整的流水信息
- **差异分析**：区分盘盈、盘亏和正常情况

#### 12.2.4 统计报表
- **盘点统计**：显示盘点单数量、完成情况等
- **差异统计**：统计盘盈盘亏的数量和金额
- **时间范围查询**：支持按时间范围查询统计数据

**技术实现**：
- **模型层**：`InventoryCheck.php`、`InventoryCheckDetail.php`
- **服务层**：`InventoryCheckService.php` - 业务逻辑处理
- **控制器**：`InventoryCheck.php` - 接口和页面控制
- **视图层**：完整的前端界面（列表、新建、详情、统计）

**创建的文件**：
- `app/warehouse/model/InventoryCheck.php`
- `app/warehouse/model/InventoryCheckDetail.php`
- `app/warehouse/service/InventoryCheckService.php`
- `app/warehouse/controller/InventoryCheck.php`
- `app/warehouse/view/inventory_check/index.html`
- `app/warehouse/view/inventory_check/add.html`
- `app/warehouse/view/inventory_check/detail.html`
- `app/warehouse/view/inventory_check/statistics.html`
