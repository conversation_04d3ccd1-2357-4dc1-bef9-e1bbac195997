<?php
declare (strict_types = 1);
namespace app\engineering\controller;
use app\base\BaseController;
use app\engineering\model\BomMaster;
use app\engineering\model\BomItem;
use app\engineering\model\BomCheckRecord;
use app\engineering\model\BomChangeLog;
use app\engineering\validate\BomValidate;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Bom extends BaseController
{
    /**
     * 构造函数
     */
    protected $bomModel;
    protected $itemModel;
    protected $recordModel;
    protected $logModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->bomModel = new BomMaster();
        $this->itemModel = new BomItem();
        $this->recordModel = new BomCheckRecord();
        $this->logModel = new BomChangeLog();
    }
    
    /**
     * BOM管理首页
     */
    public function index()
    {
        return view();
    }
    
    /**
     * BOM列表
     */
    public function datalist()
    {
        if (request()->isAjax()) {
            $param = get_params();
            
            // 构建查询条件
            $where = [];
            if (!empty($param['keywords'])) {
                $where[] = ['bm.product_name|bm.bom_code', 'like', '%' . $param['keywords'] . '%'];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['bm.status', '=', $param['status']];
            }
            if (!empty($param['product_id'])) {
                $where[] = ['bm.product_id', '=', $param['product_id']];
            }
            
            // 直接使用数据库查询，确保使用正确的表名和字段
            try {
                // 分页参数
                $limit = isset($param['limit']) ? intval($param['limit']) : 10;
                $page = isset($param['page']) ? intval($param['page']) : 1;
                $start = ($page - 1) * $limit;
                
                // 排序
                $order = isset($param['order']) ? $param['order'] : 'id';
                $dir = isset($param['dir']) ? $param['dir'] : 'desc';
                
                // 查询总数
                $count = Db::name('bom_master')
                    ->alias('bm')
                    ->leftJoin('product p', 'bm.product_id = p.id')
                    ->where($where)
                    ->where('bm.delete_time', 0)
                    ->count();
                
                // 查询数据
                $list = Db::name('bom_master')
                    ->alias('bm')
                    ->leftJoin('product p', 'bm.product_id = p.id')
                    ->field('bm.*, p.title as product_title, p.material_code as product_material_code, p.source_type as product_source_type, p.specs as product_specs')
                    ->where($where)
                    ->where('bm.delete_time', 0)
                    ->limit($start, $limit)
                    ->order("bm.$order $dir")
                    ->select()
                    ->map(function($item) {
                        // 格式化状态
                        $statusMap = [
                            0 => '草稿',
                            1 => '审核中',
                            2 => '已审核',
                            3 => '已拒绝',
                            4 => '已作废'
                        ];
                        $item['status_text'] = $statusMap[$item['status']] ?? '未知';
                        
                        // 格式化时间
                        if (!empty($item['create_time'])) {
                            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                        }
                        if (!empty($item['update_time'])) {
                            $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
                        }
                        
                        // 获取子项数量
                        $item['items_count'] = Db::name('bom_item')
                            ->where('bom_id', $item['id'])
                            ->count();
                            
                        return $item;
                    });
                
                $result = [
                    'code' => 0,
                    'msg' => '',
                    'count' => $count,
                    'data' => $list
                ];
                return json($result);
            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg' => '获取BOM列表失败：' . $e->getMessage(),
                    'count' => 0,
                    'data' => []
                ]);
            }
        } else {
            return view();
        }
    }
    
    /**
     * 新增/编辑BOM
     */
    public function add()
    {
        if (request()->isAjax()) {
            try {
                // 获取参数
                $param = request()->param();
                
                // 确保必要的字段存在
                if (empty($param['product_id'])) {
                    return json(['code' => 1, 'msg' => '请选择产品', 'data' => []]);
                }
                
                if (empty($param['product_name'])) {
                    // 自动从产品获取名称
                    $product = Db::name('product')->where('id', $param['product_id'])->find();
                    if ($product) {
                        $param['product_name'] = $product['title'];
                    } else {
                        return json(['code' => 1, 'msg' => '选择的产品不存在', 'data' => []]);
                    }
                }
                
                // 设置默认版本号
                if (empty($param['version'])) {
                    $param['version'] = 'V1.0';
                }
                
                // 生成BOM编号
                if (empty($param['bom_code'])) {
                    $param['bom_code'] = $this->generateBomCode();
                }
                
                // 设置其他必要字段
                $param['create_time'] = time();
                $param['update_time'] = time();
                $param['status'] = 0; // 草稿状态
                $param['admin_id'] = $this->uid;
                $param['did'] = $this->did;

               
                
                // 验证数据
                $validate = new BomValidate();
                if (!$validate->scene('add')->check($param)) {
                    return json(['code' => 1, 'msg' => $validate->getError(), 'data' => []]);
                }
                
                // 从物料获取名称等信息
                if (!empty($param['material_id']) && (empty($param['material_name']) || empty($param['material_code']))) {
                    $material = Db::name('product')
                        ->where('id', $param['material_id'])
                        ->find();
                    
                    if ($material) {
                        $param['material_name'] = $material['title'];
                        $param['material_code'] = $material['material_code'] ?? '';
                        $param['uom_id'] = $material['uom_id'] ?? 0;
                        $param['uom_name'] = $material['unit'] ?? '';
                        $param['source_type'] = $material['source_type'] ?? 1;
                    }
                }
                
                // 不需要字段名兼容处理
                // 表单中使用的是 remark 字段，与数据库一致
                
                // 检查模型中是否有对应的修改动作
                try {
                    // 获取表结构
                    $fields = Db::getTableFields('bom_master');
                    
                    // 清理不存在的字段
                    foreach ($param as $key => $value) {
                        if (!in_array($key, $fields)) {
                            unset($param[$key]);
                        }
                    }
                } catch (\Exception $e) {
                    // 忽略获取表结构异常，继续尝试插入
                }
                
                // 直接使用数据库查询而非模型
                // 检查BOM编号和版本是否已存在
                $exists = Db::name('bom_master')
                    ->where('bom_code', $param['bom_code'])
                    ->where('version', $param['version'])
                    ->where('delete_time', 0)
                    ->find();
                
                if ($exists) {
                    return json(['code' => 1, 'msg' => '该BOM编号和版本号组合已存在，请修改后重试', 'data' => []]);
                }
                
                // 直接使用数据库插入
                Db::startTrans();
                try {
                    // 插入主表数据
                    $id = Db::name('bom_master')->insertGetId($param);
                    
                    if (!$id) {
                        Db::rollback();
                        return json(['code' => 1, 'msg' => '添加BOM主数据失败', 'data' => []]);
                    }
                    
                    // 添加变更日志（可选）
                    try {
                        Db::name('bom_change_log')->insert([
                            'bom_id' => $id,
                            'change_type' => 'create',
                            'change_desc' => '创建BOM',
                            'admin_id' => $this->uid,
                            'admin_name' => get_admin($this->uid)['name'] ?? '',
                            'create_time' => time()
                        ]);
                    } catch (\Exception $e) {
                        // 忽略日志添加错误
                    }
                    
                    Db::commit();
                    return json(['code' => 0, 'msg' => '添加BOM成功', 'data' => ['aid' => $id]]);
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '添加BOM数据失败：' . $e->getMessage(), 'data' => []]);
                }
            } catch (\PDOException $e) {
                return json(['code' => 1, 'msg' => '数据库错误：' . $e->getMessage(), 'data' => []]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
            } catch (\Throwable $e) {
                return json(['code' => 1, 'msg' => '未知错误：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            // 显示添加页面
            $products = Db::name('product')
                ->where('delete_time', 0)
                ->where('status', 1)
               // ->where('source_type', 1)
                ->field('id, title, material_code, unit, source_type')
                ->order('id desc')
                ->select()
                ->toArray();
                
            View::assign('product_list', $products);
            if (empty($products)) {
                View::assign('warnings', ['警告：未找到可用的产品数据，请先添加产品。']);
            }
            
            return View::fetch();
        }
    }
    
    /**
     * 查看BOM详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        echo "<!-- 正在加载BOM详情, ID: {$id} -->";
        
        // 直接使用Db查询获取BOM详情
        try {
            $detail = Db::name('bom_master')
                ->alias('bm')
                ->leftJoin('product p', 'bm.product_id = p.id')
                ->field('bm.*, p.title as product_title, p.material_code as product_material_code,p.specs as product_specs')
                ->where('bm.id', $id)
                ->where('bm.delete_time', 0)
                ->find();
            
            if (!$detail) {
                return redirect('/engineering/bom/index')->with('error', 'BOM不存在');
            }
            
            // 格式化状态和时间
            $statusMap = [0 => '草稿', 1 => '审核中', 2 => '已审核', 3 => '已拒绝', 4 => '已作废'];
            $detail['status_text'] = $statusMap[$detail['status']] ?? '未知';
            
            if (!empty($detail['create_time'])) {
                $detail['create_time_text'] = date('Y-m-d H:i:s', $detail['create_time']);
            }
            if (!empty($detail['update_time'])) {
                $detail['update_time_text'] = date('Y-m-d H:i:s', $detail['update_time']);
            }
            
            // 获取管理员信息
            if (!empty($detail['admin_id'])) {
                $admin = get_admin($detail['admin_id']);
                $detail['admin_name'] = $admin['name'] ?? '';
            }
            
            // 获取审核员信息
            if (!empty($detail['check_admin_id'])) {
                $admin = get_admin($detail['check_admin_id']);
                $detail['check_admin_name'] = $admin['name'] ?? '';
            }
            
            // 获取BOM子项
            $items = $this->itemModel->getItemsByBomId($id);
            if (!empty($items)) {
                foreach ($items as &$item) {
                    if (!isset($item['is_needed'])) {
                        $item['is_needed'] = 1; // 默认为需要
                    }
                    // 确保product_specs字段存在
                    if (!isset($item['product_specs'])) {
                        // 从数据库中查询该物料的规格
                        try {
                            $product = \think\facade\Db::name('product')
                                ->where('id', $item['material_id'])
                                ->field('specs')
                                ->find();
                                
                            \think\facade\Log::record('物料ID ' . $item['material_id'] . ' 的规格查询结果: ' . json_encode($product, JSON_UNESCAPED_UNICODE), 'info');
                            
                            if ($product && isset($product['specs'])) {
                                $item['product_specs'] = $product['specs'];
                            } else {
                                $item['product_specs'] = ''; // 默认为空字符串
                            }
                        } catch (\Exception $e) {
                            \think\facade\Log::record('查询物料规格失败: ' . $e->getMessage(), 'error');
                            $item['product_specs'] = ''; // 失败时设置为空字符串
                        }
                    }
                }
            }
            

            
            // 获取审核记录
            $records = $this->recordModel->getRecordsByBomId($id);
            
            // 获取变更记录
            $logs = $this->logModel->getVersionHistory($detail['bom_code']);
            
            View::assign('detail', $detail);
            View::assign('items', $items);
            View::assign('records', $records);
            View::assign('logs', $logs);
            return view();
        } catch (\Exception $e) {
            return redirect('/engineering/bom/index')->with('error', '获取BOM详情失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除BOM
     */
    public function del()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        if (request()->isDelete()) {
            try {
                // 检查BOM状态
                $bom = Db::name('bom_master')->where('id', $id)->find();
                if (!$bom) {
                    return json(['code' => 1, 'msg' => 'BOM不存在', 'data' => []]);
                }
                
                if ($bom['status'] == 1 || $bom['status'] == 2) {
                    return json(['code' => 1, 'msg' => '审核中或已审核的BOM不能删除', 'data' => []]);
                }
                
                // 开启事务
                Db::startTrans();
                
                // 直接删除BOM或标记为已删除
                // Db::name('bom_master')->where('id', $id)->update([
                //     'status' => 4, // 标记为已作废
                //     'delete_time' => time(),
                // ]);
                Db::name('bom_master')->where('id', $id)->delete();

                // 直接删除BOM子项或标记为已删除
                Db::name('bom_item')->where('bom_id', $id)->delete();
                
                // 提交事务
                Db::commit();
                
                return json(['code' => 0, 'msg' => '删除成功', 'data' => []]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code' => 1, 'msg' => '删除失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            return json(['code' => 1, 'msg' => '错误的请求', 'data' => []]);
        }
    }
    
    /**
     * BOM子项管理
     */
    public function items()
    {
        $param = get_params();
        $bom_id = isset($param['bom_id']) ? intval($param['bom_id']) : 0;
        
        if (request()->isAjax()) {
            try {
                // 获取BOM子项
                $items = $this->itemModel->getItemsByBomId($bom_id);

                // 调试：记录查询结果到日志
                //\think\facade\Log::record('BOM子项查询结果原始数据: ' . json_encode($items, JSON_UNESCAPED_UNICODE), 'info');

                // 注意：如果数据库中没有 is_needed 字段，需要执行以下 SQL 添加该字段：
                // ALTER TABLE `bom_item` ADD COLUMN `is_needed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要：1-需要，0-不需要';
                
                // 确保每个子项都有 is_needed 字段，如果数据库中没有，默认为 1（需要）
                if (!empty($items)) {
                    foreach ($items as &$item) {
                        if (!isset($item['is_needed'])) {
                            $item['is_needed'] = 1; // 默认为需要
                        }
                        // 确保product_specs字段存在
                        if (!isset($item['product_specs'])) {
                            // 从数据库中查询该物料的规格
                            try {
                                $product = \think\facade\Db::name('product')
                                    ->where('id', $item['material_id'])
                                    ->field('specs')
                                    ->find();
                                    
                                \think\facade\Log::record('物料ID ' . $item['material_id'] . ' 的规格查询结果: ' . json_encode($product, JSON_UNESCAPED_UNICODE), 'info');
                                
                                if ($product && isset($product['specs'])) {
                                    $item['product_specs'] = $product['specs'];
                                } else {
                                    $item['product_specs'] = ''; // 默认为空字符串
                                }
                            } catch (\Exception $e) {
                                \think\facade\Log::record('查询物料规格失败: ' . $e->getMessage(), 'error');
                                $item['product_specs'] = ''; // 失败时设置为空字符串
                            }
                        }
                    }
                }
                
                // 调试：记录处理后的查询结果到日志
                \think\facade\Log::record('BOM子项查询结果处理后: ' . json_encode($items, JSON_UNESCAPED_UNICODE), 'info');
                
                return json(['code' => 0, 'msg' => '', 'data' => $items]);
            } catch (\Exception $e) {
                \think\facade\Log::record('获取BOM子项失败: ' . $e->getMessage(), 'error');
                return json(['code' => 1, 'msg' => '获取BOM子项失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            try {
                // 获取BOM信息
                $detail = Db::name('bom_master')
                    ->where('id', $bom_id)
                    ->find();
                
                if (!$detail) {
                    return redirect('/engineering/bom/index')->with('error', 'BOM不存在');
                }
                

                 View::assign('detail', $detail);
                return view();
            } catch (\Exception $e) {
                return redirect('/engineering/bom/index')->with('error', '获取BOM信息失败：' . $e->getMessage());
            }
        }
    }
    
    /**
     * 添加BOM子项
     */
    public function addItem()
    {
        $param = get_params();
        
        
        if (request()->isAjax()) {
            try {
                // 检查BOM状态
                $bom_id = isset($param['bom_id']) ? intval($param['bom_id']) : 0;
                $bom = Db::name('bom_master')
                    ->where('id', $bom_id)
                    ->find();
                
                if (!$bom) {
                    return json(['code' => 1, 'msg' => 'BOM不存在', 'data' => []]);
                }
                
                if ($bom['status'] != 0 && $bom['status'] != 3) {
                    return json(['code' => 1, 'msg' => '只有草稿或已拒绝状态的BOM才能添加子项', 'data' => []]);
                }
                
                // 从物料获取名称等信息
                if (!empty($param['material_id']) && (empty($param['material_name']) || empty($param['material_code']))) {
                    $material = Db::name('product')
                        ->where('id', $param['material_id'])
                        ->find();
                    
                    if ($material) {
                        $param['material_name'] = $material['title'];
                        $param['material_code'] = $material['material_code'] ?? '';
                        $param['uom_id'] = $material['uom_id'] ?? 0;
                        $param['uom_name'] = $material['unit'] ?? '';
                        $param['source_type'] = $material['source_type'] ?? 1;
                    }
                }
                
                // 不需要字段名兼容处理
                // 表单中使用的是 remark 字段，与数据库一致
                
                // 准备要插入的数据
                $data = [
                    'bom_id' => $bom_id,
                    'material_id' => $param['material_id'] ?? 0,
                    'material_name' => $param['material_name'] ?? '',
                    'material_code' => $param['material_code'] ?? '',
                    'qty' => $param['qty'] ?? 0,
                    'loss_rate' => $param['loss_rate'] ?? 0,
                    'uom_id' => $param['uom_id'] ?? 0,
                    'uom_name' => $param['uom_name'] ?? '',
                    'source_type' => $param['source_type'] ?? 1,
                    'position' => $param['position'] ?? '',
                    'remark' => $param['remark'] ?? ''
                ];
                
                // 检查必填项
                if (empty($data['material_id']) || empty($data['material_name'])) {
                    return json(['code' => 1, 'msg' => '请选择物料', 'data' => []]);
                }
                
                if (empty($data['qty']) || $data['qty'] <= 0) {
                    return json(['code' => 1, 'msg' => '用量必须大于0', 'data' => []]);
                }
                
                // 检查是否已存在相同物料
                $exists = Db::name('bom_item')
                    ->where('bom_id', $bom_id)
                    ->where('material_id', $data['material_id'])
                    ->find();
                
                if ($exists) {
                    return json(['code' => 1, 'msg' => '该BOM中已存在此物料，请勿重复添加', 'data' => []]);
                }
                
                // 插入数据
                $result = Db::name('bom_item')->insertGetId($data);
                
                if ($result) {
                    // 更新BOM的修改时间
                    Db::name('bom_master')
                        ->where('id', $bom_id)
                        ->update(['update_time' => time()]);
                    
                    return json(['code' => 0, 'msg' => '添加子项成功', 'data' => ['id' => $result]]);
                } else {
                    return json(['code' => 1, 'msg' => '添加子项失败', 'data' => []]);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '添加子项失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            $bom_id = isset($param['bom_id']) ? intval($param['bom_id']) : 0;
            
           
            // 检查BOM ID是否有效
            if (empty($bom_id) || $bom_id <= 0) {
               
                return redirect('/engineering/bom/index')->with('error', '无效的BOM ID，请从BOM列表进入');
            }
           
            try {
                // 获取BOM信息
                $bom = Db::name('bom_master')
                    ->where('id', $bom_id)
                    ->find();
                
                if (!$bom) {
                    return redirect('/engineering/bom/index')->with('error', 'BOM不存在');
                }
                
                
                // 获取可用物料列表
                $materials = Db::name('product')
                    ->where('status', 1) // 假设状态1为可用
                    ->field('id, title, material_code, unit, source_type')
                    ->order('id desc')
                    ->select()
                    ->toArray();
                
                // 获取单位列表
                $uom_list ='';
                
                View::assign('bom', $bom);
                View::assign('material_list', $materials);
                View::assign('uom_list', $uom_list);
                return view('add_item');
            } catch (\Exception $e) {
                return redirect('/engineering/bom/index')->with('error', '获取BOM信息失败：' . $e->getMessage());
            }
        }
    }
    
    /**
     * 编辑BOM子项
     */
    public function editItem()
    {
        $param = get_params();
        if (request()->isAjax()) {
            try {
                $id = isset($param['id']) ? intval($param['id']) : 0;
                
                // 检查子项是否存在
                $item = Db::name('bom_item')
                    ->where('id', $id)
                    ->find();
                
                if (!$item) {
                    return json(['code' => 1, 'msg' => '子项不存在', 'data' => []]);
                }
                
                // 检查BOM状态
                $bom = Db::name('bom_master')
                    ->where('id', $item['bom_id'])
                    ->where('delete_time', 0)
                    ->find();
                
                if (!$bom) {
                    return json(['code' => 1, 'msg' => 'BOM不存在', 'data' => []]);
                }
                
                if ($bom['status'] != 0 && $bom['status'] != 3) {
                    return json(['code' => 1, 'msg' => '只有草稿或已拒绝状态的BOM才能编辑子项', 'data' => []]);
                }
                
                // 准备要更新的数据
                $data = [
                    'qty' => $param['qty'] ?? $item['qty'],
                    'loss_rate' => $param['loss_rate'] ?? $item['loss_rate'],
                    'position' => $param['position'] ?? $item['position'],
                    'remark' => $param['remark'] ?? $item['remark']
                ];
                
                // 检查必填项
                if (empty($data['qty']) || $data['qty'] <= 0) {
                    return json(['code' => 1, 'msg' => '用量必须大于0', 'data' => []]);
                }
                
                // 更新数据
                $result = Db::name('bom_item')
                    ->where('id', $id)
                    ->update($data);
                
                if ($result !== false) {
                    // 更新BOM的修改时间
                    Db::name('bom_master')
                        ->where('id', $item['bom_id'])
                        ->update(['update_time' => time()]);
                    
                    return json(['code' => 0, 'msg' => '更新子项成功', 'data' => []]);
                } else {
                    return json(['code' => 1, 'msg' => '更新子项失败', 'data' => []]);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '更新子项失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            $id = isset($param['id']) ? intval($param['id']) : 0;
            try {
                // 获取子项信息
                $item = Db::name('bom_item')
                    ->where('id', $id)
                    ->find();
                
                if (!$item) {
                    return redirect('/engineering/bom/items?bom_id=' . $param['bom_id'])->with('error', '子项不存在');
                }
                
                // 获取BOM信息
                $bom = Db::name('bom_master')
                    ->where('id', $item['bom_id'])
                    ->find();
                
                if (!$bom) {
                    return redirect('/engineering/bom/index')->with('error', 'BOM不存在');
                }
                
                View::assign('item', $item);
                View::assign('bom', $bom);
                return view('edit_item');
            } catch (\Exception $e) {
                return redirect('/engineering/bom/index')->with('error', '获取数据失败：' . $e->getMessage());
            }
        }
    }
    
    /**
     * 删除BOM子项
     */
    public function delItem()
    {
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if (request()->isDelete()) {
            try {
                // 检查子项是否存在
                $item = Db::name('bom_item')
                    ->where('id', $id)
                    ->find();
                
                if (!$item) {
                    return json(['code' => 1, 'msg' => '子项不存在', 'data' => []]);
                }
                
                // 检查BOM状态
                $bom = Db::name('bom_master')
                    ->where('id', $item['bom_id'])
                    ->find();
                
                if (!$bom) {
                    return json(['code' => 1, 'msg' => 'BOM不存在', 'data' => []]);
                }
                
                if ($bom['status'] != 0 && $bom['status'] != 3) {
                    return json(['code' => 1, 'msg' => '只有草稿或已拒绝状态的BOM才能删除子项', 'data' => []]);
                }
                
                // 直接删除子项
                $result = Db::name('bom_item')
                    ->where('id', $id)
                    ->delete();
                
                if ($result !== false) {
                    // 更新BOM的修改时间
                    Db::name('bom_master')
                        ->where('id', $item['bom_id'])
                        ->update(['update_time' => time()]);
                    
                    return json(['code' => 0, 'msg' => '删除子项成功', 'data' => []]);
                } else {
                    return json(['code' => 1, 'msg' => '删除子项失败', 'data' => []]);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '删除子项失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            return json(['code' => 1, 'msg' => '错误的请求', 'data' => []]);
        }
    }
    
    /**
     * 批量导入BOM子项
     */
    public function importItems()
    {
        $param = get_params();
        if (request()->isAjax()) {
            // 检查上传文件
            if (empty($_FILES['file'])) {
                return to_assign(1, '请选择要导入的文件');
            }
            
            // 检查BOM状态
            $bom_id = $param['bom_id'];
            $bom = $this->bomModel->getById($bom_id);
            if (!$bom) {
                return to_assign(1, 'BOM不存在');
            }
            if ($bom['status'] != 0 && $bom['status'] != 3) {
                return to_assign(1, '只有草稿或已拒绝状态的BOM才能导入子项');
            }
            
            // 解析Excel文件
            $items = $this->parseExcel($_FILES['file']['tmp_name']);
            if (empty($items)) {
                return to_assign(1, '导入文件格式错误或没有数据');
            }
            
            // 添加BOM ID
            foreach ($items as &$item) {
                $item['bom_id'] = $bom_id;
            }
            
            // 批量添加子项
            $result = $this->itemModel->batchAdd($items);
            return $result;
        } else {
            $bom_id = isset($param['bom_id']) ? $param['bom_id'] : 0;
            $bom = $this->bomModel->getById($bom_id);
            View::assign('bom', $bom);
            return view();
        }
    }
    
    /**
     * 更新子项排序
     */
    public function updateItemSort()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $items = $param['items'];
            if (empty($items)) {
                return to_assign(1, '参数错误');
            }
            
            $result = $this->itemModel->updateItemSort($items);
            return $result;
        }
    }
    
    /**
     * 提交审核
     */
    public function submitCheck()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $reason = isset($param['reason']) ? $param['reason'] : '';
            
            // 创建调试信息数组
            $debug = [];
            $debug[] = '请求参数: ' . json_encode($param, JSON_UNESCAPED_UNICODE);
            
            // 确保ID是数字
            if ($id <= 0) {
                return to_assign(1, 'BOM ID无效');
            }
            
            try {
                // 检查BOM是否存在
                try {
                    $bom = Db::name('bom_master')->where('id', $id)->find();
                    if (!$bom) {
                        return to_assign(1, 'BOM不存在');
                    }
                    
                    $debug[] = '获取BOM信息成功: ID=' . $id;
                } catch (\Exception $e) {
                    $debug[] = '获取BOM失败: ' . $e->getMessage();
                    return to_assign(1, '获取BOM信息失败: ' . $e->getMessage());
                }
                
                // 检查状态
                if ($bom['status'] != 0 && $bom['status'] != 3) {
                    return to_assign(1, '只有草稿或已拒绝状态的BOM才能提交审核');
                }
                
                // 检查子项
                try {
                    $items_count = Db::name('bom_item')->where('bom_id', $id)->count();
                    $debug[] = 'BOM子项数量: ' . $items_count;
                    
                    if ($items_count == 0) {
                        return to_assign(1, 'BOM未添加子项，不能提交审核');
                    }
                } catch (\Exception $e) {
                    $debug[] = '获取BOM子项失败: ' . $e->getMessage();
                    return to_assign(1, '获取BOM子项信息失败: ' . $e->getMessage());
                }
                
                // 获取当前用户信息
                $admin_info = get_admin($this->uid);
                $admin_name = isset($admin_info['name']) ? $admin_info['name'] : '';
                $debug[] = '当前用户: ID=' . $this->uid . ', Name=' . $admin_name;
                
                // 开启事务
                Db::startTrans();
                try {
                    // 1. 更新BOM状态
                    $now = time();
                    $debug[] = '准备更新BOM状态为审核中';
                    
                    $update_result = Db::name('bom_master')
                        ->where('id', $id)
                        ->update([
                            'status' => 1, // 审核中
                            'update_time' => $now
                        ]);
                    
                    $debug[] = '更新BOM状态结果: ' . $update_result;
                    
                    if ($update_result === false) {
                        throw new \Exception('更新BOM状态失败');
                    }
                    
                    // 2. 添加审核记录
                    $record_data = [
                        'bom_id' => $id,
                        'admin_id' => $this->uid,
                        'admin_name' => $admin_name,
                        'check_time' => $now,
                        'status' => 0, // 提交审核
                        'step' => 1,
                        'reason' => $reason,
                        'create_time' => $now,
                        'update_time' => $now
                    ];
                    
                    $debug[] = '准备插入审核记录: ' . json_encode($record_data, JSON_UNESCAPED_UNICODE);
                    
                    $insert_result = Db::name('bom_check_record')->insert($record_data);
                    $debug[] = '插入审核记录结果: ' . ($insert_result ? '成功' : '失败');
                    
                    if (!$insert_result) {
                        throw new \Exception('添加审核记录失败');
                    }
                    
                    // 3. 记录变更日志(可选)
                    try {
                        $log_data = [
                            'bom_id' => $id,
                            'change_type' => 'submit_check',
                            'change_desc' => '提交审核',
                            'admin_id' => $this->uid,
                            'admin_name' => $admin_name,
                            'create_time' => $now
                        ];
                        Db::name('bom_change_log')->insert($log_data);
                    } catch (\Exception $e) {
                        // 忽略日志添加错误，不影响主流程
                        $debug[] = '添加变更日志异常(忽略): ' . $e->getMessage();
                    }
                    
                    // 提交事务
                    Db::commit();

                    // 确保在事务成功后立即返回成功响应
                    return to_assign(0, '提交审核成功');
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $debug[] = '事务处理异常: ' . $e->getMessage();
                    return to_assign(1, '操作失败: ' . $e->getMessage());
                }
            } catch (\PDOException $e) {
                $debug[] = 'PDO数据库异常: ' . $e->getMessage();
                return to_assign(1, '数据库操作异常: ' . $e->getMessage());
            }  
        } else {
            $param = get_params();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            
            // 使用直接的数据库查询获取BOM信息
            $bom = Db::name('bom_master')->where('id', $id)->find();
            
            if ($bom) {
                // 格式化状态文本
                $statusMap = [
                    0 => '草稿',
                    1 => '审核中', 
                    2 => '已审核', 
                    3 => '已拒绝', 
                    4 => '已作废'
                ];
                $bom['status_text'] = isset($statusMap[$bom['status']]) ? $statusMap[$bom['status']] : '未知状态';
                
                // 格式化时间
                if (isset($bom['create_time'])) {
                    $bom['create_time_text'] = date('Y-m-d H:i:s', $bom['create_time']);
                }
                if (isset($bom['update_time'])) {
                    $bom['update_time_text'] = date('Y-m-d H:i:s', $bom['update_time']);
                }
            }
            
            View::assign('bom', $bom);
            return view('submit_check');
        }
    }
    
    /**
     * 审核操作
     */
    public function check()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'];
            $status = $param['status']; // 1-通过，2-拒绝
            $reason = $param['reason'] ?? '';
            
            // 检查BOM状态
            $bom = $this->bomModel->getById($id);
            if (!$bom) {
                return to_assign(1, 'BOM不存在');
            }
            if ($bom['status'] != 1) {
                return to_assign(1, '只有审核中状态的BOM才能进行审核操作');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 获取上一条审核记录
                $last_record = $this->recordModel->getLastRecord($id);
                $step = $last_record ? $last_record['step'] + 1 : 1;
                
                // 更新BOM状态
                $this->bomModel->updateStatus($id, $status == 1 ? 2 : 3, $this->uid); // 2-已审核，3-已拒绝
                
                // 添加审核记录
                $record_data = [
                    'bom_id' => $id,
                    'admin_id' => $this->uid,
                    'admin_name' => get_admin($this->uid)['name'],
                    'check_time' => time(),
                    'status' => $status, // 1-审核通过，2-审核拒绝
                    'step' => $step,
                    'reason' => $reason,
                ];
                $this->recordModel->add($record_data);
                
                // 如果审核通过，设置为标准BOM
                if ($status == 1) {
                    $this->bomModel->where('id', $id)->update(['is_standard' => 1]);
                }
                
                // 提交事务
                Db::commit();
                
                return to_assign(0, $status == 1 ? '审核通过成功' : '审核拒绝成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '操作失败，原因：'.$e->getMessage());
            }
        } else {
            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;
            $bom = $this->bomModel->getById($id);
            
            // 获取BOM子项
            $items = $this->itemModel->getItemsByBomId($id);
            
            // 获取审核记录
            $records = $this->recordModel->getRecordsByBomId($id);
            
            View::assign('bom', $bom);
            View::assign('items', $items);
            View::assign('records', $records);
            return view();
        }
    }
    
    /**
     * 撤回审核
     */
    public function cancelCheck()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'];
            $reason = $param['reason'] ?? '';
            
            // 检查BOM状态
            $bom = $this->bomModel->getById($id);
            if (!$bom) {
                return to_assign(1, 'BOM不存在');
            }
            if ($bom['status'] != 1) {
                return to_assign(1, '只有审核中状态的BOM才能撤回审核');
            }
            
            // 检查是否是提交人
            $last_record = $this->recordModel->getLastRecord($id);
            if (!$last_record || $last_record['admin_id'] != $this->uid) {
                return to_assign(1, '只有提交人才能撤回审核');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 更新BOM状态
                $this->bomModel->updateStatus($id, 0); // 0-草稿
                
                // 添加审核记录
                $record_data = [
                    'bom_id' => $id,
                    'admin_id' => $this->uid,
                    'admin_name' => get_admin($this->uid)['name'],
                    'check_time' => time(),
                    'status' => 3, // 3-撤回审核
                    'step' => $last_record ? $last_record['step'] + 1 : 1,
                    'reason' => $reason,
                ];
                $this->recordModel->add($record_data);
                
                // 提交事务
                Db::commit();
                
                return to_assign(0, '撤回审核成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '操作失败，原因：'.$e->getMessage());
            }
        } else {
            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;
            $bom = $this->bomModel->getById($id);
            View::assign('bom', $bom);
            return view();
        }
    }
    
    /**
     * 作废BOM
     */
    public function invalid()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'];
            $reason = $param['reason'] ?? '';
            
            // 检查BOM状态
            $bom = $this->bomModel->getById($id);
            if (!$bom) {
                return to_assign(1, 'BOM不存在');
            }
            if ($bom['status'] != 2) {
                return to_assign(1, '只有已审核状态的BOM才能作废');
            }
            
            // 开启事务
            Db::startTrans();
            try {
                // 更新BOM状态
                $this->bomModel->updateStatus($id, 4); // 4-已作废
                
                // 添加审核记录
                $record_data = [
                    'bom_id' => $id,
                    'admin_id' => $this->uid,
                    'admin_name' => get_admin($this->uid)['name'],
                    'check_time' => time(),
                    'status' => 4, // 4-反确认(作废)
                    'step' => 1,
                    'reason' => $reason,
                ];
                $this->recordModel->add($record_data);
                
                // 添加变更日志
                $log_data = [
                    'bom_id' => $id,
                    'bom_code' => $bom['bom_code'],
                    'version_from' => $bom['version'],
                    'version_to' => $bom['version'],
                    'change_type' => 3, // 作废
                    'change_reason' => $reason,
                    'change_content' => '作废BOM',
                    'admin_id' => $this->uid,
                    'admin_name' => get_admin($this->uid)['name'],
                    'create_time' => time()
                ];
                $this->logModel->add($log_data);
                
                // 提交事务
                Db::commit();
                
                return to_assign(0, 'BOM作废成功');
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return to_assign(1, '操作失败，原因：'.$e->getMessage());
            }
        } else {
            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;
            $bom = $this->bomModel->getById($id);
            View::assign('bom', $bom);
            return view();
        }
    }
    
    /**
     * 创建新版本
     */
    public function createVersion()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = $param['id'];
            $version = $param['version'];
            $reason = $param['reason'] ?? '';
            
            // 检查BOM状态
            $bom = $this->bomModel->getById($id);
            if (!$bom) {
                return to_assign(1, 'BOM不存在');
            }
            
            // 检查版本号是否已存在
            $exists = Db::name('BomMaster')
                ->where('bom_code', $bom['bom_code'])
                ->where('version', $version)
                ->where('delete_time', 0)
                ->find();
                
            if ($exists) {
                return to_assign(1, '该版本号已存在，请使用其他版本号');
            }
            
            $result = $this->bomModel->createNewVersion($id, $version, $reason, $this->uid);
            return $result;
        } else {
            $param = get_params();
            $id = isset($param['id']) ? $param['id'] : 0;
            $bom = $this->bomModel->getById($id);
            
            // 生成推荐版本号
            $version = $this->generateNextVersion($bom['version']);
            
            View::assign('bom', $bom);
            View::assign('version', $version);
            return view();
        }
    }
    
    /**
     * 获取BOM版本历史
     */
    public function versionHistory()
    {
        $param = get_params();
        if (request()->isAjax()) {
            $bom_code = $param['bom_code'];
            $logs = $this->logModel->getVersionHistory($bom_code);
            return to_assign(0, '', $logs);
        } else {
            $bom_code = isset($param['bom_code']) ? $param['bom_code'] : '';
            $bom_id = isset($param['id']) ? $param['id'] : 0;
            
            if ($bom_id > 0) {
                $bom = $this->bomModel->getById($bom_id);
                if ($bom) {
                    $bom_code = $bom['bom_code'];
                }
            }
            
            View::assign('bom_code', $bom_code);
            return view();
        }
    }
    
    /**
     * 比较两个版本的BOM
     */
    public function compareVersion()
    {
        $param = get_params();
        if (request()->isAjax()) {
            $bom_id1 = $param['bom_id1'];
            $bom_id2 = $param['bom_id2'];
            
            // 获取两个BOM的子项
            $items1 = $this->itemModel->getItemsByBomId($bom_id1)->toArray();
            $items2 = $this->itemModel->getItemsByBomId($bom_id2)->toArray();
            
            // 进行比较
            $result = $this->compareItems($items1, $items2);
            
            return to_assign(0, '', $result);
        } else {
            $bom_code = isset($param['bom_code']) ? $param['bom_code'] : '';
            
            // 获取所有版本的BOM
            $bom_list = Db::name('BomMaster')
                ->where('bom_code', $bom_code)
                ->where('delete_time', 0)
                ->order('id desc')
                ->select();
                
            View::assign('bom_list', $bom_list);
            View::assign('bom_code', $bom_code);
            return view();
        }
    }
    
    /**
     * 导出BOM
     */
    public function export()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        
        // 获取BOM信息
        $bom = $this->bomModel->getById($id);
        if (!$bom) {
            return to_assign(1, 'BOM不存在');
        }
        
        // 获取BOM子项
        $items = $this->itemModel->getItemsByBomId($id);
        
        // 导出Excel
        $this->exportToExcel($bom, $items);
    }
    
    /**
     * 自动生成BOM编号
     */
    private function generateBomCode()
    {
        // 创建基本编号
        $prefix = 'BOM'; // 前缀
        $date = date('Ymd', time()); // 日期部分
        $randomPart = mt_rand(1000, 9999); // 随机数部分
        $code = $prefix . $date . $randomPart;
        
        // 检查是否已存在，如果存在则重新生成
        $count = 0;
        $originalCode = $code;
        while ($this->bomModel->where('bom_code', $code)->where('delete_time', 0)->find() && $count < 10) {
            $randomPart = mt_rand(1000, 9999);
            $code = $originalCode . '_' . $randomPart;
            $count++;
        }
        
        return $code;
    }
    
    /**
     * 生成下一个版本号
     */
    private function generateNextVersion($version)
    {
        // 检查当前版本号格式
        if (empty($version)) {
            // 如果当前版本为空，返回默认版本号
            return 'V1.0';
        }
        
        // 尝试匹配标准版本格式 Vx.y
        if (preg_match('/V(\d+)\.(\d+)/', $version, $matches)) {
            $major = $matches[1];
            $minor = $matches[2];
            $newVersion = 'V' . $major . '.' . ($minor + 1);
            return $newVersion;
        } 
        
        // 尝试匹配其他常见格式，如 x.y.z
        if (preg_match('/(\d+)\.(\d+)(?:\.(\d+))?/', $version, $matches)) {
            if (isset($matches[3])) {
                // 有三段式版本号 x.y.z
                $newVersion = $matches[1] . '.' . $matches[2] . '.' . ($matches[3] + 1);
            } else {
                // 两段式版本号 x.y
                $newVersion = $matches[1] . '.' . ($matches[2] + 1);
            }
            return $newVersion;
        }
        
        // 其他不规则格式，添加后缀
        $newVersion = $version . '_new';
        return $newVersion;
    }
    
    /**
     * 解析Excel文件
     */
    private function parseExcel($file)
    {
        // 这里只是示例，实际项目中需要根据具体的Excel库来实现
        // 例如可以使用PHPExcel或者PHPSpreadsheet
        // 返回格式为[['material_id'=>1, 'material_name'=>'xxx', 'qty'=>10, ...], ...]
        return [];
    }
    
    /**
     * 导出Excel文件
     */
    private function exportToExcel($bom, $items)
    {
        // 这里只是示例，实际项目中需要根据具体的Excel库来实现
        // 例如可以使用PHPExcel或者PHPSpreadsheet
    }
    
    /**
     * 比较两个版本的BOM子项
     */
    private function compareItems($items1, $items2)
    {
        $result = [
            'added' => [], // 在items2中新增的
            'removed' => [], // 在items1中但不在items2中的
            'changed' => [], // 在两者中都有但内容不同的
            'unchanged' => [], // 完全相同的
        ];
        
        // 创建物料ID索引
        $items1_index = [];
        foreach ($items1 as $item) {
            $key = $item['material_id'];
            $items1_index[$key] = $item;
        }
        
        $items2_index = [];
        foreach ($items2 as $item) {
            $key = $item['material_id'];
            $items2_index[$key] = $item;
        }
        
        // 查找变化
        foreach ($items2 as $item2) {
            $key = $item2['material_id'];
            if (!isset($items1_index[$key])) {
                // 新增的项
                $result['added'][] = $item2;
            } else {
                $item1 = $items1_index[$key];
                // 判断是否有变化
                if ($item1['qty'] != $item2['qty'] || $item1['loss_rate'] != $item2['loss_rate']) {
                    // 有变化
                    $result['changed'][] = [
                        'old' => $item1,
                        'new' => $item2
                    ];
                } else {
                    // 无变化
                    $result['unchanged'][] = $item2;
                }
            }
        }
        
        // 查找删除的项
        foreach ($items1 as $item1) {
            $key = $item1['material_id'];
            if (!isset($items2_index[$key])) {
                // 删除的项
                $result['removed'][] = $item1;
            }
        }
        
        return $result;
    }

    /**
     * 切换子项的"是否需要"状态
     */
    public function toggleItemNeeded()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $id = isset($param['id']) ? intval($param['id']) : 0;
            $status = isset($param['status']) ? intval($param['status']) : 1; // 1-需要，0-不需要
            
            try {
                // 检查子项是否存在
                $item = Db::name('bom_item')
                    ->where('id', $id)
                    ->find();
                
                if (!$item) {
                    return json(['code' => 1, 'msg' => '子项不存在', 'data' => []]);
                }
                
                // 检查BOM状态
                $bom = Db::name('bom_master')
                    ->where('id', $item['bom_id'])
                    ->where('delete_time', 0)
                    ->find();
                
                if (!$bom) {
                    return json(['code' => 1, 'msg' => 'BOM不存在', 'data' => []]);
                }
                
                if ($bom['status'] != 0 && $bom['status'] != 3) {
                    return json(['code' => 1, 'msg' => '只有草稿或已拒绝状态的BOM才能修改子项', 'data' => []]);
                }
                
                // 更新子项的"是否需要"状态
                // 注意：如果数据库中没有 is_needed 字段，需要先添加该字段
                $result = Db::name('bom_item')
                    ->where('id', $id)
                    ->update(['is_needed' => $status]);
                
                if ($result !== false) {
                    // 更新BOM的修改时间
                    Db::name('bom_master')
                        ->where('id', $item['bom_id'])
                        ->update(['update_time' => time()]);
                    
                    return json(['code' => 0, 'msg' => '更新成功', 'data' => []]);
                } else {
                    return json(['code' => 1, 'msg' => '更新失败', 'data' => []]);
                }
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage(), 'data' => []]);
            }
        } else {
            return json(['code' => 1, 'msg' => '错误的请求', 'data' => []]);
        }
    }

    /**
     * 获取BOM详细信息
     */
    public function getBomInfo()
    {
        $product_id = request()->param('id/d', 0);
        
        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 查询最新有效的BOM信息
        $bomInfo = Db::name('bom_master')
            ->where('product_id', $product_id)
            ->where('check_status', 2) // 已审核状态
            ->where('delete_time', 0)
            ->order('create_time desc')
            ->find();

            
            
        if (!$bomInfo) {
            return json(['code' => 1, 'msg' => '该产品没有有效的BOM']);
        }
        
        // 获取产品信息
        $productInfo = Db::name('product')
            ->where('id', $product_id)
            ->field('id, title, material_code, specs, unit')
            ->find();
            
        // 获取BOM子项
        $bomItems = Db::name('bom_item')
            ->alias('bi')
            ->join('product p', 'p.id = bi.material_id', 'left')
            ->where('bi.bom_id', $bomInfo['id'])
            ->where('bi.is_needed', 1) // 只获取需要的子项
            ->field('bi.*, p.title as material_name, p.material_code, p.specs as product_specs, p.unit as uom_name')
            ->select()
            ->toArray();
            
        // 获取每个物料的库存
        foreach ($bomItems as &$item) {
            // 获取库存数量
            $item['stock'] = $this->getProductStock($item['material_id']);
        }
        
        $bomInfo['items'] = $bomItems;
        $bomInfo['product'] = $productInfo;
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomInfo]);
    }
    
    /**
     * 获取产品当前库存
     * @param int $product_id 产品ID
     * @return float 库存数量
     */
    private function getProductStock($product_id)
    {
        // 获取可用库存
        $stock = Db::name('inventory')
            ->where('product_id', $product_id)
            ->where('status', 1) // 可用状态
            ->sum('quantity');
            
        return floatval($stock);
    }
} 