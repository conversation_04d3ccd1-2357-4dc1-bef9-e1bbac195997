-- 出库系统数据表设计
-- 基于入库系统设计，创建对应的出库表结构
-- 创建时间：2025-08-05

-- 1. 出库单主表
CREATE TABLE IF NOT EXISTS `oa_outbound` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '出库单ID',
  `outbound_no` varchar(50) NOT NULL COMMENT '出库单号',
  `outbound_type` varchar(20) NOT NULL DEFAULT 'sales' COMMENT '出库类型:sales销售出库,production生产出库,transfer调拨出库,return退货出库,other其他出库',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联业务类型:customer_order,production_order,transfer_order,return_order',
  `ref_id` int(11) DEFAULT 0 COMMENT '关联业务ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联业务单号',
  `warehouse_id` int(11) NOT NULL COMMENT '出库仓库ID',
  `customer_id` int(11) DEFAULT 0 COMMENT '客户ID（销售出库时使用）',
  `department_id` int(11) DEFAULT 0 COMMENT '部门ID（生产出库时使用）',
  `to_warehouse_id` int(11) DEFAULT 0 COMMENT '目标仓库ID（调拨出库时使用）',
  `outbound_date` date NOT NULL COMMENT '出库日期',
  `total_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '总出库数量',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总出库金额',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0草稿,1已提交,2已审核,3部分出库,4全部出库,5已取消',
  `priority` tinyint(1) DEFAULT 1 COMMENT '优先级:1普通,2紧急,3特急',
  `notes` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `approved_by` int(11) DEFAULT 0 COMMENT '审核人ID',
  `approved_time` int(11) DEFAULT 0 COMMENT '审核时间',
  `submitted_by` int(11) DEFAULT 0 COMMENT '提交人ID',
  `submitted_at` int(11) DEFAULT 0 COMMENT '提交时间',
  `outbound_by` int(11) DEFAULT 0 COMMENT '出库操作人ID',
  `outbound_time` int(11) DEFAULT 0 COMMENT '出库时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_outbound_no` (`outbound_no`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_ref` (`ref_type`, `ref_id`),
  KEY `idx_status` (`status`),
  KEY `idx_date` (`outbound_date`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单主表';

-- 2. 出库单明细表
CREATE TABLE IF NOT EXISTS `oa_outbound_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `outbound_id` int(11) NOT NULL COMMENT '出库单ID',
  `ref_detail_id` int(11) DEFAULT 0 COMMENT '关联业务明细ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_name` varchar(255) NOT NULL COMMENT '产品名称',
  `product_code` varchar(100) NOT NULL COMMENT '产品编码',
  `specification` varchar(255) DEFAULT '' COMMENT '规格型号',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '出库数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `batch_no` varchar(100) DEFAULT '' COMMENT '批次号',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `expiry_date` date DEFAULT NULL COMMENT '过期日期',
  `location` varchar(100) DEFAULT '' COMMENT '库位',
  `quality_status` tinyint(1) DEFAULT 1 COMMENT '质量状态:1合格,2不合格,3待检',
  `actual_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '实际出库数量',
  `shortage_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '缺货数量',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_outbound_id` (`outbound_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_ref_detail` (`ref_detail_id`),
  KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单明细表';

-- 3. 出库审核日志表
CREATE TABLE IF NOT EXISTS `oa_outbound_approval_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `outbound_id` int(11) NOT NULL COMMENT '出库单ID',
  `action` varchar(20) NOT NULL COMMENT '操作:submit提交,approve通过,reject拒绝,cancel取消',
  `from_status` tinyint(1) NOT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `notes` text COMMENT '操作备注',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `create_time` int(11) NOT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_outbound_id` (`outbound_id`),
  KEY `idx_operator` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库审核日志表';

-- 4. 出库拣货任务表
CREATE TABLE IF NOT EXISTS `oa_outbound_picking_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_no` varchar(50) NOT NULL COMMENT '任务编号',
  `outbound_id` int(11) NOT NULL COMMENT '出库单ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `picker_id` int(11) DEFAULT 0 COMMENT '拣货员ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待分配,1拣货中,2已完成,3已取消',
  `priority` tinyint(1) DEFAULT 1 COMMENT '优先级:1普通,2紧急,3特急',
  `assigned_time` int(11) DEFAULT 0 COMMENT '分配时间',
  `start_time` int(11) DEFAULT 0 COMMENT '开始拣货时间',
  `finish_time` int(11) DEFAULT 0 COMMENT '完成时间',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_no` (`task_no`),
  KEY `idx_outbound_id` (`outbound_id`),
  KEY `idx_picker` (`picker_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库拣货任务表';

-- 5. 出库拣货明细表
CREATE TABLE IF NOT EXISTS `oa_outbound_picking_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `task_id` int(11) NOT NULL COMMENT '拣货任务ID',
  `outbound_detail_id` int(11) NOT NULL COMMENT '出库明细ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `location` varchar(100) DEFAULT '' COMMENT '库位',
  `batch_no` varchar(100) DEFAULT '' COMMENT '批次号',
  `required_quantity` decimal(10,2) NOT NULL COMMENT '需求数量',
  `picked_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '已拣数量',
  `shortage_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '缺货数量',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待拣货,1拣货中,2已完成,3缺货',
  `picker_notes` text COMMENT '拣货员备注',
  `pick_time` int(11) DEFAULT 0 COMMENT '拣货时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_outbound_detail` (`outbound_detail_id`),
  KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库拣货明细表';

-- 6. 出库包装表
CREATE TABLE IF NOT EXISTS `oa_outbound_package` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '包装ID',
  `package_no` varchar(50) NOT NULL COMMENT '包装编号',
  `outbound_id` int(11) NOT NULL COMMENT '出库单ID',
  `package_type` varchar(20) DEFAULT 'box' COMMENT '包装类型:box箱子,bag袋子,pallet托盘,other其他',
  `weight` decimal(8,2) DEFAULT 0.00 COMMENT '重量(kg)',
  `volume` decimal(8,2) DEFAULT 0.00 COMMENT '体积(m³)',
  `length` decimal(8,2) DEFAULT 0.00 COMMENT '长度(cm)',
  `width` decimal(8,2) DEFAULT 0.00 COMMENT '宽度(cm)',
  `height` decimal(8,2) DEFAULT 0.00 COMMENT '高度(cm)',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待包装,1已包装,2已发货',
  `packer_id` int(11) DEFAULT 0 COMMENT '包装员ID',
  `pack_time` int(11) DEFAULT 0 COMMENT '包装时间',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_package_no` (`package_no`),
  KEY `idx_outbound_id` (`outbound_id`),
  KEY `idx_packer` (`packer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库包装表';

-- 7. 出库包装明细表
CREATE TABLE IF NOT EXISTS `oa_outbound_package_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `package_id` int(11) NOT NULL COMMENT '包装ID',
  `outbound_detail_id` int(11) NOT NULL COMMENT '出库明细ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '包装数量',
  `batch_no` varchar(100) DEFAULT '' COMMENT '批次号',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_package_id` (`package_id`),
  KEY `idx_outbound_detail` (`outbound_detail_id`),
  KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库包装明细表';

-- 8. 出库发货表
CREATE TABLE IF NOT EXISTS `oa_outbound_shipment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '发货ID',
  `shipment_no` varchar(50) NOT NULL COMMENT '发货单号',
  `outbound_id` int(11) NOT NULL COMMENT '出库单ID',
  `carrier` varchar(100) DEFAULT '' COMMENT '承运商',
  `tracking_no` varchar(100) DEFAULT '' COMMENT '运单号',
  `vehicle_no` varchar(50) DEFAULT '' COMMENT '车牌号',
  `driver_name` varchar(50) DEFAULT '' COMMENT '司机姓名',
  `driver_phone` varchar(20) DEFAULT '' COMMENT '司机电话',
  `ship_date` date NOT NULL COMMENT '发货日期',
  `estimated_arrival` date DEFAULT NULL COMMENT '预计到达日期',
  `actual_arrival` date DEFAULT NULL COMMENT '实际到达日期',
  `freight_cost` decimal(10,2) DEFAULT 0.00 COMMENT '运费',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待发货,1已发货,2运输中,3已到达,4已签收',
  `shipper_id` int(11) DEFAULT 0 COMMENT '发货员ID',
  `ship_time` int(11) DEFAULT 0 COMMENT '发货时间',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_shipment_no` (`shipment_no`),
  KEY `idx_outbound_id` (`outbound_id`),
  KEY `idx_tracking_no` (`tracking_no`),
  KEY `idx_ship_date` (`ship_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库发货表';

-- 创建索引优化查询性能
ALTER TABLE `oa_outbound` ADD INDEX `idx_type_status` (`outbound_type`, `status`);
ALTER TABLE `oa_outbound` ADD INDEX `idx_date_status` (`outbound_date`, `status`);
ALTER TABLE `oa_outbound_detail` ADD INDEX `idx_product_warehouse` (`product_id`, `outbound_id`);
ALTER TABLE `oa_outbound_picking_task` ADD INDEX `idx_warehouse_status` (`warehouse_id`, `status`);
ALTER TABLE `oa_outbound_picking_detail` ADD INDEX `idx_product_status` (`product_id`, `status`);

-- 插入初始化数据
INSERT INTO `oa_outbound` (`id`, `outbound_no`, `outbound_type`, `warehouse_id`, `outbound_date`, `status`, `created_by`, `create_time`, `update_time`) VALUES
(1, 'OUT202508050001', 'sales', 1, '2025-08-05', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 添加注释说明
ALTER TABLE `oa_outbound` COMMENT = '出库单主表 - 记录所有出库业务的主要信息';
ALTER TABLE `oa_outbound_detail` COMMENT = '出库单明细表 - 记录出库的具体产品信息';
ALTER TABLE `oa_outbound_approval_log` COMMENT = '出库审核日志表 - 记录出库单的审核流程';
ALTER TABLE `oa_outbound_picking_task` COMMENT = '出库拣货任务表 - 管理仓库拣货作业';
ALTER TABLE `oa_outbound_picking_detail` COMMENT = '出库拣货明细表 - 记录具体的拣货操作';
ALTER TABLE `oa_outbound_package` COMMENT = '出库包装表 - 管理出库商品的包装信息';
ALTER TABLE `oa_outbound_package_detail` COMMENT = '出库包装明细表 - 记录包装的具体内容';
ALTER TABLE `oa_outbound_shipment` COMMENT = '出库发货表 - 管理出库商品的物流发货信息';
