-- 生产计划表
CREATE TABLE `oa_production_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date NOT NULL COMMENT '计划结束日期',
  `plan_days` int(11) DEFAULT 1 COMMENT '计划生产天数',
  `actual_start_date` date DEFAULT NULL COMMENT '实际开始日期',
  `actual_end_date` date DEFAULT NULL COMMENT '实际结束日期',
  `progress` decimal(5,2) DEFAULT 0.00 COMMENT '完成进度(%)',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待开始,1生产中,2已完成,3已延期',
  `priority` int(11) DEFAULT 1 COMMENT '优先级:1-5',
  `auto_scheduled` tinyint(1) DEFAULT 0 COMMENT '是否自动排产',
  `estimated_hours` decimal(8,2) DEFAULT 0.00 COMMENT '预计工时',
  `actual_hours` decimal(8,2) DEFAULT 0.00 COMMENT '实际工时',
  `notes` text COMMENT '备注',
  `create_uid` int(11) DEFAULT NULL COMMENT '创建人ID',
  `create_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `update_uid` int(11) DEFAULT NULL COMMENT '更新人ID',
  `update_name` varchar(50) DEFAULT NULL COMMENT '更新人姓名',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_plan_date` (`plan_start_date`,`plan_end_date`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产计划表';

-- 日生产统计表
CREATE TABLE `oa_daily_production_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date` date NOT NULL COMMENT '日期',
  `total_orders` int(11) DEFAULT 0 COMMENT '当日总订单数',
  `completed_orders` int(11) DEFAULT 0 COMMENT '完成订单数',
  `in_progress_orders` int(11) DEFAULT 0 COMMENT '进行中订单数',
  `pending_orders` int(11) DEFAULT 0 COMMENT '待开始订单数',
  `delayed_orders` int(11) DEFAULT 0 COMMENT '延期订单数',
  `total_quantity` int(11) DEFAULT 0 COMMENT '总生产数量',
  `completed_quantity` int(11) DEFAULT 0 COMMENT '完成数量',
  `workload_score` decimal(8,2) DEFAULT 0.00 COMMENT '工作量评分',
  `efficiency_rate` decimal(5,2) DEFAULT 0.00 COMMENT '效率率(%)',
  `on_time_rate` decimal(5,2) DEFAULT 0.00 COMMENT '准时率(%)',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date` (`date`),
  KEY `idx_date_range` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日生产统计表';

-- 排产日志表
CREATE TABLE `oa_production_plan_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` int(11) NOT NULL COMMENT '生产计划ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `old_data` text COMMENT '原数据JSON',
  `new_data` text COMMENT '新数据JSON',
  `reason` varchar(255) DEFAULT NULL COMMENT '操作原因',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_action` (`action`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排产日志表';

-- 为生产订单表添加排产相关字段（如果不存在）
ALTER TABLE `oa_produce_order` 
ADD COLUMN `plan_id` int(11) DEFAULT 0 COMMENT '排产计划ID' AFTER `material_status`,
ADD COLUMN `plan_status` tinyint(1) DEFAULT 0 COMMENT '排产状态:0未排产,1已排产,2生产中,3已完成' AFTER `plan_id`,
ADD COLUMN `scheduled_date` date DEFAULT NULL COMMENT '排产日期' AFTER `plan_status`,
ADD COLUMN `estimated_days` int(11) DEFAULT 1 COMMENT '预计生产天数' AFTER `scheduled_date`;

-- 添加索引
ALTER TABLE `oa_produce_order` 
ADD KEY `idx_plan_id` (`plan_id`),
ADD KEY `idx_plan_status` (`plan_status`),
ADD KEY `idx_scheduled_date` (`scheduled_date`);
