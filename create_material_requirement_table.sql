-- 创建销售订单物料需求表
-- 用于记录销售订单审核时产生的物料需求

CREATE TABLE IF NOT EXISTS `oa_material_requirement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) NOT NULL COMMENT '销售订单ID',
  `order_no` varchar(100) DEFAULT '' COMMENT '销售订单号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `product_code` varchar(50) DEFAULT '' COMMENT '产品编码',
  `product_specs` varchar(200) DEFAULT '' COMMENT '产品规格',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `quantity` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '需求数量',
  `available_quantity` decimal(12,2) DEFAULT 0.00 COMMENT '可用库存数量',
  `gap` decimal(12,2) DEFAULT 0.00 COMMENT '缺口数量',
  `source_id` int(11) NOT NULL COMMENT '来源明细ID',
  `source_type` varchar(50) NOT NULL DEFAULT 'customer_order_detail' COMMENT '来源类型',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '处理状态:0=未处理,1=部分处理,2=已完成,3=已取消',
  `priority` tinyint(1) DEFAULT 1 COMMENT '优先级:1=低,2=中,3=高,4=紧急',
  `required_date` int(11) DEFAULT 0 COMMENT '需求日期',
  `warehouse_id` int(11) DEFAULT 0 COMMENT '目标仓库ID',
  `notes` text COMMENT '备注信息',
  `create_by` int(11) NOT NULL COMMENT '创建人ID',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_source` (`source_type`, `source_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority_date` (`priority` DESC, `required_date` ASC),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单物料需求表';

-- 创建索引以提高查询性能
CREATE INDEX `idx_gap_status` ON `oa_material_requirement` (`gap`, `status`);
CREATE INDEX `idx_order_status` ON `oa_material_requirement` (`order_id`, `status`);
