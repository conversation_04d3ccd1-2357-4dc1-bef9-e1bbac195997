# MRP采购建议实时库存适配设计

## 需求背景

用户反馈：`http://tc.xinqiyu.cn:8830/api/test/getmrppurchase_suggestion` 接口需要适配新的实时库存系统，并增加"在途"和"在库"列，使数据更加明确。

关键问题：
1. 在途库存可能是为其他订单采购的，需要考虑锁库机制，正确计算剩余缺口
2. 需要增加一列区分主产品和BOM物料，让采购知道是产品还是产品的物料

**第二次需求**：增加"主/BOM"列
- **主产品**：直接在销售订单明细中的产品，显示"主"
- **BOM物料**：不在销售订单明细中，通过BOM展开产生的物料需求，显示所属的主产品名称

**判断逻辑**：
- 如果 `material_requirement.product_id` == `customer_order_detail.product_id` → 主产品
- 如果 `material_requirement.product_id` != `customer_order_detail.product_id` → BOM物料

## 核心逻辑设计

### 1. 库存计算逻辑

#### 原有逻辑问题
- 只考虑了简单的库存数量
- 没有区分在库和在途
- 没有考虑锁库机制

#### 新的计算逻辑
```
剩余缺口 = 需求数量 - (在库可用数量 + 在途可用数量)

其中：
- 在库可用数量 = 在库总数量 - 在库锁定数量
- 在途可用数量 = 在途总数量 - 在途锁定数量
```

### 2. 数据源适配

#### 实时库存数据（inventory_realtime表）
- `quantity`: 总库存数量
- `available_quantity`: 可用数量（已扣除锁定）
- `locked_quantity`: 锁定数量

#### 在途库存数据（purchase_order_detail表）
- 查询条件：采购订单状态为已审核(2)、部分入库(3)、未完成(4)
- 计算公式：`quantity - received_quantity`（订购数量 - 已收货数量）

#### 在途锁定数据（inventory_lock表）
- 查询条件：`ref_type = 'purchase_order'` 且 `status = 1`（锁定中）
- 用于计算在途可用数量

#### 主/BOM区分数据（material_requirement表）
- `source_type = 'customer_order_detail'`: BOM物料需求
- `source_type = 'customer_order'`: 主产品需求
- 通过关联`customer_order_detail`和`product`表获取主产品信息

## 技术实现

### 1. 后端API修改（app/api/controller/Test.php）

#### 批量查询优化
```php
// 批量获取实时库存数据
$inventoryData = Db::name('inventory_realtime')
    ->field('product_id, SUM(quantity) as total_quantity, SUM(available_quantity) as available_quantity, SUM(locked_quantity) as locked_quantity')
    ->whereIn('product_id', $productIds)
    ->group('product_id')
    ->select()
    ->toArray();

// 批量获取在途库存数据
$inTransitData = Db::name('purchase_order_detail')
    ->alias('d')
    ->join('purchase_order o', 'd.order_id = o.id')
    ->whereIn('d.product_id', $productIds)
    ->where('o.status', 'in', [2, 3, 4])
    ->where('d.received_quantity', '<', Db::raw('d.quantity'))
    ->field('d.product_id, SUM(d.quantity - d.received_quantity) as in_transit_quantity')
    ->group('d.product_id')
    ->select()
    ->toArray();

// 批量获取在途锁定数据
$inTransitLockedData = Db::name('inventory_lock')
    ->whereIn('product_id', $productIds)
    ->where('ref_type', 'purchase_order')
    ->where('status', 1)
    ->field('product_id, SUM(quantity) as locked_quantity')
    ->group('product_id')
    ->select()
    ->toArray();
```

#### 返回数据结构
```php
foreach ($list as &$item) {
    $productId = $item['product_id'];

    // 基础库存信息
    $item['stock_quantity'] = 在库总数量;
    $item['stock_available'] = 在库可用数量;
    $item['stock_locked'] = 在库锁定数量;

    // 在途库存信息
    $item['in_transit_quantity'] = 在途总数量;
    $item['in_transit_available'] = 在途可用数量;
    $item['in_transit_locked'] = 在途锁定数量;

    // 重新计算剩余缺口
    $totalAvailable = $stockAvailable + $inTransitAvailable;
    $item['remaining_gap'] = max(0, $item['quantity'] - $totalAvailable);

    // 主/BOM标识
    if ($item['source_type'] == 'customer_order_detail') {
        // 检查当前物料是否就是订单明细中的主产品
        if ($item['product_id'] == $item['main_product_id']) {
            // 主产品：显示"主"
            $item['main_or_bom'] = '主';
            $item['item_type'] = '主产品';
        } else {
            // BOM物料：显示所属主产品名称
            $item['main_or_bom'] = $item['main_product_name'] ?: '未知主产品';
            $item['item_type'] = 'BOM物料';
        }
    } else {
        // 其他类型：显示"主"
        $item['main_or_bom'] = '主';
        $item['item_type'] = '主产品';
    }
}
```

### 2. 前端表格修改（app/purchase/view/supply/cgxq.html）

#### 表格列定义
```javascript
{field: 'main_or_bom', title: '主/BOM', width: 120, align: 'center', templet: function(d){
    if (d.item_type === 'BOM物料') {
        return '<div style="text-align: center;">' +
               '<div style="color: #FF5722; font-size: 12px;">BOM物料</div>' +
               '<div style="color: #666; font-size: 11px;">' + (d.main_or_bom || '') + '</div>' +
               '</div>';
    } else {
        return '<span style="color: #009688; font-weight: bold;">主</span>';
    }
}},
{field: 'stock_quantity', title: '在库', width: 90, align: 'center', templet: function(d){
    var available = d.stock_available || 0;
    var total = d.stock_quantity || 0;
    return '<div style="text-align: center;">' +
           '<div style="color: #009688; font-weight: bold;">' + total + '</div>' +
           '<div style="font-size: 11px; color: #666;">可用:' + available + '</div>' +
           '</div>';
}},
{field: 'in_transit_quantity', title: '在途', width: 90, align: 'center', templet: function(d){
    var available = d.in_transit_available || 0;
    var total = d.in_transit_quantity || 0;
    return '<div style="text-align: center;">' +
           '<div style="color: #1E9FFF; font-weight: bold;">' + total + '</div>' +
           '<div style="font-size: 11px; color: #666;">可用:' + available + '</div>' +
           '</div>';
}},
{field: 'remaining_gap', title: '剩余缺口', width: 100, align: 'center', templet: function(d){
    var gap = d.remaining_gap || 0;
    var color = gap > 0 ? '#FF5722' : '#009688';
    return '<span style="color: ' + color + '; font-weight: bold;">' + gap + '</span>';
}}
```

## 业务价值

### 1. 数据准确性提升
- 考虑锁库机制，避免重复分配库存
- 区分在库和在途，提供更精确的库存状态

### 2. 决策支持优化
- 清晰显示可用库存vs锁定库存
- 准确计算真实的采购需求缺口

### 3. 用户体验改善
- 直观的双行显示（总数量+可用数量）
- 颜色区分不同状态的数据

## 测试要点

1. **库存数据准确性**：验证在库数量、可用数量、锁定数量的计算
2. **在途数据准确性**：验证在途数量、在途可用数量的计算
3. **缺口计算准确性**：验证剩余缺口 = 需求数量 - (在库可用 + 在途可用)
4. **性能测试**：批量查询的性能表现
5. **边界情况**：无库存、全部锁定、无在途等情况

## 后续优化建议

1. **缓存机制**：对频繁查询的库存数据添加缓存
2. **实时更新**：库存变动时实时更新MRP建议
3. **预警机制**：当剩余缺口较大时提供预警提示
4. **批量操作**：支持批量生成采购订单功能
