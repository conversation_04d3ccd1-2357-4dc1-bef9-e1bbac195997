# 库存盘点功能设计文档

## 更新日志
- 2024-12-04: 修复了库存盘点添加页面的Layui初始化问题
  - 将add.html页面转换为正确的gouguInit()模式
  - 修复了日期选择器的初始化问题，使用laydatePlus模块
  - 添加了模块加载检查和错误处理
  - 增加了调试信息以便排查问题
  - 修复了表单提交和产品搜索功能

- 2024-12-04: 修复了客户订单查看页面的库存表错误
  - 修复了 `get_inventory_stock()` 函数，使其使用新的 `oa_inventory_realtime` 表
  - 创建了 `Inventory.php` 模型作为兼容层，将旧的库存调用重定向到新表
  - 创建了 `Inventoryreserve.php` 模型用于库存预占功能
  - 更新了 `InventoryAllocationService.php` 中的所有表名引用
  - 添加了表名兼容性处理，支持新旧表名的自动切换

- 2024-12-04: 修复了客户订单查看页面的BOM表错误
  - 修复了客户订单控制器中使用旧的 `bom_master` 表的问题
  - 更新BOM查询使用新的 `oa_material_bom` 表结构
  - 创建了BOM兼容函数：`get_product_bom()`, `get_bom_items()`, `has_product_bom()`
  - 添加了新旧BOM表的字段映射兼容性处理
  - 修复了BOM物料明细查询，支持 `oa_material_bom_detail` 表
  - 统一了BOM状态字段的处理逻辑

## 功能概述

库存盘点功能是ERP系统中的重要组成部分，用于定期核查仓库中的实际库存与系统记录的库存是否一致，确保库存数据的准确性。

## 功能特点

- 支持按仓库进行盘点
- 支持产品搜索和选择
- 支持实际数量录入和差异计算
- 支持盘点状态管理（待盘点、盘点中、已完成、已取消）
- 支持批量操作（批量开始、批量完成）
- 支持盘点统计和报表

## 数据库设计

### 1. 盘点单主表 (oa_inventory_check)
```sql
CREATE TABLE `oa_inventory_check` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_no` varchar(50) NOT NULL COMMENT '盘点单号',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待盘点，1盘点中，2已完成，3已取消',
  `total_products` int(11) DEFAULT '0' COMMENT '产品总数',
  `checked_products` int(11) DEFAULT '0' COMMENT '已盘点产品数',
  `creator_id` int(11) NOT NULL COMMENT '创建人ID',
  `approver_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `check_no` (`check_no`),
  KEY `warehouse_id` (`warehouse_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点单';
```

### 2. 盘点明细表 (oa_inventory_check_detail)
```sql
CREATE TABLE `oa_inventory_check_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_id` int(11) NOT NULL COMMENT '盘点单ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `system_quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '系统数量',
  `actual_quantity` decimal(10,2) DEFAULT NULL COMMENT '实际数量',
  `difference_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '差异数量',
  `unit_price` decimal(10,2) DEFAULT '0.00' COMMENT '单价',
  `difference_amount` decimal(10,2) DEFAULT '0.00' COMMENT '差异金额',
  `notes` text COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `check_id` (`check_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点明细';
```

## 系统架构

### 1. MVC架构
- **Model层**: `InventoryCheck.php`, `InventoryCheckDetail.php`
- **Service层**: `InventoryCheckService.php` 
- **Controller层**: `InventoryCheck.php`
- **View层**: `inventory_check/` 目录下的模板文件

### 2. 核心类说明

#### InventoryCheck Model
- 盘点单数据模型
- 包含状态常量定义
- 关联仓库、创建人、审核人等关系

#### InventoryCheckDetail Model  
- 盘点明细数据模型
- 提供差异计算方法
- 关联盘点单和产品

#### InventoryCheckService
- 业务逻辑处理层
- 盘点单创建、更新、完成等核心业务
- 库存调整逻辑

#### InventoryCheck Controller
- 控制器层，处理HTTP请求
- 数据验证和响应处理
- 调用Service层完成业务逻辑

## 业务流程

### 1. 盘点单创建流程
1. 选择仓库
2. 搜索并选择需要盘点的产品
3. 系统自动获取当前库存数量作为系统数量
4. 创建盘点单和明细记录
5. 状态设置为"待盘点"

### 2. 盘点执行流程
1. 开始盘点（状态变更为"盘点中"）
2. 录入实际盘点数量
3. 系统自动计算差异数量和金额
4. 完成盘点（状态变更为"已完成"）
5. 系统根据差异自动调整库存

### 3. 状态管理
- **待盘点(0)**: 盘点单已创建，等待开始盘点
- **盘点中(1)**: 正在进行盘点，可以录入实际数量
- **已完成(2)**: 盘点完成，库存已调整
- **已取消(3)**: 盘点被取消，不进行库存调整

## 功能模块

### 1. 盘点单管理
- 盘点单列表查看（支持状态筛选）
- 盘点单创建
- 盘点单详情查看
- 盘点单状态变更（开始、完成、取消）
- 批量操作支持

### 2. 盘点执行
- 实际数量录入
- 差异自动计算
- 备注信息记录
- 实时保存功能

### 3. 统计报表
- 盘点统计概览
- 差异分析报表
- 按时间范围统计
- 按仓库统计

## 技术实现

### 1. 前端技术
- **框架**: Layui
- **模板引擎**: ThinkPHP模板引擎
- **AJAX**: 异步数据交互
- **表格组件**: tablePlus（自定义扩展）

### 2. 后端技术
- **框架**: ThinkPHP 6.0
- **数据库**: MySQL
- **架构模式**: MVC + Service层
- **数据验证**: ThinkPHP验证器

### 3. 关键特性
- **事务处理**: 确保数据一致性
- **权限控制**: 基于用户权限的操作控制
- **数据验证**: 前后端双重验证
- **错误处理**: 完善的异常处理机制

## 部署说明

### 1. 数据库初始化
执行 `inventory_tables.sql` 文件创建相关数据表

### 2. 文件部署
- 控制器文件: `app/warehouse/controller/InventoryCheck.php`
- 服务文件: `app/warehouse/service/InventoryCheckService.php`
- 模型文件: `app/warehouse/model/InventoryCheck.php`, `InventoryCheckDetail.php`
- 视图文件: `app/warehouse/view/inventory_check/` 目录

### 3. 权限配置
根据系统权限管理配置相应的菜单和操作权限

## 使用说明

### 1. 访问地址
- 盘点单列表: `/warehouse/InventoryCheck/index`
- 创建盘点单: `/warehouse/InventoryCheck/add`
- 盘点详情: `/warehouse/InventoryCheck/detail`
- 盘点统计: `/warehouse/InventoryCheck/statistics`

### 2. 操作流程
1. 进入盘点单列表页面
2. 点击"创建盘点单"按钮
3. 选择仓库和产品
4. 保存盘点单
5. 开始盘点，录入实际数量
6. 完成盘点，系统自动调整库存

## 注意事项

1. 盘点过程中建议锁定相关库存，避免并发操作
2. 完成盘点后的库存调整不可逆，请谨慎操作
3. 建议定期进行盘点，确保库存数据准确性
4. 重要盘点操作建议增加审核流程

## 后续优化

1. 增加盘点计划功能，支持定期自动创建盘点单
2. 增加盘点差异预警功能
3. 支持条码扫描录入
4. 增加盘点报告导出功能
5. 支持移动端盘点操作
