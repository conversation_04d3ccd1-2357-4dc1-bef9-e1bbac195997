{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>待审核</li>
                <li>已审核</li>
                <li>已出库</li>
                <li>已完成</li>
                <li>已取消</li>
            </ul>
        </div>
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="request_no" placeholder="领料单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px;">
            <input type="text" name="production_order_no" placeholder="生产订单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:122px;">
            <select name="status">
                <option value="">选择状态</option>
                <option value="0">待审核</option>
                <option value="1">已审核</option>
                <option value="2">已出库</option>
                <option value="3">已完成</option>
                <option value="4">已取消</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="start_time" placeholder="开始日期" readonly name="start_time">
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="end_time" placeholder="结束日期" readonly name="end_time">
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_request" lay-filter="table_request"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 新增领料单</span>
    </button>
  </div>
</script>

<!-- 操作列模板 -->
<script type="text/html" id="operationTpl">
   
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
</script>

<!-- 状态模板 -->
<script type="text/html" id="statusTpl">
    {{# if (d.status == 0) { }}
        <span class="layui-badge layui-bg-orange">待审核</span>
    {{# } else if (d.status == 1) { }}
        <span class="layui-badge layui-bg-blue">已审核</span>
    {{# } else if (d.status == 2) { }}
        <span class="layui-badge layui-bg-green">已出库</span>
    {{# } else if (d.status == 3) { }}
        <span class="layui-badge layui-bg-cyan">已完成</span>
    {{# } else if (d.status == 4) { }}
        <span class="layui-badge layui-bg-gray">已取消</span>
    {{# } else { }}
        <span class="layui-badge">未知</span>
    {{# } }}
</script>

<!-- 超领标识模板 -->
<script type="text/html" id="excessTpl">
    {{# if (d.has_excess == 1) { }}
        <span class="layui-badge layui-bg-red">有超领({{d.excess_items}}项)</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-green">正常</span>
    {{# } }}
</script>

<!-- 出库状态模板 -->
<script type="text/html" id="outboundStatusTpl">
    {{# if (d.outbound_status == 0) { }}
        <span class="layui-badge layui-bg-gray">未出库</span>
    {{# } else if (d.outbound_status == 1) { }}
        <span class="layui-badge layui-bg-orange">部分出库</span>
    {{# } else if (d.outbound_status == 2) { }}
        <span class="layui-badge layui-bg-green">全部出库</span>
    {{# } else if (d.outbound_status == 5) { }}
        <span class="layui-badge ">仓库取消</span>
    {{# } else { }}
        <span class="layui-badge">未知</span>
    {{# } }}
</script>

{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus, form = layui.form;

        // Tab切换
        element.on('tab(tab)', function(data){
            var statusMap = {0: "", 1: "0", 2: "1", 3: "2", 4: "3", 5: "4"};
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
            return false;
        });

        // 日期选择器
        var start_time = new laydatePlus({'target':'start_time'});
        var end_time = new laydatePlus({'target':'end_time'});

        // 数据表格
        layui.pageTable = table.render({
            elem: '#table_request',
            title: '生产领料单列表',
            url: '/Produce/MaterialRequest/index',
            toolbar: '#toolbarDemo',
            page: true,
            limit: 20,
            cellMinWidth: 80,
            height: 'full-152',
            cols: [[
                {field: 'id', title: 'ID', width: 80, align: 'center'},
                {field: 'request_no', title: '领料单号', width: 150},
                {field: 'production_order_no', title: '生产订单号', width: 150},
                {field: 'product_name', title: '产品名称', width: 200},
                {field: 'production_quantity', title: '生产数量', width: 100, align: 'right'},
                {field: 'total_items', title: '物料种类', width: 100, align: 'center'},
                {field: 'total_quantity', title: '领料总量', width: 120, align: 'right'},
                {field: 'has_excess', title: '超领标识', width: 120, align: 'center', templet: '#excessTpl'},
                {field: 'status', title: '状态', width: 100, align: 'center', templet: '#statusTpl'},
                {field: 'outbound_status', title: '出库状态', width: 100, align: 'center', templet: '#outboundStatusTpl'},
                 {field: 'created_name', title: '创建人', width: 100},
                {field: 'create_time', title: '创建时间', width: 160, templet: function(d) {
                    return layui.util.toDateString(d.create_time * 1000, 'yyyy-MM-dd HH:mm');
                }},
               
                {title: '操作', width: 300, align: 'center', templet: '#operationTpl', fixed: 'right'}
            ]]
        });

        // 头工具栏事件
        table.on('toolbar(table_request)', function(obj){
            if (obj.event === 'add'){
                 tool.box("/Produce/MaterialRequest/add", "新增领料单", "95%", "85%");
                return;
            }
        });

        // 监听行工具事件
        table.on('tool(table_request)', function(obj){
            var data = obj.data;
            if (obj.event === 'view'){
                tool.side('/Produce/MaterialRequest/view?id=' + data.id, '查看领料单');
                return;
            }
            if (obj.event === 'approve'){
                layer.prompt({
                    title: '审核领料单',
                    formType: 2,
                    area: ['400px', '200px'],
                    value: '审核通过'
                }, function(value, index) {
                    tool.post('/Produce/MaterialRequest/approve', {
                        id: data.id,
                        approve_notes: value
                    }, function(res) {
                        if (res.code === 0) {
                            layer.msg('审核成功');
                            layui.pageTable.reload();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'reject'){
                layer.prompt({
                    title: '拒绝原因',
                    formType: 2,
                    area: ['400px', '200px'],
                    value: ''
                }, function(value, index) {
                    if (!value.trim()) {
                        layer.msg('请填写拒绝原因');
                        return;
                    }

                    tool.post('/Produce/MaterialRequest/reject', {
                        id: data.id,
                        reject_notes: value
                    }, function(res) {
                        if (res.code === 0) {
                            layer.msg('已拒绝');
                            layui.pageTable.reload();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'delete'){
                layer.confirm('确定要删除这个领料单吗？', {icon: 3, title: '提示'}, function(index) {
                    tool.post('/Produce/MaterialRequest/delete', {id: data.id}, function(res) {
                        if (res.code === 0) {
                            layer.msg('删除成功');
                            layui.pageTable.reload();
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                    layer.close(index);
                });
                return;
            }
           
        });
    }
</script>
</script>
{/block}
