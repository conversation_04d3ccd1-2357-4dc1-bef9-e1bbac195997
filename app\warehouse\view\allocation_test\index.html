<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>库存分配功能测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/layui/css/layui.css">
    <style>
        .test-container { padding: 20px; }
        .test-section { margin-bottom: 30px; border: 1px solid #e6e6e6; padding: 15px; border-radius: 5px; }
        .test-title { font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #333; }
        .test-result { margin-top: 10px; padding: 10px; background: #f8f8f8; border-radius: 3px; }
        .success { color: #5FB878; }
        .error { color: #FF5722; }
        .info { color: #1E9FFF; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="layui-card">
            <div class="layui-card-header">
                <h2>库存分配与锁定功能测试</h2>
            </div>
            <div class="layui-card-body">
                
                <!-- 基础功能测试 -->
                <div class="test-section">
                    <div class="test-title">1. 基础功能测试</div>
                    <button class="layui-btn" onclick="testBasicAllocation()">运行基础测试</button>
                    <button class="layui-btn layui-btn-normal" onclick="createTestInventory()">创建测试库存</button>
                    <div id="basic-result" class="test-result" style="display:none;"></div>
                </div>
                
                <!-- 自动分配测试 -->
                <div class="test-section">
                    <div class="test-title">2. 入库自动分配测试</div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">产品ID</label>
                            <div class="layui-input-inline">
                                <input type="number" id="auto-product-id" value="9999" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">仓库ID</label>
                            <div class="layui-input-inline">
                                <input type="number" id="auto-warehouse-id" value="1" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">入库数量</label>
                            <div class="layui-input-inline">
                                <input type="number" id="auto-quantity" value="50" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" onclick="testAutoAllocation()">测试自动分配</button>
                        </div>
                    </div>
                    <div id="auto-result" class="test-result" style="display:none;"></div>
                </div>
                
                <!-- 反审测试 -->
                <div class="test-section">
                    <div class="test-title">3. 反审释放测试</div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">业务类型</label>
                            <div class="layui-input-inline">
                                <select id="reverse-ref-type" class="layui-input">
                                    <option value="customer_order">客户订单</option>
                                    <option value="production_order">生产订单</option>
                                    <option value="purchase_order">采购订单</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">业务ID</label>
                            <div class="layui-input-inline">
                                <input type="number" id="reverse-ref-id" value="99999" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">反审原因</label>
                            <div class="layui-input-inline">
                                <input type="text" id="reverse-reason" value="测试反审" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-warm" onclick="testReverseAudit()">测试反审</button>
                        </div>
                    </div>
                    <div id="reverse-result" class="test-result" style="display:none;"></div>
                </div>
                
                <!-- 统计查询 -->
                <div class="test-section">
                    <div class="test-title">4. 分配统计查询</div>
                    <button class="layui-btn layui-btn-normal" onclick="getAllocationStats()">获取统计数据</button>
                    <div id="stats-result" class="test-result" style="display:none;"></div>
                </div>
                
                <!-- 批量测试 -->
                <div class="test-section">
                    <div class="test-title">5. 批量测试</div>
                    <button class="layui-btn layui-btn-radius" onclick="batchTest()">运行所有测试</button>
                    <button class="layui-btn layui-btn-danger" onclick="cleanupTestData()">清理测试数据</button>
                    <div id="batch-result" class="test-result" style="display:none;"></div>
                </div>
                
            </div>
        </div>
    </div>

    <script src="/static/layui/layui.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script>
        // 基础功能测试
        function testBasicAllocation() {
            showLoading('basic-result');
            $.get('/warehouse/allocation_test/testBasicAllocation', function(res) {
                showResult('basic-result', res);
            }).fail(function() {
                showError('basic-result', '请求失败');
            });
        }
        
        // 创建测试库存
        function createTestInventory() {
            showLoading('basic-result');
            $.post('/warehouse/allocation_test/createTestInventory', {
                product_id: 9999,
                warehouse_id: 1,
                quantity: 100
            }, function(res) {
                showResult('basic-result', res);
            }).fail(function() {
                showError('basic-result', '请求失败');
            });
        }
        
        // 自动分配测试
        function testAutoAllocation() {
            var productId = $('#auto-product-id').val();
            var warehouseId = $('#auto-warehouse-id').val();
            var quantity = $('#auto-quantity').val();
            
            showLoading('auto-result');
            $.post('/warehouse/allocation_test/testAutoAllocation', {
                product_id: productId,
                warehouse_id: warehouseId,
                quantity: quantity
            }, function(res) {
                showResult('auto-result', res);
            }).fail(function() {
                showError('auto-result', '请求失败');
            });
        }
        
        // 反审测试
        function testReverseAudit() {
            var refType = $('#reverse-ref-type').val();
            var refId = $('#reverse-ref-id').val();
            var reason = $('#reverse-reason').val();
            
            showLoading('reverse-result');
            $.post('/warehouse/allocation_test/testReverseAudit', {
                ref_type: refType,
                ref_id: refId,
                reason: reason
            }, function(res) {
                showResult('reverse-result', res);
            }).fail(function() {
                showError('reverse-result', '请求失败');
            });
        }
        
        // 获取统计
        function getAllocationStats() {
            showLoading('stats-result');
            $.get('/warehouse/allocation_test/getAllocationStats', function(res) {
                showResult('stats-result', res);
            }).fail(function() {
                showError('stats-result', '请求失败');
            });
        }
        
        // 批量测试
        function batchTest() {
            showLoading('batch-result');
            $.post('/warehouse/allocation_test/batchTest', function(res) {
                showResult('batch-result', res);
            }).fail(function() {
                showError('batch-result', '请求失败');
            });
        }
        
        // 清理测试数据
        function cleanupTestData() {
            if (confirm('确定要清理所有测试数据吗？')) {
                showLoading('batch-result');
                $.post('/warehouse/allocation_test/cleanupTestData', function(res) {
                    showResult('batch-result', res);
                }).fail(function() {
                    showError('batch-result', '请求失败');
                });
            }
        }
        
        // 显示加载状态
        function showLoading(elementId) {
            $('#' + elementId).show().html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 测试中...');
        }
        
        // 显示结果
        function showResult(elementId, res) {
            var html = '';
            var className = res.code == 0 ? 'success' : 'error';
            
            html += '<div class="' + className + '">状态: ' + res.msg + '</div>';
            
            if (res.data) {
                html += '<div style="margin-top: 10px;"><strong>详细结果:</strong></div>';
                html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;">';
                html += JSON.stringify(res.data, null, 2);
                html += '</pre>';
            }
            
            $('#' + elementId).html(html);
        }
        
        // 显示错误
        function showError(elementId, message) {
            $('#' + elementId).show().html('<div class="error">错误: ' + message + '</div>');
        }
    </script>
</body>
</html>
