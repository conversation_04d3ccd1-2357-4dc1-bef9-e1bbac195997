{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form layui-form-pane search-form" lay-filter="searchForm">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单位名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="keywords" placeholder="请输入单位名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">使用状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="1">开启</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">含入类型</label>
                        <div class="layui-input-inline">
                            <select name="type">
                                <option value="">全部</option>
                                <option value="四舍五入">四舍五入</option>
                                <option value="进位">进位</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="submit" class="layui-btn" lay-submit lay-filter="searchSubmit">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <table class="layui-hide" id="unitTable" lay-filter="unitTable"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm add-new" type="button">新增单位</button>
  </div>
</script>

<script type="text/html" id="statusTpl">
  {{# if(d.status == 1){ }}
    <span class="layui-badge layui-bg-green">开启</span>
  {{# } else { }}
    <span class="layui-badge layui-bg-orange">禁用</span>
  {{# } }}
</script>

<script type="text/html" id="operateTpl">
  <div class="layui-btn-group">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{# if(d.status == 1){ }}
      <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="disable">禁用</a>
    {{# } else { }}
      <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="enable">启用</a>
    {{# } }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
  </div>
</script>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var table = layui.table, tool = layui.tool, form = layui.form;
    
    // 渲染表格
    layui.pageTable = table.render({
        elem: '#unitTable',
        toolbar: '#toolbarDemo',
        defaultToolbar: false,
        title: '单位管理列表',
        url: "/metadata/unit/index",
        page: true,
        cellMinWidth: 80,
        cols: [[
            {field: 'id', width: 80, title: 'ID', align: 'center'},
            {field: 'name', title: '单位名称', width: 150, align: 'center'},
            {field: 'status', title: '使用状态', width: 100, align: 'center', templet: '#statusTpl'},
            {field: 'precision', title: '精度(小数位数)', width: 150, align: 'center'},
            {field: 'type', title: '含入类型', width: 120, align: 'center'},
            {field: 'remark', title: '备注', minWidth: 200},
            {field: 'update_time', title: '更新时间', width: 180, align: 'center', templet: function(d){
                return d.update_time ? layui.util.toDateString(d.update_time * 1000, 'yyyy-MM-dd HH:mm:ss') : '-';
            }},
            {field: 'admin_name', title: '更新人', width: 120, align: 'center'},
            {width: 180, title: '操作', align: 'center', templet: '#operateTpl'}
        ]]
    });
    
    // 监听工具栏事件
    table.on('tool(unitTable)', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') {
            layer.open({
                type: 2,
                title: '编辑单位',
                shadeClose: true,
                shade: 0.3,
                maxmin: true,
                area: ['600px', '500px'],
                content: '/metadata/unit/add/id/' + data.id
            });
        } else if (obj.event === 'disable') {
            layer.confirm('确定要禁用该单位吗?', {icon: 3, title: '提示'}, function(index){
                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layui.pageTable.reload();
                    }
                }
                tool.post("/metadata/unit/check", { id: data.id, status: 0}, callback);
                layer.close(index);
            });
        } else if (obj.event === 'enable') {
            layer.confirm('确定要启用该单位吗?', {icon: 3, title: '提示'}, function(index){
                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layui.pageTable.reload();
                    }
                }
                tool.post("/metadata/unit/check", { id: data.id, status: 1}, callback);
                layer.close(index);
            });
        } else if (obj.event === 'delete') {
            layer.confirm('确定要删除该单位吗?', {icon: 3, title: '提示'}, function(index){
                let callback = function (e) {
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        layui.pageTable.reload();
                    }
                }
                tool.post("/metadata/unit/delete", { id: data.id}, callback);
                layer.close(index);
            });
        }
    });
    
    // 添加单位
    $('body').on('click', '.add-new', function(){
        layer.open({
            type: 2,
            title: '新增单位',
            shadeClose: true,
            shade: 0.3,
            maxmin: true,
            area: ['600px', '500px'],
            content: '/metadata/unit/add'
        });
    });
    
    // 搜索
    form.on('submit(searchSubmit)', function(data){
        layui.pageTable.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });
}
</script>
{/block}
<!-- /脚本 -->