<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use app\warehouse\model\InventoryLock;
use app\warehouse\model\InventoryRealtime;
use think\facade\Db;
use think\Exception;

/**
 * 库存锁定服务类（简化版）
 */
class InventoryLockServiceNew
{
    /**
     * 锁定库存
     * 
     * @param array $params 锁定参数
     * @return array
     * @throws Exception
     */
    public function lockInventory($params)
    {
        $productId = $params['product_id'];
        $warehouseId = $params['warehouse_id'];
        $quantity = $params['quantity'];
        $refType = $params['ref_type'];
        $refId = $params['ref_id'];
        $refNo = $params['ref_no'] ?? '';
        $notes = $params['notes'] ?? '';
        $createdBy = $params['created_by'] ?? 0;
        
        if ($quantity <= 0) {
            throw new Exception('锁定数量必须大于0');
        }
        
        // 检查库存是否充足
        if (!InventoryRealtime::hasEnoughStock($productId, $warehouseId, $quantity)) {
            throw new Exception('库存不足，无法锁定');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 锁定实时库存
            $inventoryService = new InventoryRealtimeService();
            $lockResult = $inventoryService->lockStock(
                $productId, 
                $warehouseId, 
                $quantity, 
                $refType, 
                $refId, 
                $refNo, 
                $notes, 
                $createdBy
            );
            
            // 创建锁定记录
            $lockRecord = InventoryLock::createLock([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'quantity' => $quantity,
                'ref_type' => $refType,
                'ref_id' => $refId,
                'ref_no' => $refNo,
                'notes' => $notes,
                'created_by' => $createdBy
            ]);
            
            Db::commit();
            
            return [
                'lock_record' => $lockRecord,
                'inventory_result' => $lockResult
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 释放锁定
     * 
     * @param int $lockId 锁定记录ID
     * @param int $operatorId 操作人ID
     * @return array
     * @throws Exception
     */
    public function releaseLock($lockId, $operatorId = 0)
    {
        $lock = InventoryLock::find($lockId);
        if (!$lock) {
            throw new Exception('锁定记录不存在');
        }
        
        if ($lock->status != InventoryLock::STATUS_LOCKED) {
            throw new Exception('锁定记录状态不正确，当前状态: ' . $lock->status_text);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 释放实时库存锁定
            $inventoryService = new InventoryRealtimeService();
            $unlockResult = $inventoryService->unlockStock(
                $lock->product_id,
                $lock->warehouse_id,
                $lock->quantity,
                $lock->ref_type,
                $lock->ref_id,
                $lock->ref_no,
                '释放锁定: ' . $lock->notes,
                $operatorId
            );
            
            // 更新锁定记录状态
            InventoryLock::releaseLock($lockId);
            
            Db::commit();
            
            return [
                'lock_record' => $lock,
                'inventory_result' => $unlockResult
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 使用锁定库存
     * 
     * @param int $lockId 锁定记录ID
     * @param int $operatorId 操作人ID
     * @return array
     * @throws Exception
     */
    public function useLock($lockId, $operatorId = 0)
    {
        $lock = InventoryLock::find($lockId);
        if (!$lock) {
            throw new Exception('锁定记录不存在');
        }
        
        if ($lock->status != InventoryLock::STATUS_LOCKED) {
            throw new Exception('锁定记录状态不正确，当前状态: ' . $lock->status_text);
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 使用锁定库存（实际出库）
            $inventoryService = new InventoryRealtimeService();
            $useResult = $inventoryService->useLockedStock(
                $lock->product_id,
                $lock->warehouse_id,
                $lock->quantity,
                $lock->ref_type,
                $lock->ref_id,
                $lock->ref_no,
                '使用锁定库存: ' . $lock->notes,
                $operatorId
            );
            
            // 更新锁定记录状态
            InventoryLock::useLock($lockId);
            
            Db::commit();
            
            return [
                'lock_record' => $lock,
                'inventory_result' => $useResult
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 批量锁定库存
     * 
     * @param array $lockItems 锁定项目数组
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param string $refNo 关联单号
     * @param string $notes 备注
     * @param int $createdBy 创建人
     * @return array
     * @throws Exception
     */
    public function batchLockInventory($lockItems, $refType, $refId, $refNo = '', $notes = '', $createdBy = 0)
    {
        if (empty($lockItems)) {
            throw new Exception('锁定项目不能为空');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $lockRecords = [];
            $inventoryResults = [];
            
            foreach ($lockItems as $item) {
                $result = $this->lockInventory([
                    'product_id' => $item['product_id'],
                    'warehouse_id' => $item['warehouse_id'],
                    'quantity' => $item['quantity'],
                    'ref_type' => $refType,
                    'ref_id' => $refId,
                    'ref_no' => $refNo,
                    'notes' => $notes,
                    'created_by' => $createdBy
                ]);
                
                $lockRecords[] = $result['lock_record'];
                $inventoryResults[] = $result['inventory_result'];
            }
            
            Db::commit();
            
            return [
                'lock_records' => $lockRecords,
                'inventory_results' => $inventoryResults
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 批量释放锁定
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param int $operatorId 操作人ID
     * @return int 释放的记录数
     * @throws Exception
     */
    public function batchRelease($refType, $refId, $operatorId = 0)
    {
        $locks = InventoryLock::getLocksByRef($refType, $refId, InventoryLock::STATUS_LOCKED);
        
        if ($locks->isEmpty()) {
            return 0;
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $releasedCount = 0;
            
            foreach ($locks as $lock) {
                $this->releaseLock($lock->id, $operatorId);
                $releasedCount++;
            }
            
            Db::commit();
            return $releasedCount;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 批量使用锁定库存
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param int $operatorId 操作人ID
     * @return int 使用的记录数
     * @throws Exception
     */
    public function batchUse($refType, $refId, $operatorId = 0)
    {
        $locks = InventoryLock::getLocksByRef($refType, $refId, InventoryLock::STATUS_LOCKED);
        
        if ($locks->isEmpty()) {
            return 0;
        }
        
        // 开启事务
        Db::startTrans();
        try {
            $usedCount = 0;
            
            foreach ($locks as $lock) {
                $this->useLock($lock->id, $operatorId);
                $usedCount++;
            }
            
            Db::commit();
            return $usedCount;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取关联的锁定记录
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param int $status 状态（可选）
     * @return \think\Collection
     */
    public function getLocksByRef($refType, $refId, $status = null)
    {
        return InventoryLock::getLocksByRef($refType, $refId, $status);
    }
    
    /**
     * 获取产品在指定仓库的锁定数量
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return float
     */
    public function getLockedQuantity($productId, $warehouseId)
    {
        return InventoryLock::getLockedQuantity($productId, $warehouseId);
    }
}
