# 入库单反审核功能完善

## 需求背景
原有的入库单反审核功能不完整，缺少以下关键检查和处理：
1. 未检查是否已有出库记录
2. 未释放锁定库存
3. 未恢复分配需求记录
4. 可能导致数据不一致

## 完善内容

### 1. 出库检查
**问题**：如果入库的产品已经出库，反审核会导致库存为负数
**解决方案**：
```php
// 检查是否有出库记录
$outboundCount = Db::name('outbound_detail')
    ->alias('od')
    ->join('outbound o', 'o.id = od.outbound_id')
    ->where('od.product_id', 'in', array_column($receipt->details->toArray(), 'product_id'))
    ->where('od.warehouse_id', $receipt->warehouse_id)
    ->where('o.status', '>', 0) // 已提交的出库单
    ->where('o.create_time', '>', $receipt->submitted_at ?: $receipt->create_time)
    ->count();
    
if ($outboundCount > 0) {
    return json(['code' => 1, 'msg' => '该入库单的产品已有出库记录，不允许反审核']);
}
```

### 2. 锁定库存释放
**问题**：入库时可能产生了库存锁定，反审核时需要释放这些锁定
**解决方案**：
```php
// 调用分配服务处理反审释放
$allocationService = new \app\warehouse\service\InventoryAllocationService();
$allocationService->releaseOnReverseAudit([
    'ref_type' => 'purchase_receipt',
    'ref_id' => $receipt->id,
    'reason' => '采购入库单反审核',
    'operator_id' => $this->uid
]);
```

### 3. 分配需求恢复
**问题**：入库时自动分配可能满足了分配需求，反审核时需要恢复这些需求
**解决方案**：通过 `InventoryAllocationService::releaseOnReverseAudit()` 方法处理：
- 释放相关的库存锁定记录
- 恢复分配需求的状态
- 重新分配释放的库存给其他等待需求

## 技术实现

### 1. 反审核流程
```
1. 参数验证
2. 状态检查（只允许已入库状态反审核）
3. 出库记录检查（有出库则不允许反审核）
4. 开启事务
5. 更新收货单状态为草稿
6. 恢复采购订单明细的收货数量
7. 减少库存数量
8. 恢复质检状态
9. 更新采购订单状态
10. 调用分配服务释放锁定和恢复需求
11. 提交事务
```

### 2. 分配服务处理
`InventoryAllocationService::releaseOnReverseAudit()` 方法会：
- 查找所有相关的锁定记录
- 释放锁定库存
- 删除或恢复分配需求
- 重新分配释放的库存
- 记录操作日志

### 3. 错误处理
- 分配服务的错误不影响反审核主流程
- 记录详细的错误日志便于排查
- 事务保证数据一致性

## 业务规则

### 1. 反审核条件
- ✅ 收货单状态必须是已入库（status = 1）
- ✅ 相关产品不能有后续出库记录
- ✅ 库存数量必须足够扣减

### 2. 反审核影响
- ✅ 收货单状态回到草稿
- ✅ 采购订单明细收货数量减少
- ✅ 库存数量和可用数量减少
- ✅ 质检状态重置
- ✅ 锁定库存释放
- ✅ 分配需求恢复

### 3. 数据一致性
- ✅ 库存数据与业务状态保持一致
- ✅ 分配需求与实际库存匹配
- ✅ 锁定记录与业务状态同步

## 测试用例

### 1. 正常反审核
- 入库单状态为已入库
- 无后续出库记录
- 库存充足
- 预期：反审核成功，所有相关数据正确恢复

### 2. 有出库记录
- 入库单状态为已入库
- 有后续出库记录
- 预期：反审核失败，提示已有出库记录

### 3. 库存不足
- 入库单状态为已入库
- 无出库记录但库存被其他业务占用
- 预期：反审核失败，提示库存不足

### 4. 分配需求恢复
- 入库时有自动分配
- 反审核后检查分配需求是否正确恢复
- 预期：分配需求状态正确，锁定库存释放

## 注意事项

1. **性能考虑**：出库记录检查可能涉及大量数据，需要合理的索引
2. **并发控制**：反审核过程中要防止并发操作导致数据不一致
3. **日志记录**：详细记录反审核过程，便于问题排查
4. **权限控制**：确保只有有权限的用户才能执行反审核操作

## 相关文档
- MRP采购建议实时库存适配设计.md
- 系统最新数据库定义.md
- 库存分配与锁定服务设计.md
