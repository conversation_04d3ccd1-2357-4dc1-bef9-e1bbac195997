# 生产计划排产模块实现文档

## 📋 模块概述

生产计划排产模块是一个完整的生产排程管理系统，提供可视化的甘特图界面、智能自动排产算法、拖拽式手动排产等功能，帮助企业高效管理生产计划。

## 🗄️ 数据库设计

### 1. 生产计划表 (oa_production_plan)
```sql
CREATE TABLE `oa_production_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date NOT NULL COMMENT '计划结束日期',
  `plan_days` int(11) DEFAULT 1 COMMENT '计划生产天数',
  `actual_start_date` date DEFAULT NULL COMMENT '实际开始日期',
  `actual_end_date` date DEFAULT NULL COMMENT '实际结束日期',
  `progress` decimal(5,2) DEFAULT 0.00 COMMENT '完成进度(%)',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待开始,1生产中,2已完成,3已延期',
  `priority` int(11) DEFAULT 1 COMMENT '优先级:1-5',
  `auto_scheduled` tinyint(1) DEFAULT 0 COMMENT '是否自动排产',
  `estimated_hours` decimal(8,2) DEFAULT 0.00 COMMENT '预计工时',
  `actual_hours` decimal(8,2) DEFAULT 0.00 COMMENT '实际工时',
  `notes` text COMMENT '备注',
  `create_uid` int(11) DEFAULT NULL COMMENT '创建人ID',
  `create_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `update_uid` int(11) DEFAULT NULL COMMENT '更新人ID',
  `update_name` varchar(50) DEFAULT NULL COMMENT '更新人姓名',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_plan_date` (`plan_start_date`,`plan_end_date`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产计划表';
```

### 2. 日生产统计表 (oa_daily_production_stats)
```sql
CREATE TABLE `oa_daily_production_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date` date NOT NULL COMMENT '日期',
  `total_orders` int(11) DEFAULT 0 COMMENT '当日总订单数',
  `completed_orders` int(11) DEFAULT 0 COMMENT '完成订单数',
  `in_progress_orders` int(11) DEFAULT 0 COMMENT '进行中订单数',
  `pending_orders` int(11) DEFAULT 0 COMMENT '待开始订单数',
  `delayed_orders` int(11) DEFAULT 0 COMMENT '延期订单数',
  `total_quantity` int(11) DEFAULT 0 COMMENT '总生产数量',
  `completed_quantity` int(11) DEFAULT 0 COMMENT '完成数量',
  `workload_score` decimal(8,2) DEFAULT 0.00 COMMENT '工作量评分',
  `efficiency_rate` decimal(5,2) DEFAULT 0.00 COMMENT '效率率(%)',
  `on_time_rate` decimal(5,2) DEFAULT 0.00 COMMENT '准时率(%)',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date` (`date`),
  KEY `idx_date_range` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日生产统计表';
```

### 3. 排产日志表 (oa_production_plan_log)
```sql
CREATE TABLE `oa_production_plan_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` int(11) NOT NULL COMMENT '生产计划ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `old_data` text COMMENT '原数据JSON',
  `new_data` text COMMENT '新数据JSON',
  `reason` varchar(255) DEFAULT NULL COMMENT '操作原因',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_action` (`action`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排产日志表';
```

### 4. 生产订单表扩展字段
```sql
ALTER TABLE `oa_produce_order` 
ADD COLUMN `plan_id` int(11) DEFAULT 0 COMMENT '排产计划ID' AFTER `material_status`,
ADD COLUMN `plan_status` tinyint(1) DEFAULT 0 COMMENT '排产状态:0未排产,1已排产,2生产中,3已完成' AFTER `plan_id`,
ADD COLUMN `scheduled_date` date DEFAULT NULL COMMENT '排产日期' AFTER `plan_status`,
ADD COLUMN `estimated_days` int(11) DEFAULT 1 COMMENT '预计生产天数' AFTER `scheduled_date`;
```

## 🔧 后端实现

### 1. 控制器结构 (ProductionPlan.php)

#### 核心方法：
- `index()` - 排产主页面
- `getUnscheduledOrders()` - 获取待排产订单
- `getScheduledPlans()` - 获取已排产计划
- `savePlan()` - 保存/更新排产计划
- `autoSchedule()` - 自动排产算法
- `updateProgress()` - 更新订单进度
- `getDailyStats()` - 获取每日统计
- `deletePlan()` - 删除排产计划
- `batchSchedule()` - 批量排产

#### 自动排产算法：
```php
public function autoSchedule()
{
    // 1. 获取待排产订单
    $orders = $this->getUnscheduledOrders();
    
    // 2. 计算排产优先级
    foreach ($orders as &$order) {
        $order['priority_score'] = $this->calculatePriorityScore($order);
    }
    
    // 3. 按优先级排序
    usort($orders, function($a, $b) {
        return $b['priority_score'] <=> $a['priority_score'];
    });
    
    // 4. 依次安排排产时间
    foreach ($orders as $order) {
        $availableDate = $this->findAvailableDate($currentDate, $planDays);
        $this->createPlan($order['id'], $availableDate, $planDays);
    }
}
```

#### 优先级计算公式：
```php
private function calculatePriorityScore($order)
{
    $urgencyScore = $this->calculateUrgencyScore($order);      // 紧急度 40%
    $priorityScore = $order['priority'] * 20;                 // 优先级 30%
    $quantityScore = min($order['quantity'] / 100, 10);       // 数量影响 30%
    
    return $urgencyScore * 0.4 + $priorityScore * 0.3 + $quantityScore * 0.3;
}
```

### 2. 生产订单集成 (Order.php)

#### 新增方法：
- `generatePlan()` - 生成生产计划
- `start()` - 开始生产
- `complete()` - 订单完工
- `estimateProductionDays()` - 估算生产天数
- `findAvailableDate()` - 查找可用排产日期

## 🎨 前端实现

### 1. 界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 工具栏：筛选、搜索、自动排产、批量排产、刷新                │
├─────────────────┬───────────────────────────────────────────┤
│ 左侧面板         │ 右侧甘特图                                │
│ ┌─────────────┐ │ ┌───────────────────────────────────────┐ │
│ │待排产订单列表│ │ │ 时间轴甘特图                          │ │
│ │             │ │ │ ┌─────┬─────┬─────┬─────┬─────┐       │ │
│ │ • 订单1     │ │ │ │订单 │8/11 │8/12 │8/13 │8/14 │       │ │
│ │ • 订单2     │ │ │ ├─────┼─────┼─────┼─────┼─────┤       │ │
│ │ • 订单3     │ │ │ │GD001│████ │████ │     │     │       │ │
│ │             │ │ │ │GD002│     │████ │████ │████ │       │ │
│ └─────────────┘ │ │ └─────┴─────┴─────┴─────┴─────┘       │ │
│ ┌─────────────┐ │ └───────────────────────────────────────┘ │
│ │统计信息面板  │ │                                          │
│ │ • 今日: 5单 │ │                                          │
│ │ • 本周: 25单│ │                                          │
│ │ • 完成率:80%│ │                                          │
│ └─────────────┘ │                                          │
└─────────────────┴───────────────────────────────────────────┘
```

### 2. 核心功能

#### 拖拽排产：
```javascript
// 拖拽开始
function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.orderId);
    event.target.classList.add('dragging');
}

// 拖放到甘特图
cell.addEventListener('drop', function(e) {
    const orderId = e.dataTransfer.getData('text/plain');
    const targetDate = getDateFromCell(this);
    showScheduleDialog(orderId, targetDate);
});
```

#### 甘特图渲染：
```javascript
function renderGanttChart() {
    // 生成时间轴
    const timeline = generateTimeline(startDate, endDate);
    
    // 渲染甘特条
    ganttData.forEach(plan => {
        const statusClass = getStatusClass(plan.status, plan.is_delayed);
        const ganttBar = `
            <div class="gantt-bar ${statusClass}" 
                 onclick="showPlanDetail(${plan.id})">
                ${plan.progress}%
            </div>
        `;
    });
}
```

#### 状态颜色编码：
- 🟡 **待开始** (status-pending): #fadb14
- 🔵 **生产中** (status-progress): #1890ff  
- 🟢 **已完成** (status-completed): #52c41a
- 🔴 **已延期** (status-delayed): #f5222d

### 3. 交互功能

#### 自动排产：
```javascript
function autoSchedule() {
    layer.confirm('确定要执行自动排产吗？', function(index) {
        tool.post('/Produce/ProductionPlan/autoSchedule', params, function(res) {
            layer.msg(res.msg);
            if (res.code === 0) {
                refreshData();
            }
        });
    });
}
```

#### 批量排产：
```javascript
function batchSchedule() {
    if (selectedOrders.length === 0) {
        layer.msg('请先选择要排产的订单');
        return;
    }
    
    const params = {
        order_ids: selectedOrders,
        start_date: document.getElementById('startDate').value
    };
    
    tool.post('/Produce/ProductionPlan/batchSchedule', params, callback);
}
```

## 📊 业务流程

### 1. 排产流程
```
待排产订单 → 优先级计算 → 自动/手动排产 → 生成计划 → 开始生产 → 进度跟踪 → 完工
```

### 2. 状态流转
```
未排产(0) → 已排产(1) → 生产中(2) → 已完成(3)
                    ↓
                  已延期(3)
```

### 3. 数据同步
- **订单状态** ↔ **排产状态**：双向同步
- **生产进度** → **排产进度**：实时更新
- **完工状态** → **计划完成**：自动标记

## ✅ 功能特点

### 1. 智能排产
- **优先级算法**：综合考虑交期、优先级、数量等因素
- **资源约束**：避免同一天安排过多订单
- **自动优化**：寻找最优排产方案

### 2. 可视化管理
- **甘特图显示**：直观展示排产计划
- **拖拽操作**：简单易用的排产方式
- **状态颜色**：快速识别订单状态

### 3. 实时监控
- **进度跟踪**：实时显示生产进度
- **统计分析**：每日、每周生产统计
- **预警提醒**：延期订单自动标红

### 4. 灵活调整
- **手动排产**：支持拖拽调整排产时间
- **批量操作**：支持多订单批量排产
- **计划修改**：支持排产计划的修改和删除

## 🔗 模块集成

### 1. 与生产订单模块集成
- **数据关联**：通过order_id关联生产订单
- **状态同步**：排产状态与订单状态实时同步
- **操作联动**：订单操作自动更新排产状态

### 2. 与生产报工模块集成
- **进度更新**：报工时自动更新排产进度
- **完工处理**：报工完成时自动更新排产状态

### 3. 与生产领料模块集成
- **物料准备**：排产时可直接跳转到领料功能
- **状态关联**：领料状态影响排产执行

## 🚀 使用指南

### 1. 访问排产页面
```
http://your-domain/Produce/ProductionPlan/index
```

### 2. 基本操作流程
1. **查看待排产订单**：左侧面板显示所有待排产订单
2. **执行自动排产**：点击"自动排产"按钮，系统自动安排
3. **手动调整排产**：拖拽订单到甘特图指定日期
4. **监控生产进度**：甘特图实时显示生产状态和进度
5. **统计分析**：查看每日生产统计和完成率

### 3. 高级功能
- **批量排产**：选择多个订单进行批量排产
- **计划调整**：点击甘特条查看和修改排产计划
- **视图切换**：支持日、周、月视图切换
- **数据导出**：支持排产计划数据导出

这个生产计划排产模块提供了完整的排产管理解决方案，帮助企业实现高效的生产计划管理！
