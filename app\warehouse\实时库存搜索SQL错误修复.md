# 实时库存搜索SQL错误修复

## 🔍 问题描述

实时库存页面搜索时报错：
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'keywords' in 'where clause'
```

URL示例：
```
http://tc.xinqiyu.cn:8830/warehouse/InventoryRealtimeController/index?page=1&limit=20&warehouse_id=&product_id=&keywords=M3*8%E5%9C%86%E5%A4%B4%E5%B0%96%E5%B0%BE&tab=0
```

## 🎯 问题原因

控制器使用了 `withSearch(['product_id', 'warehouse_id', 'keywords'], $filter)` 方法，但模型中缺少对应的搜索器方法，导致框架直接将参数名当作数据库字段使用。

## 🛠️ 解决方案

### 1. 添加模型搜索器方法

在 `app\warehouse\model\InventoryRealtime.php` 中添加：

```php
/**
 * 产品ID搜索器
 */
public function searchProductId($query, $value)
{
    if (!empty($value)) {
        $query->where('product_id', $value);
    }
}

/**
 * 仓库ID搜索器
 */
public function searchWarehouseId($query, $value)
{
    if (!empty($value)) {
        $query->where('warehouse_id', $value);
    }
}

/**
 * 关键词搜索器
 */
public function searchKeywords($query, $value)
{
    if (!empty($value)) {
        $keywords = trim($value);
        
        // 查询匹配的产品ID
        $productIds = Db::name('product')
            ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
            ->column('id');
        
        if (!empty($productIds)) {
            $query->whereIn('product_id', $productIds);
        } else {
            $query->where('product_id', 0);
        }
    }
}
```

### 2. 修改控制器查询逻辑

在 `app\warehouse\controller\InventoryRealtimeController.php` 中替换 `withSearch` 为直接查询：

```php
} else {
    // 只显示有库存记录的产品
    $query = $this->model->with(['product', 'warehouse']);
    
    // 添加产品ID筛选
    if (!empty($filter['product_id'])) {
        $query->where('product_id', $filter['product_id']);
    }
    
    // 添加仓库ID筛选
    if (!empty($filter['warehouse_id'])) {
        $query->where('warehouse_id', $filter['warehouse_id']);
    }
    
    // 添加关键词搜索
    if (!empty($filter['keywords'])) {
        $keywords = trim($filter['keywords']);
        
        // 查询匹配的产品ID
        $productIds = \think\facade\Db::name('product')
            ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
            ->column('id');
        
        if (!empty($productIds)) {
            $query->whereIn('product_id', $productIds);
        } else {
            $query->where('product_id', 0);
        }
    }
    
    $list = $query->order('id desc')->paginate([...]);
}
```

## 📋 搜索功能

### 支持的搜索字段
- **产品名称**：`product.title`
- **物料编码**：`product.material_code`  
- **产品规格**：`product.specs`

### 搜索示例
- 搜索 `M3*8圆头尖尾` → 匹配规格包含此关键词的产品库存
- 搜索 `螺丝` → 匹配产品名称包含"螺丝"的库存
- 搜索 `M001` → 匹配物料编码包含"M001"的库存

## 🎯 修复效果

### 修复前
- ❌ 搜索功能报SQL错误
- ❌ 无法通过关键词查找库存

### 修复后  
- ✅ 搜索功能正常工作
- ✅ 支持产品名称、编码、规格搜索
- ✅ 两种显示模式都支持搜索

现在可以正常使用关键词搜索功能了！
