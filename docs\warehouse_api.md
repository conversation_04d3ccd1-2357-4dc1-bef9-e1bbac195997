# 仓库管理系统 API 文档

## 概述

本文档描述了仓库管理系统的REST API接口，包括实时库存管理、调拨管理、库存锁定等功能。

## 认证

所有API请求都需要在请求头中包含认证信息：

```
Authorization: Bearer {access_token}
```

## 响应格式

所有API响应都采用统一的JSON格式：

```json
{
    "code": 0,
    "msg": "success",
    "data": {},
    "time": "2024-01-01 12:00:00"
}
```

- `code`: 状态码，0表示成功，非0表示失败
- `msg`: 响应消息
- `data`: 响应数据
- `time`: 响应时间

## 实时库存管理 API

### 1. 获取库存列表

**请求**
```
GET /api/inventory?page=1&limit=20&warehouse_id=1&product_id=1&keywords=产品名称
```

**参数**
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `warehouse_id`: 仓库ID，可选
- `product_id`: 产品ID，可选
- `keywords`: 关键字搜索，可选

**响应**
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "product_id": 1,
                "warehouse_id": 1,
                "quantity": 100.00,
                "available_quantity": 80.00,
                "locked_quantity": 20.00,
                "unit": "个",
                "cost_price": 10.50,
                "update_time": "2024-01-01 12:00:00",
                "product": {
                    "title": "产品名称",
                    "code": "P001"
                },
                "warehouse": {
                    "name": "仓库名称"
                }
            }
        ],
        "total": 100,
        "page": 1,
        "limit": 20
    }
}
```

### 2. 获取库存详情

**请求**
```
GET /api/inventory/{id}
```

**响应**
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "product_id": 1,
        "warehouse_id": 1,
        "quantity": 100.00,
        "available_quantity": 80.00,
        "locked_quantity": 20.00,
        "unit": "个",
        "cost_price": 10.50,
        "create_time": "2024-01-01 10:00:00",
        "update_time": "2024-01-01 12:00:00",
        "product": {
            "title": "产品名称",
            "code": "P001",
            "spec": "规格"
        },
        "warehouse": {
            "name": "仓库名称",
            "code": "WH001"
        }
    }
}
```

### 3. 库存入库

**请求**
```
POST /api/inventory/inbound
Content-Type: application/json

{
    "product_id": 1,
    "warehouse_id": 1,
    "quantity": 50.00,
    "unit": "个",
    "cost_price": 10.50,
    "ref_type": "manual",
    "ref_no": "IN202401010001",
    "ref_id": 0,
    "notes": "手动入库"
}
```

**参数**
- `product_id`: 产品ID，必填
- `warehouse_id`: 仓库ID，必填
- `quantity`: 入库数量，必填
- `unit`: 单位，必填
- `cost_price`: 成本价，必填
- `ref_type`: 关联类型，可选值：manual, purchase, production, return, adjust
- `ref_no`: 关联单号，可选
- `ref_id`: 关联ID，可选
- `notes`: 备注，可选

**响应**
```json
{
    "code": 0,
    "msg": "入库成功",
    "data": {
        "transaction_id": 123,
        "inventory_id": 1,
        "new_quantity": 150.00
    }
}
```

### 4. 库存出库

**请求**
```
POST /api/inventory/outbound
Content-Type: application/json

{
    "product_id": 1,
    "warehouse_id": 1,
    "quantity": 30.00,
    "ref_type": "manual",
    "ref_no": "OUT202401010001",
    "ref_id": 0,
    "notes": "手动出库"
}
```

**响应**
```json
{
    "code": 0,
    "msg": "出库成功",
    "data": {
        "transaction_id": 124,
        "inventory_id": 1,
        "new_quantity": 120.00
    }
}
```

### 5. 库存锁定

**请求**
```
POST /api/inventory/lock
Content-Type: application/json

{
    "product_id": 1,
    "warehouse_id": 1,
    "quantity": 20.00,
    "lock_reason": "销售订单预留",
    "ref_type": "sales_order",
    "ref_no": "SO202401010001",
    "ref_id": 100,
    "notes": "销售订单锁定库存"
}
```

**响应**
```json
{
    "code": 0,
    "msg": "锁定成功",
    "data": {
        "lock_id": 10,
        "inventory_id": 1,
        "locked_quantity": 20.00
    }
}
```

### 6. 释放库存锁定

**请求**
```
POST /api/inventory/unlock
Content-Type: application/json

{
    "lock_id": 10
}
```

**响应**
```json
{
    "code": 0,
    "msg": "释放成功",
    "data": {
        "lock_id": 10,
        "released_quantity": 20.00
    }
}
```

### 7. 使用锁定库存

**请求**
```
POST /api/inventory/use-locked
Content-Type: application/json

{
    "lock_id": 10,
    "use_quantity": 15.00,
    "ref_type": "sales_delivery",
    "ref_no": "DEL202401010001",
    "ref_id": 200,
    "notes": "销售出库"
}
```

**响应**
```json
{
    "code": 0,
    "msg": "使用成功",
    "data": {
        "lock_id": 10,
        "used_quantity": 15.00,
        "remaining_locked": 5.00
    }
}
```

### 8. 检查库存

**请求**
```
POST /api/inventory/check-stock
Content-Type: application/json

{
    "product_id": 1,
    "warehouse_id": 1,
    "quantity": 50.00
}
```

**响应**
```json
{
    "code": 0,
    "msg": "检查完成",
    "data": {
        "sufficient": true,
        "available_quantity": 80.00,
        "required_quantity": 50.00,
        "shortage": 0.00
    }
}
```

## 调拨管理 API

### 1. 获取调拨单列表

**请求**
```
GET /api/transfer?page=1&limit=20&status=1&from_warehouse_id=1&to_warehouse_id=2
```

**参数**
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `status`: 状态，可选值：0待审核，1已审核，2已完成，3已取消
- `from_warehouse_id`: 源仓库ID，可选
- `to_warehouse_id`: 目标仓库ID，可选
- `keywords`: 关键字搜索，可选

**响应**
```json
{
    "code": 0,
    "msg": "获取成功",
    "data": {
        "list": [
            {
                "id": 1,
                "transfer_no": "TR202401010001",
                "from_warehouse_id": 1,
                "to_warehouse_id": 2,
                "status": 1,
                "status_text": "已审核",
                "total_amount": 1050.00,
                "creator_id": 1,
                "create_time": "2024-01-01 10:00:00",
                "notes": "仓库调拨",
                "fromWarehouse": {
                    "name": "源仓库"
                },
                "toWarehouse": {
                    "name": "目标仓库"
                },
                "creator": {
                    "nickname": "创建人"
                }
            }
        ],
        "total": 50,
        "page": 1,
        "limit": 20
    }
}
```

### 2. 创建调拨单

**请求**
```
POST /api/transfer
Content-Type: application/json

{
    "from_warehouse_id": 1,
    "to_warehouse_id": 2,
    "notes": "仓库调拨",
    "details": [
        {
            "product_id": 1,
            "quantity": 50.00,
            "unit": "个",
            "cost_price": 10.50,
            "notes": "产品调拨"
        },
        {
            "product_id": 2,
            "quantity": 30.00,
            "unit": "个",
            "cost_price": 15.00,
            "notes": "产品调拨"
        }
    ]
}
```

**响应**
```json
{
    "code": 0,
    "msg": "创建成功",
    "data": {
        "transfer_id": 1,
        "transfer_no": "TR202401010001",
        "total_amount": 975.00
    }
}
```

### 3. 审核调拨单

**请求**
```
POST /api/transfer/{id}/approve
Content-Type: application/json

{
    "approve_notes": "审核通过"
}
```

**响应**
```json
{
    "code": 0,
    "msg": "审核成功",
    "data": {
        "transfer_id": 1,
        "status": 1,
        "approve_time": "2024-01-01 14:00:00"
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 认证失败 |
| 1003 | 权限不足 |
| 2001 | 库存不足 |
| 2002 | 产品不存在 |
| 2003 | 仓库不存在 |
| 2004 | 库存记录不存在 |
| 3001 | 调拨单不存在 |
| 3002 | 调拨单状态错误 |
| 3003 | 源仓库和目标仓库不能相同 |
| 4001 | 锁定记录不存在 |
| 4002 | 锁定状态错误 |
| 5000 | 系统错误 |

## 限流说明

- 普通API：每分钟最多1000次请求
- 批量操作API：每分钟最多100次请求
- 第三方集成API：每分钟最多100次请求

## 版本说明

当前API版本：v1.0
兼容性：向后兼容，新增字段不会影响现有功能
