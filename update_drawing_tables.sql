-- 为图纸表添加产品ID和工序ID字段
ALTER TABLE `oa_drawing` 
ADD COLUMN `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品ID' AFTER `category_id`,
ADD COLUMN `process_id` int(11) NOT NULL DEFAULT '0' COMMENT '工序ID' AFTER `product_id`;

-- 添加索引
ALTER TABLE `oa_drawing` 
ADD KEY `idx_product_id` (`product_id`),
ADD KEY `idx_process_id` (`process_id`);

-- 更新现有数据（可选，根据实际情况调整）
-- UPDATE `oa_drawing` SET `product_id` = 0, `process_id` = 0 WHERE `product_id` IS NULL OR `process_id` IS NULL;