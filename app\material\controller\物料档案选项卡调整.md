# 物料档案选项卡调整

## 修改时间
2025-08-09

## 修改内容

### 1. 选项卡结构调整

**原有结构：**
- 基础资料
- 质检信息  
- 价格信息
- 工艺管理

**调整后结构：**
- 基础资料
- 质检信息
- 价格信息
- 价格管理（原工艺管理）
- 工艺管理（新增）
- BOM管理（新增）

### 2. 价格管理选项卡

将原来的"工艺管理"选项卡改名为"价格管理"，内容保持不变：
- 供应商价格
- 外协价格  
- 附件管理

### 3. 工艺管理选项卡（新增）

新增工艺管理功能，包含：
- 工艺选择功能
- 工序列表展示
- 工序信息字段：
  - 序号（自动生成）
  - 工序名称
  - 单价
  - 对应不良项
  - 工序类型（数据记录/质量检验/生产加工）

**功能特点：**
- 从现有工艺库中选择工艺
- 自动显示选中工艺包含的所有工序
- 支持工艺模板和工程工艺两种数据源
- 友好的空状态提示

### 4. BOM管理选项卡（新增）

新增BOM管理功能，包含：
- BOM列表展示
- 添加BOM功能（跳转到BOM管理页面）
- BOM信息字段：
  - BOM编号
  - BOM名称
  - 版本
  - 状态
  - 创建时间
  - 操作（查看/编辑/删除）

**功能特点：**
- 与现有BOM管理系统集成
- 支持查看、编辑、删除BOM
- 自动加载产品关联的BOM列表

### 5. 技术实现

**前端修改：**
- `app/material/view/archive/add.html`
- 调整选项卡结构
- 添加工艺管理和BOM管理的HTML结构
- 添加相关JavaScript功能

**后端修改：**
- `app/material/controller/Archive.php`
- 新增 `getBomList()` 方法获取产品BOM列表
- 新增 `getProcessTemplates()` 方法获取工艺模板列表
- 新增 `getProcessSteps()` 方法获取工艺步骤

### 6. 数据库依赖

需要确保以下表存在：
- `oa_material_bom` - BOM主表
- `oa_material_bom_detail` - BOM明细表
- `oa_process_template` - 工艺路线模板表
- `oa_engineering_process` - 工程工艺表

### 7. 路由依赖

需要确保以下路由可用：
- `/material/bom/add` - 新增/编辑BOM
- `/material/bom/view` - 查看BOM
- `/material/bom/delete` - 删除BOM
- `/material/archive/getBomList` - 获取BOM列表
- `/material/archive/getProcessTemplates` - 获取工艺模板列表
- `/material/archive/getProcessSteps` - 获取工艺步骤

## 使用说明

1. **价格管理**：管理供应商价格、外协价格和附件
2. **工艺管理**：从工艺库中选择工艺，查看包含的工序信息
3. **BOM管理**：管理产品的物料清单

## 注意事项

1. 工艺管理从现有的工艺库中选择，支持工艺路线模板表和工程工艺表两种数据源
2. BOM管理依赖现有的BOM管理系统，确保相关功能正常运行
3. 页面加载时会自动加载已有的BOM数据和工艺模板列表（编辑模式）
4. 工艺选择后显示的工序信息为只读展示，不支持编辑
