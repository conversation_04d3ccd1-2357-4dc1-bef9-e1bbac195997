<?php
/**
 * 修复最后工序逻辑的独立脚本
 */

// 引入框架
require_once __DIR__ . '/../vendor/autoload.php';
$app = new \think\App();
$app->initialize();

use think\facade\Db;

echo "<pre>";
echo "=== 修复最后工序逻辑 ===\n\n";

try {
    // 查找所有需要处理的报工记录（status=1且is_last_process=0的记录）
    $reports = Db::name('production_work_report')
        ->where('status', 1)
        ->where('is_last_process', 0)
        ->field('id, order_id, step_id, qualified_qty')
        ->select()
        ->toArray();
    
    echo "找到 " . count($reports) . " 条需要检查的报工记录\n\n";
    
    $processedCount = 0;
    $updatedOrders = [];
    
    foreach ($reports as $report) {
        // 获取该订单的最大工序号
        $maxStepNo = Db::name('produce_order_process')
            ->where('order_id', $report['order_id'])
            ->max('step_no');
        
        // 判断是否为最后工序
        if ($report['step_id'] == $maxStepNo) {
            echo "处理报工记录 ID={$report['id']}: 订单{$report['order_id']}, 工序{$report['step_id']}, 最大工序{$maxStepNo}\n";
            
            // 开始事务
            Db::startTrans();
            
            try {
                // 1. 更新报工记录的is_last_process字段
                Db::name('production_work_report')
                    ->where('id', $report['id'])
                    ->update(['is_last_process' => 1]);
                
                // 2. 更新订单的completed_qty（避免重复累加）
                if (!in_array($report['order_id'], $updatedOrders)) {
                    // 计算该订单所有最后工序的合格品总数
                    $totalQualified = Db::name('production_work_report')
                        ->where('order_id', $report['order_id'])
                        ->where('step_id', $maxStepNo)
                        ->where('status', 1)
                        ->sum('qualified_qty');
                    
                    // 重置并设置正确的completed_qty
                    Db::name('produce_order')
                        ->where('id', $report['order_id'])
                        ->update(['completed_qty' => $totalQualified]);
                    
                    $updatedOrders[] = $report['order_id'];
                    echo "  更新订单{$report['order_id']}的completed_qty为: {$totalQualified}\n";
                }
                
                Db::commit();
                $processedCount++;
                
            } catch (\Exception $e) {
                Db::rollback();
                echo "  错误: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n处理完成！\n";
    echo "总共处理了 {$processedCount} 条记录\n";
    echo "更新了 " . count($updatedOrders) . " 个订单\n\n";
    
    // 显示更新后的订单状态
    if (!empty($updatedOrders)) {
        echo "更新后的订单状态：\n";
        $orders = Db::name('produce_order')
            ->where('id', 'in', $updatedOrders)
            ->field('id, order_no, quantity, completed_qty')
            ->select()
            ->toArray();
        
        foreach ($orders as $order) {
            $progress = $order['quantity'] > 0 ? round(($order['completed_qty'] / $order['quantity']) * 100, 2) : 0;
            echo "  订单{$order['id']} ({$order['order_no']}): {$order['completed_qty']}/{$order['quantity']} ({$progress}%)\n";
        }
    }
    
} catch (\Exception $e) {
    echo "脚本执行失败: " . $e->getMessage() . "\n";
}

echo "</pre>";
?>
