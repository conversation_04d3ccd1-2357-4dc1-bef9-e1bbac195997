# 销售订单反审核功能测试指南

## 功能概述

销售订单反审核功能允许用户在特定条件下撤销已审核的订单，将其状态回退到草稿状态。该功能具有严格的业务检查机制，确保数据完整性。

## 测试环境准备

### 1. 访问地址
- 订单列表页面：`http://tc.xinqiyu.cn:8830/customer/Order/index`

### 2. 测试数据准备
1. 创建一个测试销售订单
2. 使用自审核功能审核该订单
3. 准备不同状态的订单用于测试各种场景

## 功能测试用例

### 测试用例1：基础反审核功能
**目标**：验证基础的反审核功能是否正常工作

**前置条件**：
- 存在一个已审核的销售订单（check_status = 2）
- 该订单没有任何后续业务流程

**测试步骤**：
1. 访问订单列表页面
2. 找到已审核的订单，确认显示"反审核"按钮
3. 点击"反审核"按钮
4. 确认弹出确认对话框
5. 点击"确定反审核"

**预期结果**：
- 反审核成功
- 订单状态变为"未处理"（status = 0）
- 审核状态变为"未审核"（check_status = 0）
- 页面显示成功提示

### 测试用例2：权限控制测试
**目标**：验证只有订单创建者可以反审核自己的订单

**测试步骤**：
1. 用户A创建并审核一个订单
2. 用户B尝试反审核用户A的订单

**预期结果**：
- 用户B无法看到反审核按钮，或点击后提示权限不足

### 测试用例3：业务流程阻止测试
**目标**：验证有后续业务流程时无法反审核

**测试场景**：
1. **物料需求阻止**：订单审核后生成了已处理的物料需求
2. **采购订单阻止**：基于该订单创建了采购订单
3. **生产订单阻止**：基于该订单创建了生产订单
4. **发货记录阻止**：该订单已有发货记录
5. **付款记录阻止**：该订单已有付款记录

**测试步骤**：
1. 创建订单并审核
2. 执行相应的后续业务操作
3. 尝试反审核订单

**预期结果**：
- 点击反审核按钮后显示错误提示
- 提示信息明确说明阻止反审核的原因

### 测试用例4：数据清理验证
**目标**：验证反审核时正确清理相关数据

**测试步骤**：
1. 创建订单并审核（会生成物料需求记录）
2. 确认生成了未处理的物料需求记录
3. 执行反审核操作
4. 检查相关数据是否被正确清理

**预期结果**：
- 未处理的物料需求记录被删除
- 库存锁定记录被释放
- 操作日志被正确记录

### 测试用例5：界面交互测试
**目标**：验证前端界面的交互体验

**测试步骤**：
1. 验证按钮显示逻辑（只在已审核订单显示）
2. 验证确认对话框的显示和交互
3. 验证加载状态的显示
4. 验证错误提示的显示

**预期结果**：
- 界面交互流畅
- 提示信息清晰明确
- 加载状态正确显示

## 配置测试

### 测试用例6：配置功能测试
**目标**：验证配置文件的各项设置是否生效

**测试步骤**：
1. 修改 `config/reverse_audit.php` 中的配置
2. 测试各项配置的效果

**配置项测试**：
- `enabled = false`：功能完全禁用
- `time_limit = 3600`：设置1小时时间限制
- `checks.material_requirement = false`：不检查物料需求
- `cleanup.auto_clean = false`：不自动清理数据

## 错误场景测试

### 测试用例7：异常处理测试
**目标**：验证各种异常情况的处理

**测试场景**：
1. 网络中断时的处理
2. 数据库连接失败时的处理
3. 并发操作时的处理
4. 无效订单ID的处理

**预期结果**：
- 所有异常都有适当的错误提示
- 不会出现系统崩溃或数据不一致

## 性能测试

### 测试用例8：性能验证
**目标**：验证反审核操作的性能

**测试步骤**：
1. 创建大量测试数据
2. 测试反审核操作的响应时间
3. 测试并发反审核操作

**预期结果**：
- 单次反审核操作在3秒内完成
- 支持适度的并发操作

## 回归测试

### 测试用例9：原有功能验证
**目标**：确保新功能不影响原有功能

**测试项目**：
1. 订单创建功能
2. 订单编辑功能
3. 订单审核功能
4. 订单删除功能
5. 订单查看功能

**预期结果**：
- 所有原有功能正常工作
- 没有引入新的bug

## 测试数据清理

测试完成后，请清理测试数据：
1. 删除测试订单
2. 清理相关的物料需求记录
3. 恢复配置文件到默认状态

## 已知限制

1. 只能反审核自己创建的订单
2. 有后续业务流程的订单无法反审核
3. 反审核操作不可撤销
4. 管理员权限需要在配置中设置

## 故障排除

### 常见问题
1. **反审核按钮不显示**：检查订单状态和权限
2. **反审核失败**：检查是否有阻止反审核的业务流程
3. **配置不生效**：检查配置文件语法和缓存

### 调试方法
1. 检查浏览器控制台的错误信息
2. 查看服务器日志
3. 检查数据库中的相关记录

## 联系支持

如果在测试过程中遇到问题，请联系开发团队并提供：
1. 详细的错误描述
2. 重现步骤
3. 相关的日志信息
4. 测试环境信息
