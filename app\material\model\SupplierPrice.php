<?php

namespace app\material\model;

use think\Model;

class SupplierPrice extends Model
{   
    protected $table = 'material_supplier_price';
    protected $pk = 'id';
    
    // 关联供应商
    public function supplier()
    {
        return $this->belongsTo(\app\purchase\model\Supplier::class, 'supplier_id', 'id');
    }
    
    // 关联物料
    public function material()
    {
        return $this->belongsTo(Archive::class, 'material_id', 'id');
    }
}











