{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>库存详情</h3>
        </div>
        <div class="layui-card-body">
            <table class="layui-table">
                <tbody>
                    <tr>
                        <td width="120">产品名称</td>
                        <td>{$inventory.product.title|default='未知产品'}</td>
                        <td width="120">产品编码</td>
                        <td>{$inventory.product.material_code|default='-'}</td>
                    </tr>
                    <tr>
                        <td>规格</td>
                        <td>{$inventory.product.specs|default='-'}</td>
                        <td>单位</td>
                        <td>{$inventory.unit}</td>
                    </tr>
                    <tr>
                        <td>仓库</td>
                        <td>{$inventory.warehouse.name|default='未知仓库'}</td>
                        <td>成本价</td>
                        <td>￥{$inventory.cost_price}</td>
                    </tr>
                    <tr>
                        <td>总库存</td>
                        <td class="layui-text-em">{$inventory.quantity}</td>
                        <td>可用库存</td>
                        <td class="layui-text-em">{$inventory.available_quantity}</td>
                    </tr>
                    <tr>
                        <td>锁定库存</td>
                        <td class="layui-text-em">{$inventory.locked_quantity}</td>
                        <td>创建时间</td>
                        <td>{$inventory.create_time}</td>
                    </tr>
                    <tr>
                        <td>更新时间</td>
                        <td colspan="3">{$inventory.update_time}</td>
                    </tr>
                </tbody>
            </table>

            <div style="margin-top: 20px;">
                <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
                <button type="button" class="layui-btn" onclick="openInbound()">入库</button>
                <button type="button" class="layui-btn layui-btn-warm" onclick="openOutbound()">出库</button>
                <button type="button" class="layui-btn layui-btn-normal" onclick="openTransfer()">调拨</button>
                <button type="button" class="layui-btn layui-btn-danger" onclick="openLock()">锁定</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;

        // 定义全局函数
        window.openInbound = function() {
            var url = '/warehouse/InventoryRealtimeController/inbound?product_id={$inventory.product_id}&warehouse_id={$inventory.warehouse_id}';
            parent.layer.open({
                type: 2,
                title: '库存入库',
                area: ['800px', '600px'],
                content: url
            });
        };

        window.openOutbound = function() {
            var url = '/warehouse/InventoryRealtimeController/outbound?product_id={$inventory.product_id}&warehouse_id={$inventory.warehouse_id}';
            parent.layer.open({
                type: 2,
                title: '库存出库',
                area: ['800px', '600px'],
                content: url
            });
        };

        window.openTransfer = function() {
            var url = '/warehouse/InventoryRealtimeController/transfer?product_id={$inventory.product_id}&warehouse_id={$inventory.warehouse_id}';
            parent.layer.open({
                type: 2,
                title: '库存调拨',
                area: ['800px', '600px'],
                content: url
            });
        };

        window.openLock = function() {
            var url = '/warehouse/InventoryRealtimeController/lock?product_id={$inventory.product_id}&warehouse_id={$inventory.warehouse_id}';
            parent.layer.open({
                type: 2,
                title: '锁定库存',
                area: ['800px', '600px'],
                content: url
            });
        };
    }
</script>
{/block}
