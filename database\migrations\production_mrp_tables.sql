-- =====================================================
-- 生产订单MRP物料需求管理表结构
-- 创建时间: 2025-08-10
-- 说明: 支持多层级BOM展开、库存分配、安全库存考虑
-- =====================================================

-- 1. 生产订单物料需求表
CREATE TABLE IF NOT EXISTS `oa_produce_order_material_requirement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `order_no` varchar(100) NOT NULL COMMENT '生产订单号',
  `material_id` int(11) NOT NULL COMMENT '物料ID',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `material_specs` varchar(200) DEFAULT '' COMMENT '物料规格',
  `material_unit` varchar(20) DEFAULT '' COMMENT '物料单位',
  `bom_level` tinyint(2) NOT NULL DEFAULT 1 COMMENT 'BOM层级(1=直接物料,2=二级物料...)',
  `parent_material_id` int(11) DEFAULT 0 COMMENT '父物料ID(多层BOM时使用)',
  `bom_quantity` decimal(10,4) NOT NULL COMMENT 'BOM单位用量',
  `loss_rate` decimal(5,2) DEFAULT 0.00 COMMENT '损耗率(%)',
  `required_quantity` decimal(12,4) NOT NULL COMMENT '需求总量(含损耗)',
  `allocated_quantity` decimal(12,4) DEFAULT 0.00 COMMENT '已分配数量',
  `locked_quantity` decimal(12,4) DEFAULT 0.00 COMMENT '已锁定数量',
  `shortage_quantity` decimal(12,4) DEFAULT 0.00 COMMENT '缺口数量',
  `required_date` int(11) NOT NULL COMMENT '需求日期',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1=待分配,2=部分分配,3=完全分配,4=已锁定',
  `allocation_request_id` int(11) DEFAULT 0 COMMENT '关联的分配请求ID',
  `warehouse_id` int(11) DEFAULT 0 COMMENT '目标仓库ID',
  `notes` text COMMENT '备注信息',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_status` (`status`),
  KEY `idx_allocation_request` (`allocation_request_id`),
  KEY `idx_bom_level` (`bom_level`),
  KEY `idx_required_date` (`required_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单物料需求表';

-- 2. 为生产订单表添加物料状态字段
ALTER TABLE `oa_produce_order` 
ADD COLUMN `material_status` tinyint(1) DEFAULT 0 COMMENT '物料状态:0=待分配,1=部分齐套,2=完全齐套' AFTER `completed_processes`;

-- 3. 为产品表添加安全库存字段（如果不存在）
ALTER TABLE `oa_product` 
ADD COLUMN `safety_stock` decimal(10,2) DEFAULT 0.00 COMMENT '安全库存量' AFTER `unit`;

-- 4. 创建索引优化查询性能
CREATE INDEX `idx_produce_order_material_status` ON `oa_produce_order` (`material_status`);
CREATE INDEX `idx_produce_order_product_status` ON `oa_produce_order` (`product_id`, `status`);

-- 5. 为库存分配请求表添加生产订单相关索引（如果不存在）
CREATE INDEX `idx_inventory_allocation_production` ON `oa_inventory_allocation_request` (`ref_type`, `ref_id`) 
WHERE `ref_type` = 'production_order';

-- =====================================================
-- 初始化数据和配置
-- =====================================================

-- 1. 插入生产订单物料需求状态配置
INSERT IGNORE INTO `oa_system_config` (`name`, `value`, `description`, `group`, `create_time`) VALUES
('production_mrp_enabled', '1', '是否启用生产订单MRP功能', 'production', UNIX_TIMESTAMP()),
('production_mrp_max_bom_level', '5', 'BOM展开最大层级', 'production', UNIX_TIMESTAMP()),
('production_mrp_default_warehouse', '0', '默认仓库ID(0表示自动选择)', 'production', UNIX_TIMESTAMP()),
('production_mrp_priority_base', '50', 'MRP优先级基础分值', 'production', UNIX_TIMESTAMP());

-- =====================================================
-- 视图创建（便于查询统计）
-- =====================================================

-- 1. 生产订单物料需求汇总视图
CREATE OR REPLACE VIEW `v_produce_order_material_summary` AS
SELECT 
    o.id as order_id,
    o.order_no,
    o.product_name,
    o.quantity as produce_quantity,
    o.status as order_status,
    o.material_status,
    COUNT(r.id) as total_materials,
    COUNT(CASE WHEN r.status = 4 THEN 1 END) as locked_materials,
    COUNT(CASE WHEN r.status IN (2,3) THEN 1 END) as allocated_materials,
    COUNT(CASE WHEN r.status = 1 THEN 1 END) as pending_materials,
    SUM(r.shortage_quantity) as total_shortage,
    MIN(r.required_date) as earliest_required_date
FROM `oa_produce_order` o
LEFT JOIN `oa_produce_order_material_requirement` r ON o.id = r.order_id
GROUP BY o.id;

-- 2. 物料需求汇总视图
CREATE OR REPLACE VIEW `v_material_requirement_summary` AS
SELECT 
    r.material_id,
    r.material_code,
    r.material_name,
    r.material_unit,
    COUNT(DISTINCT r.order_id) as order_count,
    SUM(r.required_quantity) as total_required,
    SUM(r.allocated_quantity) as total_allocated,
    SUM(r.locked_quantity) as total_locked,
    SUM(r.shortage_quantity) as total_shortage,
    MIN(r.required_date) as earliest_required_date,
    MAX(r.required_date) as latest_required_date
FROM `oa_produce_order_material_requirement` r
WHERE r.shortage_quantity > 0
GROUP BY r.material_id;

-- =====================================================
-- 存储过程（可选，用于复杂的MRP计算）
-- =====================================================

DELIMITER $$

-- 计算生产订单物料状态的存储过程
CREATE PROCEDURE `sp_update_production_order_material_status`(IN order_id INT)
BEGIN
    DECLARE total_count INT DEFAULT 0;
    DECLARE allocated_count INT DEFAULT 0;
    DECLARE locked_count INT DEFAULT 0;
    DECLARE material_status TINYINT DEFAULT 0;
    
    -- 统计物料需求情况
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN status IN (3,4) THEN 1 END),
        COUNT(CASE WHEN status = 4 THEN 1 END)
    INTO total_count, allocated_count, locked_count
    FROM `oa_produce_order_material_requirement`
    WHERE order_id = order_id;
    
    -- 计算物料状态
    IF locked_count = total_count AND total_count > 0 THEN
        SET material_status = 2; -- 完全齐套
    ELSEIF allocated_count > 0 THEN
        SET material_status = 1; -- 部分齐套
    ELSE
        SET material_status = 0; -- 待分配
    END IF;
    
    -- 更新生产订单状态
    UPDATE `oa_produce_order` 
    SET material_status = material_status, update_time = UNIX_TIMESTAMP()
    WHERE id = order_id;
    
END$$

DELIMITER ;

-- =====================================================
-- 权限和安全设置
-- =====================================================

-- 注意：以下权限设置需要根据实际情况调整

-- 1. 为相关角色添加表访问权限
-- GRANT SELECT, INSERT, UPDATE ON `oa_produce_order_material_requirement` TO 'production_user'@'%';
-- GRANT SELECT ON `v_produce_order_material_summary` TO 'production_manager'@'%';
-- GRANT SELECT ON `v_material_requirement_summary` TO 'warehouse_user'@'%';

-- =====================================================
-- 完成标记
-- =====================================================
-- 生产订单MRP物料需求管理表结构创建完成
-- 版本: 1.0
-- 最后更新: 2025-08-10
