{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>待审核</li>
                <li>已审核</li>
                <li>已完成</li>
                <li>已取消</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="from_warehouse_id">
                <option value="">选择源仓库</option>
                <!-- 这里需要动态加载仓库列表 -->
            </select>
        </div>
        <div class="layui-input-inline" style="width:150px;">
            <select name="to_warehouse_id">
                <option value="">选择目标仓库</option>
                <!-- 这里需要动态加载仓库列表 -->
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="time_range" placeholder="选择时间范围" readonly name="time_range">
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，调拨单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_transfer" lay-filter="table_transfer"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 创建调拨单</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="batchApprove">
        <span>批量审核</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchExecute">
        <span>批量执行</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="statistics">
        <span>统计报表</span>
    </button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus;
        
        //tab切换
        element.on('tab(tab)', function(data){
            var statusMap = {0: "", 1: "0", 2: "1", 3: "2", 4: "3"};
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
            return false;
        });
        
        //日期范围
        var time_range = new laydatePlus({'target':'time_range'});
        
        layui.pageTable = table.render({
            elem: "#table_transfer"
            ,title: "调拨单列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/warehouse/inventory_transfer/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 50
                },{
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{ 
                    field: 'status', 
                    title: '状态', 
                    align: 'center', 
                    width: 100, 
                    templet: function (d) {
                        var statusClass = {
                            0: 'layui-bg-gray', // 待审核
                            1: 'layui-bg-orange', // 已审核
                            2: 'layui-bg-green', // 已完成
                            3: 'layui-bg-red' // 已取消
                        };
                        var html = '<span class="layui-badge ' + statusClass[d.status] + '">'+d.status_text+'</span>';
                        return html;
                    }
                },{
                    field: 'transfer_no',
                    title: '调拨单号',
                    align: 'center',
                    width: 150,
                    templet: function(d) {
                        return '<div><a href="javascript:;" class="side-a" data-href="/warehouse/inventory_transfer/detail/id/' + d.id + '.html">' + d.transfer_no + '</a></div>';
                    }
                },{
                    field: 'from_warehouse_name',
                    title: '源仓库',
                    align: 'center',
                    width: 120,
                    templet: function(d) {
                        return d.fromWarehouse ? d.fromWarehouse.name : '';
                    }
                },{
                    field: 'to_warehouse_name',
                    title: '目标仓库',
                    align: 'center',
                    width: 120,
                    templet: function(d) {
                        return d.toWarehouse ? d.toWarehouse.name : '';
                    }
                },{
                    field: 'total_amount',
                    title: '总金额',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return '¥' + parseFloat(d.total_amount).toFixed(2);
                    }
                },{
                    field: 'creator_name',
                    title: '创建人',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return d.creator ? d.creator.nickname : '';
                    }
                },{
                    field: 'create_time',
                    title: '创建时间',
                    align: 'center',
                    width: 160
                },{
                    field: 'notes',
                    title: '备注',
                    minWidth: 120
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 200,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
                        var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="cancel">取消</span>';
                        var btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="approve">审核</span>';
                        var btn4='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="execute">执行</span>';
                        
                        switch(d.status){
                            case 0: // 待审核
                                return html+btn0+btn1+btn2+btn3+'</div>';
                            case 1: // 已审核
                                return html+btn0+btn2+btn4+'</div>';
                            case 2: // 已完成
                                return html+btn0+'</div>';
                            case 3: // 已取消
                                return html+btn0+'</div>';
                            default:
                                return html+btn0+'</div>';
                        }
                    }                        
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_transfer)', function(obj){
            if (obj.event === 'add'){
                tool.side("/warehouse/inventory_transfer/add", "创建调拨单");
                return;
            }
            if (obj.event === 'batchApprove'){
                var checkStatus = table.checkStatus('table_transfer');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要审核的调拨单');
                    return;
                }
                var ids = [];
                for(var i = 0; i < data.length; i++){
                    ids.push(data[i].id);
                }
                layer.confirm('确定要批量审核选中的调拨单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/inventory_transfer/batchApprove", { ids: ids }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'batchExecute'){
                var checkStatus = table.checkStatus('table_transfer');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要执行的调拨单');
                    return;
                }
                var ids = [];
                for(var i = 0; i < data.length; i++){
                    ids.push(data[i].id);
                }
                layer.confirm('确定要批量执行选中的调拨单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/inventory_transfer/batchExecute", { ids: ids }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'statistics'){
                tool.side("/warehouse/inventory_transfer/statistics", "调拨统计");
                return;
            }
        });    
            
        table.on('tool(table_transfer)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/warehouse/inventory_transfer/detail?id="+data.id, "调拨单详情");
                return;
            }
            if (obj.event === 'edit') {
                tool.side("/warehouse/inventory_transfer/add?id="+data.id, "编辑调拨单");
                return;
            }
            if (obj.event === 'cancel') {
                layer.confirm('确定要取消该调拨单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 3, status_text: '已取消'});
                        }
                    }
                    tool.post("/warehouse/inventory_transfer/cancel", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'approve') {
                layer.confirm('确定要审核该调拨单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 1, status_text: '已审核'});
                        }
                    }
                    tool.post("/warehouse/inventory_transfer/approve", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'execute') {
                layer.confirm('确定要执行该调拨单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 2, status_text: '已完成'});
                        }
                    }
                    tool.post("/warehouse/inventory_transfer/execute", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
        });
        
        // 表单搜索
        layui.form.on('submit(table-search)', function(data){
            var formData = data.field;
            layui.pageTable.reload({
                where: formData,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 重置搜索
        layui.form.on('submit(table-reset)', function(data){
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({
                where: {},
                page: {
                    curr: 1
                }
            });
            return false;
        });
    }
</script>
{/block}
