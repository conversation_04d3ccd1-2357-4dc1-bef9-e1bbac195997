<?php
/**
 * 验证实时库存表中的锁定数量与锁定记录表是否一致
 * 
 * 使用方法：
 * 1. 在浏览器中访问：http://your-domain/warehouse/验证锁定库存数据一致性.php
 * 2. 或在命令行中运行：php 验证锁定库存数据一致性.php
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/../../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化应用
$app = new \think\App();
$app->initialize();

echo "<h1>锁定库存数据一致性验证报告</h1>\n";
echo "<p>生成时间：" . date('Y-m-d H:i:s') . "</p>\n";

try {
    // 1. 获取所有有锁定库存的实时库存记录
    $realtimeInventories = Db::name('inventory_realtime')
        ->alias('ir')
        ->leftJoin('product p', 'ir.product_id = p.id')
        ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
        ->field('ir.*, p.title as product_name, p.material_code, w.name as warehouse_name')
        ->where('ir.locked_quantity', '>', 0)
        ->order('ir.id')
        ->select()
        ->toArray();

    echo "<h2>1. 实时库存表中有锁定数量的记录（共" . count($realtimeInventories) . "条）</h2>\n";
    
    $inconsistentCount = 0;
    $totalChecked = 0;
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'>\n";
    echo "<th>产品ID</th><th>产品名称</th><th>物料编码</th><th>仓库</th>\n";
    echo "<th>实时库存锁定数量</th><th>锁定记录表统计</th><th>状态</th><th>详细信息</th>\n";
    echo "</tr>\n";
    
    foreach ($realtimeInventories as $inventory) {
        $totalChecked++;
        
        // 2. 统计该产品在该仓库的实际锁定记录
        $actualLockedQty = Db::name('inventory_lock')
            ->where('product_id', $inventory['product_id'])
            ->where('warehouse_id', $inventory['warehouse_id'])
            ->where('status', 1) // 锁定中状态
            ->sum('quantity') ?: 0;
        
        // 3. 对比数据
        $realtimeLockedQty = floatval($inventory['locked_quantity']);
        $actualLockedQty = floatval($actualLockedQty);
        
        $isConsistent = abs($realtimeLockedQty - $actualLockedQty) < 0.01; // 允许0.01的浮点误差
        
        if (!$isConsistent) {
            $inconsistentCount++;
        }
        
        // 4. 获取详细的锁定记录
        $lockDetails = Db::name('inventory_lock')
            ->where('product_id', $inventory['product_id'])
            ->where('warehouse_id', $inventory['warehouse_id'])
            ->where('status', 1)
            ->field('id, quantity, ref_type, ref_id, ref_no, create_time')
            ->select()
            ->toArray();
        
        $detailsHtml = "";
        if (!empty($lockDetails)) {
            $detailsHtml = "<ul style='margin: 0; padding-left: 20px;'>";
            foreach ($lockDetails as $detail) {
                $detailsHtml .= "<li>ID:{$detail['id']}, 数量:{$detail['quantity']}, 类型:{$detail['ref_type']}, 关联ID:{$detail['ref_id']}, 单号:{$detail['ref_no']}</li>";
            }
            $detailsHtml .= "</ul>";
        }
        
        $statusColor = $isConsistent ? '#d4edda' : '#f8d7da';
        $statusText = $isConsistent ? '✅ 一致' : '❌ 不一致';
        
        echo "<tr style='background-color: {$statusColor};'>\n";
        echo "<td>{$inventory['product_id']}</td>\n";
        echo "<td>{$inventory['product_name']}</td>\n";
        echo "<td>{$inventory['material_code']}</td>\n";
        echo "<td>{$inventory['warehouse_name']}</td>\n";
        echo "<td>{$realtimeLockedQty}</td>\n";
        echo "<td>{$actualLockedQty}</td>\n";
        echo "<td>{$statusText}</td>\n";
        echo "<td>{$detailsHtml}</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // 5. 检查是否有锁定记录但实时库存表中锁定数量为0的情况
    echo "<h2>2. 反向检查：有锁定记录但实时库存锁定数量为0的情况</h2>\n";
    
    $orphanLocks = Db::query("
        SELECT 
            il.product_id,
            il.warehouse_id,
            p.title as product_name,
            p.material_code,
            w.name as warehouse_name,
            SUM(il.quantity) as total_locked,
            ir.locked_quantity as realtime_locked,
            COUNT(il.id) as lock_count
        FROM oa_inventory_lock il
        LEFT JOIN oa_inventory_realtime ir ON il.product_id = ir.product_id AND il.warehouse_id = ir.warehouse_id
        LEFT JOIN oa_product p ON il.product_id = p.id
        LEFT JOIN oa_warehouse w ON il.warehouse_id = w.id
        WHERE il.status = 1
        GROUP BY il.product_id, il.warehouse_id
        HAVING total_locked > 0 AND (ir.locked_quantity IS NULL OR ir.locked_quantity = 0)
    ");
    
    if (!empty($orphanLocks)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background-color: #f0f0f0;'>\n";
        echo "<th>产品ID</th><th>产品名称</th><th>物料编码</th><th>仓库</th>\n";
        echo "<th>锁定记录总数量</th><th>实时库存锁定数量</th><th>锁定记录数</th>\n";
        echo "</tr>\n";
        
        foreach ($orphanLocks as $orphan) {
            echo "<tr style='background-color: #fff3cd;'>\n";
            echo "<td>{$orphan['product_id']}</td>\n";
            echo "<td>{$orphan['product_name']}</td>\n";
            echo "<td>{$orphan['material_code']}</td>\n";
            echo "<td>{$orphan['warehouse_name']}</td>\n";
            echo "<td>{$orphan['total_locked']}</td>\n";
            echo "<td>" . ($orphan['realtime_locked'] ?: '0') . "</td>\n";
            echo "<td>{$orphan['lock_count']}</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        $inconsistentCount += count($orphanLocks);
    } else {
        echo "<p style='color: green;'>✅ 未发现有锁定记录但实时库存锁定数量为0的情况</p>\n";
    }
    
    // 6. 生成汇总报告
    echo "<h2>3. 汇总报告</h2>\n";
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px;'>\n";
    echo "<p><strong>检查结果：</strong></p>\n";
    echo "<ul>\n";
    echo "<li>总共检查记录数：{$totalChecked}</li>\n";
    echo "<li>数据不一致记录数：{$inconsistentCount}</li>\n";
    echo "<li>数据一致性：" . ($inconsistentCount == 0 ? '✅ 完全一致' : '❌ 存在不一致') . "</li>\n";
    echo "</ul>\n";
    
    if ($inconsistentCount > 0) {
        echo "<p style='color: red;'><strong>⚠️ 发现数据不一致，建议：</strong></p>\n";
        echo "<ol>\n";
        echo "<li>检查锁定和释放操作是否正确调用了相应的服务类</li>\n";
        echo "<li>检查是否有直接操作数据库而未同步更新的情况</li>\n";
        echo "<li>考虑运行数据修复脚本</li>\n";
        echo "</ol>\n";
    } else {
        echo "<p style='color: green;'><strong>✅ 数据完全一致，库存锁定机制运行正常！</strong></p>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 验证过程中发生错误：" . $e->getMessage() . "</p>\n";
    echo "<p>错误详情：" . $e->getTraceAsString() . "</p>\n";
}

echo "<hr>\n";
echo "<p><small>验证完成时间：" . date('Y-m-d H:i:s') . "</small></p>\n";
?>
