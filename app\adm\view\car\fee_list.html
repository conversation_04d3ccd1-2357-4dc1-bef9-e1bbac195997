{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page" style="height:100%; box-sizing: border-box;">
	<form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" class="layui-input" id="diff_time" placeholder="费用日期" readonly name="diff_time">
		</div>
		<div class="layui-input-inline" style="width:136px;">
			<select name="types">
				<option value="">选择费用类型</option>
				{volist name=":get_base_data('basicAdm')" id="vo"}
					<option value="{$vo.id}">{$vo.title}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:300px">
			<input type="text" name="keywords" placeholder="输入关键字" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="test" lay-filter="test"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm tool-add" type="button" data-href="/adm/car/fee_add">+ 添加收费记录</button>
  </div>
</script>
{/block}
<!-- /主体 -->
{block name="copyright"}{/block}
<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool','tablePlus','laydatePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool,form = layui.form,laydatePlus = layui.laydatePlus;	
		var diff_time = new laydatePlus({'target':'diff_time'});		
		
		layui.feeTable = table.render({
			elem: '#test'
			,toolbar: '#toolbarDemo'
			,title:'收费记录列表'
			,url: "/adm/car/fee_list"
			,is_excel: true
			,page: true
			,cellMinWidth: 60
			,cols: [[
				{field:'id',width:80, title: 'ID号', align:'center'}
				,{field:'title',minWidth:300,title: '费用主题'}
				,{field:'fee_time',title: '费用日期',align: 'center',width: 100}
				,{field:'types_str',title: '费用类型',align: 'center',width: 100}
				,{field:'amount',width:100, title: '费用金额(元)', align:'center',style:"color:#16b777"}
				,{field:'car',width:120,title: '车辆名称'}
				,{field:'name',width:100, title: '车牌号码', align:'center'}
				,{field:'handled_name',title: '跟进人',align: 'center',width: 80}
				,{field:'create_time',title: '记录时间',align: 'center',width: 130}
				,{width:120,fixed:'right', title: '操作', align:'center',templet: function(d){
					var html='<div class="layui-btn-group"><a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a><a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</a><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a></div>';
					return html;
				}}
			]]
		});
		
		table.on('tool(test)',function (obj) {
			var that = this;
			if(obj.event === 'edit'){					
				tool.side("/adm/car/fee_add?id="+obj.data.id);
			}
			if(obj.event === 'view'){					
				tool.side("/adm/car/fee_view?id="+obj.data.id);
			}
			if(obj.event === 'del'){
				layer.confirm('确定要删除该收费记录吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.feeTable.reload();
						}
					}
					tool.post("/adm/car/fee_del", {id: obj.data.id}, callback);
					layer.close(index);
				});
				return;
			}
		});
	}
	</script>
{/block}
<!-- /脚本 -->