{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>手动锁定库存</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="lockForm" id="lockForm">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品<span class="text-danger">*</span></label>
                            <div class="layui-input-block">
                                <select name="product_id" lay-verify="required" lay-search lay-filter="product_id">
                                    <option value="">请选择产品</option>
                                    {volist name="products" id="product"}
                                    <option value="{$product.id}" data-unit="{$product.unit}">{$product.title} ({$product.material_code})</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">仓库<span class="text-danger">*</span></label>
                            <div class="layui-input-block">
                                <select name="warehouse_id" lay-verify="required" lay-filter="warehouse_id">
                                    <option value="">请选择仓库</option>
                                    {volist name="warehouses" id="warehouse"}
                                    <option value="{$warehouse.id}">{$warehouse.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">锁定数量<span class="text-danger">*</span></label>
                            <div class="layui-input-block">
                                <div class="layui-input-group">
                                    <input type="number" name="quantity" placeholder="请输入锁定数量" class="layui-input" lay-verify="required|number" step="0.01" min="0.01">
                                    <div class="layui-input-split layui-input-suffix" id="unit_display">件</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">关联类型<span class="text-danger">*</span></label>
                            <div class="layui-input-block">
                                <select name="ref_type" lay-verify="required">
                                    <option value="">请选择关联类型</option>
                                    <option value="order">销售订单</option>
                                    <option value="production">生产订单</option>
                                    <option value="transfer">调拨单</option>
                                    <option value="outbound">出库单</option>
                                    <option value="manual">手动锁定</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">关联ID</label>
                            <div class="layui-input-block">
                                <input type="number" name="ref_id" placeholder="请输入关联ID" class="layui-input" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">关联单号</label>
                            <div class="layui-input-block">
                                <input type="text" name="ref_no" placeholder="请输入关联单号" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="notes" placeholder="请输入备注信息" class="layui-textarea" rows="3"></textarea>
                    </div>
                </div>
                
                <!-- 库存状态显示 -->
                <div class="layui-form-item" id="inventory_status" style="display:none;">
                    <label class="layui-form-label">库存状态</label>
                    <div class="layui-input-block">
                        <div class="layui-card" style="margin:0;">
                            <div class="layui-card-body" style="padding:10px;">
                                <div class="layui-row">
                                    <div class="layui-col-md3">
                                        <div class="text-center">
                                            <div class="text-muted">总库存</div>
                                            <div class="text-primary" style="font-size:18px;font-weight:bold;" id="total_quantity">0</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="text-center">
                                            <div class="text-muted">可用库存</div>
                                            <div class="text-success" style="font-size:18px;font-weight:bold;" id="available_quantity">0</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="text-center">
                                            <div class="text-muted">已锁定</div>
                                            <div class="text-warning" style="font-size:18px;font-weight:bold;" id="locked_quantity">0</div>
                                        </div>
                                    </div>
                                    <div class="layui-col-md3">
                                        <div class="text-center">
                                            <div class="text-muted">锁定后可用</div>
                                            <div class="text-info" style="font-size:18px;font-weight:bold;" id="after_lock_quantity">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="lockSubmit">确认锁定</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','form'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;
        
        // 产品选择变化时更新单位显示和库存状态
        form.on('select(product_id)', function(data){
            var option = $(data.elem).find('option:selected');
            var unit = option.data('unit') || '件';
            $('#unit_display').text(unit);
            
            // 如果同时选择了仓库，则查询库存状态
            var warehouseId = $('select[name="warehouse_id"]').val();
            if(data.value && warehouseId) {
                checkInventoryStatus(data.value, warehouseId);
            } else {
                $('#inventory_status').hide();
            }
        });
        
        // 仓库选择变化时更新库存状态
        form.on('select(warehouse_id)', function(data){
            var productId = $('select[name="product_id"]').val();
            if(productId && data.value) {
                checkInventoryStatus(productId, data.value);
            } else {
                $('#inventory_status').hide();
            }
        });
        
        // 锁定数量变化时更新锁定后可用数量
        $('input[name="quantity"]').on('input', function(){
            var quantity = parseFloat($(this).val()) || 0;
            var availableQuantity = parseFloat($('#available_quantity').text()) || 0;
            var afterLockQuantity = availableQuantity - quantity;
            $('#after_lock_quantity').text(afterLockQuantity.toFixed(2));
            
            // 检查库存是否充足
            if(afterLockQuantity < 0) {
                $('#after_lock_quantity').removeClass('text-info').addClass('text-danger');
                layer.tips('库存不足，无法锁定', this, {tips: 1});
            } else {
                $('#after_lock_quantity').removeClass('text-danger').addClass('text-info');
            }
        });
        
        // 查询库存状态
        function checkInventoryStatus(productId, warehouseId) {
            tool.post('/warehouse/InventoryLock/checkLockable', {
                product_id: productId,
                warehouse_id: warehouseId,
                quantity: 1 // 临时数量，用于检查
            }, function(res) {
                // 这里需要调用实际的库存查询接口
                // 暂时使用模拟数据
                $('#total_quantity').text('100.00');
                $('#available_quantity').text('80.00');
                $('#locked_quantity').text('20.00');
                $('#after_lock_quantity').text('80.00');
                $('#inventory_status').show();
            });
        }
        
        // 表单提交
        form.on('submit(lockSubmit)', function(data){
            var field = data.field;
            
            // 验证库存是否充足
            var quantity = parseFloat(field.quantity) || 0;
            var availableQuantity = parseFloat($('#available_quantity').text()) || 0;
            
            if(quantity > availableQuantity) {
                layer.msg('锁定数量超过可用库存，无法锁定');
                return false;
            }
            
            // 确认锁定
            layer.confirm('确定要锁定 ' + quantity + ' ' + $('#unit_display').text() + ' 的库存吗？', {
                icon: 3,
                title: '确认锁定'
            }, function(index) {
                tool.post('/warehouse/InventoryLock/lock', field, function(res) {
                    if(res.code == 0) {
                        layer.msg('库存锁定成功');
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if(parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg);
                    }
                });
                layer.close(index);
            });
            
            return false;
        });
    }
</script>
{/block}
