# 生产入库记录接口修复

## 问题描述
生产入库记录接口 `ajax_StockList` 出现数据库表不存在的错误：
```
SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sem_erp_oa.oa_warehouse_other_input' doesn't exist
```

## 接口信息
- **URL**: `http://tc.xinqiyu.cn:8830/api/index/ajax_StockList?order_id=27&page=1&limit=10`
- **文件**: `app/api/controller/Index.php`
- **方法**: `ajax_StockList()`

## 问题分析

### 原有实现问题
1. **使用废弃的表**：
   - `warehouse_other_input` - 其他入库单主表（已废弃）
   - `warehouse_other_input_detail` - 其他入库单明细表（已废弃）

2. **查询逻辑过时**：
   ```php
   $list = Db::name('warehouse_other_input')
       ->alias('woi')
       ->leftJoin('warehouse_other_input_detail woid', 'woi.id = woid.input_id')
       ->leftJoin('admin a', 'woi.created_by = a.id')
       ->where('woi.related_bill_no', $produce_order['order_no'])
       ->field('woi.*, SUM(IFNULL(woid.quantity, 0)) as total_quantity, a.name as operator')
       ->group('woi.id')
       ->select()
       ->toArray();
   ```

### 新的数据表结构
现在系统使用 `oa_inventory_transaction` 表记录所有库存变动：

```sql
CREATE TABLE `oa_inventory_transaction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
  `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
  `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
  `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
  `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
  `ref_id` int(11) DEFAULT '0' COMMENT '关联ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT '0' COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transaction_no` (`transaction_no`),
  KEY `idx_product` (`product_id`),
  KEY `idx_warehouse` (`warehouse_id`),
  KEY `idx_type` (`transaction_type`),
  KEY `idx_ref` (`ref_type`,`ref_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';
```

## 修复方案

### 1. 重构查询逻辑
**新的查询实现**：
```php
// 从库存流水表查询生产入库记录
$query = Db::name('inventory_transaction')
    ->alias('it')
    ->leftJoin('admin a', 'it.created_by = a.id')
    ->leftJoin('product p', 'it.product_id = p.id')
    ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
    ->where('it.ref_type', 'production_order')
    ->where('it.ref_id', $order_id)
    ->where('it.transaction_type', 'in') // 只查询入库记录
    ->field([
        'it.id',
        'it.transaction_no',
        'it.product_id',
        'it.warehouse_id',
        'it.quantity',
        'it.ref_no as order_no',
        'it.notes',
        'it.create_time',
        'a.name as operator',
        'p.title as product_name',
        'p.material_code as product_code',
        'w.name as warehouse_name'
    ]);
```

### 2. 修复参数类型问题
**问题**：`page()` 方法需要整数参数，但接收到字符串
**解决**：
```php
$order_id = intval($param['order_id']);
$page = intval($param['page'] ?? 1);
$limit = intval($param['limit'] ?? 10);
```

### 3. 修复日期格式化问题
**问题**：`date()` 函数的时间戳参数类型错误
**解决**：
```php
$createTime = intval($item['create_time']); // 确保是整数类型
$item['create_time'] = $createTime ? date('Y-m-d H:i:s', $createTime) : '';
$item['input_date'] = date('Y-m-d', $createTime ?: time());
```

### 4. 保持字段兼容性
为了保持前端兼容性，添加了字段映射：
```php
$item['total_quantity'] = $item['quantity']; // 兼容原字段名
$item['input_no'] = $item['transaction_no']; // 兼容原字段名
$item['status'] = '已入库'; // 库存流水记录都是已完成的
```

## 修复后的完整实现

```php
//ajax 入库记录列表（重构版本）
public function ajax_StockList()
{
    $param = get_params();
    $order_id = intval($param['order_id']);
    $page = intval($param['page'] ?? 1);
    $limit = intval($param['limit'] ?? 10);
    
    $produce_order = Db::name('produce_order')->where('id', $order_id)->find();
    if (empty($produce_order)) {
        return json(['code' => 1, 'msg' => '订单不存在', 'count' => 0, 'data' => []]);
    }

    try {
        // 从库存流水表查询生产入库记录
        $query = Db::name('inventory_transaction')
            ->alias('it')
            ->leftJoin('admin a', 'it.created_by = a.id')
            ->leftJoin('product p', 'it.product_id = p.id')
            ->leftJoin('warehouse w', 'it.warehouse_id = w.id')
            ->where('it.ref_type', 'production_order')
            ->where('it.ref_id', $order_id)
            ->where('it.transaction_type', 'in') // 只查询入库记录
            ->field([
                'it.id',
                'it.transaction_no',
                'it.product_id',
                'it.warehouse_id',
                'it.quantity',
                'it.ref_no as order_no',
                'it.notes',
                'it.create_time',
                'a.name as operator',
                'p.title as product_name',
                'p.material_code as product_code',
                'w.name as warehouse_name'
            ]);

        // 获取总数
        $count = $query->count();

        // 分页查询
        $list = $query->order('it.create_time desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        // 处理数据格式
        foreach ($list as &$item) {
            $createTime = intval($item['create_time']); // 确保是整数类型
            $item['total_quantity'] = $item['quantity']; // 兼容原字段名
            $item['status'] = '已入库'; // 库存流水记录都是已完成的
            $item['create_time'] = $createTime ? date('Y-m-d H:i:s', $createTime) : '';
            $item['input_date'] = date('Y-m-d', $createTime ?: time()); // 兼容原字段
            $item['input_no'] = $item['transaction_no']; // 兼容原字段名
        }

        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $count,
            'data' => $list
        ]);

    } catch (\Exception $e) {
        \think\facade\Log::error('获取生产入库记录失败', [
            'order_id' => $order_id,
            'error' => $e->getMessage()
        ]);

        return json([
            'code' => 1,
            'msg' => '获取入库记录失败：' . $e->getMessage(),
            'count' => 0,
            'data' => []
        ]);
    }
}
```

## 业务逻辑说明

### 查询条件
- `ref_type = 'production_order'` - 关联类型为生产订单
- `ref_id = $order_id` - 关联的生产订单ID
- `transaction_type = 'in'` - 只查询入库记录

### 返回字段
- `transaction_no` - 库存流水号（映射为 input_no）
- `quantity` - 入库数量（映射为 total_quantity）
- `product_name` - 产品名称
- `warehouse_name` - 仓库名称
- `operator` - 操作人员
- `create_time` - 创建时间
- `status` - 状态（固定为"已入库"）

### 分页支持
- 支持标准的分页参数：`page` 和 `limit`
- 返回总记录数 `count`

## 修复效果

### 修复前：
- ❌ 数据库表不存在错误
- ❌ 无法查看生产入库记录
- ❌ 影响生产管理流程

### 修复后：
- ✅ 正确查询库存流水记录
- ✅ 显示生产订单的入库历史
- ✅ 支持分页和详细信息展示
- ✅ 保持前端兼容性

## 测试验证

### 1. 接口测试
访问：`http://tc.xinqiyu.cn:8830/api/index/ajax_StockList?order_id=27&page=1&limit=10`

**预期结果**：
- 返回生产订单27的入库记录列表
- 包含产品信息、数量、时间、操作人等
- 支持分页显示

### 2. 数据验证SQL
```sql
-- 验证生产订单27的入库记录
SELECT 
    it.transaction_no,
    it.quantity,
    it.create_time,
    p.title as product_name,
    w.name as warehouse_name,
    a.name as operator
FROM oa_inventory_transaction it
LEFT JOIN oa_product p ON it.product_id = p.id
LEFT JOIN oa_warehouse w ON it.warehouse_id = w.id
LEFT JOIN oa_admin a ON it.created_by = a.id
WHERE it.ref_type = 'production_order'
AND it.ref_id = 27
AND it.transaction_type = 'in'
ORDER BY it.create_time DESC;
```

## 总结

通过将查询逻辑从废弃的入库单表迁移到新的库存流水表，成功修复了生产入库记录接口。新的实现：

1. **数据准确性**：基于实际的库存变动记录
2. **系统一致性**：与新的库存管理系统保持一致
3. **功能完整性**：支持分页、排序、详细信息展示
4. **前端兼容性**：保持原有的字段名和数据格式

现在生产订单详情页面应该能够正常显示入库记录了。
