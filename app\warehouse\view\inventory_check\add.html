{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<style>
    .layui-form-item {
        margin-bottom: 20px;
    }
    .product-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e6e6e6;
        padding: 10px;
        margin-top: 10px;
    }
    .product-item {
        padding: 8px;
        border: 1px solid #e6e6e6;
        margin-bottom: 5px;
        cursor: pointer;
        border-radius: 3px;
    }
    .product-item:hover {
        background-color: #f5f5f5;
    }
    .product-item.selected {
        background-color: #e6f7ff;
        border-color: #1890ff;
    }
    .selected-products {
        margin-top: 15px;
    }
    .selected-product {
        display: inline-block;
        background-color: #f0f0f0;
        padding: 5px 10px;
        margin: 3px;
        border-radius: 3px;
        position: relative;
    }
    .selected-product .remove {
        margin-left: 8px;
        color: #ff4d4f;
        cursor: pointer;
    }
</style>

<div class="layui-fluid" style="padding: 20px;">
        <form class="layui-form" lay-filter="addForm">
            <div class="layui-row layui-col-space20">
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">选择仓库 <span style="color: red;">*</span></label>
                        <div class="layui-input-block">
                            <select name="warehouse_id" lay-filter="warehouse_select" lay-verify="required">
                                <option value="">请选择仓库</option>
                                {volist name="warehouses" id="warehouse"}
                                <option value="{$warehouse.id}">{$warehouse.name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">盘点日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="check_date" id="checkDate" placeholder="选择盘点日期" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="notes" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">选择产品</label>
                <div class="layui-input-block">
                    <input type="text" id="productSearch" placeholder="输入产品名称或编号搜索" class="layui-input" disabled>
                    <div class="product-list" id="productList" style="display: none;"></div>
                    <div class="selected-products" id="selectedProducts"></div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="submit">创建盘点单</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','laydatePlus'];
    function gouguInit() {
        console.log('开始初始化库存盘点添加页面...');

        // 检查必要的模块是否加载
        if (!layui.tool) {
            console.error('tool模块未加载');
            layer.msg('页面初始化失败：tool模块未加载', {icon: 2});
            return;
        }

        if (!layui.laydatePlus) {
            console.error('laydatePlus模块未加载');
            layer.msg('页面初始化失败：laydatePlus模块未加载', {icon: 2});
            return;
        }

        var form = layui.form;
        var layer = layui.layer;
        var tool = layui.tool;
        var laydatePlus = layui.laydatePlus;

        console.log('模块加载成功:', {form: !!form, layer: !!layer, tool: !!tool, laydatePlus: !!laydatePlus});

        var selectedProducts = [];
        var currentWarehouseId = '';

        // 初始化日期选择器
        try {
            var checkDate = new laydatePlus({
                'target': 'checkDate',
                'type': 'datetime'
            });
            console.log('日期选择器初始化成功');
        } catch (e) {
            console.error('日期选择器初始化失败:', e);
            // 降级使用普通laydate
            layui.laydate.render({
                elem: '#checkDate',
                type: 'datetime',
                value: new Date()
            });
        }

            // 仓库列表已通过服务端渲染

            // 仓库选择变化
            form.on('select(warehouse_select)', function(data){
                currentWarehouseId = data.value;
                if (currentWarehouseId) {
                    $('#productSearch').prop('disabled', false).attr('placeholder', '输入产品名称或编号搜索');
                } else {
                    $('#productSearch').prop('disabled', true).attr('placeholder', '请先选择仓库');
                    $('#productList').hide();
                }
                // 清空已选产品
                selectedProducts = [];
                updateSelectedProducts();
            });

            // 产品搜索
            $('#productSearch').on('input', function(){
                var keyword = $(this).val().trim();
                if (keyword.length >= 2 && currentWarehouseId) {
                    searchProducts(keyword);
                } else {
                    $('#productList').hide();
                }
            });

            // 点击其他地方隐藏产品列表
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#productSearch, #productList').length) {
                    $('#productList').hide();
                }
            });

            // 表单提交
            form.on('submit(submit)', function(data){
                console.log('表单提交开始:', data);

                if (selectedProducts.length === 0) {
                    layer.msg('请至少选择一个产品进行盘点', {icon: 2});
                    return false;
                }

                var submitData = data.field;
                submitData.products = selectedProducts.map(function(p) { return p.id; });

                // 转换日期为时间戳
                if (submitData.check_date) {
                    submitData.check_date = new Date(submitData.check_date).getTime() / 1000;
                }

                console.log('提交数据:', submitData);

                let callback = function (e) {
                    console.log('提交结果:', e);
                    layer.msg(e.msg);
                    if (e.code == 0) {
                        parent.layer.closeAll();
                    }
                }
                tool.post("/warehouse/inventory_check/add", submitData, callback);

                return false;
            });

            // 仓库列表已通过服务端渲染，无需动态加载

            // 搜索产品
            function searchProducts(keyword) {
                console.log('搜索产品:', keyword, '仓库ID:', currentWarehouseId);
                tool.get('/warehouse/inventory_check/getProducts', {
                    keyword: keyword,
                    warehouse_id: currentWarehouseId
                }, function(res) {
                    console.log('产品搜索结果:', res);
                    if (res.code == 0 && res.data.length > 0) {
                        var html = '';
                        $.each(res.data, function(i, product) {
                            var isSelected = selectedProducts.some(function(p) { return p.id == product.id; });
                            var selectedClass = isSelected ? 'selected' : '';
                            
                            html += '<div class="product-item ' + selectedClass + '" data-product=\'' + JSON.stringify(product) + '\'>';
                            html += '<div><strong>' + product.title + '</strong></div>';
                            html += '<div style="color: #666; font-size: 12px;">';
                            html += '编号: ' + (product.material_code || '无') + ' | ';
                            html += '规格: ' + (product.specs || '无') + ' | ';
                            html += '单位: ' + (product.unit || '无') + ' | ';
                            html += '系统库存: ' + (product.system_quantity || 0);
                            html += '</div>';
                            html += '</div>';
                        });
                        $('#productList').html(html).show();
                    } else {
                        $('#productList').html('<div style="text-align: center; padding: 20px; color: #999;">暂无数据</div>').show();
                    }
                });
            }

            // 选择产品
            $(document).on('click', '.product-item', function() {
                var product = JSON.parse($(this).attr('data-product'));
                var isSelected = selectedProducts.some(function(p) { return p.id == product.id; });
                
                if (isSelected) {
                    // 取消选择
                    selectedProducts = selectedProducts.filter(function(p) { return p.id != product.id; });
                    $(this).removeClass('selected');
                } else {
                    // 添加选择
                    selectedProducts.push(product);
                    $(this).addClass('selected');
                }
                
                updateSelectedProducts();
            });

            // 更新已选产品显示
            function updateSelectedProducts() {
                var html = '';
                if (selectedProducts.length > 0) {
                    html += '<div style="margin-bottom: 10px; color: #666;">已选择 ' + selectedProducts.length + ' 个产品：</div>';
                    $.each(selectedProducts, function(i, product) {
                        html += '<span class="selected-product">';
                        html += product.title;
                        html += '<span class="remove" data-id="' + product.id + '">×</span>';
                        html += '</span>';
                    });
                }
                $('#selectedProducts').html(html);
            }

        // 移除已选产品
        $(document).on('click', '.selected-product .remove', function() {
            var productId = $(this).data('id');
            selectedProducts = selectedProducts.filter(function(p) { return p.id != productId; });
            updateSelectedProducts();

            // 更新产品列表中的选中状态
            $('.product-item').each(function() {
                var product = JSON.parse($(this).attr('data-product'));
                if (product.id == productId) {
                    $(this).removeClass('selected');
                }
            });
        });

        console.log('库存盘点添加页面初始化完成');
    }
</script>
{/block}
