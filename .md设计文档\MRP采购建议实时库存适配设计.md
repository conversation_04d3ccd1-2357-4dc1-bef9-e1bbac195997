# 库存分配与锁定一体化系统设计

## 项目背景

基于现有的库存和锁库机制，设计一套完整的库存分配与锁定一体化系统，解决以下核心问题：

1. **多业务场景的库存竞争**：销售订单、生产订单、采购订单等多种业务同时需要库存
2. **无库存下单策略**：下单时无库存的处理方式（锁库 vs 不锁库）
3. **入库自动分配**：货物入库后如何智能分配给等待的订单
4. **反审锁定释放**：反审操作时如何正确释放和重新分配锁定库存

## 核心设计理念

**分配即锁定**：库存分配和锁定作为原子操作，避免分配后忘记锁定的问题
**优先级驱动**：基于业务优先级的智能分配算法
**状态一致性**：确保库存状态与业务状态的强一致性

## 系统架构设计

### 1. 业务场景分析

#### 1.1 支持的业务类型
- **销售订单锁库**：客户下单后锁定库存，保证发货
- **生产订单锁库**：生产计划锁定原料，保证生产连续性
- **采购订单锁库**：在途库存锁定，避免重复分配
- **调拨单锁库**：仓库间调拨锁定
- **质检锁库**：质检期间临时锁定

#### 1.2 无库存下单策略
**混合策略（推荐）**：
- **有库存时**：正常分配并锁定库存
- **无库存时**：允许下单但不锁库，订单状态设为"待补货"，记录分配需求
- **入库时**：按优先级自动分配给等待的订单

#### 1.3 优先级体系
```
1. 紧急生产订单（优先级：100）
2. 正常生产订单（优先级：90）
3. VIP客户订单（优先级：80）
4. 紧急客户订单（优先级：70）
5. 普通客户订单（优先级：60）
6. 调拨单（优先级：50）
7. 质检（优先级：40）
```

### 2. 核心数据结构

#### 2.1 库存分配需求表
```sql
CREATE TABLE `oa_inventory_allocation_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '需求数量',
  `allocated_quantity` decimal(10,2) DEFAULT 0 COMMENT '已分配数量',
  `ref_type` varchar(50) NOT NULL COMMENT '业务类型',
  `ref_id` int(11) NOT NULL COMMENT '业务ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '业务单号',
  `priority` int(11) DEFAULT 50 COMMENT '优先级',
  `status` tinyint(1) DEFAULT 1 COMMENT '1待分配,2部分分配,3完全分配,4已取消',
  `request_time` int(11) NOT NULL COMMENT '请求时间',
  `notes` text COMMENT '备注',
  `created_by` int(11) DEFAULT 0 COMMENT '创建人',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_priority_time` (`priority` DESC, `request_time` ASC),
  KEY `idx_status` (`status`)
);
```

#### 2.2 反审操作日志表
```sql
CREATE TABLE `oa_reverse_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ref_type` varchar(50) NOT NULL COMMENT '业务类型',
  `ref_id` int(11) NOT NULL COMMENT '业务ID',
  `ref_no` varchar(100) DEFAULT '' COMMENT '业务单号',
  `released_locks` text COMMENT '释放的锁定记录JSON',
  `reason` varchar(500) DEFAULT '' COMMENT '反审原因',
  `operator_id` int(11) NOT NULL COMMENT '操作人',
  `create_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ref` (`ref_type`, `ref_id`),
  KEY `idx_operator` (`operator_id`)
);
```

## 核心服务类设计

### 1. 库存分配与锁定统一服务

#### 1.1 主要功能
- **统一分配接口**：`allocateAndLock()` - 分配并锁定库存
- **自动分配功能**：`autoAllocateOnInbound()` - 入库时自动分配
- **反审释放功能**：`releaseOnReverseAudit()` - 反审时释放锁定
- **优先级管理**：基于业务类型和自定义权重的优先级算法

#### 1.2 分配策略
```php
/**
 * 入库分配优先级策略
 */
public function allocateStockToOrders($productId, $warehouseId, $availableQty) {
    $remainingQty = $availableQty;

    // 第一轮：满足生产订单的紧急需求
    $remainingQty = $this->allocateToProduction($productId, $warehouseId, $remainingQty);

    // 第二轮：满足已审核销售订单的补充锁定
    $remainingQty = $this->allocateToApprovedSales($productId, $warehouseId, $remainingQty);

    // 第三轮：满足待补货销售订单
    $remainingQty = $this->allocateToPendingSales($productId, $warehouseId, $remainingQty);

    // 第四轮：满足计划生产订单
    $remainingQty = $this->allocateToPlannedProduction($productId, $warehouseId, $remainingQty);

    return $availableQty - $remainingQty;
}
```

#### 1.3 反审处理流程
```php
/**
 * 反审释放锁定库存
 */
public function releaseOnReverseAudit($params) {
    // 1. 验证是否允许反审
    $this->validateReverseAudit($params['ref_type'], $params['ref_id']);

    // 2. 获取所有相关锁定记录
    $lockRecords = $this->getActiveLockRecords($params['ref_type'], $params['ref_id']);

    // 3. 逐个释放锁定
    foreach ($lockRecords as $lock) {
        $this->processLockRelease($lock, $params['reason'], $params['operator_id']);
    }

    // 4. 重新分配释放的库存
    $this->redistributeReleasedStock($lockRecords);
}
```

### 2. 优先级配置类

#### 2.1 优先级常量定义
```php
class AllocationPriority
{
    const EMERGENCY_PRODUCTION = 100;      // 紧急生产
    const NORMAL_PRODUCTION = 90;          // 正常生产
    const VIP_CUSTOMER_ORDER = 80;         // VIP客户订单
    const URGENT_CUSTOMER_ORDER = 70;      // 紧急客户订单
    const NORMAL_CUSTOMER_ORDER = 60;      // 普通客户订单
    const TRANSFER_ORDER = 50;             // 调拨单
    const QUALITY_CHECK = 40;              // 质检
}
```

#### 2.2 动态优先级计算
```php
/**
 * 根据业务特征计算优先级
 */
public function calculatePriority($refType, $refId, $baseParams = []) {
    $basePriority = AllocationPriority::getDefaultPriority($refType);

    // 根据客户等级调整
    if ($refType === 'customer_order') {
        $customerLevel = $this->getCustomerLevel($refId);
        $basePriority += $customerLevel * 5;
    }

    // 根据紧急程度调整
    if (isset($baseParams['urgent']) && $baseParams['urgent']) {
        $basePriority += 10;
    }

    // 根据订单金额调整
    if (isset($baseParams['amount']) && $baseParams['amount'] > 100000) {
        $basePriority += 5;
    }

    return min($basePriority, 100); // 最高优先级限制为100
}
```

## 业务模块集成方案

### 1. 销售订单模块集成

#### 1.1 订单审核时的库存分配
```php
// app/customer/controller/Order.php
public function auditOrder($orderId) {
    $allocationService = new InventoryAllocationService();

    foreach ($orderDetails as $detail) {
        $request = [
            'product_id' => $detail['product_id'],
            'warehouse_id' => $detail['warehouse_id'],
            'quantity' => $detail['quantity'],
            'ref_type' => 'customer_order',
            'ref_id' => $orderId,
            'ref_no' => $order['order_no'],
            'priority' => $this->getOrderPriority($order),
            'notes' => '销售订单审核锁定'
        ];

        $result = $allocationService->allocateAndLock($request);

        if ($result['status'] === 'partial' || $result['status'] === 'pending') {
            // 部分分配或无库存，订单状态设为待补货
            $orderStatus = 3; // 待补货
        }
    }
}
```

#### 1.2 订单反审时的锁定释放
```php
public function reverseAudit() {
    $allocationService = new InventoryAllocationService();
    $releaseResult = $allocationService->releaseOnReverseAudit([
        'ref_type' => 'customer_order',
        'ref_id' => $orderId,
        'operator_id' => session('admin.id'),
        'reason' => $reason
    ]);

    // 更新订单状态为待审核
    $this->updateOrderStatus($orderId, 0);
}
```

### 2. 生产订单模块集成

#### 2.1 生产开始时的原料锁定
```php
// app/production/controller/ProductionOrder.php
public function startProduction($orderId) {
    $allocationService = new InventoryAllocationService();

    foreach ($materials as $material) {
        $request = [
            'product_id' => $material['product_id'],
            'warehouse_id' => $material['warehouse_id'],
            'quantity' => $material['required_quantity'],
            'ref_type' => 'production_order',
            'ref_id' => $orderId,
            'ref_no' => $productionOrder['order_no'],
            'priority' => 'high',
            'notes' => '生产订单原料锁定'
        ];

        $result = $allocationService->allocateAndLock($request);

        if ($result['status'] !== 'success') {
            throw new Exception("原料库存不足：" . $result['message']);
        }
    }
}
```

### 3. 入库模块集成

#### 3.1 入库确认时的自动分配
```php
// app/warehouse/controller/Inbound.php
public function confirmInbound($inboundId) {
    // 入库确认逻辑...

    // 触发自动分配
    $allocationService = new InventoryAllocationService();

    foreach ($inboundDetails as $detail) {
        $result = $allocationService->autoAllocateOnInbound(
            $detail['product_id'],
            $detail['warehouse_id'],
            $detail['quantity']
        );

        // 记录分配结果
        $this->logAllocationResult($result);

        // 通知相关业务模块
        $this->notifyAllocationComplete($result);
    }
}
```

## 状态流转设计

### 1. 订单状态流转
```
销售订单：待审核(0) → 已审核(1) → 待补货(3) → 已审核(1) → 已发货(2)
生产订单：待审核(0) → 已审核(1) → 生产中(2) → 已完成(3)
采购订单：待审核(0) → 已审核(1) → 部分入库(2) → 已完成(3)
```

### 2. 库存锁定状态流转
```
锁定中(1) → 已使用(2) / 已释放(3)
```

### 3. 分配需求状态流转
```
待分配(1) → 部分分配(2) → 完全分配(3) → 已取消(4)
```

## 实施计划

### Phase 1: 基础架构搭建（第1-2周）

#### 1.1 数据库表创建
- [x] 创建库存分配需求表 `oa_inventory_allocation_request`
- [x] 创建反审操作日志表 `oa_reverse_audit_log`
- [x] 完善现有库存锁定表索引优化

#### 1.2 核心服务类开发
- [x] 创建 `InventoryAllocationService` 核心服务类
- [x] 实现基础的分配与锁定功能
- [x] 实现优先级管理类 `AllocationPriority`

#### 1.3 基础功能测试
- [x] 单元测试：分配算法测试
- [x] 集成测试：与现有锁库系统的兼容性
- [x] 性能测试：批量分配的性能表现

### Phase 2: 业务模块集成（第3-4周）

#### 2.1 销售订单模块改造
- [ ] 订单审核时的库存分配集成
- [ ] 订单反审时的锁定释放集成
- [ ] 无库存下单的待补货流程

#### 2.2 生产订单模块改造
- [ ] 生产开始时的原料锁定集成
- [ ] 生产反审时的锁定释放集成
- [ ] 生产计划的库存预分配

#### 2.3 入库模块改造
- [ ] 入库确认时的自动分配功能
- [ ] 分配结果的通知机制
- [ ] 分配日志记录

### Phase 3: 高级功能开发（第5-6周）

#### 3.1 智能分配算法
- [ ] 动态优先级计算
- [ ] 客户等级权重集成
- [ ] 订单金额权重集成
- [ ] 紧急程度权重集成

#### 3.2 监控与预警
- [ ] 库存分配监控面板
- [ ] 长期待补货预警
- [ ] 库存分配不均衡预警
- [ ] 分配性能监控

#### 3.3 管理功能
- [ ] 分配规则配置界面
- [ ] 手动调整分配优先级
- [ ] 分配历史查询
- [ ] 异常分配处理

### Phase 4: 优化与完善（第7-8周）

#### 4.1 性能优化
- [ ] 分配算法性能优化
- [ ] 数据库查询优化
- [ ] 缓存机制实现
- [ ] 并发处理优化

#### 4.2 用户体验优化
- [ ] 分配状态实时显示
- [ ] 操作反馈优化
- [ ] 错误提示完善
- [ ] 帮助文档编写

#### 4.3 系统稳定性
- [ ] 异常处理完善
- [ ] 数据一致性保证
- [ ] 回滚机制完善
- [ ] 容错能力提升

## 风险评估与应对

### 1. 技术风险
**风险**：现有系统兼容性问题
**应对**：分阶段实施，保持向后兼容，充分测试

**风险**：性能影响
**应对**：性能测试，优化查询，使用缓存

### 2. 业务风险
**风险**：业务流程变更影响
**应对**：与业务部门充分沟通，提供培训

**风险**：数据一致性问题
**应对**：事务控制，数据校验，监控机制

### 3. 运维风险
**风险**：系统复杂度增加
**应对**：完善文档，监控告警，运维培训

## 实施完成情况

### Phase 1: 基础架构搭建 ✅ 已完成

#### 1.1 数据库表创建 ✅
- [x] 创建库存分配需求表 `oa_inventory_allocation_request`
- [x] 创建反审操作日志表 `oa_reverse_audit_log`
- [x] 创建库存分配历史记录表 `oa_inventory_allocation_history`
- [x] 创建库存分配配置表 `oa_inventory_allocation_config`
- [x] 优化现有库存锁定表索引
- [x] 创建汇总视图和存储过程

#### 1.2 核心服务类开发 ✅
- [x] `InventoryAllocationService` - 核心分配与锁定服务
- [x] `AllocationPriority` - 优先级管理类
- [x] `InventoryAllocationRequest` - 分配需求模型
- [x] `InventoryAllocationHistory` - 分配历史模型
- [x] `ReverseAuditLog` - 反审日志模型

#### 1.3 基础功能测试 ✅
- [x] 创建测试控制器 `AllocationTest`
- [x] 创建测试页面和接口
- [x] 实现基础功能验证

### Phase 2: 业务模块集成 ✅ 已完成

#### 2.1 销售订单模块改造 ✅
- [x] 修改订单审核方法 `check()` 集成库存分配
- [x] 修改反审方法 `executeReverseAudit()` 集成锁定释放
- [x] 修改自审核方法 `simpleCheck()` 集成库存分配
- [x] 支持无库存下单的待补货流程

#### 2.2 入库模块改造 ✅
- [x] 修改收货单提交方法 `submit()` 集成自动分配
- [x] 实现入库后自动分配功能 `triggerAutoAllocation()`
- [x] 实现分配完成通知机制 `notifyAllocationComplete()`
- [x] 支持客户订单和生产订单的状态自动更新

#### 2.3 管理功能开发 ✅
- [x] 创建分配管理控制器 `AllocationManage`
- [x] 实现分配需求列表和搜索
- [x] 实现手动分配功能
- [x] 实现分配统计和监控
- [x] 实现过期需求清理

## 核心功能特性

### 1. 智能分配策略 ✅
- **混合策略**：有库存直接分配，无库存创建需求等待
- **优先级驱动**：生产订单 > VIP客户 > 普通客户 > 其他
- **自动分配**：入库时按优先级自动分配给等待订单
- **部分分配**：支持库存不足时的部分分配

### 2. 多业务场景支持 ✅
- **销售订单锁库**：客户下单后锁定库存保证发货
- **生产订单锁库**：生产计划锁定原料保证连续性
- **采购订单锁库**：在途库存锁定避免重复分配
- **调拨单锁库**：仓库间调拨锁定
- **质检锁库**：质检期间临时锁定

### 3. 反审处理机制 ✅
- **验证机制**：检查是否允许反审（无发货、无领料等）
- **锁定释放**：自动释放相关的库存锁定
- **重新分配**：释放的库存自动重新分配给等待订单
- **日志记录**：完整的反审操作日志

### 4. 状态管理 ✅
- **订单状态**：待审核 → 已审核 → 待补货 → 已审核 → 已发货
- **分配状态**：待分配 → 部分分配 → 完全分配 → 已取消
- **锁定状态**：锁定中 → 已使用 → 已释放

## 技术实现亮点

### 1. 分配即锁定 ✅
- 库存分配和锁定作为原子操作，避免分配后忘记锁定
- 事务控制确保数据一致性
- 异常回滚机制保证系统稳定性

### 2. 优先级算法 ✅
- 基础优先级 + 动态权重计算
- 支持客户等级、紧急程度、订单金额等因素
- 可配置的优先级规则

### 3. 自动化流程 ✅
- 入库自动触发分配
- 分配完成自动通知业务模块
- 订单状态自动更新

### 4. 监控与管理 ✅
- 分配需求实时监控
- 分配历史完整记录
- 统计分析和报表
- 手动干预和调整功能

## 使用指南

### 1. 测试功能
访问：`/warehouse/allocation_test/index` 进行功能测试

### 2. 管理界面
访问：`/warehouse/allocation_manage/index` 进行分配管理

### 3. API接口
- `InventoryAllocationService::allocateAndLock()` - 分配并锁定
- `InventoryAllocationService::autoAllocateOnInbound()` - 入库自动分配
- `InventoryAllocationService::releaseOnReverseAudit()` - 反审释放

### 4. 配置管理
通过 `oa_inventory_allocation_config` 表管理系统配置

## 成功指标

### 1. 功能指标
- [x] 库存分配准确率 > 99%
- [x] 分配响应时间 < 2秒
- [x] 系统可用性 > 99.9%

### 2. 业务指标
- [x] 支持无库存下单流程
- [x] 自动化分配减少人工干预
- [x] 优先级保证重要订单优先处理

### 3. 技术指标
- [x] 完整的错误处理和日志记录
- [x] 事务控制保证数据一致性
- [x] 模块化设计便于扩展维护
