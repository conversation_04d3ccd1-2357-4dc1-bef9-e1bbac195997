{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-page">
	<h3 class="pb-3">服务内容</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray">服务内容<font>*</font></td>
			<td colspan="3"><input type="text" name="title" lay-verify="required" autocomplete="off" placeholder="请输入服务内容" lay-reqText="请输入服务内容" class="layui-input" value="{$detail.title|default=''}"></td>
		</tr>
		<tr>
			<td class="layui-td-gray">费用(元)<font>*</font></td>
			<td><input type="text" name="price" lay-verify="required" autocomplete="off" placeholder="请输入费用" lay-reqText="请输入费用" class="layui-input" value="{$detail.price|default=''}"></td>
			<td class="layui-td-gray">排序</td>
			<td><input type="text" name="sort" placeholder="请输入排序，数字" autocomplete="off" class="layui-input" value="{$detail.sort|default=''}"></td>
		</tr>
	</table>
	<div class="pt-4">
		<input type="hidden" name="id" value="{$detail.id|default=0}"/>
		<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform">立即提交</button>
		<button type="reset" class="layui-btn layui-btn-primary">重置</button>
	</div>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	var moduleInit = ['tool'];

	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		//监听提交
		form.on('submit(webform)', function (data) {
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					tool.sideClose(1000);
				}
			}
			let clickbtn = $(this);
			tool.post("/contract/services/add", data.field, callback,clickbtn);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->