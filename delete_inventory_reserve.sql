-- 删除预占相关表和数据的SQL脚本

-- 1. 删除预占表数据
DROP TABLE IF EXISTS `oa_inventory_reserve`;

-- 2. 删除预占日志表（如果存在）
DROP TABLE IF EXISTS `oa_inventory_reserve_log`;

-- 3. 从库存表中删除预占相关字段（如果存在）
-- 检查字段是否存在，如果存在则删除
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory' 
     AND table_schema = DATABASE() 
     AND column_name = 'reserved_quantity') > 0,
    'ALTER TABLE `oa_inventory` DROP COLUMN `reserved_quantity`',
    'SELECT "Column reserved_quantity does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory' 
     AND table_schema = DATABASE() 
     AND column_name = 'allocated_quantity') > 0,
    'ALTER TABLE `oa_inventory` DROP COLUMN `allocated_quantity`',
    'SELECT "Column allocated_quantity does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 创建库存锁定表（如果不存在）
CREATE TABLE IF NOT EXISTS `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inventory_id` int(11) NOT NULL DEFAULT 0 COMMENT '库存记录ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT '仓库ID',
  `location_id` int(11) NOT NULL DEFAULT 0 COMMENT '库位ID',
  `batch_no` varchar(100) NOT NULL DEFAULT '' COMMENT '批次号',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '锁定数量',
  `unit` varchar(20) NOT NULL DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `ref_type` varchar(50) NOT NULL DEFAULT '' COMMENT '关联单据类型',
  `ref_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联单据ID',
  `ref_no` varchar(100) NOT NULL DEFAULT '' COMMENT '关联单据编号',
  `lock_time` int(11) NOT NULL DEFAULT 0 COMMENT '锁定时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `auto_release` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动释放',
  `release_condition` varchar(100) NOT NULL DEFAULT '' COMMENT '释放条件',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0已释放，1已锁定，2已使用，3已过期',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_inventory_id` (`inventory_id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_ref_type_id` (`ref_type`, `ref_id`),
  KEY `idx_status_expire` (`status`, `expire_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表';

-- 5. 检查并添加缺失的字段到库存锁定表
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'warehouse_id') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT "仓库ID" AFTER `inventory_id`',
    'SELECT "Column warehouse_id already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'location_id') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `location_id` int(11) NOT NULL DEFAULT 0 COMMENT "库位ID" AFTER `warehouse_id`',
    'SELECT "Column location_id already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'batch_no') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `batch_no` varchar(100) NOT NULL DEFAULT "" COMMENT "批次号" AFTER `location_id`',
    'SELECT "Column batch_no already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'unit') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `unit` varchar(20) NOT NULL DEFAULT "" COMMENT "单位" AFTER `quantity`',
    'SELECT "Column unit already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'cost_price') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `cost_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT "成本价" AFTER `unit`',
    'SELECT "Column cost_price already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'auto_release') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `auto_release` tinyint(1) NOT NULL DEFAULT 1 COMMENT "是否自动释放" AFTER `expire_time`',
    'SELECT "Column auto_release already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND column_name = 'release_condition') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD COLUMN `release_condition` varchar(100) NOT NULL DEFAULT "" COMMENT "释放条件" AFTER `auto_release`',
    'SELECT "Column release_condition already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 创建库存锁定日志表
CREATE TABLE IF NOT EXISTS `oa_inventory_lock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lock_id` int(11) NOT NULL DEFAULT 0 COMMENT '锁定记录ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT '仓库ID',
  `operation_type` varchar(20) NOT NULL DEFAULT '' COMMENT '操作类型：lock,release,use,expire',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作数量',
  `before_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作前数量',
  `after_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作后数量',
  `ref_type` varchar(50) NOT NULL DEFAULT '' COMMENT '关联单据类型',
  `ref_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联单据ID',
  `ref_no` varchar(100) NOT NULL DEFAULT '' COMMENT '关联单据编号',
  `notes` text COMMENT '备注',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '操作人ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lock_id` (`lock_id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_ref_type_id` (`ref_type`, `ref_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定日志表';

-- 7. 添加索引优化查询性能（检查是否已存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_product_warehouse') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD INDEX `idx_product_warehouse` (`product_id`, `warehouse_id`)',
    'SELECT "Index idx_product_warehouse already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_ref_type_id') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD INDEX `idx_ref_type_id` (`ref_type`, `ref_id`)',
    'SELECT "Index idx_ref_type_id already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_name = 'oa_inventory_lock' 
     AND table_schema = DATABASE() 
     AND index_name = 'idx_status_expire') = 0,
    'ALTER TABLE `oa_inventory_lock` ADD INDEX `idx_status_expire` (`status`, `expire_time`)',
    'SELECT "Index idx_status_expire already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 清理可能存在的预占相关代码引用
-- 这部分需要手动检查和清理代码中的引用

SELECT '库存锁定机制数据库结构更新完成！' as message;