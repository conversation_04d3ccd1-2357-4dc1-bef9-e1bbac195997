-- 删除预占相关表和数据的SQL脚本（简化版）

-- 1. 删除预占表数据
DROP TABLE IF EXISTS `oa_inventory_reserve`;

-- 2. 删除预占日志表（如果存在）
DROP TABLE IF EXISTS `oa_inventory_reserve_log`;

-- 3. 创建库存锁定表（如果不存在）
CREATE TABLE IF NOT EXISTS `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inventory_id` int(11) NOT NULL DEFAULT 0 COMMENT '库存记录ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT '仓库ID',
  `location_id` int(11) NOT NULL DEFAULT 0 COMMENT '库位ID',
  `batch_no` varchar(100) NOT NULL DEFAULT '' COMMENT '批次号',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '锁定数量',
  `unit` varchar(20) NOT NULL DEFAULT '' COMMENT '单位',
  `cost_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `ref_type` varchar(50) NOT NULL DEFAULT '' COMMENT '关联单据类型',
  `ref_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联单据ID',
  `ref_no` varchar(100) NOT NULL DEFAULT '' COMMENT '关联单据编号',
  `lock_time` int(11) NOT NULL DEFAULT 0 COMMENT '锁定时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `auto_release` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动释放',
  `release_condition` varchar(100) NOT NULL DEFAULT '' COMMENT '释放条件',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0已释放，1已锁定，2已使用，3已过期',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_inventory_id` (`inventory_id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_ref_type_id` (`ref_type`, `ref_id`),
  KEY `idx_status_expire` (`status`, `expire_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表';

-- 4. 创建库存锁定日志表
CREATE TABLE IF NOT EXISTS `oa_inventory_lock_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lock_id` int(11) NOT NULL DEFAULT 0 COMMENT '锁定记录ID',
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '产品ID',
  `warehouse_id` int(11) NOT NULL DEFAULT 0 COMMENT '仓库ID',
  `operation_type` varchar(20) NOT NULL DEFAULT '' COMMENT '操作类型：lock,release,use,expire',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作数量',
  `before_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作前数量',
  `after_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作后数量',
  `ref_type` varchar(50) NOT NULL DEFAULT '' COMMENT '关联单据类型',
  `ref_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联单据ID',
  `ref_no` varchar(100) NOT NULL DEFAULT '' COMMENT '关联单据编号',
  `notes` text COMMENT '备注',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '操作人ID',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_lock_id` (`lock_id`),
  KEY `idx_product_warehouse` (`product_id`, `warehouse_id`),
  KEY `idx_ref_type_id` (`ref_type`, `ref_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定日志表';

-- 5. 手动检查并删除库存表中的预占相关字段（如果存在）
-- ALTER TABLE `oa_inventory` DROP COLUMN `reserved_quantity`;
-- ALTER TABLE `oa_inventory` DROP COLUMN `allocated_quantity`;

SELECT '库存锁定机制数据库结构创建完成！请手动检查并删除库存表中的预占相关字段。' as message;