<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>二维码测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            padding: 20px;
        }
        
        .barcode-label {
            width: 350px;
            height: 220px;
            border: 2px solid #333;
            padding: 15px;
            margin: 20px auto;
            display: flex;
            background: white;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        
        .label-left {
            flex: 1;
            padding-right: 15px;
        }
        
        .label-right {
            width: 120px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .label-info {
            font-size: 13px;
            line-height: 1.8;
            margin-bottom: 4px;
            color: #333;
        }
        
        .label-info strong {
            display: inline-block;
            width: 70px;
            font-weight: bold;
            color: #000;
        }
        
        .qr-code {
            width: 100px;
            height: 100px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
        }
        
        .page-info {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <h2>二维码打印测试</h2>
    
    <div class="barcode-label">
        <div class="label-left">
            <div class="label-info"><strong>物料名称:</strong> 前进护手片</div>
            <div class="label-info"><strong>物料编号:</strong> BQ-1-G</div>
            <div class="label-info"><strong>物料规格:</strong> 套料1490</div>
            <div class="label-info"><strong>单位:</strong> -</div>
            <div class="label-info"><strong>打印时间:</strong> 2025-07-31 04:10</div>
            <div class="label-info"><strong>备注:</strong></div>
        </div>
        <div class="label-right">
            <canvas id="qrcode" class="qr-code"></canvas>
            <div class="page-info">1-1</div>
        </div>
    </div>

    <script>
        // 生成二维码
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('qrcode');
            const qrContent = "物料名称:前进护手片\n物料编号:BQ-1-G\n物料规格:套料1490\n单位:-\n打印时间:2025-07-31 04:10\n备注:";
            
            QRCode.toCanvas(canvas, qrContent, {
                width: 100,
                height: 100,
                margin: 1,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function (error) {
                if (error) {
                    console.error('二维码生成失败:', error);
                    const ctx = canvas.getContext('2d');
                    ctx.font = '10px Arial';
                    ctx.fillText('生成失败', 10, 50);
                }
            });
        });
    </script>
</body>
</html>
