# 跨仓库库存分配机制

## 问题背景
在实际业务中，创建分配需求时可能无法确定具体会入库到哪个仓库，但当实际收货入库时，需要根据实际的入库仓库来重新调整分配仓库。

## 解决方案

### 1. 智能仓库匹配
当收货入库时，系统会按以下优先级查找分配需求：
1. **优先匹配**：查找指定仓库的待分配需求
2. **跨仓库匹配**：如果指定仓库无需求，查找其他仓库的需求
3. **自动调整**：将其他仓库的需求调整到实际入库仓库

### 2. 核心流程
```
收货入库 → 查找同仓库需求 → 查找跨仓库需求 → 调整仓库 → 执行分配 → 锁定库存
```

## 技术实现

### 1. 修改后的分配逻辑
```php
public function autoAllocateOnInbound($productId, $warehouseId, $inboundQty)
{
    // 1. 首先获取指定仓库的待分配需求
    $pendingRequests = $this->getPendingAllocationRequests($productId, $warehouseId);
    
    // 2. 如果指定仓库没有待分配需求，查找其他仓库的需求并调整仓库
    if (empty($pendingRequests)) {
        $pendingRequests = $this->getPendingAllocationRequestsAnyWarehouse($productId);
        
        // 调整需求的仓库为实际入库仓库
        if (!empty($pendingRequests)) {
            $this->adjustAllocationWarehouse($pendingRequests, $warehouseId);
        }
    }
    
    // 3. 执行分配和锁定
    // ...
}
```

### 2. 跨仓库需求查找
```php
private function getPendingAllocationRequestsAnyWarehouse($productId)
{
    return Db::name('inventory_allocation_request')
        ->where('product_id', $productId)
        ->where('status', 'in', [1, 2]) // 待分配和部分分配
        ->where('quantity', '>', Db::raw('allocated_quantity'))
        ->order('priority DESC, request_time ASC')
        ->select()
        ->toArray();
}
```

### 3. 仓库调整机制
```php
private function adjustAllocationWarehouse($requests, $newWarehouseId)
{
    Db::startTrans();
    try {
        foreach ($requests as &$request) {
            $oldWarehouseId = $request['warehouse_id'];
            
            // 更新数据库中的仓库ID
            Db::name('inventory_allocation_request')
                ->where('id', $request['id'])
                ->update([
                    'warehouse_id' => $newWarehouseId,
                    'update_time' => time(),
                    'notes' => ($request['notes'] ?? '') . ' [仓库调整：' . $oldWarehouseId . '→' . $newWarehouseId . ']'
                ]);
            
            // 更新内存中的数据
            $request['warehouse_id'] = $newWarehouseId;
        }
        
        Db::commit();
    } catch (\Exception $e) {
        Db::rollback();
        throw new \Exception('调整分配仓库失败：' . $e->getMessage());
    }
}
```

## 业务场景

### 场景1：同仓库分配
```
需求：销售订单需要产品A，100个，仓库1
入库：采购入库产品A，50个，仓库1
结果：直接分配50个给销售订单，剩余50个继续等待
```

### 场景2：跨仓库分配
```
需求：销售订单需要产品A，100个，仓库1
入库：采购入库产品A，50个，仓库2
结果：
1. 查找仓库2的需求 → 无
2. 查找其他仓库需求 → 发现仓库1有需求
3. 调整需求仓库：仓库1 → 仓库2
4. 分配50个给销售订单
5. 锁定仓库2的库存
```

### 场景3：多仓库竞争
```
需求1：销售订单A，产品X，50个，仓库1，优先级1
需求2：生产订单B，产品X，30个，仓库3，优先级2
入库：采购入库产品X，60个，仓库2
结果：
1. 查找仓库2需求 → 无
2. 查找其他仓库需求 → 发现需求1和需求2
3. 按优先级调整：需求1调整到仓库2
4. 分配50个给需求1，10个给需求2
5. 锁定仓库2的库存
```

## 数据变更记录

### 1. 仓库调整日志
在`notes`字段中记录仓库调整历史：
```
原始备注 [仓库调整：1→2]
```

### 2. 系统日志
```php
trace('调整分配需求仓库：需求ID=' . $request['id'] . 
      ', 原仓库=' . $oldWarehouseId . 
      ', 新仓库=' . $newWarehouseId, 'info');

trace('发现跨仓库分配需求，已调整仓库：产品ID=' . $productId . 
      ', 调整到仓库ID=' . $warehouseId . 
      ', 需求数量=' . count($pendingRequests), 'info');
```

## 优势特点

### 1. 灵活性
- 支持任意仓库入库
- 自动匹配最合适的需求
- 无需预先指定入库仓库

### 2. 智能化
- 优先匹配同仓库需求
- 自动跨仓库调整
- 按优先级分配

### 3. 可追溯性
- 完整的调整记录
- 详细的操作日志
- 便于问题排查

### 4. 业务连续性
- 不影响现有流程
- 向下兼容
- 提高分配成功率

## 注意事项

### 1. 仓库调整限制
- 只调整待分配和部分分配的需求
- 已完全分配的需求不调整
- 已锁定的库存不受影响

### 2. 优先级保持
- 调整仓库不改变优先级
- 按原有优先级顺序分配
- 确保重要需求优先满足

### 3. 事务安全
- 仓库调整在事务中执行
- 失败时自动回滚
- 确保数据一致性

### 4. 性能考虑
- 优先查找同仓库需求
- 减少不必要的跨仓库查询
- 批量更新提高效率

## 配置选项

### 1. 启用跨仓库分配
```php
// 在配置文件中设置
'enable_cross_warehouse_allocation' => true
```

### 2. 仓库调整策略
```php
// 调整策略：auto=自动调整，manual=手动确认
'warehouse_adjustment_strategy' => 'auto'
```

### 3. 优先级权重
```php
// 不同业务类型的优先级权重
'allocation_priorities' => [
    'customer_order' => 1,      // 销售订单最高
    'production_order' => 2,    // 生产订单次之
    'transfer_order' => 3,      // 调拨订单较低
]
```

## 监控指标

### 1. 跨仓库分配率
- 统计跨仓库分配的比例
- 分析仓库规划合理性
- 优化库存布局

### 2. 仓库调整频率
- 监控仓库调整次数
- 识别频繁调整的产品
- 改进需求预测

### 3. 分配成功率
- 统计分配成功的比例
- 分析失败原因
- 持续优化算法

## 未来扩展

### 1. 智能仓库推荐
- 基于历史数据推荐最佳仓库
- 考虑运输成本和时效
- 提高分配效率

### 2. 预测性分配
- 基于需求预测提前分配
- 减少等待时间
- 提高客户满意度

### 3. 多级仓库支持
- 支持总仓-分仓结构
- 自动调拨补货
- 全网库存优化

通过这套跨仓库分配机制，系统能够灵活应对各种入库场景，最大化库存利用率，提高业务效率。
