{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
		<div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
			<ul class="layui-tab-title">
				<li><a href="/contract/contract/voidlist">销售合同</a></li>
				<li class="layui-this"><a href="/contract/purchase/voidlist">采购合同</a></li>
			</ul>
		</div> 
	</div> 
	<form class="layui-form gg-form-bar border-x">
		<div class="layui-input-inline" style="width:122px;">
			<select name="types">
				<option value="">选择合同性质</option>
				<option value="1">普通采购</option>
				<option value="2">物品采购</option>
				<option value="3">服务采购</option>
			</select>
		</div>
		<div class="layui-input-inline" style="width:122px;">
			<select name="cate_id">
				<option value="">选择合同类别</option>
				{volist name=":get_base_data('ContractCate');" id="vo"}
				<option value="{$vo.id}">{$vo.title}</option>
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:175px;">
			<input type="text" class="layui-input" id="sign_time" placeholder="选择合同签订日期" readonly name="sign_time">
		</div>
		<div class="layui-input-inline" style="width:175px;">
			<input type="text" class="layui-input" id="end_time" placeholder="选择合同到期日期" readonly name="end_time">
		</div>
		<div class="layui-input-inline" style="width:220px;">
			<input type="text" name="keywords" placeholder="输入关键字，合同编号/合同名称" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<input type="hidden" name="tab" value="0" />
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="table_contract" lay-filter="table_contract"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	<h3>作废合同</h3>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus','laydatePlus'];
	function gouguInit() {
		var table = layui.tablePlus,dropdown = layui.dropdown, tool = layui.tool,laydatePlus = layui.laydatePlus;
		//日期范围
		var sign_time = new laydatePlus({'target':'sign_time'});
		var end_time = new laydatePlus({'target':'end_time'});
		layui.pageTable = table.render({
			elem: "#table_contract"
			, toolbar: "#toolbarDemo"
			,url: "/contract/purchase/voidlist"
			,is_excel: true
			,page: true
			,limit: 20
			,cellMinWidth: 60
			,height: 'full-152'
			,cols: [[ //表头
				{
					field: 'id',
					title: 'ID号',
					align: 'center',
					width: 80
				},{
					field: 'code',
					title: '合同编号',
					align: 'center',
					width: 150
				},{
					field: 'name',
					title: '合同名称',
					minWidth:240,
					templet: '<div><a data-href="/contract/purchase/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
				},{
					field: 'cate_title',
					title: '合同类别',
					align: 'center',
					width: 100
				},{
					field: 'types_name',
					title: '合同性质',
					align: 'center',
					width: 80,
					templet: function (d) {
						var html = '<span class="layui-color-' + d.types + '">' + d.types_name + '</span>';
						return html;
					}
				},{
					field: 'interval_time',
					title: '合同有效时间',
					align: 'center',
					width: 248,
					templet: function (d) {
						var html = d.interval_time;
						if (d.delay > 0 && d.delay < 30) {
							html += '<span class="red ml-1" style="font-size:12px;">' + d.delay + '天后到期</span>';
						}
						if (d.delay == 0) {
							html += '<span class="red ml-1" style="font-size:12px;">已过期</span>';
						}
						return html;
					}
				},{
					field: 'cost',
					title: '合同金额(元)',
					align: 'right',
					width: 100
				},{
					field: 'void_name',
					title: '作废人',
					align: 'center',
					width: 80
				},{
					field: 'void_time',
					title: '作废日期',
					align: 'center',
					width: 90
				},{
					field: 'sign_name',
					title: '签定人',
					align: 'center',
					width: 80
				},{
					field: 'sign_time',
					title: '签订日期',
					align: 'center',
					width: 90
				},{
					field: 'keeper_name',
					title: '保管人',
					align: 'center',
					width: 80
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 60,
					align: 'center',
					ignoreExport:true,
					templet: function (d) {
						var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
						return btn0;
					}						
				}
			]]
		});
			
		table.on('tool(table_contract)',function (obj) {
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side("/contract/purchase/view?id="+data.id);
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->