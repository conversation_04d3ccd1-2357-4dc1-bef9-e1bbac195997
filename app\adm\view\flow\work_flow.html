{extend name="../../base/view/common/base" /}
{block name="style"}
<style>
.flow-content{position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 1;overflow-x: scroll;overflow-y: auto;padding:30px 0;background: #F5F5F7;user-select: none;}
.flow-box *{margin: 0;padding: 0;box-sizing: border-box;}
.remove-node{position: absolute; top: 6px; right: 8px; display: none; font-style:normal}
.condition-priority{position: absolute; top: 6px; right: 8px;}
.select-icon{position: absolute; top: 42px; right: 3px;}

.top-left-cover-line{position: absolute;height: 3px;width: 50%;background-color: #f5f5f7;top: -2px;left: -1px;}
.bottom-left-cover-line{position: absolute;height: 3px;width: 50%;background-color: #f5f5f7;bottom: -2px;left: -1px;}
.top-right-cover-line{position: absolute;height: 3px;width: 50%;background-color: #f5f5f7;top: -2px;right: -1px;}
.bottom-right-cover-line{position: absolute;height: 3px;width: 50%;background-color: #f5f5f7;bottom: -2px;right: -1px;}

.flow-zoom{position: fixed; padding:10px; height: 32px;right: 36px;top: 36px; z-index: 10;font-size: 16px; box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .3);background: #FFFFFF;border-radius: 5px;}
.flow-zoom span.zoom-num{padding:0 12px; display: inline-block; height: 30px; line-height:28px;}
.flow-zoom span.zoom-ctrl{display: inline-block;width: 30px; height: 30px; line-height:30px; border-radius:50%; border:1px solid #666; font-size: 20px; text-align: center;color: #666;cursor: pointer;}
.flow-zoom span.zoom-ctrl:hover{color:#1e9fff;border-color:#1e9fff}
.flow-zoom .layui-btn-group{display: inline-block;vertical-align:top; margin-left:12px;}

.node-wrap{display: inline-flex;flex-direction: column;align-items: center;width: 100%;padding: 0 50px;position: relative;}
.node-wrap-box{width: 220px;display: inline-flex;align-items: center;flex-direction: column;background-color: #FFFFFF;border-radius: 5px;box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .1);cursor: pointer;border: 1px solid #F5F5F5;position: relative;}

.node-wrap-box:hover{box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .3);transition: all .3s;}
.node-wrap-box::before{content: "";position: absolute;top: -8px;left: 50%;-webkit-transform: translateX(-50%);transform: translateX(-50%);width: 0;height: 4px;border-style: solid;border-width: 8px 6px 4px;border-color: #cacaca transparent transparent;background: #f5f5f7;}
.node-start::before{display:none;}

.node-wrap-box:hover .remove-node{display: block;}
.node-wrap-box .user-edit{display: inline-block;}
.node-wrap-box:hover .user-edit{}
.node-wrap-box .node-title{border-radius: 4px 5px 0 0 }
.node-wrap-box>div{width: 100%;}
.node-wrap-box>div:nth-of-type(1){padding: 6px 10px;color: white;font-size: 12px;position: relative;}
.node-wrap-box>div:nth-of-type(2){padding: 15px 10px;display: flex;justify-content: space-between;}

.node-add-btn-box{position: relative;}
.node-add-btn-box::before{content: "";position: absolute;top: 0;left: 0;right: 0;bottom: 0;z-index: -1;margin: auto;width: 2px;height: 100%;background-color: #cacaca;}
.add-node-btn{padding: 20px 0 24px;}
.add-node-btn button.add-node{border-radius: 50%;background: #1e9fff;font-size: 25px;border-color: #1e9fff;width: 32px;line-height: 30px;padding: 0;margin: 0;color: #fff;display: inline-block;font-weight: 400;text-align: center;white-space: nowrap;vertical-align: middle;touch-action: manipulation;cursor: pointer;border: 1px solid transparent;}
.add-node-btn button.add-node:hover{transform: scale(1.2);transition:all .2s }

.branch-box{display: flex;border-bottom: 2px solid #cccccc;border-top: 2px solid #cccccc;position: relative;}
.branch-box-wrap{display: inline-flex;flex-direction: column;align-items: center;width: 100%;}
.add-branch{position: absolute;display: inline-block;left: 50%;top: -15px;z-index: 1;border-radius: 15px;color: rgb(50, 150, 250);border: none;box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .1);background: #FFFFFF;padding: 5px 10px;font-size: 12px;cursor: pointer;line-height: 20px;transform: translateX(-50%);}

.col-box{display: inline-flex;background: #f5f5f7;;flex-direction: column;align-items: center;position: relative;}
.col-box::before{content: "";position: absolute;top: 0;left: 0;right: 0;bottom: 0;z-index: 0;margin: auto;width: 2px;height: 100%;background-color: #cacaca;}

.condition-judge{background-color: #FFFFFF;border-radius: 5px;width: 220px;position: relative;box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .1);cursor: pointer;border: 1px solid #FFFFFF;}
.condition-judge:hover{border: 1px solid #16b777;transition:all .3s}
.condition-judge:hover .remove-node{display: block;}
.condition-judge:hover .condition-priority{display:none;}
.condition-judge>div:nth-of-type(2){padding: 15px 10px;}

.condition-node-box{display: inline-flex;flex-direction: column;align-items: center;position: relative;padding: 30px 30px 0 30px;}
.condition-node-box::before{content: "";position: absolute;top: 0;left: 0;right: 0;bottom: 0;margin: auto;width: 2px;height: 100%;background-color: #cacaca;}
.condition-input{line-height: 19px;height: 18px;display: inline-block;width:150 px;padding: 0;padding-left: 5px;font-size: 12px;border: none;}
.condition-title{padding: 5px 0;background-color: #ffffff;border-radius: 5px 5px 0 0;color: #15bc83;font-size: 12px;position: relative;}

.end-node{border-radius: 50%;font-size: 14px;color: rgba(25, 31, 37, .4);text-align: left;}
.end-node-circle{width: 10px;height: 10px;margin: auto;border-radius: 50%;background: #dbdcdc;}.end-node-text{margin-top: 5px;text-align: center;}
</style>
{/block}
<!-- 主体 -->
{block name="body"}
<div class="flow-content">
	<div id="flowBar" class="flow-zoom" data-zoom="10">               
		<span class="zoom-ctrl" data-type="1"><strong>－</strong></span>         
		<span class="zoom-num">100%</span>         
		<span class="zoom-ctrl" data-type="2">+</span>
		<div class="layui-btn-group">
		  <button type="button" class="layui-btn layui-btn-sm">预览</button>
		  <button type="button" class="layui-btn layui-btn-sm layui-bg-blue">发布</button>
		</div>
	</div>
		
	<div id="flowBox" class="flow-box" style="transform: scale(1); transform-origin: 50% 0px 0px;">
		<!-- 发起人 -->
		<div>
			<div class="node-wrap">
				<div class="node-wrap-box node-start">
					<div class="node-title" style="background: #2f363c;">
						<span>发起人</span>
					</div>
					<div>
						<span>请设置发起人</span>
						<i class="select-icon"><?xml version="1.0" encoding="UTF-8"?><svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 12L31 24L19 36" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg></i>
					</div>
				</div>
				<!-- 箭头 -->
				<div class="node-add-btn-box">
					<div class="add-node-btn">
						<button class="add-node">+</button>
					</div>
				</div>
			</div>
		</div>
		<!-- 审核人 -->
		<div>
			<div class="node-wrap">
				<div class="node-wrap-box">
					<div class="node-title" style="background:#ffb800">
						<span class="user-edit">审核人</span>
						<i class="remove-node">✖</i>
					</div>
					<div>
						<span>请设置审核人</span>
						<i class="select-icon"><?xml version="1.0" encoding="UTF-8"?><svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 12L31 24L19 36" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg></i>
					</div>
				</div>
				<!-- 箭头 -->
				<div class="node-add-btn-box">
					<div class="add-node-btn">
						<button class="add-node">+</button>
					</div>
				</div>
			</div> 
		</div>
		<!-- 流程结束 -->
		<div class="end-node">
			<div class="end-node-circle"></div>
			<div class="end-node-text">
				结束流程
			</div>
		</div>
	</div>
</div>
{/block}
<!-- /主体 -->
{block name="copyright"}{/block}
<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','oaFlow','oaPicker'];
	function gouguInit() {
		var form = layui.form,tool=layui.tool,oaFlow = layui.oaFlow;
		oaFlow.init();		
	}
</script>
{/block}
<!-- /脚本 -->