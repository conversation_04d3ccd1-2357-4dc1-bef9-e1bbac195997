# 销售订单反审核机制设计文档

## 1. 功能概述

设计一个完整的销售订单反审核机制，确保只有在后续业务流程未展开的情况下才允许反审核，保证业务数据的完整性和一致性。

## 2. 业务流程分析

### 2.1 销售订单审核后的业务流程链

```
销售订单审核 → 物料需求生成 → 采购/生产订单 → 库存锁定 → 发货 → 收款
     ↓              ↓              ↓           ↓        ↓       ↓
   check_status=2  material_req  purchase_order  inventory_lock delivery payment
```

### 2.2 关联业务表分析

| 业务环节 | 相关表 | 关联字段 | 状态字段 |
|---------|--------|----------|----------|
| 物料需求 | `material_requirement` | `order_id` | `status` |
| 采购订单 | `purchase_order` | `source_order_id` | `status` |
| 生产订单 | `produce_order` | 通过`purchase_requirement_order`关联 | `status` |
| 库存锁定 | `oa_inventory_lock` | `ref_id` (ref_type='customer_order') | `status` |
| 发货记录 | `customer_delivery` | `order_id` | `status` |
| 付款记录 | `customer_order_payment` | `order_id` | `status` |
| 发票记录 | `customer_invoice` | `order_id` | `status` |

## 3. 反审核条件检查

### 3.1 基础条件检查

1. **订单状态检查**
   - 订单必须是已审核状态 (`check_status = 2`)
   - 订单不能是已完成或已取消状态

2. **权限检查**
   - 只有订单创建者可以反审核自己的订单
   - 管理员可以反审核任何订单（可选）

### 3.2 关联业务检查

#### 3.2.1 物料需求检查
```php
// 检查是否有已处理的物料需求
$materialRequirements = Db::name('material_requirement')
    ->where('order_id', $orderId)
    ->where('status', '>', 0) // 已处理的需求
    ->count();
```

#### 3.2.2 采购订单检查
```php
// 检查是否有关联的采购订单
$purchaseOrders = Db::name('purchase_order')
    ->where('source_order_id', $orderId)
    ->where('source_type', 'customer_order')
    ->where('status', '>', 0) // 非草稿状态
    ->count();
```

#### 3.2.3 生产订单检查
```php
// 通过purchase_requirement_order表检查生产订单
$produceOrders = Db::name('purchase_requirement_order')
    ->alias('pro')
    ->join('produce_order po', 'pro.purchase_id = po.id')
    ->where('pro.order_id', $orderId)
    ->where('po.status', '>', 0) // 非草稿状态
    ->count();
```

#### 3.2.4 库存锁定检查
```php
// 检查是否有库存锁定记录
$inventoryLocks = Db::name('oa_inventory_lock')
    ->where('ref_type', 'customer_order')
    ->where('ref_id', $orderId)
    ->where('status', 1) // 锁定状态
    ->count();
```

#### 3.2.5 发货记录检查
```php
// 检查是否有发货记录
$deliveries = Db::name('customer_delivery')
    ->where('order_id', $orderId)
    ->where('status', '>', 0) // 非草稿状态
    ->count();
```

#### 3.2.6 付款记录检查
```php
// 检查是否有付款记录
$payments = Db::name('customer_order_payment')
    ->where('order_id', $orderId)
    ->where('delete_time', 0)
    ->count();
```

## 4. 反审核实现方案

### 4.1 控制器方法设计

```php
/**
 * 反审核订单
 */
public function reverseAudit()
{
    $id = input('id');
    
    if (!$id) {
        return json(['code' => 1, 'msg' => '参数错误']);
    }
    
    // 1. 基础检查
    $checkResult = $this->checkReverseAuditConditions($id);
    if ($checkResult['code'] != 0) {
        return json($checkResult);
    }
    
    // 2. 关联业务检查
    $businessCheckResult = $this->checkRelatedBusiness($id);
    if ($businessCheckResult['code'] != 0) {
        return json($businessCheckResult);
    }
    
    // 3. 执行反审核
    return $this->executeReverseAudit($id);
}
```

### 4.2 关联业务检查方法

```php
/**
 * 检查关联业务是否允许反审核
 */
private function checkRelatedBusiness($orderId)
{
    $blockingReasons = [];
    
    // 检查物料需求
    $materialReqCount = Db::name('material_requirement')
        ->where('order_id', $orderId)
        ->where('status', '>', 0)
        ->count();
    if ($materialReqCount > 0) {
        $blockingReasons[] = "存在{$materialReqCount}条已处理的物料需求";
    }
    
    // 检查采购订单
    $purchaseCount = Db::name('purchase_order')
        ->where('source_order_id', $orderId)
        ->where('source_type', 'customer_order')
        ->where('status', '>', 0)
        ->count();
    if ($purchaseCount > 0) {
        $blockingReasons[] = "存在{$purchaseCount}个关联的采购订单";
    }
    
    // 检查生产订单
    $produceCount = Db::name('purchase_requirement_order')
        ->alias('pro')
        ->join('produce_order po', 'pro.purchase_id = po.id')
        ->where('pro.order_id', $orderId)
        ->where('po.status', '>', 0)
        ->count();
    if ($produceCount > 0) {
        $blockingReasons[] = "存在{$produceCount}个关联的生产订单";
    }
    
    // 检查库存锁定
    $lockCount = Db::name('oa_inventory_lock')
        ->where('ref_type', 'customer_order')
        ->where('ref_id', $orderId)
        ->where('status', 1)
        ->count();
    if ($lockCount > 0) {
        $blockingReasons[] = "存在{$lockCount}条库存锁定记录";
    }
    
    // 检查发货记录
    $deliveryCount = Db::name('customer_delivery')
        ->where('order_id', $orderId)
        ->where('status', '>', 0)
        ->count();
    if ($deliveryCount > 0) {
        $blockingReasons[] = "存在{$deliveryCount}条发货记录";
    }
    
    // 检查付款记录
    $paymentCount = Db::name('customer_order_payment')
        ->where('order_id', $orderId)
        ->where('delete_time', 0)
        ->count();
    if ($paymentCount > 0) {
        $blockingReasons[] = "存在{$paymentCount}条付款记录";
    }
    
    if (!empty($blockingReasons)) {
        return [
            'code' => 1,
            'msg' => '无法反审核，原因：' . implode('；', $blockingReasons),
            'data' => $blockingReasons
        ];
    }
    
    return ['code' => 0, 'msg' => '检查通过'];
}
```

## 5. 前端界面设计

### 5.1 反审核按钮显示条件

```javascript
// 在订单列表中添加反审核按钮
{
    field: 'operate', 
    title: '操作', 
    width: 200,
    templet: function(d) {
        var html = '';
        
        // 审核按钮（未审核状态）
        if (d.check_status == 0) {
            html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="auditOrder(' + d.id + ')">审核</button> ';
        }
        
        // 反审核按钮（已审核状态）
        if (d.check_status == 2) {
            html += '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="reverseAuditOrder(' + d.id + ')">反审核</button> ';
        }
        
        // 其他操作按钮...
        
        return html;
    }
}
```

### 5.2 反审核确认对话框

```javascript
function reverseAuditOrder(id) {
    layer.confirm('确定要反审核此订单吗？<br><span style="color:red;">注意：反审核后订单将回到草稿状态，相关的物料需求等数据将被清理。</span>', {
        icon: 3,
        title: '反审核确认',
        area: ['400px', '200px']
    }, function(index) {
        // 执行反审核
        $.post('/customer/order/reverseAudit', {id: id}, function(res) {
            if (res.code == 0) {
                layer.msg('反审核成功', {icon: 1});
                layui.table.reload('orderTable'); // 刷新表格
            } else {
                layer.alert(res.msg, {icon: 2, title: '反审核失败'});
            }
        });
        layer.close(index);
    });
}
```

## 6. 数据清理策略

### 6.1 反审核时的数据处理

当订单反审核时，需要清理相关的业务数据：

```php
/**
 * 执行反审核操作
 */
private function executeReverseAudit($orderId)
{
    Db::startTrans();
    try {
        // 1. 更新订单状态
        Db::name('customer_order')->where('id', $orderId)->update([
            'status' => 0,           // 回到草稿状态
            'check_status' => 0,     // 未审核
            'check_time' => 0,       // 清空审核时间
            'check_user_id' => 0,    // 清空审核人
            'update_time' => time()
        ]);

        // 2. 清理物料需求记录（仅清理未处理的）
        Db::name('material_requirement')
            ->where('order_id', $orderId)
            ->where('status', 0) // 只清理未处理的
            ->delete();

        // 3. 释放库存锁定（如果存在）
        $this->releaseInventoryLocks($orderId);

        // 4. 记录操作日志
        $this->addOrderLog($orderId, '订单反审核', '订单已反审核，回到草稿状态');

        Db::commit();
        return ['code' => 0, 'msg' => '反审核成功'];

    } catch (\Exception $e) {
        Db::rollback();
        return ['code' => 1, 'msg' => '反审核失败：' . $e->getMessage()];
    }
}
```

### 6.2 库存锁定释放

```php
/**
 * 释放订单相关的库存锁定
 */
private function releaseInventoryLocks($orderId)
{
    $locks = Db::name('oa_inventory_lock')
        ->where('ref_type', 'customer_order')
        ->where('ref_id', $orderId)
        ->where('status', 1)
        ->select();

    foreach ($locks as $lock) {
        // 释放锁定库存
        Db::name('oa_inventory_lock')
            ->where('id', $lock['id'])
            ->update([
                'status' => 2, // 已释放
                'release_time' => time(),
                'release_by' => session('admin.id')
            ]);

        // 更新实时库存
        Db::name('oa_inventory_realtime')
            ->where('id', $lock['inventory_id'])
            ->inc('available_quantity', $lock['quantity'])
            ->dec('locked_quantity', $lock['quantity'])
            ->update();
    }
}
```

## 7. 权限控制方案

### 7.1 权限级别设计

1. **普通用户**：只能反审核自己创建的订单
2. **部门主管**：可以反审核本部门的订单
3. **系统管理员**：可以反审核任何订单

### 7.2 权限检查实现

```php
/**
 * 检查反审核权限
 */
private function checkReverseAuditPermission($orderId)
{
    $order = Db::name('customer_order')->find($orderId);
    $currentUserId = session('admin.id');
    $userRole = session('admin.role_id');

    // 系统管理员（假设role_id=1）
    if ($userRole == 1) {
        return ['code' => 0, 'msg' => '管理员权限'];
    }

    // 订单创建者
    if ($order['admin_id'] == $currentUserId) {
        return ['code' => 0, 'msg' => '创建者权限'];
    }

    // 部门主管权限检查（可选实现）
    if ($this->isDepartmentManager($currentUserId, $order['admin_id'])) {
        return ['code' => 0, 'msg' => '部门主管权限'];
    }

    return ['code' => 1, 'msg' => '无权限反审核此订单'];
}
```

## 8. 业务规则配置

### 8.1 可配置的反审核规则

```php
// 在配置文件中定义反审核规则
return [
    'reverse_audit' => [
        'enabled' => true,                    // 是否启用反审核功能
        'time_limit' => 24 * 3600,           // 审核后多长时间内可以反审核（秒）
        'check_material_requirement' => true, // 是否检查物料需求
        'check_purchase_order' => true,      // 是否检查采购订单
        'check_produce_order' => true,       // 是否检查生产订单
        'check_inventory_lock' => true,      // 是否检查库存锁定
        'check_delivery' => true,            // 是否检查发货记录
        'check_payment' => true,             // 是否检查付款记录
        'auto_clean_data' => true,           // 是否自动清理相关数据
    ]
];
```

### 8.2 时间限制检查

```php
/**
 * 检查时间限制
 */
private function checkTimeLimit($orderId)
{
    $timeLimit = config('reverse_audit.time_limit', 0);

    if ($timeLimit > 0) {
        $order = Db::name('customer_order')->find($orderId);
        $checkTime = $order['check_time'];

        if (time() - $checkTime > $timeLimit) {
            return [
                'code' => 1,
                'msg' => '超过反审核时间限制（' . ($timeLimit / 3600) . '小时）'
            ];
        }
    }

    return ['code' => 0, 'msg' => '时间检查通过'];
}
```

## 9. 前端界面完整实现

### 9.1 订单列表页面修改

在 `app/customer/view/order/index.html` 中添加反审核按钮：

```html
<!-- 在操作列的模板中添加 -->
<script type="text/html" id="operateBar">
    {{# if(d.check_status == 0) { }}
        <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="auditOrder({{d.id}})">审核</button>
    {{# } }}

    {{# if(d.check_status == 2) { }}
        <button class="layui-btn layui-btn-xs layui-btn-warm" onclick="reverseAuditOrder({{d.id}})">反审核</button>
    {{# } }}

    <button class="layui-btn layui-btn-xs" onclick="viewOrder({{d.id}})">查看</button>
    <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteOrder({{d.id}})">删除</button>
</script>
```

### 9.2 JavaScript函数实现

```javascript
/**
 * 反审核订单
 */
function reverseAuditOrder(id) {
    // 先检查是否可以反审核
    $.get('/customer/order/checkReverseAudit', {id: id}, function(res) {
        if (res.code == 0) {
            // 可以反审核，显示确认对话框
            showReverseAuditConfirm(id);
        } else {
            // 不能反审核，显示原因
            layer.alert(res.msg, {
                icon: 2,
                title: '无法反审核',
                area: ['400px', '300px']
            });
        }
    });
}

/**
 * 显示反审核确认对话框
 */
function showReverseAuditConfirm(id) {
    layer.confirm(
        '确定要反审核此订单吗？<br>' +
        '<span style="color:red;">注意：反审核后订单将回到草稿状态，相关的物料需求等数据将被清理。</span>',
        {
            icon: 3,
            title: '反审核确认',
            area: ['450px', '200px'],
            btn: ['确定反审核', '取消']
        },
        function(index) {
            // 执行反审核
            executeReverseAudit(id);
            layer.close(index);
        }
    );
}

/**
 * 执行反审核操作
 */
function executeReverseAudit(id) {
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.post('/customer/order/reverseAudit', {id: id}, function(res) {
        layer.close(loadingIndex);

        if (res.code == 0) {
            layer.msg('反审核成功', {icon: 1});
            // 刷新表格
            layui.table.reload('orderTable');
        } else {
            layer.alert(res.msg, {
                icon: 2,
                title: '反审核失败',
                area: ['400px', '200px']
            });
        }
    }).fail(function() {
        layer.close(loadingIndex);
        layer.msg('网络错误，请重试', {icon: 2});
    });
}
```

## 10. 实施建议

### 10.1 分阶段实施

1. **第一阶段**：实现基础的反审核功能和权限控制
2. **第二阶段**：完善关联业务检查逻辑
3. **第三阶段**：添加配置化规则和时间限制
4. **第四阶段**：优化用户体验和错误提示

### 10.2 测试要点

1. **权限测试**：验证不同角色的反审核权限
2. **业务流程测试**：测试各种业务状态下的反审核限制
3. **数据一致性测试**：验证反审核后数据的完整性
4. **并发测试**：测试多用户同时操作的情况
5. **异常处理测试**：测试各种异常情况的处理

### 10.3 监控和日志

1. **操作日志**：记录所有反审核操作
2. **业务监控**：监控反审核频率和成功率
3. **数据审计**：定期检查数据一致性

## 11. 总结

这个反审核机制设计确保了：

1. **业务完整性**：只有在后续业务未展开时才允许反审核
2. **权限安全**：严格的权限控制机制
3. **数据一致性**：完整的数据清理和状态回滚
4. **用户体验**：清晰的提示和友好的操作界面
5. **可配置性**：灵活的业务规则配置

通过这个设计，可以在保证业务安全的前提下，为用户提供必要的订单修正能力。

## 12. 实现步骤

### 12.1 立即可实施的方案

基于当前系统的实际情况，建议按以下步骤实施：

1. **在Order控制器中添加反审核相关方法**
2. **修改订单列表页面，添加反审核按钮**
3. **实现基础的业务检查逻辑**
4. **测试和优化用户体验**

### 12.2 核心代码实现位置

- **控制器**：`app/customer/controller/Order.php`
- **前端页面**：`app/customer/view/order/index.html`
- **配置文件**：`config/reverse_audit.php`（新建）

这个设计方案充分考虑了系统的业务完整性和数据安全性，同时提供了灵活的配置选项和良好的用户体验。
