<?php
// 测试BOM表是否存在
require_once __DIR__ . '/vendor/autoload.php';

use think\facade\Db;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    echo "测试数据库表是否存在:\n\n";
    
    // 1. 测试 oa_material_bom 表
    echo "1. 测试 oa_material_bom 表:\n";
    try {
        $count = Db::query("SELECT COUNT(*) as count FROM oa_material_bom");
        echo "   ✅ oa_material_bom 表存在，记录数: " . $count[0]['count'] . "\n";
    } catch (\Exception $e) {
        echo "   ❌ oa_material_bom 表不存在: " . $e->getMessage() . "\n";
    }
    
    // 2. 测试 oa_material_bom_detail 表
    echo "\n2. 测试 oa_material_bom_detail 表:\n";
    try {
        $count = Db::query("SELECT COUNT(*) as count FROM oa_material_bom_detail");
        echo "   ✅ oa_material_bom_detail 表存在，记录数: " . $count[0]['count'] . "\n";
    } catch (\Exception $e) {
        echo "   ❌ oa_material_bom_detail 表不存在: " . $e->getMessage() . "\n";
    }
    
    // 3. 列出所有BOM相关的表
    echo "\n3. 列出所有BOM相关的表:\n";
    $tables = Db::query("SHOW TABLES LIKE '%bom%'");
    if (empty($tables)) {
        echo "   没有找到BOM相关的表\n";
    } else {
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            echo "   - $tableName\n";
        }
    }
    
    // 4. 测试ThinkPHP的表名映射
    echo "\n4. 测试ThinkPHP的表名映射:\n";
    try {
        $count = Db::name('material_bom')->count();
        echo "   ✅ Db::name('material_bom') 工作正常，记录数: $count\n";
    } catch (\Exception $e) {
        echo "   ❌ Db::name('material_bom') 失败: " . $e->getMessage() . "\n";
    }
    
    // 5. 测试物料档案页面的BOM查询
    echo "\n5. 测试物料档案页面的BOM查询:\n";
    try {
        // 获取一个产品ID进行测试
        $product = Db::name('product')->where('id', '>', 0)->find();
        if ($product) {
            echo "   测试产品: ID={$product['id']}, 名称={$product['title']}\n";
            
            // 测试BOM查询
            $validBomCount = Db::name('material_bom')->where('product_id', $product['id'])
                ->where('status', 1)
                ->count();
            echo "   该产品的BOM数量: $validBomCount\n";
        } else {
            echo "   没有找到产品数据\n";
        }
    } catch (\Exception $e) {
        echo "   ❌ 测试失败: " . $e->getMessage() . "\n";
    }
    
} catch (\Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
}
