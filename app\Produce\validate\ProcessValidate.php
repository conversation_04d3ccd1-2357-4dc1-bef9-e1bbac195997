<?php
/**
 * @copyright Copyright (c) 2021 勾股工作室
 * @license https://opensource.org/licenses/Apache-2.0
 * @link https://www.gougucms.com
 */

declare (strict_types = 1);

namespace app\Produce\validate;

use think\Validate;

class ProcessValidate extends Validate
{
    protected $rule = [
        'code' => 'max:50',
        'name' => 'require|max:100',
        'group_id' => 'require|number|gt:0',
        'standard_price' => 'require|float|egt:0',
        'efficiency' => 'require|float|gt:0',
        'pricing_method' => 'require|in:1,2',
        'quantity_ratio' => 'require|float|gt:0',
        'description' => 'max:500',
        'serial_number' => 'max:100'
    ];

    protected $message = [
        'code.require' => '工序编号不能为空',
        'code.max' => '工序编号不能超过50个字符',
        'name.require' => '工序名称不能为空',
        'name.max' => '工序名称不能超过100个字符',
        'group_id.require' => '请选择工作组',
        'group_id.number' => '工作组参数错误',
        'group_id.gt' => '请选择工作组',
        'standard_price.require' => '标准单价不能为空',
        'standard_price.float' => '标准单价必须是数字',
        'standard_price.egt' => '标准单价不能小于0',
        'efficiency.require' => '标准效率不能为空',
        'efficiency.float' => '标准效率必须是数字',
        'efficiency.gt' => '标准效率必须大于0',
        'pricing_method.require' => '请选择计价方式',
        'pricing_method.in' => '计价方式参数错误',
        'quantity_ratio.require' => '数量单比不能为空',
        'quantity_ratio.float' => '数量单比必须是数字',
        'quantity_ratio.gt' => '数量单比必须大于0',
        'description.max' => '工序描述不能超过500个字符',
        'serial_number.max' => '工序序号不能超过100个字符'
    ];

    protected $scene = [
        'add' => ['name', 'group_id', 'standard_price', 'efficiency', 'pricing_method', 'quantity_ratio', 'description', 'serial_number'],
        'edit' => ['name', 'group_id', 'standard_price', 'efficiency', 'pricing_method', 'quantity_ratio', 'description', 'serial_number']
    ];
}