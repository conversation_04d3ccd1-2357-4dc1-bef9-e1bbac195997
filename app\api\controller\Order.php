<?php
declare (strict_types = 1);
namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;

class Order extends BaseController
{
    /**
     * 验证来源单号
     * @return \think\Response
     */
    public function checkSourceNo()
    {
        if (!request()->isAjax()) {
            return json(['code' => 1, 'msg' => '非法请求']);
        }

        $source_type = input('source_type/d', 0);
        $source_no = input('source_no', '');

        if (empty($source_type) || empty($source_no)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        // 根据来源类型查询不同的表
        switch ($source_type) {
            case 1: // 客户订单
                $order = Db::name('customer_order')
                    ->where('order_no', $source_no)
                    ->where('delete_time', 0)
                    ->find();
                if (!$order) {
                    return json(['code' => 1, 'msg' => '客户订单不存在']);
                }
                break;

            case 2: // 生产领料
                $order = Db::name('produce_order')
                    ->where('order_no', $source_no)
                    ->where('delete_time', 0)
                    ->find();
                if (!$order) {
                    return json(['code' => 1, 'msg' => '生产订单不存在']);
                }
                break;

            default:
                return json(['code' => 1, 'msg' => '无效的来源类型']);
        }

        return json([
            'code' => 0,
            'msg' => '验证成功',
            'data' => [
                'id' => $order['id']
            ]
        ]);
    }
} 