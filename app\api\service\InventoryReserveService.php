<?php
namespace app\api\service;

use app\api\model\InventoryReserve;
use app\warehouse\model\Inventory;
use app\oa\model\InventoryLog;
use app\customer\model\CustomerOrder;
use think\facade\Db;
use think\Exception;
use think\facade\Log;

class InventoryReserveService {
    /**
     * 创建预占记录
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 数量
     * @param int $orderId 订单ID
     * @param string $orderNo 订单编号
     * @param int $locationId 库位ID
     * @param string $batchNo 批次号
     * @return array
     */
    public function createReserve($productId,$ref_type,$ref_id,$quantity, $orderNo, $created_by) {
        Db::startTrans();
        try {
            // 2. 创建预占记录
            $reserve = new InventoryReserve();
            $reserve->product_id = $productId;
            $reserve->quantity = $quantity;
            $reserve->ref_type = $ref_type;
            $reserve->ref_id = $ref_id;
            $reserve->ref_no = $orderNo;
            $reserve->status = 1; // 已预占
            $reserve->created_by = $created_by;
            $reserve->create_time = time();
            $reserve->save();
            Db::commit();
            return [
                'success' => true,
                'reserveId' => $reserve->id,
                'quantity' => $quantity,
                'warning' => $inventory->quantity == 0 ? "商品暂无库存，已创建预占记录" : ""
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 预占转为锁定
     * 
     * @param int $orderId 订单ID
     * @return array
     */
    public function convertToLock($orderId,$created_by) {
        Db::startTrans();
        try {
            // 1. 查找订单的所有预占记录
            $reserves = InventoryReserve::where('ref_id', $orderId)
                ->where('ref_type', 'customer_order')
                ->where('status', 1)
                ->select();
            
            if ($reserves->isEmpty()) {
               // throw new Exception("未找到订单的预占记录");
                return [
                    'success' => true,
                    'message' => '未找到订单的预占记录'
                ];
            }
            
            $lockIds = [];
            
            // 2. 处理每条预占记录
            foreach ($reserves as $reserve) {
                // 查找库存记录
                $inventory = Inventory::find($reserve->inventory_id);
                if (!$inventory) {
                    throw new Exception("库存记录不存在: " . $reserve->inventory_id);
                }
                
                // 创建锁定记录
                $lock = new InventoryLock();
                $lock->inventory_id = $reserve->inventory_id;
                $lock->product_id = $reserve->product_id;
                $lock->warehouse_id = $reserve->warehouse_id;
                $lock->location_id = $reserve->location_id;
                $lock->batch_no = $reserve->batch_no;
                $lock->quantity = $reserve->quantity;
                $lock->ref_type = $reserve->ref_type;
                $lock->ref_id = $reserve->ref_id;
                $lock->ref_no = $reserve->ref_no;
                $lock->lock_time = time();
                $lock->expire_time = time() + 7 * 86400; // 7天后过期
                $lock->status = 1; // 已锁定
                $lock->created_by = $created_by;
                $lock->create_time = time();
                $lock->update_time = time();
                $lock->save();
                
                $lockIds[] = $lock->id;
                
                // 更新库存记录
                $inventory->reserved_quantity -= $reserve->quantity;
                $inventory->locked_quantity += $reserve->quantity;
                $inventory->save();
                
                // 更新预占记录状态
                $reserve->status = 2; // 已转锁定
                $reserve->update_time = time();
                $reserve->save();
                
                // 记录库存日志
                $log = new InventoryLog();
                $log->warehouse_id = $reserve->warehouse_id;
                $log->location_id = $reserve->location_id ?: 0;
                $log->product_id = $reserve->product_id;
                $log->batch_no = $reserve->batch_no;
                $log->type = 8; // 预占转锁定
                $log->quantity = $reserve->quantity;
                $log->before_quantity = $inventory->quantity;
                $log->after_quantity = $inventory->quantity;
                $log->before_locked = $inventory->locked_quantity - $reserve->quantity;
                $log->after_locked = $inventory->locked_quantity;
                $log->direction = 2; // 预占转锁定
                $log->ref_type = $reserve->ref_type;
                $log->ref_id = $reserve->ref_id;
                $log->ref_no = $reserve->ref_no;
                $log->operation_by = $created_by;
                $log->operation_time = time();
                $log->create_time = time();
                $log->inventory_id = $inventory->id;
                $log->save();
            }
            
            Db::commit();
            return [
                'success' => true,
                'lockIds' => $lockIds,
                'message' => '预占库存已成功转为锁定'
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 释放预占
     * 
     * @param int $orderId 订单ID
     * @return array
     */
    public function releaseReserve($orderId,$operation_by) {
        Db::startTrans();
        try {
            // 1. 查找订单的所有预占记录
            $reserves = InventoryReserve::where('ref_id', $orderId)
                ->where('ref_type', 'customer_order')
                ->where('status', 1)
                ->select();
            
            if ($reserves->isEmpty()) {
                // 没有预占记录，直接返回成功
                Db::commit();
                return [
                    'success' => true,
                    'message' => '没有需要释放的预占记录'
                ];
            }
            
            // 2. 处理每条预占记录
            foreach ($reserves as $reserve) {
                // 查找库存记录
                $inventory = Inventory::find($reserve->inventory_id);
                if (!$inventory) {
                    throw new Exception("库存记录不存在: " . $reserve->inventory_id);
                }
                
                // 更新库存记录
                $inventory->reserved_quantity -= $reserve->quantity;
                $inventory->save();
                
                // 更新预占记录状态
                $reserve->status = 0; // 已释放
                $reserve->update_time = time();
                $reserve->save();
                
                // 记录库存日志
                $log = new InventoryLog();
                $log->warehouse_id = $reserve->warehouse_id;
                $log->location_id = $reserve->location_id ?: 0;
                $log->product_id = $reserve->product_id;
                $log->batch_no = $reserve->batch_no;
                $log->type = 9; // 预占释放
                $log->quantity = $reserve->quantity;
                $log->before_quantity = $inventory->quantity;
                $log->after_quantity = $inventory->quantity;
                $log->before_locked = $inventory->locked_quantity;
                $log->after_locked = $inventory->locked_quantity;
                $log->direction = 0; // 释放预占
                $log->ref_type = $reserve->ref_type;
                $log->ref_id = $reserve->ref_id;
                $log->ref_no = $reserve->ref_no;
                $log->operation_by = $operation_by;
                $log->operation_time = time();
                $log->create_time = time();
                $log->inventory_id = $inventory->id;
                $log->save();
            }
            
            Db::commit();
            return [
                'success' => true,
                'message' => '预占库存已成功释放'
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查并处理过期预占
     * 
     * @return array
     */
    public function checkExpiredReserves() {
        Db::startTrans();
        try {
            // 1. 查询所有过期的预占记录
            $expiredReserves = InventoryReserve::where('expire_time', '<', time())
                ->where('status', 1)
                ->select();
            
            if ($expiredReserves->isEmpty()) {
                Db::commit();
                return [
                    'success' => true,
                    'message' => '没有过期的预占记录'
                ];
            }
            
            // 2. 循环处理每条记录
            foreach ($expiredReserves as $reserve) {
                // 查找库存记录
                $inventory = Inventory::find($reserve->inventory_id);
                if (!$inventory) {
                    continue; // 跳过无效的库存记录
                }
                
                // 更新库存表的预占数量
                $inventory->reserved_quantity -= $reserve->quantity;
                $inventory->save();
                
                // 更新预占状态为已释放
                $reserve->status = 0;
                $reserve->update_time = time();
                $reserve->save();
                
                // 记录库存日志
                $log = new InventoryLog();
                $log->warehouse_id = $reserve->warehouse_id;
                $log->location_id = $reserve->location_id ?: 0;
                $log->product_id = $reserve->product_id;
                $log->batch_no = $reserve->batch_no;
                $log->type = 9; // 预占释放
                $log->quantity = $reserve->quantity;
                $log->before_quantity = $inventory->quantity;
                $log->after_quantity = $inventory->quantity;
                $log->before_locked = $inventory->locked_quantity;
                $log->after_locked = $inventory->locked_quantity;
                $log->direction = 0; // 释放预占
                $log->ref_type = $reserve->ref_type;
                $log->ref_id = $reserve->ref_id;
                $log->ref_no = $reserve->ref_no;
                $log->operation_by = 0; // 系统自动操作
                $log->operation_time = time();
                $log->create_time = time();
                $log->inventory_id = $inventory->id;
                $log->save();
            }
            
            Db::commit();
            return [
                'success' => true,
                'count' => count($expiredReserves),
                'message' => count($expiredReserves) . ' 条过期预占已释放'
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取订单的预占状态
     * 
     * @param int $orderId 订单ID
     * @return array
     */
    public function getOrderReserveStatus($orderId) {
        // 查询订单的预占情况
        $reserves = InventoryReserve::where('ref_id', $orderId)
            ->where('ref_type', 'customer_order')
            ->select()
            ->toArray();
        
        return [
            'success' => true,
            'data' => $reserves
        ];
    }

    //自动预占
    public function aout_createReserve($productId, $quantity, $orderId, $orderNo, $created_by) {
        Db::startTrans();
        try {
            // 查找所有包含该产品的库存记录，按可用数量降序排序
            $inventories = Inventory::where('product_id', $productId)
                ->where('status', 1)
                ->field('id, warehouse_id, location_id, batch_no, quantity, locked_quantity, reserved_quantity')
                ->select()
                ->toArray();
            // 计算每条记录的可用数量并排序
            $availableInventories = [];
            foreach ($inventories as $inv) {
                $availableQty = $inv['quantity'] - $inv['locked_quantity'] - $inv['reserved_quantity'];
               
                if ($availableQty > 0) {
                    $inv['available_quantity'] = $availableQty;
                    $availableInventories[] = $inv;
                }
            }

            
            
            // 按可用数量降序排序
            usort($availableInventories, function($a, $b) {
                return $b['available_quantity'] <=> $a['available_quantity'];
            });
            
            // 开始分配预占
            $remainingQuantity = $quantity;
            $reserveResults = [];
            $totalReserved = 0;
            
            foreach ($availableInventories as $inv) {
                if ($remainingQuantity <= 0) {
                    break;
                }
                
                // 计算当前库存记录可预占的数量
                $reserveQty = min($remainingQuantity, $inv['available_quantity']);
               
                
                // 获取库存对象
                $inventory = Inventory::find($inv['id']);
                
                
                // 创建预占记录
                $reserve = new InventoryReserve();
                $reserve->inventory_id = $inv['id'];
                $reserve->product_id = $productId;
                $reserve->warehouse_id = $inv['warehouse_id'];
                $reserve->location_id = $inv['location_id'] ?? 0;
                $reserve->batch_no = $inv['batch_no'] ?? '';
                $reserve->quantity = $reserveQty;
                $reserve->ref_type = 'customer_order_detail';
                $reserve->ref_id = $orderId;
                $reserve->ref_no = $orderNo;
                $reserve->reserve_time = time();
                $reserve->expire_time = time() + 86400; // 24小时后过期
                $reserve->status = 1; // 已预占
                $reserve->created_by = $created_by;
                $reserve->create_time = time();
                $reserve->update_time = time();
                $reserve->save();
                
                // 更新库存表的预占数量 reserved_quantity
                $beforeReserved = $inventory->reserved_quantity;
                $inventory->reserved_quantity += $reserveQty;
                
                // 确保是否正确更新
                
                $result = $inventory->save();
                
                // 记录库存日志
                $log = new InventoryLog();
                $log->warehouse_id = $inv['warehouse_id'];
                $log->location_id = $inv['location_id'] ?? 0;
                $log->product_id = $productId;
                $log->batch_no = $inv['batch_no'] ?? '';
                $log->type = 7; // 预占类型
                $log->quantity = $reserveQty;
                $log->before_quantity = $inventory->quantity;
                $log->after_quantity = $inventory->quantity;
                $log->before_locked = $inventory->locked_quantity;
                $log->after_locked = $inventory->locked_quantity;
                $log->direction = 1; // 增加预占
                $log->ref_type = 'customer_order_detail';
                $log->ref_id = $orderId;
                $log->ref_no = $orderNo;
                $log->operation_by = $created_by;
                $log->operation_time = time();
                $log->create_time = time();
                $log->inventory_id = $inv['id'];
                $log->save();
                
                // 更新剩余需要预占的数量
                $remainingQuantity -= $reserveQty;
                $totalReserved += $reserveQty;
                
                // 记录本次预占结果
                $reserveResults[] = [
                    'reserve_id' => $reserve->id,
                    'inventory_id' => $inv['id'],
                    'warehouse_id' => $inv['warehouse_id'],
                    'location_id' => $inv['location_id'] ?? 0,
                    'quantity' => $reserveQty
                ];
            }
            
            Db::commit();
            
            // 返回预占结果
            return [
                'success' => true,
                'total_quantity' => $quantity,
                'reserved_quantity' => $totalReserved,
               // 'quantity'=> $reserveQty,
                'remaining_quantity' => $remainingQuantity,
                'reserve_results' => $reserveResults,
                'is_fully_reserved' => $remainingQuantity <= 0,
                'warning' => $remainingQuantity > 0 ? "商品库存不足，还需 {$remainingQuantity} 个单位" : ""
            ];
        } catch (Exception $e) {
            Db::rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
