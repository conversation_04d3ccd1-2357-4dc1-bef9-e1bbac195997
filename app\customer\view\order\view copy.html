{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-tab-title .layui-this{background-color:#fff;}
.layui-tab-card,.layui-card{box-shadow:0 0 0 0 rgb(0 0 0 / 10%); border-radius:0; border-top:none;}
#checkBox .pb-2{display:none;}
.check-status-color-0{color:#FF9800;}
.check-status-color-1{color:#2196F3;}
.check-status-color-2{color:#4CAF50;}
.check-status-color-3{color:#F44336;}
.check-status-color-4{color:#4CAF50;}
.check-status-color-5{color:#9E9E9E;}
/* BOM弹窗样式 */
.bom-detail-table {width: 100%; border-collapse: collapse;}
.bom-detail-table th, .bom-detail-table td {border: 1px solid #e6e6e6; padding: 8px; text-align: center;}
.bom-detail-table th {background-color: #f2f2f2;}
.bom-toggle {cursor: pointer; color: #1E9FFF;}
.bom-toggle:hover {text-decoration: underline;}
.bom-shortage {color: #F44336; font-weight: bold;}
/* 父子商品关系样式 - 增强版 */
.child-product {background-color: #f8f8f8; border-left: 3px solid #1E9FFF;}
.child-product-name {
    padding-left: 30px; 
    position: relative;
    color: #333;
    font-weight: normal;
}
.child-product-name:before {
    content: "└─";
    position: absolute;
    left: 8px;
    color: #1E9FFF;
    font-weight: bold;
    font-size: 16px;
}
/* 父商品标记 */
.parent-product-marker {
    display: inline-block;
    background-color: #1E9FFF;
    color: white;
    font-size: 12px;
    padding: 0 5px;
    border-radius: 3px;
    margin-right: 5px;
    vertical-align: middle;
}
</style>
{/block}

{block name="body"}
<div class="p-page">
    <h3 class="pb-3">订单详情</h3>
    <table class="layui-table layui-table-form">
        <tr>
            <td class="layui-td-gray">订单编号</td>
            <td>{$order.order_no|default=''}</td>
            <td class="layui-td-gray">订单状态</td>
            <td>
                <span class="check-status-color-{$order.status|default='0'}">『{$order.status_text|default='待审核'}』</span>
                {if $order.check_status == 2}
                <button type="button" class="layui-btn layui-btn-danger layui-btn-xs" id="btnRevertAudit">反审</button>
                {/if}
            </td>
        </tr>
        <tr>
            <td class="layui-td-gray">客户名称</td>
            <td>{$order.customer.name|default=''}</td>
            <td class="layui-td-gray">订单类型</td>
            <td>{$order.order_type_text|default=''}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">交货日期</td>
            <td>{$order.delivery_date|default=''}</td>
            <td class="layui-td-gray">税率</td>
            <td>{$order.tax_rate|default='0'}%</td>
        </tr>
        <tr>
            <td class="layui-td-gray">创建人</td>
            <td>{$order.adduser.name|default=''}</td>
            <td class="layui-td-gray">创建时间</td>
            <td>{$order.create_time_format|default=''}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">管销费</td>
            <td>{$order.glf|default='0'}</td>
            <td class="layui-td-gray">运费</td>
            <td>{$order.yunfei|default='0'}</td>
        </tr>
        <tr>
            <td class="layui-td-gray">已付款</td>
            <td>{$order.pay_money|default='0'}</td>
            <td class="layui-td-gray"></td>
            <td style="color: red;">{if $order.pay_status<2 && $order.order_type == 1}{if $order.delivery_flag == 0}未付款不可发货{else}可发货 [部门主管审批通过]{/if}{else} {/if}</td>
        </tr>
        
        
        {if !empty($order.remark)}
        <tr>
            <td class="layui-td-gray">备注说明</td>
            <td colspan="3">{$order.remark}</td>
        </tr>
        {/if}
        <tr>
            <td colspan="4"><strong>订单明细</strong></td>
        </tr>
        <tr>
            <td colspan="4" style="padding: 0;">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th>材料编码</th>
                            <th>产品名称</th>
                            <th style="text-align: center;">数量</th>
                            <th style="text-align: center;">单价</th>
                            <th style="text-align: center;">金额</th>
                            <th style="text-align: center;">税率</th>
                            <th style="text-align: center;">税额</th>
                            <th style="text-align: center;">库存</th>
                            <th style="text-align: center;">备注</th>
                            <th style="text-align: center;">BOM</th>
                            <th style="text-align: center;">来源</th>
                            <th style="text-align: center;">附件</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $items as $key => $item}
                        <tr class="{$item.is_child ? 'child-product' : ''}" 
                            data-product-id="{$item.product_id}"
                            {if $item.is_child}data-parent-id="{$item.parent_product_id}"{/if}>
                            {if !$item.is_child}
                                 <td style="text-align: center;">{$key+1}</td>
                                 {else}
                                 <td style="text-align: center;"> </td>
                            {/if}   

                            <td>{$item.material_code|default=''}</td>
                            <td class="{$item.is_child ? 'child-product-name' : ''}">
                                {if !$item.is_child}
                                    <span class="parent-product-marker">主品</span>
                                    {if $item.has_children}
                                        <span class="layui-badge layui-bg-green" title="包含{$item.child_count}个子商品">{$item.child_count}</span>
                                    {/if}
                                {else}
                                    <span class="layui-badge layui-bg-orange">子品</span>
                                    <span class="layui-badge-rim" title="所属父商品">{$item.parent_product_name|default=''}</span>
                                {/if}
                                {$item.product_name|default=''}
                            </td>
                            <td style="text-align: center;">{$item.quantity|default='0'}</td>
                            <td style="text-align: center;">{$item.unit_price|default='0'}</td>
                            <td style="text-align: center;">{$item.amount|default='0'}</td>
                            <td style="text-align: center;">{$item.tax_rate|default='0'}%</td>
                            <td style="text-align: center;">{$item.tax_amount|default='0'}</td>
                            <td style="text-align: center;">{$item.stock|default='0'}</td>
                            <td style="text-align: center;">{$item.remark|default=''}</td>
                            <td style="text-align: center;">
                                {if $item.has_bom == '有'}
                                <span class="bom-toggle" data-product-id="{$item.product_id}" data-order-qty="{$item.quantity}">
                                    <i class="layui-icon layui-icon-form"></i> {$item.has_bom}
                                </span>
                                {else}
                                {$item.has_bom|default=''}
                                {/if}
                            </td>
                            <td style="text-align: center;">{$item.source_type|default=''}</td>
                            <td style="text-align: center;">
                                {if !empty($item.attachment)}
                                {php}
                                $attachment_ids = explode(',', $item['attachment']);
                                foreach($attachment_ids as $aid) {
                                    $file = \think\facade\Db::name('file')->where('id', $aid)->find();
                                    if($file && !empty($file['filepath'])) {
                                        echo '<div class="layui-inline" style="margin-bottom:5px;">';
                                        echo '<img src="'.$file['filepath'].'" style="max-width:80px;max-height:80px;margin-right:5px;" onclick="previewImage(\''.$file['filepath'].'\')">';
                                        echo '</div>';
                                    }
                                }
                                {/php}
                                {else}
                                <span class="layui-badge layui-bg-gray">无附件</span>
                                {/if}
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="5" style="text-align: right;"><strong>合计(含税)：</strong></td>
                            <td style="text-align: right;"><strong>{$order.total_amount|default=$totalAmount}</strong></td>
                            <td>&nbsp;</td>
                            <td style="text-align: right;"> </td>
                            <td>&nbsp;</td>
                        </tr>
                    </tfoot>
                </table>
            </td>
        </tr>
    </table>
        <!-- 操作按钮 -->
        {gt name="$is_leader" value="0"}
        {if $order.check_status == 2 }
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-btn-container">
                        {if $order.pay_status<2}
                            {if $order.order_type == 1 && $order.delivery_flag == 0}
                                <button class="layui-btn layui-btn-warm" id="reportException">同意未收款发货</button>
                            {/if}
                        {/if}
                        
                    </div>
                </div>
            </div>
        {/if}
        {/gt}
    
    <div class="layui-tab layui-tab-card" style="margin:0; background-color:#fff;" lay-filter="purchase" id="purchaseTab">
        <ul class="layui-tab-title">
			<li class="layui-this" data-load="true">审批信息</li>
            <li >付款记录</li>
            <li >发货记录</li>
			<li >操作记录</li>
		</ul>
		<div class="layui-tab-content" style="padding:0;">
			<div class="layui-tab-item layui-show" style="padding-top:12px; background-color:#fff;">	
				<div id="checkBox" data-status="{$order.check_status}" data-id="{$order.id}" data-checkflowid="{$order.check_flow_id}" class="px-3 pb-3"></div>
			</div>
            <div class="layui-tab-item" >
				{include file="order/view_payment" /}
			</div>
            <div class="layui-tab-item">
				{include file="order/view_delivery" /}
			</div>
			<div class="layui-tab-item" >
				{include file="order/view_log" /}
			</div>
		</div>
    </div>  
</div>

<!-- 隐藏的数据字段 -->
<input type="hidden" id="order_id" value="{$order.id}">
<input type="hidden" id="order_status" value="{$order.status}">
{/block}


<!-- 脚本 -->
{block name="script"}
<script>
    var order_id = "{$order.id|default=0}";
    var purchase_id = "{$order.id|default=0}";
    var auth = "{$auth|default=0}";
    var moduleInit = ['tool','oaCheck','oaPicker','oaEdit'];
    //var checking_btn = '<span class="layui-btn layui-btn-warm" data-event="stop" data-status="1">中止订单</span><span class="layui-btn layui-btn-danger" data-event="void" data-status="1">作废订单</span>';
    var checking_btn = '';
    
    // 图片预览函数 - 移到全局作用域
    function previewImage(filepath) {
        // 检查layer对象是否已定义
        if (typeof layer === 'undefined') {
            console.warn('layer对象未定义，使用备用预览方法');
            window.open(filepath, '_blank');
            return;
        }
        
        try {
            layer.photos({
                photos: {
                    "data": [{"src": filepath}]
                },
                anim: 5,
                shade: 0.8,
                closeBtn: 1,
                shadeClose: true
            });
        } catch (e) {
            console.error("图片预览错误:", e, filepath);
            // 如果上述方法失败，直接在新窗口打开
            window.open(filepath, '_blank');
        }
    }
    
    function gouguInit() {
        var $ = layui.jquery,
            tool = layui.tool,
            layer = layui.layer,
            element = layui.element,
            oaCheck = layui.oaCheck,
            oaPicker = layui.oaPicker,
            tips = layui.tips,
            oaEdit = layui.oaEdit;

        
        // 初始化审批信息组件
        try {
            oaCheck.init({
                check_name: 'customer_order',
                check_copy: 0,
                check_back: 0,
                checking_btn: checking_btn
            });
        } catch(e) {
            console.error('审批信息加载失败:', e);
            layer.msg('审批信息加载失败', {icon: 2});
        }
        
        // Tab切换事件
        element.on('tab(purchase)', function(data){
            let index = data.index;
            if(index == 1){
                loadPaymentLog();
            }else if(index === 2){ // 发货记录选项卡
                loadDeliveryLog();
            } else if(index === 3){ // 操作记录选项卡
                log();
            }
        });
        
        // 报告异常按钮事件
        $('#reportException').on('click', function() {
            layer.confirm('确定要同意未收款发货吗,一旦操作不可撤销？', {
                icon: 3,
                title: '提示',
                area: ['400px', '150px']
            }, function(index) {
                let url = 'Delivery_without_payment';
                let postData = {
                    id: order_id
                };
                layer.prompt({
                    formType: 2,
                    title: '请输入同意未收款发货原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    postData.remark = value;
                    submitAction(url, postData);
                    layer.close(promptIndex);
                });
                
                layer.close(index);
            });
        });
        
        // 中止和作废订单操作
        $('body').on('click', '[data-event]', function() {
            let event = $(this).data('event');
            let status = $(this).data('status');
            let tips = '';
            
            if(event == 'stop') {
                tips = status == 1 ? '确定要中止此订单吗？' : '确定要取消中止此订单吗？';
            } else if(event == 'void') {
                tips = status == 1 ? '确定要作废此订单吗？' : '确定要取消作废此订单吗？';
            }
            
            if(tips) {
                layer.confirm(tips, {
                    icon: 3,
                    title: '提示'
                }, function(index) {
                    let url = `/purchase/order/${event}`;
                    let postData = {
                        id: order_id,
                        status: status
                    };
                    
                    if(status == 1) {
                        layer.prompt({
                            formType: 2,
                            title: '请输入' + (event == 'stop' ? '中止' : '作废') + '原因',
                            area: ['300px', '150px']
                        }, function(value, promptIndex) {
                            postData.remark = value;
                            submitAction(url, postData);
                            layer.close(promptIndex);
                        });
                    } else {
                        submitAction(url, postData);
                    }
                    
                    layer.close(index);
                });
            }
        });
        
        function submitAction(url, data) {
            tool.post(url, data, function(res) {
                if(res.code == 0) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            });
        }

        // BOM展开功能
        $('.bom-toggle').on('click', function() {
            var productId = $(this).data('product-id');
            var orderQty = $(this).data('order-qty');
            var $this = $(this);
            var loadingIndex = layer.load(1); // 显示加载中
            
            // 加载BOM数据
            tool.get('/api/test/getBomInfo', {id: productId}, function(res) {
                layer.close(loadingIndex); // 关闭加载中
                
                if (res.code == 0 && res.data) {
                    var bomData = res.data;
                    var html = '<div style="padding: 15px;"><table class="bom-detail-table">';
                    html += '<thead><tr>';
                    html += '<th>序号</th>';
                    html += '<th>物料编码</th>';
                    html += '<th>物料名称</th>';
                    html += '<th>规格型号</th>';
                    html += '<th>单位</th>';
                    html += '<th>单位用量</th>';
                    html += '<th>订单用量</th>';
                    html += '<th>当前库存</th>';
                    html += '<th>缺口数量</th>';
                    html += '</tr></thead><tbody>';
                    
                    if (bomData.items && bomData.items.length > 0) {
                        bomData.items.forEach(function(item, index) {
                            // 计算订单用量 = 单位用量 × 订单数量
                            var totalQty = (parseFloat(item.qty) * parseFloat(orderQty)).toFixed(2);
                            // 计算缺口 = 订单用量 - 当前库存
                            var stock = item.stock || 0;
                            var shortage = totalQty - stock;
                            
                            html += '<tr>';
                            html += '<td>' + (index + 1) + '</td>';
                            html += '<td>' + (item.material_code || '') + '</td>';
                            html += '<td>' + (item.material_name || '') + '</td>';
                            html += '<td>' + (item.product_specs || '') + '</td>';
                            html += '<td>' + (item.uom_name || '') + '</td>';
                            html += '<td>' + (item.qty || '0') + '</td>';
                            html += '<td>' + totalQty + '</td>';
                            html += '<td>' + stock + '</td>';
                            html += '<td class="' + (shortage > 0 ? 'bom-shortage' : '') + '">' + 
                                   (shortage > 0 ? shortage.toFixed(2) : '0') + '</td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="9">暂无BOM数据</td></tr>';
                    }
                    
                    html += '</tbody></table></div>';
                    
                    // 使用layer.open创建弹出窗口
                    layer.open({
                        type: 1,
                        title: 'BOM清单详情',
                        area: ['80%', '800px'], // 设置弹窗宽高
                        shadeClose: true, // 点击遮罩关闭
                        maxmin: true, // 允许最大化最小化
                        content: html, // 弹窗内容
                        success: function(layero, index) {
                            // 弹窗创建成功后的回调
                            console.log('BOM弹窗已创建，ID: ' + index);
                        }
                    });
                } else {
                    layer.msg('加载BOM数据失败: ' + (res.msg || '未知错误'), {icon: 2});
                }
            });
        });
        
        // 提交审核按钮
        $('#btnSubmit').click(function() {
            layer.confirm('确定要提交此订单进行审核吗？', {icon: 3, title:'提示'}, function(index){
                tool.post('/purchase/order/submit', {id: order_id}, function(res) {
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
        
        // 删除按钮
        $('#btnDelete').click(function() {
            layer.confirm('确定要删除此订单吗？', {icon: 3, title:'提示'}, function(index){
                tool.post('/purchase/order/delete', {id: order_id}, function(res) {
                    if(res.code == 0) {
                        layer.msg(res.msg, {icon: 1});
                        setTimeout(function() {
                            tool.back();
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
        
        // 取消按钮
        $('#btnCancel').click(function() {
            layer.confirm('确定要取消此订单吗？', {icon: 3, title:'提示'}, function(index){
                layer.prompt({
                    formType: 2,
                    title: '请输入取消原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    tool.post('/purchase/order/cancel', {id: order_id, remark: value}, function(res) {
                        if(res.code == 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(promptIndex);
                });
                layer.close(index);
            });
        });
        
        // 查看收货单
        $('.viewReceipt').click(function() {
            let receiptId = $(this).data('id');
            tool.side('/warehouse/Receipt/detail.html?id=' + receiptId);
        });
        
        // 反审按钮点击事件
        $('#btnRevertAudit').click(function() {
            layer.confirm('确定要反审此订单吗？反审后订单状态将恢复为待审核状态。', {
                icon: 3,
                title: '反审确认',
                area: ['400px', '200px']
            }, function(index) {
                layer.prompt({
                    formType: 2,
                    title: '请输入反审原因',
                    area: ['300px', '150px']
                }, function(value, promptIndex) {
                    // 发送反审请求
                    tool.post('/customer/order/revertAudit', {
                        id: order_id,
                        reason: value
                    }, function(res) {
                        if(res.code == 0) {
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                    layer.close(promptIndex);
                });
                layer.close(index);
            });
        });
    }

    // 查看附件
    function viewAttachment(fileId, productName) {
        if (!fileId) {
            layer.msg('未找到附件');
            return;
        }
        
        $.ajax({
            url: '/api/index/get_file_info',
            type: 'get',
            data: {file_id: fileId},
            success: function(res) {
                if (res.code == 0 && res.data) {
                    var fileInfo = res.data;
                    var content = '<div style="padding: 20px;">' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">商品</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + productName + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">文件名</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + fileInfo.name + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item">' +
                        '<label class="layui-form-label">文件类型</label>' +
                        '<div class="layui-input-block">' +
                        '<input type="text" class="layui-input" value="' + fileInfo.fileext + '" readonly>' +
                        '</div></div>' +
                        '<div class="layui-form-item" style="margin-top: 20px; text-align: center;">' +
                        '<a class="layui-btn layui-btn-normal" href="' + fileInfo.filepath + '" target="_blank">' +
                        '<i class="layui-icon layui-icon-download-circle"></i> 下载文件</a>' +
                        '</div></div>';
                        
                    layer.open({
                        type: 1,
                        title: '附件信息',
                        content: content,
                        area: ['500px', '350px'],
                        shade: 0.3,
                        closeBtn: 1,
                        shadeClose: true
                    });
                } else {
                    layer.msg(res.msg || '获取文件信息失败');
                }
            },
            error: function() {
                layer.msg('请求失败，请重试');
            }
        });
    }

    layui.use(['layer', 'form', 'laydate', 'element', 'table'], function(){
        var layer = layui.layer;
        var form = layui.form;
        var laydate = layui.laydate;
        var element = layui.element;
        var table = layui.table;
        var $ = layui.jquery;
        
        // 增强子商品视觉效果
        $(function() {
            // 为子商品添加缩进和前缀
            $('.child-product').each(function() {
                // 如果子商品还没有正确的样式，则应用样式
                if (!$(this).find('.child-product-name').length) {
                    $(this).find('td:eq(2)').addClass('child-product-name');
                }
            });
        });
        
        // BOM细节显示
        $(document).on('click', '.bom-toggle', function(){
            var productId = $(this).data('product-id');
            var orderQty = $(this).data('order-qty');
            
            layer.load(2);
            $.post('/customer/order/get_bom_detail', {product_id: productId, order_qty: orderQty}, function(res){
                layer.closeAll('loading');
                if(res.code === 0){
                    var content = '<div style="padding: 15px;">';
                    content += '<h3 style="margin-bottom:15px;">BOM清单明细</h3>';
                    content += '<table class="bom-detail-table">';
                    content += '<thead><tr><th>序号</th><th>材料编码</th><th>材料名称</th><th>规格</th><th>单位</th><th>数量</th><th>在库数量</th><th>状态</th></tr></thead>';
                    content += '<tbody>';
                    
                    var items = res.data;
                    for(var i = 0; i < items.length; i++){
                        var item = items[i];
                        var shortage = item.stock < item.required_qty ? 'bom-shortage' : '';
                        content += '<tr>';
                        content += '<td>' + (i+1) + '</td>';
                        content += '<td>' + (item.material_code || '') + '</td>';
                        content += '<td>' + (item.material_name || '') + '</td>';
                        content += '<td>' + (item.specs || '') + '</td>';
                        content += '<td>' + (item.unit || '') + '</td>';
                        content += '<td>' + (item.required_qty || 0) + '</td>';
                        content += '<td>' + (item.stock || 0) + '</td>';
                        content += '<td class="' + shortage + '">' + (item.stock < item.required_qty ? '缺货' : '充足') + '</td>';
                        content += '</tr>';
                    }
                    
                    content += '</tbody></table></div>';
                    
                    layer.open({
                        type: 1,
                        title: 'BOM明细',
                        area: ['800px', '500px'],
                        content: content,
                        shadeClose: true
                    });
                } else {
                    layer.msg(res.msg || '获取BOM明细失败', {icon: 2});
                }
            });
        });
        
        // 反审按钮点击事件
        $('#btnRevertAudit').on('click', function(){
            layer.prompt({
                formType: 2,
                title: '请输入反审原因',
                area: ['300px', '150px']
            }, function(value, index){
                layer.close(index);
                if(!value.trim()){
                    layer.msg('反审原因不能为空', {icon: 2});
                    return;
                }
                
                layer.confirm('确定要将此订单反审为未审核状态吗？', function(index){
                    layer.close(index);
                    var order_id = $('#order_id').val();
                    
                    layer.load(1);
                    $.post('/customer/order/revertAudit', {id: order_id, reason: value}, function(res){
                        layer.closeAll('loading');
                        if(res.code === 0){
                            layer.msg('反审成功', {icon: 1}, function(){
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg || '反审失败', {icon: 2});
                        }
                    });
                });
            });
        });

        // 预览图片
        window.previewImage = function(url) {
            if (!url) {
                layer.msg('无效的图片链接');
                return;
            }
            
            layer.photos({
                photos: {
                    "data": [{
                        "src": url
                    }]
                },
                anim: 5
            });
        };
        
        // 同意未收款发货
        $('#reportException').on('click', function(){
            layer.prompt({
                formType: 2,
                title: '请输入备注',
                area: ['350px', '150px']
            }, function(value, index){
                layer.close(index);
                
                if(!value.trim()){
                    layer.msg('备注不能为空', {icon: 2});
                    return;
                }
                
                var orderId = $('#order_id').val();
                
                layer.confirm('确认同意未收款发货吗？', function(index){
                    layer.close(index);
                    
                    $.post('/customer/order/Delivery_without_payment', {
                        id: orderId,
                        remark: value
                    }, function(res){
                        if(res.code === 0){
                            layer.msg('操作成功', {icon: 1}, function(){
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg || '操作失败', {icon: 2});
                        }
                    });
                });
            });
        });
        
        // 加载审核流程
        var checkflowid = $('#checkBox').data('checkflowid');
        var status = $('#checkBox').data('status');
        var id = $('#checkBox').data('id');
        
        $.get('/approve/common/load_check?table_name=customer_order&flow_id='+checkflowid+'&data_id='+id+'&status='+status, function (res) {
            $('#checkBox').html(res);
        });

        
        // 加载选项卡
        element.on('tab(purchase)', function(){
            var elem = this;
            var id = $('#order_id').val();
            var isloaded = elem.getAttribute('data-load');
            if (!isloaded) {
                if (elem.getAttribute('lay-id')=='payment') {
                    // 加载付款记录
                    $("#paymentContainer").load('/customer/order/payment_list?id='+id);
                } else if (elem.getAttribute('lay-id')=='delivery') {
                    // 加载发货记录
                    $("#deliveryContainer").load('/customer/order/delivery_list?id='+id);
                } else if (elem.getAttribute('lay-id')=='log') {
                    // 加载操作记录
                    $("#logContainer").load('/customer/order/log_list?id='+id);
                } else if (elem.getAttribute('lay-id')=='production') {
                    // 加载生产记录
                    $("#productionContainer").load('/customer/order/production_list?id='+id);
                }
                elem.setAttribute('data-load', 'true');
            }
        });
        
        // 增强父子商品关系的交互效果
        $(function() {
            // 父商品点击事件 - 高亮显示相关子商品
            $('tr').not('.child-product').on('click', function() {
                // 清除所有高亮
                $('tr').removeClass('layui-table-click');
                
                // 获取当前父商品ID
                var productId = $(this).data('product-id');
                if (!productId) return;
                
                // 高亮父商品
                $(this).addClass('layui-table-click');
                
                // 查找并高亮所有相关子商品
                $('tr.child-product').each(function() {
                    // 检查此子商品的parent_product_id是否与当前父商品ID匹配
                    if ($(this).hasClass('child-product') && $(this).data('parent-id') == productId) {
                        $(this).addClass('layui-table-click');
                    }
                });
                
                // 滚动到父商品位置
                $('html, body').animate({
                    scrollTop: $(this).offset().top - 100
                }, 300);
            });
            
            // 点击表格外部，清除所有高亮
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.layui-table').length) {
                    $('tr').removeClass('layui-table-click');
                }
            });
            
            // 添加表格鼠标悬停效果
            $('tr').hover(
                function() {
                    $(this).addClass('layui-table-hover');
                },
                function() {
                    $(this).removeClass('layui-table-hover');
                }
            );
        });
    });
</script>
{/block}
<!-- /脚本 -->
