{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
		<div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
			<ul class="layui-tab-title">
				<li class="layui-this">全部公文</li>
				<li>我发的公文</li>
				<li>我收到的公文</li>
				<li>抄送给我的公文</li>
				<li>共享查阅的公文</li>
			</ul>
		</div>
	</div>
	<form class="layui-form gg-form-bar border-x" id="barsearchform">
		<div class="layui-input-inline" style="width:128px">
			<select name="secrets">
				<option value="">选择秘密程度</option>
				{volist name="$secrets" id="vo"}
				{gt name="$key" value="0"}
				<option value="{$key}">{$vo}</option>
				{/gt}
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:128px">
			<select name="urgency">
				<option value="">选择重要程度</option>
				{volist name="$urgency" id="vo"}
				{gt name="$key" value="0"}
				<option value="{$key}">{$vo}</option>
				{/gt}
				{/volist}
			</select>
		</div>
		<div class="layui-input-inline" style="width:300px">
			<input type="text" name="keywords" placeholder="关键字" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<input type="hidden" name="tab" value="0" />
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="table_official" lay-filter="table_official"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm tool-add" type="button" data-href="/adm/official/add">+ 添加公文</button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool, element = layui.element;
		//tab切换
		element.on('tab(tab)', function(data){
			$('[name="tab"]').val(data.index);
			$("#barsearchform")[0].reset();
			layui.pageTable.reload({where:{tab:data.index},page:{curr:1}});
			return false;
		});
		
		layui.pageTable = table.render({
			elem: "#table_official"
			, toolbar: "#toolbarDemo"
			,url: "/adm/official/datalist"
			,page: true
			,limit: 20
			,cellMinWidth: 60
			,height: 'full-152'
			,cols: [[
				{field:'id',width:80, title: 'ID号', align:'center'}
				,{field:'check_status',title: '审核状态',width:110, align:'center',templet: function(d){
					var html = '<span class="check-status-color-'+d.check_status+'">『'+d.check_status_str+'』</span>';
					return html;
				}}
				,{field:'title',title: '公文主题',minWidth:240}
				,{field:'code',title: '公文编号',width:160}
				,{field:'secrets_str',title: '密级程度',width:80, align:'center'}
				,{field:'urgency_str',title: '紧急程度',width:80, align:'center'}
				,{field:'draft_name',title: '拟稿人',width:80, align:'center'}
				,{field:'draft_time',title: '拟稿日期',width:100, align:'center'}
				,{field:'draft_dname',title: '拟稿部门',width:100, align:'center'}
				,{field:'create_time', title: '创建时间',width:150,align:'center'}
				,{width:120,fixed:'right',title: '操作', align:'center',templet: function(d){
					var html='';
					var btn1='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详细</a>';
					var btn2='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
					var btn3='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>';
					if((d.check_status==0 || d.check_status==4)&& d.admin_id == login_admin){
						html = '<div class="layui-btn-group">'+btn1+btn2+btn3+'</div>';
					}
					else{
						html = '<div class="layui-btn-group">'+btn1+'</div>';
					}
					return html;
				}}
			]]
		});
		
		//表头工具栏事件
		table.on('toolbar(table_official)', function(obj){
			if (obj.event === 'add') {
				tool.side("/adm/official/add");
				return;
			}
		});	
			
		table.on('tool(table_official)',function (obj) {
			var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
			var data = obj.data;
			if (obj.event === 'view') {
				tool.side("/adm/official/view?id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/adm/official/add?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除该内容吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.delete("/adm/official/del", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->