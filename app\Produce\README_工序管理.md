# 工序管理功能部署说明

## 功能概述

本工序管理功能是为ERP系统生产模块新增的功能，包含以下特性：

### 主要功能
1. **工序管理** - 添加、编辑、删除、查看工序信息
2. **工作组管理** - 管理工序所属的工作组
3. **计价方式** - 支持按件计价和按时计价两种方式
4. **标准效率** - 设置工序的标准效率和单价
5. **报工用户** - 指定可以进行报工的用户

### 技术特点
- 遵循系统原有架构和代码规范
- 使用统一的模板基类和样式
- 支持数据验证和错误处理
- 自动生成工序编号
- 完整的增删改查功能

## 文件结构

```
app/Produce/
├── controller/
│   ├── Process.php              # 工序管理控制器
│   └── ProcessGroup.php         # 工作组管理控制器
├── model/
│   └── ProcessModel.php         # 工序数据模型
├── validate/
│   └── ProcessValidate.php      # 工序数据验证器
├── view/
│   ├── process/
│   │   ├── index.html          # 工序列表页面
│   │   ├── add.html            # 工序添加/编辑页面
│   │   └── view.html           # 工序详情页面
│   └── process_group/
│       ├── index.html          # 工作组列表页面
│       ├── add.html            # 工作组添加/编辑页面
│       └── view.html           # 工作组详情页面
└── sql/
    └── process.sql             # 数据库表结构
```

## 部署步骤

### 1. 数据库部署

执行SQL文件创建数据表：

```sql
-- 在MySQL中执行以下命令
source app/Produce/sql/process.sql;
```

或者直接执行SQL内容：

```sql
-- 工序表
CREATE TABLE IF NOT EXISTS `oa_produce_process` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工序ID',
  `code` varchar(50) NOT NULL DEFAULT '' COMMENT '工序编号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工序名称',
  `group_id` int(11) NOT NULL DEFAULT '0' COMMENT '工作组ID',
  `report_user` varchar(255) DEFAULT '' COMMENT '报工用户',
  `standard_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '标准单价',
  `efficiency` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '标准效率(件/小时)',
  `pricing_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计价方式:1按件计价,2按时计价',
  `quantity_ratio` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '数量单比',
  `description` text COMMENT '工序描述',
  `serial_number` varchar(100) DEFAULT '' COMMENT '工序序号',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `group_id` (`group_id`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工序表';

-- 工作组表
CREATE TABLE IF NOT EXISTS `oa_process_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工作组ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工作组名称',
  `description` text COMMENT '工作组描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作组表';
```

### 2. 菜单配置

在系统菜单管理中添加以下菜单项：

```
生产管理
├── 工序管理 (/Produce/process/index)
└── 工作组管理 (/Produce/processGroup/index)
```

### 3. 权限配置

为相关角色分配以下权限：
- 工序管理查看权限
- 工序管理添加权限
- 工序管理编辑权限
- 工序管理删除权限
- 工作组管理权限

## 使用说明

### 工作组管理

1. **添加工作组**
   - 进入"工作组管理"页面
   - 点击"添加工作组"按钮
   - 填写工作组名称、描述等信息
   - 设置排序和状态
   - 保存

2. **管理工作组**
   - 可以编辑、删除、启用/禁用工作组
   - 查看工作组下的工序列表
   - 支持按状态筛选

### 工序管理

1. **添加工序**
   - 进入"工序管理"页面
   - 点击"添加工序"按钮
   - 填写工序信息：
     - 工序编号（可选择系统自动生成）
     - 工序名称
     - 选择工作组
     - 设置计价方式（按件/按时）
     - 设置标准单价和效率
     - 设置数量单比
     - 填写工序描述

2. **工序管理**
   - 查看工序列表
   - 编辑工序信息
   - 删除工序
   - 按工作组、计价方式筛选
   - 关键字搜索

### 字段说明

- **工序编号**: 唯一标识，格式为GX001、GX002等
- **工序名称**: 工序的名称描述
- **工作组**: 工序所属的工作组
- **报工用户**: 可以对此工序进行报工的用户
- **标准单价**: 工序的标准价格
- **标准效率**: 单位时间内的产出数量
- **计价方式**: 按件计价或按时计价
- **数量单比**: 用于计算的数量比例
- **工序序号**: 在工艺流程中的序号

## 注意事项

1. **数据完整性**
   - 删除工作组前需确保没有关联的工序
   - 工序编号具有唯一性约束

2. **权限控制**
   - 确保用户具有相应的操作权限
   - 删除操作需要管理员权限

3. **数据备份**
   - 在生产环境部署前请备份数据库
   - 建议先在测试环境验证功能

4. **系统兼容性**
   - 本功能基于现有系统架构开发
   - 使用了系统统一的样式和组件
   - 遵循现有的代码规范

## 扩展功能

后续可以考虑添加以下功能：
1. 工序工时统计
2. 工序成本分析
3. 工序效率监控
4. 工序质量管控
5. 工序排程优化

## 技术支持

如有问题请联系开发团队或查看系统日志进行排查。