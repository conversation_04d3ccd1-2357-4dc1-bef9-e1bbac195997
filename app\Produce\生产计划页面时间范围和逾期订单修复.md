# 生产计划页面时间范围和逾期订单修复

## 🔍 问题描述

生产计划页面（`http://tc.xinqiyu.cn:8830/Produce/productionPlan/index`）存在以下问题：

1. **时间范围选择问题**：选择时间范围后没有正确写回到前后两个时间控件
2. **逾期订单不显示**：已经逾期的生产订单没有在待排产列表中显示

## 🎯 问题原因分析

### 1. 时间范围选择问题
- 使用了 `laydatePlus` 但配置不当
- 缺少范围选择器的正确实现
- 两个独立的时间控件没有联动

### 2. 逾期订单不显示问题
- 查询条件过于严格：`plan_status = 0`（只显示未排产）
- 没有考虑逾期但需要重新排产的订单
- 缺少逾期标识和提醒

## 🛠️ 解决方案

### 1. 时间范围选择修复

#### 前端修改
**替换日期选择器实现**：
```javascript
// 修改前：使用laydatePlus
const moduleInit = ['tool', 'laydatePlus'];
var startDatePicker = new laydatePlus({...});

// 修改后：使用layui原生laydate
const moduleInit = ['tool'];
layui.use(['laydate'], function(){
    var laydate = layui.laydate;
    
    // 范围选择器
    laydate.render({
        elem: '#dateRange',
        type: 'date',
        range: '至',
        done: function(value, date, endDate) {
            if (value) {
                var dates = value.split(' 至 ');
                if (dates.length === 2) {
                    document.getElementById('startDate').value = dates[0];
                    document.getElementById('endDate').value = dates[1];
                    window.refreshGanttChart();
                }
            }
        }
    });
});
```

#### HTML结构优化
```html
<!-- 主要的范围选择器 -->
<div class="filter-item date-range-picker">
    <label>时间范围：</label>
    <input type="text" id="dateRange" class="layui-input" placeholder="选择日期范围" style="width: 250px;">
</div>

<!-- 隐藏的独立控件（用于程序读取） -->
<div class="filter-item date-range-picker" style="display: none;">
    <input type="text" id="startDate" class="layui-input">
    <input type="text" id="endDate" class="layui-input">
</div>
```

### 2. 逾期订单显示修复

#### 后端查询逻辑修改
**扩展查询条件**：
```php
// 修改前：只显示未排产订单
$where[] = ['plan_status', '=', 0]; // 未排产

// 修改后：显示未排产和需要重新排产的订单
$where[] = ['plan_status', 'in', [0, 1, 2]]; // 未排产、已排产、生产中
$where[] = ['status', 'in', [0, 1, 2]]; // 待排产、已排产、生产中状态

// 添加逾期订单的特殊处理
$currentTime = time();
$where[] = ['delivery_date', '>=', $currentTime - 30 * 24 * 3600]; // 显示30天内的订单（包括逾期）
```

#### 逾期标识计算
```php
// 计算逾期信息
$currentTime = time();
foreach ($orders->items() as &$order) {
    // 判断是否逾期
    $order['is_overdue'] = $order['delivery_date'] < $currentTime;
    $order['overdue_days'] = $order['is_overdue'] ? ceil(($currentTime - $order['delivery_date']) / 86400) : 0;
    
    // 其他计算...
}
```

### 3. 前端逾期订单显示

#### 订单渲染增强
```javascript
orders.forEach(order => {
    // 逾期标识
    const overdueClass = order.is_overdue ? 'order-overdue' : '';
    const overdueText = order.is_overdue ? `<span class="overdue-badge">逾期${order.overdue_days}天</span>` : '';

    html += `
        <div class="order-item ${overdueClass}" ...>
            <div class="order-no">
                ${order.order_no}
                <span class="priority-badge ${priorityClass}">P${order.priority}</span>
                ${overdueText}
            </div>
            ...
        </div>
    `;
});
```

#### 逾期样式定义
```css
.order-item.order-overdue {
    border-left: 4px solid #ff4757;
    background: #fff5f5;
}

.order-item.order-overdue:hover {
    border-color: #ff4757;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.2);
}

.overdue-badge {
    background: #ff4757;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    margin-left: 5px;
}
```

## 📋 修复效果

### 1. 时间范围选择优化
- ✅ **统一范围选择器**：使用一个输入框选择日期范围
- ✅ **自动回写**：选择范围后自动更新开始和结束日期
- ✅ **实时刷新**：时间变更后自动刷新甘特图
- ✅ **用户友好**：更直观的日期范围选择体验

### 2. 逾期订单显示
- ✅ **逾期订单可见**：逾期的生产订单正常显示在列表中
- ✅ **视觉提醒**：逾期订单有红色边框和背景色
- ✅ **逾期天数**：显示具体逾期多少天
- ✅ **优先处理**：逾期订单更容易被识别和处理

### 3. 业务流程改进
- ✅ **完整视图**：显示所有需要关注的订单
- ✅ **风险提醒**：逾期订单醒目提示
- ✅ **灵活排产**：支持对逾期订单重新排产
- ✅ **时间管理**：更好的时间范围控制

## 🎨 视觉效果

### 时间选择器
- **主选择器**：250px宽度的范围选择输入框
- **占位文本**："选择日期范围"
- **选择格式**："2025-08-12 至 2025-09-11"

### 逾期订单标识
- **边框颜色**：红色左边框（4px）
- **背景颜色**：淡红色背景（#fff5f5）
- **逾期标签**：红色背景白色文字
- **悬停效果**：红色阴影效果

## 🧪 测试场景

### 1. 时间范围选择测试
- 点击时间范围输入框
- 选择开始和结束日期
- 确认后检查甘特图是否刷新
- 验证隐藏的开始/结束日期控件是否正确更新

### 2. 逾期订单显示测试
- 创建交期已过的生产订单
- 访问生产计划页面
- 确认逾期订单显示在列表中
- 验证逾期标识和天数计算正确

### 3. 综合功能测试
- 调整时间范围包含逾期订单
- 验证逾期订单在甘特图中的显示
- 测试逾期订单的拖拽排产功能

## 🎯 预期收益

1. **提高效率**：更便捷的时间范围选择
2. **风险控制**：及时发现和处理逾期订单
3. **用户体验**：更直观的界面和操作
4. **生产管理**：更完整的订单视图和排产控制

修复完成后，生产计划页面将提供更完善的时间管理和订单显示功能。
