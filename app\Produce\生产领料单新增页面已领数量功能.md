# 生产领料单新增页面已领数量功能

## 🎯 功能描述

在生产领料单新增页面（`http://tc.xinqiyu.cn:8830/Produce/MaterialRequest/add?order_id=27`）的物料清单中添加"已领数量"和"剩余需求"列，帮助用户了解每个物料的领取情况，避免重复领料或遗漏。

## 📋 新增列说明

### 1. 已领数量
- **显示内容**：该生产订单已经领取的物料数量
- **数据来源**：统计该生产订单所有领料单明细中该物料的实际出库数量
- **显示样式**：蓝色文字，突出显示
- **计算逻辑**：`SUM(production_material_request_detail.actual_quantity)`

### 2. 剩余需求
- **显示内容**：标准需求量 - 已领数量
- **计算公式**：`remaining_quantity = standard_quantity - received_quantity`
- **显示样式**：
  - 剩余数量 > 0：红色文字（需要继续领料）
  - 剩余数量 = 0：绿色文字（已满足需求）
- **业务意义**：指导本次领料的申请数量

## 🛠️ 技术实现

### 1. 后端修改

#### 控制器方法增强
在 `app\Produce\controller\MaterialRequest.php` 的 `getBomMaterials()` 方法中：

```php
// 计算已领数量：查询该生产订单已经领取的物料数量
$receivedQuantity = $this->getReceivedQuantity($productionOrderId, $material['material_id']);

$processedMaterials[] = [
    // ... 其他字段
    'received_quantity' => round($receivedQuantity, 4), // 已领数量
    'remaining_quantity' => round($standardQuantity - $receivedQuantity, 4), // 剩余需求
    'request_quantity' => round(max(0, $standardQuantity - $receivedQuantity), 4) // 默认申请剩余数量
];
```

#### 新增查询方法
```php
/**
 * 获取生产订单某个物料的已领数量
 * @param int $productionOrderId 生产订单ID
 * @param int $materialId 物料ID
 * @return float 已领数量
 */
private function getReceivedQuantity($productionOrderId, $materialId)
{
    try {
        // 查询该生产订单的所有领料单明细中该物料的实际出库数量
        $receivedQuantity = Db::name('production_material_request_detail')
            ->alias('d')
            ->leftJoin('production_material_request r', 'd.request_no = r.request_no')
            ->where('r.production_order_id', $productionOrderId)
            ->where('d.material_id', $materialId)
            ->sum('d.actual_quantity');
        
        return floatval($receivedQuantity ?: 0);
        
    } catch (\Exception $e) {
        Log::error('获取已领数量失败', [
            'production_order_id' => $productionOrderId,
            'material_id' => $materialId,
            'error' => $e->getMessage()
        ]);
        
        return 0;
    }
}
```

### 2. 前端修改

#### 表格列定义
在 `app\Produce\view\material_request\add.html` 中添加新列：

```javascript
{field: 'received_quantity', title: '已领数量', width: 90, align: 'right', templet: function(d) {
    return '<span style="color: #1E9FFF;">' + (d.received_quantity || 0) + '</span>';
}},
{field: 'remaining_quantity', title: '剩余需求', width: 90, align: 'right', templet: function(d) {
    var remaining = d.remaining_quantity || 0;
    var color = remaining > 0 ? '#FF5722' : '#5FB878';
    return '<span style="color: ' + color + ';">' + remaining + '</span>';
}},
```

## 📊 列布局优化

### 调整后的列顺序
1. **物料编码** (120px)
2. **物料名称** (180px)
3. **规格** (100px)
4. **单位** (60px)
5. **BOM用量** (90px)
6. **标准需求** (90px)
7. **已领数量** (90px) ✨ 新增
8. **剩余需求** (90px) ✨ 新增
9. **可用库存** (90px)
10. **申请数量** (120px)
11. **超领状态** (100px)
12. **库存状态** (90px)
13. **操作** (80px)

### 智能申请数量
- **默认值调整**：申请数量默认为剩余需求量，而不是标准需求量
- **避免超领**：当已领数量已满足需求时，默认申请数量为0
- **用户友好**：减少手动计算，提高操作效率

## 🎨 视觉效果

### 颜色方案
- **已领数量**：蓝色 (#1E9FFF) - 表示已完成的部分
- **剩余需求**：
  - 红色 (#FF5722) - 还需要领料
  - 绿色 (#5FB878) - 已满足需求

### 数据精度
- 所有数量字段保留4位小数
- 右对齐显示，便于数值比较

## 🔍 业务价值

### 1. 避免重复领料
- 清楚显示已领数量，避免重复申请
- 减少库存浪费和管理成本

### 2. 提高效率
- 自动计算剩余需求，减少手动计算
- 智能设置默认申请数量

### 3. 数据透明
- 完整的物料需求和领取历史
- 便于生产计划和物料管理

### 4. 决策支持
- 基于实际需求进行领料申请
- 支持精细化的生产物料管理

## 🧪 测试场景

### 1. 首次领料
- 已领数量：0
- 剩余需求：等于标准需求
- 申请数量：默认为标准需求

### 2. 部分领料后
- 已领数量：显示累计领取量
- 剩余需求：标准需求 - 已领数量
- 申请数量：默认为剩余需求

### 3. 已满足需求
- 已领数量：等于或大于标准需求
- 剩余需求：0或负数（绿色显示）
- 申请数量：默认为0

### 4. 超领情况
- 已领数量：大于标准需求
- 剩余需求：负数（绿色显示）
- 申请数量：默认为0

## 📈 预期效果

通过添加"已领数量"和"剩余需求"列，用户可以：

1. **快速了解**每个物料的领取状态
2. **准确申请**所需的物料数量
3. **避免错误**如重复领料或遗漏
4. **提高效率**减少手动计算和查询
5. **优化管理**更好地控制物料流转

这个功能将显著提升生产领料管理的精确性和用户体验。
