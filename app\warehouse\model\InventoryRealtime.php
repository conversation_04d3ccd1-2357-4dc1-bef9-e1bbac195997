<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;
use think\facade\Db;

/**
 * 实时库存模型
 */
class InventoryRealtime extends Model
{
    // 设置表名
    protected $name = 'inventory_realtime';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 设置字段信息
    protected $schema = [
        'id'                 => 'int',
        'product_id'         => 'int',
        'warehouse_id'       => 'int',
        'quantity'           => 'float',
        'available_quantity' => 'float',
        'locked_quantity'    => 'float',
        'unit'               => 'string',
        'cost_price'         => 'float',
        'create_time'        => 'int',
        'update_time'        => 'int',
    ];
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\product\model\Product', 'product_id', 'id');
    }

    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'warehouse_id', 'id');
    }
    
    /**
     * 检查库存是否充足
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 需要数量
     * @return bool
     */
    public static function hasEnoughStock($productId, $warehouseId, $quantity)
    {
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        if (!$inventory) {
            return false;
        }
        
        return $inventory->available_quantity >= $quantity;
    }
    
    /**
     * 增加库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 增加数量
     * @param string $unit 单位
     * @param float $costPrice 成本价
     * @return array 更新后的库存信息
     */
    public static function increaseStock($productId, $warehouseId, $quantity, $unit = '', $costPrice = 0)
    {
        // 检查是否存在相同产品、仓库的库存记录
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        if ($inventory) {
            // 更新现有库存
            $beforeQuantity = $inventory->quantity;
            $inventory->quantity += $quantity;
            $inventory->available_quantity += $quantity;
            
            // 更新成本价（加权平均）
            if ($costPrice > 0 && $beforeQuantity > 0) {
                $totalCost = ($beforeQuantity * $inventory->cost_price) + ($quantity * $costPrice);
                $inventory->cost_price = $totalCost / $inventory->quantity;
            } elseif ($costPrice > 0) {
                $inventory->cost_price = $costPrice;
            }
            
            $inventory->save();
        } else {
            // 创建新库存记录
            $inventory = self::create([
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'quantity' => $quantity,
                'available_quantity' => $quantity,
                'locked_quantity' => 0,
                'unit' => $unit,
                'cost_price' => $costPrice
            ]);
            $beforeQuantity = 0;
        }
        
        return [
            'inventory' => $inventory,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity
        ];
    }
    
    /**
     * 减少库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 减少数量
     * @return array 更新后的库存信息
     * @throws \Exception 库存不足时抛出异常
     */
    public static function decreaseStock($productId, $warehouseId, $quantity)
    {
        // 查询库存记录
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        // 如果库存记录不存在或库存不足，抛出异常
        if (!$inventory) {
            throw new \Exception('库存记录不存在');
        }
        
        if ($inventory->available_quantity < $quantity) {
            throw new \Exception('可用库存不足，当前可用: ' . $inventory->available_quantity . ', 需求: ' . $quantity);
        }
        
        // 更新库存
        $beforeQuantity = $inventory->quantity;
        $inventory->quantity -= $quantity;
        $inventory->available_quantity -= $quantity;
        $inventory->save();
        
        return [
            'inventory' => $inventory,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity
        ];
    }
    
    /**
     * 锁定库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 锁定数量
     * @return array 更新后的库存信息
     * @throws \Exception 库存不足时抛出异常
     */
    public static function lockStock($productId, $warehouseId, $quantity)
    {
        // 查询库存记录
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        // 如果库存记录不存在或可用库存不足，抛出异常
        if (!$inventory) {
            throw new \Exception('库存记录不存在');
        }
        
        if ($inventory->available_quantity < $quantity) {
            throw new \Exception('可用库存不足，当前可用: ' . $inventory->available_quantity . ', 需求: ' . $quantity);
        }
        
        // 锁定库存
        $beforeAvailable = $inventory->available_quantity;
        $inventory->available_quantity -= $quantity;
        $inventory->locked_quantity += $quantity;
        $inventory->save();
        
        return [
            'inventory' => $inventory,
            'before_available' => $beforeAvailable,
            'after_available' => $inventory->available_quantity,
            'locked_quantity' => $inventory->locked_quantity
        ];
    }
    
    /**
     * 释放锁定库存
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 释放数量
     * @return array 更新后的库存信息
     * @throws \Exception 锁定库存不足时抛出异常
     */
    public static function unlockStock($productId, $warehouseId, $quantity)
    {
        // 查询库存记录
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        // 如果库存记录不存在或锁定库存不足，抛出异常
        if (!$inventory) {
            throw new \Exception('库存记录不存在');
        }
        
        if ($inventory->locked_quantity < $quantity) {
            throw new \Exception('锁定库存不足，当前锁定: ' . $inventory->locked_quantity . ', 释放: ' . $quantity);
        }
        
        // 释放锁定库存
        $beforeLocked = $inventory->locked_quantity;
        $inventory->locked_quantity -= $quantity;
        $inventory->available_quantity += $quantity;
        $inventory->save();
        
        return [
            'inventory' => $inventory,
            'before_locked' => $beforeLocked,
            'after_locked' => $inventory->locked_quantity,
            'available_quantity' => $inventory->available_quantity
        ];
    }
    
    /**
     * 使用锁定库存（实际出库）
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $quantity 使用数量
     * @return array 更新后的库存信息
     * @throws \Exception 锁定库存不足时抛出异常
     */
    public static function useLockedStock($productId, $warehouseId, $quantity)
    {
        // 查询库存记录
        $inventory = self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId
        ])->find();
        
        // 如果库存记录不存在或锁定库存不足，抛出异常
        if (!$inventory) {
            throw new \Exception('库存记录不存在');
        }
        
        if ($inventory->locked_quantity < $quantity) {
            throw new \Exception('锁定库存不足，当前锁定: ' . $inventory->locked_quantity . ', 使用: ' . $quantity);
        }
        
        // 使用锁定库存
        $beforeQuantity = $inventory->quantity;
        $beforeLocked = $inventory->locked_quantity;
        
        $inventory->quantity -= $quantity;
        $inventory->locked_quantity -= $quantity;
        $inventory->save();
        
        return [
            'inventory' => $inventory,
            'before_quantity' => $beforeQuantity,
            'after_quantity' => $inventory->quantity,
            'before_locked' => $beforeLocked,
            'after_locked' => $inventory->locked_quantity
        ];
    }
    
    /**
     * 获取产品在所有仓库的库存汇总
     * 
     * @param int $productId 产品ID
     * @return array
     */
    public static function getProductInventorySummary($productId)
    {
        $inventories = self::where('product_id', $productId)
            ->with(['warehouse'])
            ->select();
        
        $totalQuantity = 0;
        $totalAvailable = 0;
        $totalLocked = 0;
        $warehouses = [];
        
        foreach ($inventories as $inventory) {
            $totalQuantity += $inventory->quantity;
            $totalAvailable += $inventory->available_quantity;
            $totalLocked += $inventory->locked_quantity;
            
            $warehouses[] = [
                'warehouse_id' => $inventory->warehouse_id,
                'warehouse_name' => $inventory->warehouse->name ?? '',
                'quantity' => $inventory->quantity,
                'available_quantity' => $inventory->available_quantity,
                'locked_quantity' => $inventory->locked_quantity,
                'unit' => $inventory->unit,
                'cost_price' => $inventory->cost_price
            ];
        }
        
        return [
            'product_id' => $productId,
            'total_quantity' => $totalQuantity,
            'total_available' => $totalAvailable,
            'total_locked' => $totalLocked,
            'warehouses' => $warehouses
        ];
    }

    /**
     * 产品ID搜索器
     * @param \think\db\Query $query
     * @param mixed $value
     * @return void
     */
    public function searchProductId($query, $value)
    {
        if (!empty($value)) {
            $query->where('product_id', $value);
        }
    }

    /**
     * 仓库ID搜索器
     * @param \think\db\Query $query
     * @param mixed $value
     * @return void
     */
    public function searchWarehouseId($query, $value)
    {
        if (!empty($value)) {
            $query->where('warehouse_id', $value);
        }
    }

    /**
     * 关键词搜索器
     * @param \think\db\Query $query
     * @param string $value
     * @return void
     */
    public function searchKeywords($query, $value)
    {
        if (!empty($value)) {
            $keywords = trim($value);

            // 查询匹配的产品ID
            $productIds = Db::name('product')
                ->where('title|material_code|specs', 'like', '%' . $keywords . '%')
                ->column('id');

            if (!empty($productIds)) {
                // 如果找到匹配的产品，按产品ID搜索
                $query->whereIn('product_id', $productIds);
            } else {
                // 如果没有找到匹配的产品，返回空结果
                $query->where('product_id', 0);
            }
        }
    }
}
