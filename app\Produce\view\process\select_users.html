{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar" id="searchform">
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，姓名/手机号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_users" lay-filter="table_users"></table>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        // 已选择的用户ID数组
        var selectedUserIds = '{$selected_ids}'.split(',').filter(function(id) {
            return id && id.trim() !== '';
        });
        
        layui.pageTable = table.render({
            elem: "#table_users"
            ,title: "选择报工用户"
            ,url: "/Produce/process/getUsers"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-100'
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 60,
                    templet: function(d) {
                        var checked = selectedUserIds.indexOf(d.id.toString()) !== -1 ? 'checked' : '';
                        return '<input type="checkbox" name="user_checkbox" value="' + d.id + '" ' + checked + ' lay-skin="primary">';
                    }
                },{
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'show_name',
                    title: '姓名',
                    minWidth: 120
                },{
                    field: 'mobile',
                    title: '手机号',
                    align: 'center',
                    width: 120
                },{
                    field: 'department_name',
                    title: '部门',
                    align: 'center',
                    width: 150
                }
            ]]
            ,done: function(res, curr, count) {
                // 表格渲染完成后，重新设置复选框状态
                $('input[name="user_checkbox"]').each(function() {
                    var userId = $(this).val();
                    if (selectedUserIds.indexOf(userId) !== -1) {
                        $(this).prop('checked', true);
                    }
                });
                layui.form.render('checkbox');
            }
        });
        
        // 监听复选框变化
        $(document).on('change', 'input[name="user_checkbox"]', function() {
            var userId = $(this).val();
            var isChecked = $(this).is(':checked');
            
            if (isChecked) {
                // 添加到已选择列表
                if (selectedUserIds.indexOf(userId) === -1) {
                    selectedUserIds.push(userId);
                }
            } else {
                // 从已选择列表移除
                var index = selectedUserIds.indexOf(userId);
                if (index !== -1) {
                    selectedUserIds.splice(index, 1);
                }
            }
        });
        
        // 提供给父页面调用的方法
        window.getSelectedUsers = function() {
            var selectedUsers = [];
            $('input[name="user_checkbox"]:checked').each(function() {
                var $tr = $(this).closest('tr');
                var index = $tr.attr('data-index');
                var tableData = layui.pageTable.config.data;
                
                // 从当前行获取数据
                if (tableData && tableData[index]) {
                    var row = tableData[index];
                    selectedUsers.push({
                        id: row.id,
                        name: row.name,
                        nickname: row.nickname,
                        show_name: row.show_name,
                        mobile: row.mobile,
                        department_name: row.department_name
                    });
                } else {
                    // 备用方案：从DOM中获取数据
                    var userId = $(this).val();
                    var userName = $tr.find('td').eq(2).text();
                    var userMobile = $tr.find('td').eq(3).text();
                    var userDept = $tr.find('td').eq(4).text();
                    
                    selectedUsers.push({
                        id: userId,
                        name: userName,
                        nickname: '',
                        show_name: userName,
                        mobile: userMobile,
                        department_name: userDept
                    });
                }
            });
            return selectedUsers;
        };
    }
</script>
{/block}
{block name="body"}
<div class="p-page">
    <form class="layui-form gg-form-bar" id="searchform">
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，姓名/手机号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_users" lay-filter="table_users"></table>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        // 已选择的用户ID数组
        var selectedUserIds = '{$selected_ids}'.split(',').filter(function(id) {
            return id && id.trim() !== '';
        });
        
        layui.pageTable = table.render({
            elem: "#table_users"
            ,title: "选择报工用户"
            ,url: "/Produce/process/getUsers"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-100'
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 60,
                    templet: function(d) {
                        var checked = selectedUserIds.indexOf(d.id.toString()) !== -1 ? 'checked' : '';
                        return '<input type="checkbox" name="user_checkbox" value="' + d.id + '" ' + checked + ' lay-skin="primary">';
                    }
                },{
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'show_name',
                    title: '姓名',
                    minWidth: 120
                },{
                    field: 'mobile',
                    title: '手机号',
                    align: 'center',
                    width: 120
                },{
                    field: 'department_name',
                    title: '部门',
                    align: 'center',
                    width: 150
                }
            ]]
            ,done: function(res, curr, count) {
                // 表格渲染完成后，重新设置复选框状态
                $('input[name="user_checkbox"]').each(function() {
                    var userId = $(this).val();
                    if (selectedUserIds.indexOf(userId) !== -1) {
                        $(this).prop('checked', true);
                    }
                });
                layui.form.render('checkbox');
            }
        });
        
        // 监听复选框变化
        $(document).on('change', 'input[name="user_checkbox"]', function() {
            var userId = $(this).val();
            var isChecked = $(this).is(':checked');
            
            if (isChecked) {
                // 添加到已选择列表
                if (selectedUserIds.indexOf(userId) === -1) {
                    selectedUserIds.push(userId);
                }
            } else {
                // 从已选择列表移除
                var index = selectedUserIds.indexOf(userId);
                if (index !== -1) {
                    selectedUserIds.splice(index, 1);
                }
            }
        });
        
        // 提供给父页面调用的方法
        window.getSelectedUsers = function() {
            var selectedUsers = [];
            $('input[name="user_checkbox"]:checked').each(function() {
                var row = table.cache.table_users[$(this).closest('tr').data('index')];
                if (row) {
                    selectedUsers.push({
                        id: row.id,
                        name: row.name,
                        nickname: row.nickname,
                        show_name: row.show_name,
                        mobile: row.mobile,
                        department_name: row.department_name
                    });
                }
            });
            return selectedUsers;
        };
    }
</script>
{/block}