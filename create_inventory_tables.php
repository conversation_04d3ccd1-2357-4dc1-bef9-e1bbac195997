<?php
/**
 * 创建库存管理相关数据库表
 */

// 数据库配置（请根据实际情况修改）
$config = [
    'host' => 'localhost',
    'dbname' => 'sem_erp_oa',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}", 
        $config['username'], 
        $config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 创建库存管理数据库表 ===\n\n";
    
    // 1. 实时库存表
    echo "1. 创建实时库存表 (oa_inventory_realtime)...\n";
    $sql1 = "CREATE TABLE IF NOT EXISTS `oa_inventory_realtime` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL COMMENT '产品ID',
      `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
      `quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总库存数量',
      `available_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '可用数量',
      `locked_quantity` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '锁定数量',
      `unit` varchar(20) DEFAULT '' COMMENT '单位',
      `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
      `create_time` int(11) NOT NULL,
      `update_time` int(11) NOT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_inventory` (`product_id`,`warehouse_id`),
      KEY `idx_product` (`product_id`),
      KEY `idx_warehouse` (`warehouse_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时库存表'";
    $pdo->exec($sql1);
    echo "   ✅ 实时库存表创建成功\n";
    
    // 2. 库存锁定表
    echo "2. 创建库存锁定表 (oa_inventory_lock)...\n";
    $sql2 = "CREATE TABLE IF NOT EXISTS `oa_inventory_lock` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `product_id` int(11) NOT NULL COMMENT '产品ID',
      `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
      `quantity` decimal(10,2) NOT NULL COMMENT '锁定数量',
      `ref_type` varchar(50) NOT NULL COMMENT '关联类型(order,production,transfer等)',
      `ref_id` int(11) NOT NULL COMMENT '关联ID',
      `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
      `status` tinyint(1) DEFAULT 1 COMMENT '状态:1锁定中,2已使用,3已释放',
      `notes` text COMMENT '备注',
      `created_by` int(11) DEFAULT 0 COMMENT '创建人',
      `create_time` int(11) NOT NULL,
      `update_time` int(11) NOT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_product` (`product_id`),
      KEY `idx_warehouse` (`warehouse_id`),
      KEY `idx_ref` (`ref_type`,`ref_id`),
      KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存锁定表'";
    $pdo->exec($sql2);
    echo "   ✅ 库存锁定表创建成功\n";
    
    // 3. 库存流水表
    echo "3. 创建库存流水表 (oa_inventory_transaction)...\n";
    $sql3 = "CREATE TABLE IF NOT EXISTS `oa_inventory_transaction` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `transaction_no` varchar(50) NOT NULL COMMENT '流水号',
      `product_id` int(11) NOT NULL COMMENT '产品ID',
      `warehouse_id` int(11) NOT NULL COMMENT '仓库ID',
      `transaction_type` varchar(20) NOT NULL COMMENT '交易类型:in,out,transfer_out,transfer_in,adjust,lock,unlock',
      `quantity` decimal(10,2) NOT NULL COMMENT '变动数量(正数入库,负数出库)',
      `before_quantity` decimal(10,2) NOT NULL COMMENT '变动前数量',
      `after_quantity` decimal(10,2) NOT NULL COMMENT '变动后数量',
      `ref_type` varchar(50) DEFAULT '' COMMENT '关联类型',
      `ref_id` int(11) DEFAULT 0 COMMENT '关联ID',
      `ref_no` varchar(100) DEFAULT '' COMMENT '关联单号',
      `notes` text COMMENT '备注',
      `created_by` int(11) DEFAULT 0 COMMENT '操作人',
      `create_time` int(11) NOT NULL,
      `update_time` int(11) NOT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_transaction_no` (`transaction_no`),
      KEY `idx_product` (`product_id`),
      KEY `idx_warehouse` (`warehouse_id`),
      KEY `idx_type` (`transaction_type`),
      KEY `idx_ref` (`ref_type`,`ref_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表'";
    $pdo->exec($sql3);
    echo "   ✅ 库存流水表创建成功\n";
    
    // 4. 仓库间调拨单表
    echo "4. 创建仓库间调拨单表 (oa_inventory_transfer)...\n";
    $sql4 = "CREATE TABLE IF NOT EXISTS `oa_inventory_transfer` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `transfer_no` varchar(50) NOT NULL COMMENT '调拨单号',
      `from_warehouse_id` int(11) NOT NULL COMMENT '源仓库ID',
      `to_warehouse_id` int(11) NOT NULL COMMENT '目标仓库ID',
      `status` tinyint(1) DEFAULT 0 COMMENT '状态:0待审核,1已审核,2已完成,3已取消',
      `total_amount` decimal(10,2) DEFAULT 0 COMMENT '调拨总金额',
      `notes` text COMMENT '备注',
      `created_by` int(11) DEFAULT 0 COMMENT '创建人',
      `approved_by` int(11) DEFAULT 0 COMMENT '审核人',
      `approved_time` int(11) DEFAULT 0 COMMENT '审核时间',
      `create_time` int(11) NOT NULL,
      `update_time` int(11) NOT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_transfer_no` (`transfer_no`),
      KEY `idx_from_warehouse` (`from_warehouse_id`),
      KEY `idx_to_warehouse` (`to_warehouse_id`),
      KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库间调拨单表'";
    $pdo->exec($sql4);
    echo "   ✅ 仓库间调拨单表创建成功\n";
    
    // 5. 调拨单明细表
    echo "5. 创建调拨单明细表 (oa_inventory_transfer_detail)...\n";
    $sql5 = "CREATE TABLE IF NOT EXISTS `oa_inventory_transfer_detail` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `transfer_id` int(11) NOT NULL COMMENT '调拨单ID',
      `product_id` int(11) NOT NULL COMMENT '产品ID',
      `quantity` decimal(10,2) NOT NULL COMMENT '调拨数量',
      `unit` varchar(20) DEFAULT '' COMMENT '单位',
      `cost_price` decimal(10,2) DEFAULT 0 COMMENT '成本价',
      `amount` decimal(10,2) DEFAULT 0 COMMENT '金额',
      `notes` text COMMENT '备注',
      PRIMARY KEY (`id`),
      KEY `idx_transfer` (`transfer_id`),
      KEY `idx_product` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调拨单明细表'";
    $pdo->exec($sql5);
    echo "   ✅ 调拨单明细表创建成功\n";
    
    echo "\n=== 所有表创建完成 ===\n";
    echo "请刷新页面重新访问系统。\n";
    
} catch (PDOException $e) {
    echo "数据库操作失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置信息\n";
}
