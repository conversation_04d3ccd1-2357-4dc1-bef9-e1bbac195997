# 销售订单库存状态更新功能测试

## 功能概述

在销售订单的 `simpleCheck` 审核过程中，系统会自动更新 `customer_order_detail` 表中每个物料的库存状态和缺口数量。

## 测试场景

### 场景1：库存充足
- **条件**：产品可用库存 >= 订单需求数量
- **预期结果**：
  - `inventory_status = 1`（库存）
  - `gap = 0`（无缺口）

### 场景2：库存不足
- **条件**：产品可用库存 < 订单需求数量 且 > 0
- **预期结果**：
  - `inventory_status = 2`（缺货）
  - `gap = 需求数量 - 可用库存`

### 场景3：无库存
- **条件**：产品可用库存 = 0
- **预期结果**：
  - `inventory_status = 2`（缺货）
  - `gap = 需求数量`

## 测试步骤

### 1. 准备测试数据
```sql
-- 创建测试订单
INSERT INTO oa_customer_order (order_no, customer_id, admin_id, status, check_status) 
VALUES ('TEST001', 1, 1, 0, 0);

-- 创建测试订单明细
INSERT INTO oa_customer_order_detail (order_id, product_id, quantity, inventory_status, gap) 
VALUES 
(LAST_INSERT_ID(), 1, 10.00, 0, 0),  -- 产品1需求10个
(LAST_INSERT_ID(), 2, 5.00, 0, 0),   -- 产品2需求5个
(LAST_INSERT_ID(), 3, 20.00, 0, 0);  -- 产品3需求20个

-- 设置测试库存
-- 产品1：库存15个（充足）
-- 产品2：库存3个（不足）
-- 产品3：库存0个（无库存）
```

### 2. 执行审核
调用 `/customer/order/simpleCheck` 接口，传入订单ID。

### 3. 验证结果
```sql
-- 检查订单明细的库存状态更新
SELECT 
    product_id,
    quantity,
    inventory_status,
    gap,
    CASE inventory_status 
        WHEN 0 THEN '未处理'
        WHEN 1 THEN '库存'
        WHEN 2 THEN '缺货'
        WHEN 3 THEN '补货完成'
    END as status_text
FROM oa_customer_order_detail 
WHERE order_id = [测试订单ID];
```

### 4. 预期结果
| 产品ID | 需求数量 | 可用库存 | inventory_status | gap | 状态说明 |
|--------|----------|----------|------------------|-----|----------|
| 1      | 10.00    | 15.00    | 1                | 0   | 库存充足 |
| 2      | 5.00     | 3.00     | 2                | 2   | 库存不足 |
| 3      | 20.00    | 0.00     | 2                | 20  | 无库存   |

## 关键代码说明

### 库存状态判断逻辑
```php
// 获取产品可用库存
$inventoryStatus = $inventoryService->getProductAvailableQuantity($productId, $warehouseId);
$availableQty = $inventoryStatus['available_quantity'];

// 判断库存状态和缺口数量
if ($availableQty >= $requiredQty) {
    // 库存充足
    $inventoryStatusCode = 1; // 库存
    $gap = 0;
} else {
    // 库存不足或无库存
    $inventoryStatusCode = 2; // 缺货
    $gap = $requiredQty - $availableQty;
    if ($gap < 0) $gap = 0;
}
```

### 数据库更新
```php
// 更新订单明细的库存状态
Db::name('customer_order_detail')->where('id', $detail['id'])->update([
    'inventory_status' => $inventoryStatusCode,
    'gap' => $gap,
    'update_time' => time()
]);
```

## 技术实现

### 公共库存查询服务
创建了 `app\common\service\InventoryQueryService` 公共服务类，基于 `inventory_realtime` 表提供统一的库存查询接口：

```php
// 获取产品可用库存
$inventoryStatus = \app\common\service\InventoryQueryService::getProductAvailableQuantity($productId, $warehouseId);
$availableQty = $inventoryStatus['available_quantity'];
```

### 库存状态更新逻辑
```php
// 使用公共库存查询服务获取产品可用库存
$inventoryStatus = \app\common\service\InventoryQueryService::getProductAvailableQuantity($productId, $warehouseId);
$availableQty = $inventoryStatus['available_quantity'];

// 判断库存状态和缺口数量
if ($availableQty >= $requiredQty) {
    $inventoryStatusCode = 1; // 库存充足
    $gap = 0;
} else {
    $inventoryStatusCode = 2; // 缺货
    $gap = $requiredQty - $availableQty;
}
```

## 注意事项

1. **库存计算**：使用新的 `InventoryQueryService::getProductAvailableQuantity()` 方法获取实际可用库存
2. **数据表**：基于 `inventory_realtime` 表进行库存查询，不再使用已删除的 `oa_inventory` 表
3. **事务处理**：整个审核过程在数据库事务中执行，确保数据一致性
4. **错误处理**：如果库存检查失败，整个审核会回滚
5. **仓库支持**：支持指定仓库的库存检查，默认仓库ID为1
6. **公共复用**：库存查询服务可被其他模块复用

## 后续优化建议

1. **BOM展开**：对于有BOM的产品，需要展开子物料进行库存检查
2. **批次管理**：支持按批次进行库存状态管理
3. **预警机制**：当库存不足时发送预警通知
4. **补货建议**：自动生成补货建议和采购需求
