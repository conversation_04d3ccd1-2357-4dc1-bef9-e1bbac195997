<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存盘点明细模型
 */
class InventoryCheckDetail extends Model
{
    // 设置表名
    protected $name = 'inventory_check_detail';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'check_id'          => 'int',
        'product_id'        => 'int',
        'system_quantity'   => 'decimal',
        'actual_quantity'   => 'decimal',
        'difference'        => 'decimal',
        'unit'              => 'string',
        'cost_price'        => 'decimal',
        'notes'             => 'string',
        'create_time'       => 'int',
        'update_time'       => 'int',
    ];
    
    /**
     * 关联盘点单
     */
    public function check()
    {
        return $this->belongsTo(InventoryCheck::class, 'check_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\warehouse\model\Product', 'product_id', 'id');
    }
    
    /**
     * 获取差异状态
     */
    public function getDifferenceStatusAttr($value, $data)
    {
        $difference = isset($data['difference']) ? $data['difference'] : $this->difference;
        
        if ($difference > 0) {
            return 'surplus'; // 盘盈
        } elseif ($difference < 0) {
            return 'deficit'; // 盘亏
        } else {
            return 'normal'; // 正常
        }
    }
    
    /**
     * 获取差异状态文本
     */
    public function getDifferenceStatusTextAttr($value, $data)
    {
        $status = $this->getDifferenceStatusAttr($value, $data);
        
        $statusArray = [
            'surplus' => '盘盈',
            'deficit' => '盘亏',
            'normal' => '正常'
        ];
        
        return isset($statusArray[$status]) ? $statusArray[$status] : '未知';
    }
    
    /**
     * 获取差异金额
     */
    public function getDifferenceAmountAttr($value, $data)
    {
        $difference = isset($data['difference']) ? $data['difference'] : $this->difference;
        $costPrice = isset($data['cost_price']) ? $data['cost_price'] : $this->cost_price;
        
        return round($difference * $costPrice, 2);
    }
    
    /**
     * 搜索器：产品ID
     */
    public function searchProductIdAttr($query, $value)
    {
        if (!empty($value)) {
            $query->where('product_id', $value);
        }
    }
    
    /**
     * 搜索器：差异状态
     */
    public function searchDifferenceStatusAttr($query, $value)
    {
        if (!empty($value)) {
            switch ($value) {
                case 'surplus':
                    $query->where('difference', '>', 0);
                    break;
                case 'deficit':
                    $query->where('difference', '<', 0);
                    break;
                case 'normal':
                    $query->where('difference', '=', 0);
                    break;
            }
        }
    }
}
