<?php
// 调试出库状态脚本
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化数据库配置
Config::load('config/database.php', 'database');

echo "=== 调试出库状态 ===\n\n";

// 查询所有领料单
$requests = Db::name('production_material_request')
    ->field('id, request_no, outbound_id, outbound_no, status')
    ->order('id desc')
    ->limit(10)
    ->select()
    ->toArray();

echo "最近10个领料单:\n";
foreach ($requests as $request) {
    echo "ID: {$request['id']}, 单号: {$request['request_no']}, 出库单ID: {$request['outbound_id']}, 出库单号: {$request['outbound_no']}, 状态: {$request['status']}\n";
    
    // 查询关联的出库单（通过outbound_id）
    if ($request['outbound_id'] > 0) {
        $outbound = Db::name('outbound')
            ->where('id', $request['outbound_id'])
            ->field('id, outbound_no, status, ref_type, ref_no')
            ->find();
        if ($outbound) {
            echo "  -> 直接关联出库单: ID={$outbound['id']}, 单号={$outbound['outbound_no']}, 状态={$outbound['status']}\n";
        } else {
            echo "  -> 直接关联出库单不存在\n";
        }
    }
    
    // 查询关联的出库单（通过ref_type和ref_no）
    $outbounds = Db::name('outbound')
        ->where('ref_type', 'production_material_request')
        ->where('ref_no', $request['request_no'])
        ->field('id, outbound_no, status, ref_type, ref_no')
        ->select()
        ->toArray();
    
    if (!empty($outbounds)) {
        echo "  -> 通过ref查找到的出库单:\n";
        foreach ($outbounds as $outbound) {
            echo "     ID={$outbound['id']}, 单号={$outbound['outbound_no']}, 状态={$outbound['status']}, ref_type={$outbound['ref_type']}, ref_no={$outbound['ref_no']}\n";
        }
    } else {
        echo "  -> 通过ref未找到关联出库单\n";
    }
    
    echo "\n";
}

echo "\n=== 查询所有生产领料相关的出库单 ===\n";
$outbounds = Db::name('outbound')
    ->where('ref_type', 'production_material_request')
    ->field('id, outbound_no, status, ref_type, ref_no, ref_id')
    ->order('id desc')
    ->limit(10)
    ->select()
    ->toArray();

foreach ($outbounds as $outbound) {
    echo "出库单ID: {$outbound['id']}, 单号: {$outbound['outbound_no']}, 状态: {$outbound['status']}, ref_type: {$outbound['ref_type']}, ref_no: {$outbound['ref_no']}, ref_id: {$outbound['ref_id']}\n";
}

echo "\n调试完成\n";
?>
