# 物料档案管理系统

## 功能概述

物料档案管理系统是ERP系统中的核心模块，用于管理企业的所有物料信息，包括原材料、半成品、成品等。

## 主要功能

### 1. 物料档案管理
- **物料列表**：展示所有物料信息，支持分类筛选和关键字搜索
- **新增物料**：添加新的物料档案，包含完整的物料信息
- **编辑物料**：修改现有物料的详细信息
- **删除物料**：删除不需要的物料档案
- **复制物料**：基于现有物料快速创建相似物料
- **批量操作**：支持批量删除、批量修改等操作

### 2. 物料分类管理
- **分类树形结构**：支持多级分类管理
- **分类增删改**：完整的分类管理功能
- **分类状态控制**：启用/禁用分类

### 3. 物料信息字段

#### 基础资料
- 物料编号（唯一标识）
- 物料名称
- 物料分类
- 物料来源（自制/外购/委外）
- 型号、类别、颜色
- 物料等级
- 备注信息
- 物料条形码
- 附件上传

#### 价格信息
- 参考成本价
- 销售单价（含税）
- 最低销售单价
- 最高销售单价
- 价格区间校验设置

#### 单位信息
- 基本单位
- 默认仓库
- 最小起订量
- 最小包装量
- 物料有效期
- 存货计价方法

#### 质检信息
- 质检管理开关
- 免检设置
  - 采购入库检
  - 外协入库检
  - 生产入库检
  - 销售出库检

## 数据库表结构

### 物料分类表 (oa_material_category)
```sql
- id: 分类ID
- pid: 父级分类ID
- name: 分类名称
- sort: 排序
- status: 状态
- create_time: 创建时间
- update_time: 更新时间
```

### 物料档案表 (oa_material_archive)
```sql
- id: 物料ID
- material_code: 物料编号
- material_name: 物料名称
- category_id: 物料分类ID
- material_source: 物料来源
- model: 型号
- category: 类别
- color: 颜色
- material_level: 物料等级
- remark: 备注
- reference_cost: 参考成本价
- sales_price: 销售单价
- min_sales_price: 最低销售单价
- max_sales_price: 最高销售单价
- base_unit: 基本单位
- default_warehouse: 默认仓库
- min_order_qty: 最小起订量
- min_package_qty: 最小包装量
- material_validity: 物料有效期
- cost_calculation: 存货计价方法
- quality_management: 质检管理
- quality_settings: 质检设置
- admin_id: 创建人ID
- create_time: 创建时间
- update_time: 更新时间
```

## 文件结构

```
app/material/
├── controller/
│   ├── Archive.php          # 物料档案控制器
│   └── Category.php         # 物料分类控制器
├── model/
│   ├── Archive.php          # 物料档案模型
│   └── Category.php         # 物料分类模型
├── validate/
│   └── Archive.php          # 物料档案验证器
├── view/
│   ├── archive/
│   │   ├── index.html       # 物料档案列表页
│   │   ├── add.html         # 物料档案添加/编辑页
│   │   └── view.html        # 物料档案详情页
│   └── category/
│       ├── index.html       # 物料分类列表页
│       └── add.html         # 物料分类添加/编辑页
├── sql/
│   └── material_archive.sql # 数据库表结构
├── route/
│   └── app.php              # 路由配置
└── README.md                # 说明文档
```

## 使用说明

### 1. 安装部署
1. 执行SQL文件创建数据库表
2. 确保路由配置正确
3. 访问物料档案管理页面

### 2. 基本操作
1. **查看物料列表**：访问 `/material/archive/index`
2. **添加物料**：点击"新增物料"按钮
3. **编辑物料**：点击列表中的"编辑"按钮
4. **查看详情**：点击列表中的"详情"按钮
5. **删除物料**：点击列表中的"删除"按钮

### 3. 分类管理
1. **查看分类**：左侧分类树展示所有分类
2. **添加分类**：点击"新增分类"按钮
3. **编辑分类**：在分类管理页面进行操作

## 注意事项

1. 物料编号必须唯一，不能重复
2. 删除分类前需确保该分类下没有物料
3. 质检设置为JSON格式存储，支持多种质检类型
4. 价格信息支持区间校验功能
5. 物料有效期以时间戳格式存储

## 扩展功能

系统预留了以下扩展接口：
- 物料导入/导出功能
- 批量打印条码标签
- 物料图片上传
- 物料单价管理
- 外协单价管理

## 技术栈

- **后端框架**：ThinkPHP 8.0
- **前端框架**：Layui
- **数据库**：MySQL 5.7+
- **模板引擎**：Think Template