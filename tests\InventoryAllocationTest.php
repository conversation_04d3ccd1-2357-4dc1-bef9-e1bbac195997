<?php
/**
 * 库存分配与锁定功能测试
 * 
 * 测试用例说明：
 * 1. 测试有库存时的直接分配
 * 2. 测试无库存时的需求创建
 * 3. 测试部分库存的分配
 * 4. 测试入库自动分配
 * 5. 测试反审释放锁定
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\Db;
use app\warehouse\service\InventoryAllocationService;
use app\warehouse\service\AllocationPriority;
use app\warehouse\model\InventoryAllocationRequest;

class InventoryAllocationTest
{
    private $allocationService;
    private $testProductId = 9999;
    private $testWarehouseId = 1;
    private $testOrderId = 9999;
    
    public function __construct()
    {
        $this->allocationService = new InventoryAllocationService();
        $this->setupTestData();
    }
    
    /**
     * 设置测试数据
     */
    private function setupTestData()
    {
        echo "设置测试数据...\n";
        
        // 清理测试数据
        $this->cleanupTestData();
        
        // 创建测试库存
        Db::name('inventory_realtime')->insert([
            'product_id' => $this->testProductId,
            'warehouse_id' => $this->testWarehouseId,
            'quantity' => 100,
            'available_quantity' => 100,
            'locked_quantity' => 0,
            'create_time' => time(),
            'update_time' => time()
        ]);
        
        echo "测试数据设置完成\n";
    }
    
    /**
     * 清理测试数据
     */
    private function cleanupTestData()
    {
        Db::name('inventory_realtime')->where('product_id', $this->testProductId)->delete();
        Db::name('inventory_lock')->where('product_id', $this->testProductId)->delete();
        Db::name('inventory_allocation_request')->where('product_id', $this->testProductId)->delete();
        Db::name('inventory_allocation_history')->where('product_id', $this->testProductId)->delete();
        Db::name('reverse_audit_log')->where('ref_id', $this->testOrderId)->delete();
    }
    
    /**
     * 测试1：有库存时的直接分配
     */
    public function testDirectAllocation()
    {
        echo "\n=== 测试1：有库存时的直接分配 ===\n";
        
        $request = [
            'product_id' => $this->testProductId,
            'warehouse_id' => $this->testWarehouseId,
            'quantity' => 50,
            'ref_type' => 'customer_order',
            'ref_id' => $this->testOrderId,
            'ref_no' => 'TEST001',
            'notes' => '测试订单',
            'created_by' => 1
        ];
        
        try {
            $result = $this->allocationService->allocateAndLock($request);
            
            if ($result['code'] == 0 && $result['status'] == 'success') {
                echo "✓ 直接分配成功\n";
                echo "  - 分配数量: {$result['allocated_quantity']}\n";
                echo "  - 锁定ID: {$result['lock_id']}\n";
                
                // 验证库存变化
                $inventory = Db::name('inventory_realtime')
                    ->where('product_id', $this->testProductId)
                    ->find();
                    
                if ($inventory['available_quantity'] == 50) {
                    echo "✓ 库存扣减正确\n";
                } else {
                    echo "✗ 库存扣减错误，期望50，实际{$inventory['available_quantity']}\n";
                }
                
            } else {
                echo "✗ 直接分配失败: {$result['msg']}\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 测试异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试2：无库存时的需求创建
     */
    public function testPendingAllocation()
    {
        echo "\n=== 测试2：无库存时的需求创建 ===\n";
        
        // 先消耗掉所有库存
        Db::name('inventory_realtime')
            ->where('product_id', $this->testProductId)
            ->update(['available_quantity' => 0]);
        
        $request = [
            'product_id' => $this->testProductId,
            'warehouse_id' => $this->testWarehouseId,
            'quantity' => 30,
            'ref_type' => 'customer_order',
            'ref_id' => $this->testOrderId + 1,
            'ref_no' => 'TEST002',
            'notes' => '测试无库存订单',
            'created_by' => 1
        ];
        
        try {
            $result = $this->allocationService->allocateAndLock($request);
            
            if ($result['code'] == 0 && $result['status'] == 'pending') {
                echo "✓ 无库存需求创建成功\n";
                echo "  - 待分配数量: {$result['pending_quantity']}\n";
                echo "  - 需求ID: {$result['request_id']}\n";
                
                // 验证需求记录
                $allocationRequest = Db::name('inventory_allocation_request')
                    ->where('id', $result['request_id'])
                    ->find();
                    
                if ($allocationRequest && $allocationRequest['status'] == 1) {
                    echo "✓ 分配需求记录创建正确\n";
                } else {
                    echo "✗ 分配需求记录创建错误\n";
                }
                
            } else {
                echo "✗ 无库存需求创建失败: {$result['msg']}\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 测试异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试3：入库自动分配
     */
    public function testAutoAllocationOnInbound()
    {
        echo "\n=== 测试3：入库自动分配 ===\n";
        
        try {
            // 模拟入库80个
            $result = $this->allocationService->autoAllocateOnInbound(
                $this->testProductId,
                $this->testWarehouseId,
                80
            );
            
            if ($result['code'] == 0) {
                echo "✓ 入库自动分配成功\n";
                echo "  - 总分配数量: {$result['total_allocated']}\n";
                echo "  - 剩余库存: {$result['remaining_stock']}\n";
                echo "  - 分配详情数量: " . count($result['allocation_details']) . "\n";
                
                // 验证分配结果
                foreach ($result['allocation_details'] as $detail) {
                    if ($detail['code'] == 0) {
                        echo "  ✓ 订单{$detail['ref_no']}分配{$detail['allocated_quantity']}个\n";
                    } else {
                        echo "  ✗ 订单{$detail['ref_no']}分配失败\n";
                    }
                }
                
            } else {
                echo "✗ 入库自动分配失败: {$result['msg']}\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 测试异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试4：优先级计算
     */
    public function testPriorityCalculation()
    {
        echo "\n=== 测试4：优先级计算 ===\n";
        
        // 测试不同业务类型的默认优先级
        $customerOrderPriority = AllocationPriority::getDefaultPriority('customer_order');
        $productionOrderPriority = AllocationPriority::getDefaultPriority('production_order');
        
        echo "客户订单默认优先级: $customerOrderPriority\n";
        echo "生产订单默认优先级: $productionOrderPriority\n";
        
        if ($productionOrderPriority > $customerOrderPriority) {
            echo "✓ 生产订单优先级高于客户订单\n";
        } else {
            echo "✗ 优先级设置错误\n";
        }
        
        // 测试动态优先级计算
        $dynamicPriority = AllocationPriority::calculatePriority('customer_order', $this->testOrderId, [
            'urgency' => 'urgent',
            'is_vip' => true
        ]);
        
        echo "动态计算优先级: $dynamicPriority\n";
        
        if ($dynamicPriority > $customerOrderPriority) {
            echo "✓ 动态优先级计算正确\n";
        } else {
            echo "✗ 动态优先级计算错误\n";
        }
    }
    
    /**
     * 测试5：反审释放锁定
     */
    public function testReverseAuditRelease()
    {
        echo "\n=== 测试5：反审释放锁定 ===\n";
        
        try {
            $result = $this->allocationService->releaseOnReverseAudit([
                'ref_type' => 'customer_order',
                'ref_id' => $this->testOrderId,
                'operator_id' => 1,
                'reason' => '测试反审'
            ]);
            
            if ($result['code'] == 0) {
                echo "✓ 反审释放成功\n";
                echo "  - 释放总数量: {$result['total_released']}\n";
                echo "  - 释放详情数量: " . count($result['details']) . "\n";
                
                // 验证锁定记录状态
                $lockCount = Db::name('inventory_lock')
                    ->where('ref_type', 'customer_order')
                    ->where('ref_id', $this->testOrderId)
                    ->where('status', 1) // 锁定中
                    ->count();
                    
                if ($lockCount == 0) {
                    echo "✓ 锁定记录已正确释放\n";
                } else {
                    echo "✗ 仍有{$lockCount}个锁定记录未释放\n";
                }
                
            } else {
                echo "✗ 反审释放失败: {$result['msg']}\n";
            }
            
        } catch (Exception $e) {
            echo "✗ 测试异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始库存分配与锁定功能测试\n";
        echo "=====================================\n";
        
        $this->testDirectAllocation();
        $this->testPendingAllocation();
        $this->testAutoAllocationOnInbound();
        $this->testPriorityCalculation();
        $this->testReverseAuditRelease();
        
        echo "\n=====================================\n";
        echo "测试完成，清理测试数据...\n";
        $this->cleanupTestData();
        echo "测试数据清理完成\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new InventoryAllocationTest();
    $test->runAllTests();
}
