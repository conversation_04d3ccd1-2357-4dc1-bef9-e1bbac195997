-- =====================================================
-- 生产领料模块数据库表结构
-- 创建时间: 2025-08-10
-- 说明: 重新设计的生产领料模块，支持手动审核和超领管理
-- =====================================================

-- 1. 生产领料单主表
DROP TABLE IF EXISTS `oa_production_material_request`;
CREATE TABLE `oa_production_material_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_no` varchar(50) NOT NULL COMMENT '领料单号',
  `production_order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `production_order_no` varchar(50) NOT NULL COMMENT '生产订单号',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `production_quantity` decimal(10,2) NOT NULL COMMENT '生产数量',
  `request_type` tinyint(1) DEFAULT 1 COMMENT '领料类型:1=正常领料,2=补料,3=超领',
  `total_items` int(11) DEFAULT 0 COMMENT '物料种类数',
  `total_quantity` decimal(12,4) DEFAULT 0.0000 COMMENT '领料总数量',
  `has_excess` tinyint(1) DEFAULT 0 COMMENT '是否有超领:0=否,1=是',
  `excess_items` int(11) DEFAULT 0 COMMENT '超领物料种类数',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=待审核,1=已审核,2=已出库,3=已完成,4=已取消',
  `warehouse_id` int(11) DEFAULT 0 COMMENT '出库仓库ID',
  `warehouse_name` varchar(50) DEFAULT '' COMMENT '出库仓库名称',
  `outbound_id` int(11) DEFAULT 0 COMMENT '关联出库单ID',
  `outbound_no` varchar(50) DEFAULT '' COMMENT '出库单号',
  `notes` text COMMENT '备注说明',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_name` varchar(50) NOT NULL COMMENT '创建人姓名',
  `approved_by` int(11) DEFAULT 0 COMMENT '审核人ID',
  `approved_name` varchar(50) DEFAULT '' COMMENT '审核人姓名',
  `approve_time` int(11) DEFAULT 0 COMMENT '审核时间',
  `approve_notes` text COMMENT '审核备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `delete_time` int(11) DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_no` (`request_no`),
  KEY `idx_production_order` (`production_order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_approved_by` (`approved_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产领料单主表';

-- 2. 生产领料单明细表
DROP TABLE IF EXISTS `oa_production_material_request_detail`;
CREATE TABLE `oa_production_material_request_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` int(11) NOT NULL COMMENT '领料单ID',
  `request_no` varchar(50) NOT NULL COMMENT '领料单号',
  `material_id` int(11) NOT NULL COMMENT '物料ID',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `material_specs` varchar(200) DEFAULT '' COMMENT '物料规格',
  `unit` varchar(20) DEFAULT '' COMMENT '单位',
  `bom_quantity` decimal(10,4) DEFAULT 0.0000 COMMENT 'BOM单位用量',
  `standard_quantity` decimal(12,4) NOT NULL COMMENT '标准需求数量',
  `request_quantity` decimal(12,4) NOT NULL COMMENT '申请领料数量',
  `excess_quantity` decimal(12,4) DEFAULT 0.0000 COMMENT '超领数量',
  `is_excess` tinyint(1) DEFAULT 0 COMMENT '是否超领:0=否,1=是',
  `available_stock` decimal(12,4) DEFAULT 0.0000 COMMENT '可用库存',
  `actual_quantity` decimal(12,4) DEFAULT 0.0000 COMMENT '实际出库数量',
  `warehouse_id` int(11) DEFAULT 0 COMMENT '出库仓库ID',
  `location_id` int(11) DEFAULT 0 COMMENT '库位ID',
  `batch_no` varchar(50) DEFAULT '' COMMENT '批次号',
  `notes` varchar(500) DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_is_excess` (`is_excess`),
  KEY `idx_request_no` (`request_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产领料单明细表';

-- =====================================================
-- 初始化数据和配置
-- =====================================================

-- 1. 插入系统配置
INSERT IGNORE INTO `oa_system_config` (`name`, `value`, `description`, `group`, `create_time`) VALUES
('material_request_no_prefix', 'MR', '领料单号前缀', 'production', UNIX_TIMESTAMP()),
('material_request_auto_approve', '0', '是否自动审核领料单:0=否,1=是', 'production', UNIX_TIMESTAMP()),
('material_request_allow_excess', '1', '是否允许超领:0=否,1=是', 'production', UNIX_TIMESTAMP()),
('material_request_excess_limit', '20', '超领限制百分比(%)', 'production', UNIX_TIMESTAMP());

-- =====================================================
-- 视图创建（便于查询统计）
-- =====================================================

-- 1. 领料单汇总视图
CREATE OR REPLACE VIEW `v_production_material_request_summary` AS
SELECT 
    r.id,
    r.request_no,
    r.production_order_no,
    r.product_name,
    r.production_quantity,
    r.request_type,
    r.total_items,
    r.total_quantity,
    r.has_excess,
    r.excess_items,
    r.status,
    r.warehouse_name,
    r.created_name,
    r.approved_name,
    r.create_time,
    r.approve_time,
    CASE r.status
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '已审核'
        WHEN 2 THEN '已出库'
        WHEN 3 THEN '已完成'
        WHEN 4 THEN '已取消'
        ELSE '未知'
    END as status_text,
    CASE r.request_type
        WHEN 1 THEN '正常领料'
        WHEN 2 THEN '补料'
        WHEN 3 THEN '超领'
        ELSE '未知'
    END as request_type_text
FROM `oa_production_material_request` r
WHERE r.delete_time = 0;

-- 2. 物料需求明细视图
CREATE OR REPLACE VIEW `v_production_material_request_detail_summary` AS
SELECT 
    d.id,
    d.request_id,
    d.request_no,
    d.material_code,
    d.material_name,
    d.material_specs,
    d.unit,
    d.bom_quantity,
    d.standard_quantity,
    d.request_quantity,
    d.excess_quantity,
    d.is_excess,
    d.available_stock,
    d.actual_quantity,
    r.status as request_status,
    r.production_order_no,
    r.product_name,
    CASE d.is_excess
        WHEN 0 THEN '正常'
        WHEN 1 THEN '超领'
        ELSE '未知'
    END as excess_status_text
FROM `oa_production_material_request_detail` d
LEFT JOIN `oa_production_material_request` r ON d.request_id = r.id
WHERE r.delete_time = 0;

-- =====================================================
-- 存储过程（可选，用于复杂的业务逻辑）
-- =====================================================

DELIMITER $$

-- 生成领料单号的存储过程
CREATE PROCEDURE `sp_generate_material_request_no`(OUT request_no VARCHAR(50))
BEGIN
    DECLARE prefix VARCHAR(10) DEFAULT 'MR';
    DECLARE date_part VARCHAR(8);
    DECLARE seq_part VARCHAR(4);
    DECLARE max_seq INT DEFAULT 0;
    
    -- 获取配置的前缀
    SELECT `value` INTO prefix FROM `oa_system_config` WHERE `name` = 'material_request_no_prefix' LIMIT 1;
    
    -- 生成日期部分 YYYYMMDD
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号
    SELECT IFNULL(MAX(CAST(RIGHT(request_no, 4) AS UNSIGNED)), 0) INTO max_seq
    FROM `oa_production_material_request`
    WHERE request_no LIKE CONCAT(prefix, date_part, '%')
    AND delete_time = 0;
    
    -- 生成新序号
    SET seq_part = LPAD(max_seq + 1, 4, '0');
    
    -- 组合完整单号
    SET request_no = CONCAT(prefix, date_part, seq_part);
    
END$$

DELIMITER ;

-- =====================================================
-- 权限和安全设置
-- =====================================================

-- 注意：以下权限设置需要根据实际情况调整

-- 1. 为相关角色添加表访问权限
-- GRANT SELECT, INSERT, UPDATE ON `oa_production_material_request` TO 'production_user'@'%';
-- GRANT SELECT, INSERT, UPDATE ON `oa_production_material_request_detail` TO 'production_user'@'%';
-- GRANT SELECT ON `v_production_material_request_summary` TO 'production_manager'@'%';

-- =====================================================
-- 完成标记
-- =====================================================
-- 生产领料模块数据库表结构创建完成
-- 版本: 1.0
-- 最后更新: 2025-08-10
