<?php
declare (strict_types = 1);

namespace app\Produce\model;

use think\Model;
use think\facade\Db;

class ProcessTemplateModel extends Model
{
    protected $table = 'oa_process_template';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'template_no'     => 'string',
        'name'            => 'string',
        'remark'          => 'text',
        'steps'           => 'text',
        'create_time'     => 'int',
        'update_time'     => 'int',
        'delete_time'     => 'int'
    ];

    /**
     * 获取工艺路线列表
     * @param array $param 查询参数
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public function getList($param = [], $page = 1, $limit = 15)
    {
        $where = [];
        
        // 搜索条件
        if (!empty($param['keywords'])) {
            $where[] = ['template_no|name', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 查询数据
        $list = $this->where($where)
            ->field('*')
            ->order('create_time desc')
            ->page((int)$page, (int)$limit)
            ->select()
            ->each(function ($item) {
                // 格式化时间
                $item['create_time_format'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                $item['update_time_format'] = date('Y-m-d H:i:s', (int)$item['update_time']);
                
                // 解析工艺步骤
                if (!empty($item['steps'])) {
                    $steps = json_decode($item['steps'], true);
                    $item['step_count'] = is_array($steps) ? count($steps) : 0;
                    
                    // 获取工艺步骤名称列表
                    $stepNames = [];
                    if (is_array($steps)) {
                        foreach ($steps as $step) {
                            if (isset($step['name'])) {
                                $stepNames[] = $step['name'];
                            }
                        }
                    }
                    $item['step_names'] = implode(' → ', array_slice($stepNames, 0, 6)); // 最多显示6个步骤
                    if (count($stepNames) > 6) {
                        $item['step_names'] .= '...';
                    }
                } else {
                    $item['step_count'] = 0;
                    $item['step_names'] = '';
                }
                
                return $item;
            })
            ->toArray();
        
        // 获取总数
        $count = $this->where($where)->count();
        
        return [
            'data' => $list,
            'count' => $count
        ];
    }

    /**
     * 获取工艺路线详情
     * @param int $id 工艺路线ID
     * @return array|null
     */
    public function getDetail($id)
    {
        $info = $this->find($id);
        if (!$info) {
            return null;
        }
        
        $info = $info->toArray();
        
        // 格式化时间
        $info['create_time_format'] = date('Y-m-d H:i:s', (int)$info['create_time']);
        $info['update_time_format'] = date('Y-m-d H:i:s', (int)$info['update_time']);
        
        // 解析工艺步骤
        if (!empty($info['steps'])) {
            $steps = json_decode($info['steps'], true);
            $info['steps_data'] = is_array($steps) ? $steps : [];
        } else {
            $info['steps_data'] = [];
        }
        
        return $info;
    }

    /**
     * 检查工艺路线是否被使用
     * @param int $id 工艺路线ID
     * @return bool
     */
    public function isUsed($id)
    {
        // 检查是否被生产订单使用
        $orderCount = Db::name('produce_order')
            ->where('process_template_id', $id)
            ->count();
        
        return $orderCount > 0;
    }

    /**
     * 获取工艺路线选项列表（用于下拉选择）
     * @return array
     */
    public function getOptions()
    {
        return $this->field('id, template_no, name')
            ->order('create_time desc')
            ->select()
            ->toArray();
    }
}