<?php
namespace app\customer\validate;

use think\Validate;

class Delivery extends Validate
{
    // 验证规则
    protected $rule = [
        'order_id' => 'require|number',
        'items' => 'require|array',
        'items.*.order_detail_id' => 'require|number',
        'items.*.quantity' => 'require|number|gt:0',
        'special_requirements' => 'max:500',
    ];
    
    // 错误提示
    protected $message = [
        'order_id.require' => '订单ID不能为空',
        'order_id.number' => '订单ID必须是数字',
        'items.require' => '发货商品明细不能为空',
        'items.array' => '发货商品明细必须是数组',
        'items.*.order_detail_id.require' => '订单明细ID不能为空',
        'items.*.order_detail_id.number' => '订单明细ID必须是数字',
        'items.*.quantity.require' => '发货数量不能为空',
        'items.*.quantity.number' => '发货数量必须是数字',
        'items.*.quantity.gt' => '发货数量必须大于0',
        'special_requirements.max' => '特殊要求不能超过500个字符',
    ];
    
    // 验证场景
    protected $scene = [
        'add' => ['order_id', 'items', 'items.*.order_detail_id', 'items.*.quantity', 'special_requirements'],
    ];
} 