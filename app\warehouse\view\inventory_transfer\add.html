{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>创建调拨单</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="transferForm" style="padding: 20px;">
                <!-- 基本信息 -->
                <fieldset class="layui-elem-field">
                    <legend>基本信息</legend>
                    <div class="layui-field-box">
                        <div class="layui-row">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">源仓库</label>
                                    <div class="layui-input-block">
                                        <select name="from_warehouse_id" lay-verify="required" lay-filter="from_warehouse_select" id="fromWarehouseSelect">
                                            <option value="">请选择源仓库</option>
                                            <!-- 动态加载仓库列表 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">目标仓库</label>
                                    <div class="layui-input-block">
                                        <select name="to_warehouse_id" lay-verify="required" lay-filter="to_warehouse_select" id="toWarehouseSelect">
                                            <option value="">请选择目标仓库</option>
                                            <!-- 动态加载仓库列表 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea name="notes" placeholder="请输入备注信息" class="layui-textarea" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </fieldset>
                
                <!-- 调拨明细 -->
                <fieldset class="layui-elem-field">
                    <legend>调拨明细</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <button type="button" class="layui-btn layui-btn-sm" id="addDetail">
                                <i class="layui-icon layui-icon-add-1"></i> 添加明细
                            </button>
                        </div>
                        
                        <table class="layui-table" id="detailTable">
                            <thead>
                                <tr>
                                    <th width="200">产品</th>
                                    <th width="120">调拨数量</th>
                                    <th width="80">单位</th>
                                    <th width="120">成本价</th>
                                    <th width="120">金额</th>
                                    <th width="150">备注</th>
                                    <th width="80">操作</th>
                                </tr>
                            </thead>
                            <tbody id="detailTableBody">
                                <!-- 动态添加的明细行 -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" style="text-align: right; font-weight: bold;">总计：</td>
                                    <td id="totalAmount" style="font-weight: bold; color: #ff5722;">¥0.00</td>
                                    <td colspan="2"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </fieldset>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="transfer-submit">创建调拨单</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="parent.layer.closeAll()">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 明细行模板 -->
<script type="text/html" id="detailRowTpl">
<tr data-index="{{d.index}}">
    <td>
        <div class="layui-input-group">
            <input type="hidden" name="details[{{d.index}}][product_id]" class="product-id" data-index="{{d.index}}">
            <input type="text" placeholder="请输入产品名称或编号搜索" class="layui-input product-search" data-index="{{d.index}}" autocomplete="off">
            <div class="layui-input-split layui-input-suffix" style="cursor: pointer;">
                <i class="layui-icon layui-icon-search product-search-btn" data-index="{{d.index}}"></i>
            </div>
        </div>
        <div class="product-dropdown" data-index="{{d.index}}" style="display: none; position: absolute; z-index: 999; background: white; border: 1px solid #e6e6e6; max-height: 200px; overflow-y: auto; width: 100%;"></div>
    </td>
    <td>
        <input type="number" name="details[{{d.index}}][quantity]" lay-verify="required|number" placeholder="数量" class="layui-input detail-quantity" step="0.01" min="0.01" data-index="{{d.index}}">
    </td>
    <td>
        <input type="text" name="details[{{d.index}}][unit]" placeholder="单位" class="layui-input detail-unit" readonly>
    </td>
    <td>
        <input type="number" name="details[{{d.index}}][cost_price]" lay-verify="required|number" placeholder="成本价" class="layui-input detail-cost-price" step="0.01" min="0" data-index="{{d.index}}">
    </td>
    <td>
        <span class="detail-amount" data-index="{{d.index}}">¥0.00</span>
    </td>
    <td>
        <input type="text" name="details[{{d.index}}][notes]" placeholder="备注" class="layui-input">
    </td>
    <td>
        <button type="button" class="layui-btn layui-btn-danger layui-btn-xs remove-detail" data-index="{{d.index}}">删除</button>
    </td>
</tr>
</script>
{/block}

{block name="script"}
<style>
.product-dropdown {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 2px;
}
.product-item:hover {
    background-color: #f5f5f5;
}
.layui-input-group {
    position: relative;
}
.layui-btn-disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
}
.layui-disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
}
.product-search:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
}
</style>
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 单独加载 laytpl 模块
        layui.use('laytpl', function(){
            var laytpl = layui.laytpl;

            var detailIndex = 0; // 明细行索引
        
            // 添加明细行
            function addDetailRow() {
                var getTpl = document.getElementById('detailRowTpl').innerHTML;
                var view = document.getElementById('detailTableBody');
                laytpl(getTpl).render({index: detailIndex}, function(html){
                    $(view).append(html);
                    form.render('select');
                    detailIndex++;
                });
            }

            $('#addDetail').click(addDetailRow);

            // 删除明细行
            $(document).on('click', '.remove-detail', function() {
                var index = $(this).data('index');
                $('tr[data-index="' + index + '"]').remove();
                calculateTotal();
            });

            // 产品搜索功能
            $(document).on('input', '.product-search', function() {
                var $this = $(this);
                var index = $this.data('index');
                var keyword = $this.val();
                var $dropdown = $('.product-dropdown[data-index="' + index + '"]');

                // 检查是否已选择源仓库
                var fromWarehouseId = $('select[name="from_warehouse_id"]').val();
                if (!fromWarehouseId) {
                    layer.msg('请先选择源仓库', {icon: 2});
                    $this.val('');
                    return;
                }

                if (keyword.length >= 2) {
                    searchProducts(keyword, index, $dropdown);
                } else {
                    $dropdown.hide();
                }
            });

            // 点击搜索按钮
            $(document).on('click', '.product-search-btn', function() {
                var index = $(this).data('index');
                var keyword = $('.product-search[data-index="' + index + '"]').val();
                var $dropdown = $('.product-dropdown[data-index="' + index + '"]');

                if (keyword.length >= 1) {
                    searchProducts(keyword, index, $dropdown);
                }
            });

            // 选择产品
            $(document).on('click', '.product-item', function() {
                var $this = $(this);
                var clickable = $this.data('clickable');

                if (!clickable) {
                    layer.msg('该产品库存不足，无法选择', {icon: 2});
                    return;
                }

                var index = $this.data('index');
                var productData = $this.data('product');

                selectProduct(productData, index);
            });

            // 禁用无库存产品的点击
            $(document).on('click', '.product-item-disabled', function() {
                layer.msg('该产品库存不足，无法选择', {icon: 2});
            });

            // 点击其他地方隐藏下拉框
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.product-search, .product-dropdown').length) {
                    $('.product-dropdown').hide();
                }
            });

            // 数量或成本价变化时重新计算金额
            $(document).on('input', '.detail-quantity, .detail-cost-price', function() {
                var index = $(this).data('index');
                calculateRowAmount(index);
                calculateTotal();
            });

            // 数量输入验证
            $(document).on('blur', '.detail-quantity', function() {
                var $this = $(this);
                var index = $this.data('index');
                var inputQuantity = parseFloat($this.val()) || 0;
                var maxStock = parseFloat($this.data('max-stock')) || 0;
                var productId = $('.product-id[data-index="' + index + '"]').val();

                if (productId && inputQuantity > maxStock) {
                    layer.msg('调拨数量不能超过可用库存 ' + maxStock, {icon: 2});
                    $this.val(maxStock);
                    $this.focus();
                    calculateRowAmount(index);
                    calculateTotal();
                }
            });

            // 数量或成本价变化时计算金额
            $(document).on('input', '.detail-quantity, .detail-cost-price', function() {
                var index = $(this).data('index');
                calculateRowAmount(index);
                calculateTotal();
            });

            // 计算单行金额
            function calculateRowAmount(index) {
                var quantity = parseFloat($('input[name="details[' + index + '][quantity]"]').val()) || 0;
                var costPrice = parseFloat($('input[name="details[' + index + '][cost_price]"]').val()) || 0;
                var amount = quantity * costPrice;
                $('.detail-amount[data-index="' + index + '"]').text('¥' + amount.toFixed(2));
            }

            // 计算总金额
            function calculateTotal() {
                var total = 0;
                $('.detail-amount').each(function() {
                    var amount = parseFloat($(this).text().replace('¥', '')) || 0;
                    total += amount;
                });
                $('#totalAmount').text('¥' + total.toFixed(2));
            }

            // 搜索产品
            function searchProducts(keyword, index, $dropdown) {
                var warehouseId = $('select[name="from_warehouse_id"]').val();

                if (!warehouseId) {
                    layer.msg('请先选择源仓库', {icon: 2});
                    return;
                }

                $.get('/warehouse/inventory_transfer/getProducts', {
                    keyword: keyword,
                    warehouse_id: warehouseId
                }, function(res) {
                    if (res.code == 0 && res.data.length > 0) {
                        var html = '';
                        $.each(res.data, function(i, product) {
                            var stockInfo = '';
                            var stockClass = '';
                            var clickable = true;

                            if (product.available_quantity !== undefined) {
                                var stock = parseFloat(product.available_quantity);
                                if (stock <= 0) {
                                    stockInfo = ' (库存: 0 - 无库存)';
                                    stockClass = 'color: #f56c6c;';
                                    clickable = false;
                                } else {
                                    stockInfo = ' (可用库存: ' + stock + ')';
                                    stockClass = 'color: #5FB878;';
                                }
                            } else {
                                stockInfo = ' (库存: 未知)';
                                stockClass = 'color: #FFB800;';
                            }

                            var itemClass = clickable ? 'product-item' : 'product-item-disabled';
                            var cursor = clickable ? 'pointer' : 'not-allowed';
                            var opacity = clickable ? '1' : '0.6';

                            html += '<div class="' + itemClass + '" data-index="' + index + '" data-product=\'' + JSON.stringify(product) + '\' data-clickable="' + clickable + '" style="padding: 8px; cursor: ' + cursor + '; border-bottom: 1px solid #f0f0f0; opacity: ' + opacity + ';">';
                            html += '<div style="font-weight: bold;">' + product.title + '</div>';
                            html += '<div style="color: #999; font-size: 12px;">编号: ' + (product.material_code || '') + ' | 规格: ' + (product.specs || '') + '</div>';
                            html += '<div style="' + stockClass + ' font-size: 12px; font-weight: bold;">' + stockInfo + '</div>';
                            html += '</div>';
                        });

                        $dropdown.html(html).show();
                    } else {
                        $dropdown.html('<div style="padding: 8px; color: #999;">未找到相关产品</div>').show();
                    }
                }).fail(function() {
                    $dropdown.html('<div style="padding: 8px; color: #f56c6c;">搜索失败，请重试</div>').show();
                });
            }

            // 选择产品
            function selectProduct(product, index) {
                $('.product-id[data-index="' + index + '"]').val(product.id);
                $('.product-search[data-index="' + index + '"]').val(product.title);
                $('.detail-unit[data-index="' + index + '"]').val(product.unit || '');
                $('.detail-cost-price[data-index="' + index + '"]').val(product.cost_price || '');
                $('.product-dropdown[data-index="' + index + '"]').hide();

                // 设置数量输入框的最大值和提示
                var $quantityInput = $('.detail-quantity[data-index="' + index + '"]');
                var availableStock = parseFloat(product.available_quantity) || 0;

                if (availableStock > 0) {
                    $quantityInput.attr('max', availableStock);
                    $quantityInput.attr('placeholder', '最大可调拨: ' + availableStock);
                    $quantityInput.data('max-stock', availableStock);
                } else {
                    $quantityInput.attr('max', 0);
                    $quantityInput.attr('placeholder', '无库存');
                    $quantityInput.data('max-stock', 0);
                }

                // 重新计算金额
                calculateRowAmount(index);
                calculateTotal();
            }


            // 加载仓库列表
            function loadWarehouses() {
                $.get('/warehouse/inventory_transfer/getWarehouses', function(res) {
                    if (res.code == 0) {
                        var fromOptions = '<option value="">请选择源仓库</option>';
                        var toOptions = '<option value="">请选择目标仓库</option>';

                        $.each(res.data, function(i, warehouse) {
                            fromOptions += '<option value="' + warehouse.id + '">' + warehouse.name + '</option>';
                            toOptions += '<option value="' + warehouse.id + '">' + warehouse.name + '</option>';
                        });

                        $('#fromWarehouseSelect').html(fromOptions);
                        $('#toWarehouseSelect').html(toOptions);
                        form.render('select');
                    }
                });
            }

            // 源仓库变化时，清空产品选择并启用/禁用产品选择
            form.on('select(from_warehouse_select)', function(data){
                var warehouseId = data.value;

                // 清空所有产品选择
                $('.product-search').val('');
                $('.product-id').val('');
                $('.detail-unit').val('');
                $('.detail-cost-price').val('');
                $('.detail-quantity').val('');
                $('.product-dropdown').hide();
                calculateTotal();

                // 控制产品选择的启用/禁用状态
                if (warehouseId) {
                    // 启用产品选择
                    $('.product-search').prop('disabled', false).attr('placeholder', '请输入产品名称或编号搜索');
                    $('.product-search-btn').removeClass('layui-disabled');
                    $('#addDetail').removeClass('layui-btn-disabled').prop('disabled', false);
                } else {
                    // 禁用产品选择
                    $('.product-search').prop('disabled', true).attr('placeholder', '请先选择源仓库');
                    $('.product-search-btn').addClass('layui-disabled');
                    $('#addDetail').addClass('layui-btn-disabled').prop('disabled', true);

                    // 显示提示信息
                    layer.tips('请先选择源仓库才能添加调拨明细', '#addDetail', {
                        tips: [1, '#FF5722'],
                        time: 2000
                    });
                }
            });

            // 表单提交
            form.on('submit(transfer-submit)', function(data){
                var formData = data.field;

                // 验证基本信息
                if (!formData.from_warehouse_id) {
                    layer.msg('请选择源仓库');
                    return false;
                }
                if (!formData.to_warehouse_id) {
                    layer.msg('请选择目标仓库');
                    return false;
                }
                if (formData.from_warehouse_id == formData.to_warehouse_id) {
                    layer.msg('源仓库和目标仓库不能相同');
                    return false;
                }

                // 验证明细
                var details = [];
                var hasDetail = false;

                $('tr[data-index]').each(function() {
                    var index = $(this).data('index');
                    var productId = $('.product-id[data-index="' + index + '"]').val();
                    var quantity = $('input[name="details[' + index + '][quantity]"]').val();
                    var unit = $('input[name="details[' + index + '][unit]"]').val();
                    var costPrice = $('input[name="details[' + index + '][cost_price]"]').val();
                    var notes = $('input[name="details[' + index + '][notes]"]').val();

                    if (productId && quantity && costPrice) {
                        details.push({
                            product_id: productId,
                            quantity: parseFloat(quantity),
                            unit: unit,
                            cost_price: parseFloat(costPrice),
                            notes: notes
                        });
                        hasDetail = true;
                    }
                });

                if (!hasDetail) {
                    layer.msg('请至少添加一条调拨明细', {icon: 2});
                    return false;
                }

                // 验证库存是否充足
                var stockError = false;
                var errorMsg = '';
                $('tr[data-index]').each(function() {
                    var index = $(this).data('index');
                    var productId = $('.product-id[data-index="' + index + '"]').val();
                    var quantity = parseFloat($('input[name="details[' + index + '][quantity]"]').val()) || 0;
                    var maxStock = parseFloat($('.detail-quantity[data-index="' + index + '"]').data('max-stock')) || 0;
                    var productName = $('.product-search[data-index="' + index + '"]').val();

                    if (productId && quantity > maxStock) {
                        stockError = true;
                        errorMsg = '产品 "' + productName + '" 调拨数量(' + quantity + ')超过可用库存(' + maxStock + ')';
                        return false;
                    }
                });

                if (stockError) {
                    layer.msg(errorMsg, {icon: 2});
                    return false;
                }

                // 组装提交数据
                var submitData = {
                    from_warehouse_id: formData.from_warehouse_id,
                    to_warehouse_id: formData.to_warehouse_id,
                    notes: formData.notes,
                    details: details
                };

                // 提交数据
                var callback = function(res) {
                    if (res.code == 0) {
                        layer.msg('创建成功', {icon: 1});
                        setTimeout(function() {
                            parent.layer.closeAll();
                            if (parent.layui && parent.layui.pageTable) {
                                parent.layui.pageTable.reload();
                            }
                        }, 1000);
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                };

                tool.post('/warehouse/inventory_transfer/add', submitData, callback);
                return false;
            });
        
            // 初始化
            loadWarehouses(); // 加载仓库列表
            addDetailRow(); // 添加一行明细

            // 初始状态禁用产品选择和添加按钮
            $('.product-search').prop('disabled', true).attr('placeholder', '请先选择源仓库');
            $('.product-search-btn').addClass('layui-disabled');
            $('#addDetail').addClass('layui-btn-disabled').prop('disabled', true);
        });
    }
</script>
{/block}
