# 生产物料申请功能优化说明

## 优化内容

### 1. 删除出库仓库选择
**原因**：根据实际的锁库及分配仓库自动确定出库仓库，无需手动选择

**修改**：
- 前端删除出库仓库选择框
- 后端删除仓库验证逻辑
- 改为基于锁定库存的仓库信息

### 2. 增加前端删除功能
**功能**：允许删除库存不足的物料

**实现**：
- 在物料表格中添加删除按钮列
- 实现删除物料的JavaScript函数
- 支持删除后重新渲染表格

### 3. 基于锁定库存创建出库单
**逻辑**：保存时根据实际的锁库及分配仓库创建出库单

**流程**：
1. 检查物料的锁定库存
2. 按仓库分组物料
3. 为每个仓库创建独立的出库单
4. 关联锁定记录

### 4. 更新生产订单状态
**功能**：创建领料单后自动更新生产订单的物料状态

## 技术实现

### 前端修改

#### 1. 删除出库仓库选择
```html
<!-- 原来的仓库选择 -->
<div class="layui-col-md6">
    <label class="layui-form-label">出库仓库</label>
    <div class="layui-input-block">
        <select name="warehouse_id" lay-verify="required">
            <!-- 仓库选项 -->
        </select>
    </div>
</div>

<!-- 修改为备注说明 -->
<div class="layui-col-md6">
    <label class="layui-form-label">说明</label>
    <div class="layui-input-block">
        <input type="text" name="notes" placeholder="请输入备注说明" class="layui-input">
    </div>
</div>
```

#### 2. 添加删除按钮列
```javascript
cols: [[
    {field: 'material_code', title: '物料编码', width: 120},
    {field: 'material_name', title: '物料名称', width: 180},
    // ... 其他列
    {title: '操作', width: 80, align: 'center', templet: '#operationTpl'}
]]
```

#### 3. 删除功能实现
```javascript
// 删除物料
window.removeMaterial = function(materialId) {
    layer.confirm('确定要删除这个物料吗？', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        // 从数组中删除
        for (var i = 0; i < materialData.length; i++) {
            if (materialData[i].material_id == materialId) {
                materialData.splice(i, 1);
                break;
            }
        }
        
        // 重新渲染表格
        renderMaterialTable();
        layer.close(index);
        layer.msg('物料已删除');
    });
}
```

### 后端修改

#### 1. 删除仓库验证
```php
// 删除的验证
if (empty($param['warehouse_id'])) {
    return json(['code' => 1, 'msg' => '请选择出库仓库']);
}

// 删除的仓库信息获取
$warehouse = Db::name('warehouse')->where('id', $param['warehouse_id'])->find();
```

#### 2. 基于锁定库存的处理
```php
// 获取该物料的锁定库存信息
$lockedStocks = $this->getLockedStockByMaterial($material['material_id'], $param['production_order_id'], $requestQuantity);

if (empty($lockedStocks)) {
    throw new \Exception("物料【{$material['material_name']}】没有足够的锁定库存，请先进行库存分配");
}

// 按仓库分组
foreach ($lockedStocks as $stock) {
    $warehouseId = $stock['warehouse_id'];
    if (!isset($warehouseGroups[$warehouseId])) {
        $warehouseGroups[$warehouseId] = [
            'warehouse_id' => $warehouseId,
            'warehouse_name' => $stock['warehouse_name'],
            'materials' => []
        ];
    }
    // 添加物料到对应仓库组
}
```

#### 3. 创建出库单逻辑
```php
private function createOutboundOrdersByWarehouse($warehouseGroups, $requestId, $requestNo, $order)
{
    $outboundOrders = [];

    foreach ($warehouseGroups as $warehouseGroup) {
        // 生成出库单号
        $outboundNo = $this->generateOutboundNo();

        // 创建出库单主表
        $outboundData = [
            'outbound_no' => $outboundNo,
            'warehouse_id' => $warehouseGroup['warehouse_id'],
            'warehouse_name' => $warehouseGroup['warehouse_name'],
            'outbound_type' => 3, // 生产领料
            'ref_type' => 'production_material_request',
            'ref_id' => $requestId,
            'ref_no' => $requestNo,
            'status' => 1, // 已审核，可以直接出库
            // ... 其他字段
        ];

        $outboundId = Db::name('warehouse_outbound')->insertGetId($outboundData);

        // 创建出库单明细
        foreach ($warehouseGroup['materials'] as $material) {
            $detailData = [
                'outbound_id' => $outboundId,
                'product_id' => $material['material_id'],
                'quantity' => $material['quantity'],
                'lock_id' => $material['lock_id'], // 关联锁定记录
                // ... 其他字段
            ];

            Db::name('warehouse_outbound_detail')->insert($detailData);
        }
    }

    return $outboundOrders;
}
```

#### 4. 更新生产订单状态
```php
private function updateProductionOrderStatus($productionOrderId)
{
    // 检查生产订单的物料齐套状态
    $materialStatus = $this->checkProductionMaterialStatus($productionOrderId);
    
    // 更新生产订单的物料状态
    Db::name('produce_order')
        ->where('id', $productionOrderId)
        ->update([
            'material_status' => $materialStatus,
            'update_time' => time()
        ]);
}
```

## 业务流程

### 优化前流程
```
1. 选择生产订单
2. 手动选择出库仓库
3. 加载BOM物料
4. 检查库存（基于选择的仓库）
5. 保存领料单（待审核状态）
6. 手动审核后创建出库单
```

### 优化后流程
```
1. 选择生产订单
2. 加载BOM物料
3. 删除库存不足的物料（可选）
4. 保存领料单时：
   - 检查锁定库存
   - 按仓库分组物料
   - 自动创建多个出库单
   - 更新生产订单状态
```

## 数据关联

### 锁定库存表 (inventory_lock)
```sql
- product_id: 物料ID
- warehouse_id: 仓库ID
- quantity: 锁定数量
- ref_type: 'production_order'
- ref_id: 生产订单ID
- status: 1 (已锁定)
```

### 出库单明细 (warehouse_outbound_detail)
```sql
- outbound_id: 出库单ID
- product_id: 物料ID
- quantity: 出库数量
- warehouse_id: 出库仓库ID
- lock_id: 关联的锁定记录ID
```

### 领料单主表 (production_material_request)
```sql
- request_no: 领料单号
- production_order_id: 生产订单ID
- status: 1 (直接设为已审核)
- 删除字段: warehouse_id, warehouse_name
```

## 优势特点

### 1. 自动化程度提高
- 无需手动选择仓库
- 自动基于锁定库存创建出库单
- 自动更新生产订单状态

### 2. 数据准确性
- 基于实际锁定库存，避免库存不足
- 按实际仓库分组，避免跨仓库问题
- 关联锁定记录，便于追踪

### 3. 操作便捷性
- 支持删除库存不足的物料
- 减少手动操作步骤
- 提高处理效率

### 4. 业务合理性
- 符合实际业务流程
- 基于库存分配结果
- 支持多仓库场景

## 注意事项

### 1. 前置条件
- 生产订单必须已进行库存分配
- 物料必须有足够的锁定库存
- 锁定记录状态必须正确

### 2. 异常处理
- 锁定库存不足时给出明确提示
- 创建出库单失败时回滚事务
- 记录详细的操作日志

### 3. 数据一致性
- 使用事务确保数据完整性
- 正确关联锁定记录和出库明细
- 及时更新相关状态

通过这些优化，生产物料申请功能更加智能化和自动化，提高了操作效率和数据准确性。
