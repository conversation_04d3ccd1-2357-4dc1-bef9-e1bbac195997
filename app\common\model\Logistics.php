<?php
namespace app\common\model;

use think\Model;

class Logistics extends Model
{
    // 设置表名
    protected $name = 'logistics';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    /**
     * 获取有效的物流公司列表
     */
    public static function getValidList()
    {
        return self::where('status', 1)
            ->order('id', 'asc')
            ->select()
            ->toArray();
    }
} 