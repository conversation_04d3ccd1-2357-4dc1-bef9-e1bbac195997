# 采购订单需求关联机制说明

## 概述
在采购需求转采购订单时，需要在采购订单明细表中记录需求来源信息，以便后续能够追溯每个采购明细是为了满足哪个具体需求而产生的。

## 关联字段说明

### 采购订单明细表字段
在`oa_purchase_order_detail`表中使用以下字段记录关联信息：
- `ref_type`：关联单据类型
- `ref_id`：关联单据ID

## 需求来源类型

### 1. 销售订单缺货 (Sales Shortage)
**数据来源**：`oa_customer_order_detail`表
**关联设置**：
- `ref_type` = `customer_order_detail`
- `ref_id` = 销售订单明细ID
- `notes` = "销售缺货采购 - 订单号"

**业务场景**：
- 销售订单审核后发现库存不足
- 需要采购补充库存以满足销售需求

### 2. 生产物料缺货 (Production Material Shortage)
**数据来源**：`oa_produce_order_material_requirement`表
**关联设置**：
- `ref_type` = `produce_order_material_requirement`
- `ref_id` = 生产物料需求ID
- `notes` = "生产缺料采购 - 生产订单号"

**业务场景**：
- 生产订单创建后发现物料不足
- 需要采购物料以满足生产需求

## 前端数据传递

### 提交数据结构
```javascript
{
    products: [
        {
            product_id: 123,
            quantity: 100,
            source_type: "sales_shortage|production_shortage",
            source_id: 456,  // 需求记录ID
            business_no: "SC202508110001",  // 订单号
            detail_ids: [789],  // 兼容旧逻辑
            detailGap: [50]
        }
    ],
    supplier_id: 1,
    warehouse_id: 2,
    expected_delivery_date: "2025-08-20",
    decomposition_note: "采购备注"
}
```

### 关键字段说明
- `source_type`：需求来源类型，用于确定`ref_type`
- `source_id`：需求记录的主键ID，用于设置`ref_id`
- `business_no`：订单号，用于生成备注信息

## 后端处理逻辑

### 关联信息设置
```php
// 根据需求来源类型设置关联信息
if ($product['source_type'] === 'sales_shortage') {
    $refType = 'customer_order_detail';
    $refId = $product['source_id'];
    $notes = "销售缺货采购 - " . $product['business_no'];
} elseif ($product['source_type'] === 'production_shortage') {
    $refType = 'produce_order_material_requirement';
    $refId = $product['source_id'];
    $notes = "生产缺料采购 - " . $product['business_no'];
}

// 创建采购订单明细
$orderDetails[] = [
    'product_id' => $product['product_id'],
    'quantity' => $quantity,
    'price' => $price,
    'amount' => $amount,
    'ref_type' => $refType,
    'ref_id' => $refId,
    'notes' => $notes
];
```

## 关联查询示例

### 查询采购明细的需求来源
```sql
-- 查询销售缺货相关的采购明细
SELECT 
    pod.*,
    cod.order_id as sales_order_id,
    cod.product_name as sales_product_name,
    cod.quantity as sales_quantity,
    cod.gap as sales_gap
FROM oa_purchase_order_detail pod
LEFT JOIN oa_customer_order_detail cod ON pod.ref_id = cod.id
WHERE pod.ref_type = 'customer_order_detail';

-- 查询生产缺料相关的采购明细
SELECT 
    pod.*,
    pomr.order_id as production_order_id,
    pomr.material_name,
    pomr.required_quantity,
    pomr.shortage_quantity
FROM oa_purchase_order_detail pod
LEFT JOIN oa_produce_order_material_requirement pomr ON pod.ref_id = pomr.id
WHERE pod.ref_type = 'produce_order_material_requirement';
```

### 统计需求满足情况
```sql
-- 统计销售订单缺货的采购情况
SELECT 
    cod.id as detail_id,
    cod.product_name,
    cod.gap as shortage_quantity,
    COALESCE(SUM(pod.quantity), 0) as purchased_quantity,
    (cod.gap - COALESCE(SUM(pod.quantity), 0)) as remaining_shortage
FROM oa_customer_order_detail cod
LEFT JOIN oa_purchase_order_detail pod ON pod.ref_type = 'customer_order_detail' AND pod.ref_id = cod.id
WHERE cod.inventory_status = 2 AND cod.gap > 0
GROUP BY cod.id;
```

## 业务价值

### 1. 需求追溯
- 每个采购明细都能追溯到具体的需求来源
- 便于分析采购效果和需求满足情况

### 2. 数据分析
- 统计销售缺货和生产缺料的采购比例
- 分析不同需求类型的采购周期和成本

### 3. 流程优化
- 根据需求来源优化采购策略
- 提高采购计划的准确性和及时性

### 4. 库存管理
- 更好地理解库存消耗的原因
- 优化安全库存和采购策略

## 注意事项

### 1. 数据一致性
- 确保`ref_type`和`ref_id`的准确性
- 定期检查关联数据的完整性

### 2. 兼容性处理
- 保留对旧数据格式的兼容性
- 逐步迁移到新的关联机制

### 3. 性能考虑
- 在`ref_type`和`ref_id`字段上建立索引
- 优化关联查询的性能

### 4. 业务规则
- 明确不同需求类型的处理优先级
- 建立需求变更时的关联更新机制

## 扩展性

### 未来可能的需求来源
- 安全库存补充：`ref_type = 'safety_stock'`
- 预测性采购：`ref_type = 'forecast_demand'`
- 紧急采购：`ref_type = 'emergency_purchase'`

通过这种灵活的关联机制，系统可以轻松扩展支持更多类型的采购需求来源。
