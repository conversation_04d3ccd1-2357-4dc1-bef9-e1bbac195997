<?php
declare (strict_types = 1);
namespace app\Produce\controller;

use app\base\BaseController;
use think\facade\Db;
use think\facade\View;

class ProcessGroup extends BaseController
{
    /**
     * 工作组管理首页
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            if (!empty($param['keywords'])) {
                $where[] = ['name', 'like', '%' . $param['keywords'] . '%'];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['status', '=', $param['status']];
            }
            
            $list = Db::name('process_group')
                ->where($where)
                ->field('id,name,description,sort,status,admin_id,create_time,update_time')
                ->order('sort asc, id asc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 20,
                    'page' => isset($param['page']) ? (int)$param['page'] : 1,
                    'query' => $param
                ])
                ->each(function ($item) {
                    $item['status_name'] = $item['status'] ? '启用' : '禁用';
                    $item['create_time'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                    // 统计工作组下的工序数量
                    $item['process_count'] = Db::name('produce_process')->where('group_id', $item['id'])->count();
                    return $item;
                });
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }

    /**
     * 添加/编辑工作组
     */
    public function add()
    {
        $param = get_params();
        if (request()->isPost()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                // 编辑工作组
                $param['update_time'] = time();
                $res = Db::name('process_group')->where('id', $param['id'])->update($param);
                if ($res !== false) {
                    add_log('edit', $param['id'], $param);
                    return to_assign(0, "工作组编辑成功");
                } else {
                    return to_assign(1, "工作组编辑失败");
                }
            } else {
                // 添加工作组
                $param['admin_id'] = $this->uid;
                $param['create_time'] = time();
                $param['update_time'] = time();
                $insertId = Db::name('process_group')->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                    return to_assign(0, "工作组添加成功", ['aid' => $insertId]);
                } else {
                    return to_assign(1, "工作组添加失败");
                }
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = [];
            if ($id > 0) {
                $detail = Db::name('process_group')->where('id', $id)->find();
                if (empty($detail)) {
                    throw new \think\exception\HttpException(404, '找不到信息');
                }
                View::assign('detail', $detail);
            }
            
            View::assign('id', $id);
            return view();
        }
    }

    /**
     * 查看工作组详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = Db::name('process_group')->where('id', $id)->find();
        if (empty($detail)) {
            throw new \think\exception\HttpException(404, '找不到信息');
        }
        $detail['status_name'] = $detail['status'] ? '启用' : '禁用';
        $detail['create_time'] = date('Y-m-d H:i:s', (int)$detail['create_time']);
        $detail['update_time'] = date('Y-m-d H:i:s', (int)$detail['update_time']);
        
        // 获取工作组下的工序列表
        $processes = Db::name('produce_process')
            ->where('group_id', $id)
            ->field('id,code,name,standard_price,efficiency,pricing_method')
            ->order('id asc')
            ->select()
            ->toArray();
        
        View::assign('detail', $detail);
        View::assign('processes', $processes);
        return view();
    }

    /**
     * 删除工作组
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id > 0) {
            // 检查是否有关联的工序
            $check = Db::name('produce_process')->where('group_id', $id)->find();
            if ($check) {
                return to_assign(1, "该工作组下还有工序，无法删除");
            }
            
            $res = Db::name('process_group')->where('id', $id)->delete();
            if ($res) {
                add_log('delete', $id);
                return to_assign(0, "删除成功");
            } else {
                return to_assign(1, "删除失败");
            }
        } else {
            return to_assign(1, "参数错误");
        }
    }

    /**
     * 更新工作组状态
     */
    public function updateStatus()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $status = isset($param['status']) ? $param['status'] : 1;
        
        if ($id > 0) {
            $res = Db::name('process_group')->where('id', $id)->update([
                'status' => $status,
                'update_time' => time()
            ]);
            if ($res !== false) {
                return to_assign(0, "状态更新成功");
            } else {
                return to_assign(1, "状态更新失败");
            }
        } else {
            return to_assign(1, "参数错误");
        }
    }
}