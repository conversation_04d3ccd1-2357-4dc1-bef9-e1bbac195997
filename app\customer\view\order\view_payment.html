<div>
    <table id="paymentLogList" lay-filter="paymentLogList"></table>

</div>
<script>	
function loadPaymentLog(){
	// 确保从layui获取table模块
    var table = layui.table; 

    // 完整表格配置
    table.render({
        elem: '#paymentLogList',
        url: '/api/index/get_list?name=customer_order_payment&action_id='+purchase_id,
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'expense_time', title: '收款日期', width: 180,templet: function(d){
                return '<a href="javascript:void(0)" onclick="viewDeliveryDetail('+d.id+')">'+d.expense_time+'</a>'
            }},
           
            {field: 'type', title: '支付类型' , width: 150, templet: function(d){
                var status = {
                   'payment': '<span class="layui-badge layui-bg-yellow">支付</span>',
                    'refund': '<span class="layui-badge layui-bg-black">退款</span>'
                };
                return status[d.type] || '';
                
            }},
            {field: 'amount', title: '金额', width: 160, sort: true},

            {field: 'status', title: '到账状态' , width: 150, templet: function(d){
                var status = {
                   0: '<span class="layui-badge layui-bg-gray">未受理</span>',
                   1: '<span class="layui-badge layui-bg-green">已到账</span>',
                   2: '<span class="layui-badge layui-bg-blue">失败</span>'
                };
                return status[d.status] || '';
                
            }},
            {field: 'confirmed_at', title: '确认时间', width: 150, templet: function(d){
                var date = new Date(d.confirmed_at * 1000);
                var year = date.getFullYear();
                var month = ('0' + (date.getMonth() + 1)).slice(-2);
                var day = ('0' + date.getDate()).slice(-2);
                return year + '-' + month + '-' + day;
            }},
			{field: 'created_at', title: '操作时间', width: 200, templet: function(d){
                var date = new Date(d.created_at * 1000);
                var year = date.getFullYear();
                var month = ('0' + (date.getMonth() + 1)).slice(-2);
                var day = ('0' + date.getDate()).slice(-2);
                var hours = ('0' + date.getHours()).slice(-2);
                var minutes = ('0' + date.getMinutes()).slice(-2);
                var seconds = ('0' + date.getSeconds()).slice(-2);
                return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            }},
            {fixed: 'right', title: '操作', toolbar: '#tableToolbar', width: 150}
        ]],
        page: true,
        limit: 10,
        loading: true,
        response: {
            statusCode: 0, // 对应接口的code字段
            countName: 'count', // 总条数字段
            dataName: 'data' // 数据列表字段
        },
        parseData: function(res){ // 数据格式转换
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.count,
                "data": res.data
            };
        }
    });
}
function viewDeliveryDetail(id){
			layer.open({
				type: 2,
				title: '发货详情',
				shadeClose: true,
				shade: 0.8,
				area: ['80%', '80%'],
				content: '/customer/delivery/view.html?id=' + id
			});
}
</script>	