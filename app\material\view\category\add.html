{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card">
		<div class="layui-card-header">
			<h3>{if condition="$id > 0"}编辑{else/}添加{/if}物料分类</h3>
		</div>
		<div class="layui-card-body">
			<form class="layui-form" lay-filter="addForm" id="addForm">
				{if condition="$id > 0"}
				<input type="hidden" name="id" value="{$id}" />
				{/if}
				
				<div class="layui-form-item">
					<label class="layui-form-label required">分类名称</label>
					<div class="layui-input-block">
						<input type="text" name="name" value="{$detail.name|default=''}" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
					</div>
				</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">父级分类</label>
					<div class="layui-input-block">
						<select name="pid">
							<option value="0" {if condition="$detail.pid == 0 || empty($detail.pid)"}selected{/if}>顶级分类</option>
							{volist name="categories" id="category"}
							{if condition="$category.id != $id"}
							<option value="{$category.id}" {if condition="$detail.pid == $category.id"}selected{/if}>{$category.name}</option>
							{/if}
							{/volist}
						</select>
					</div>
				</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">排序</label>
					<div class="layui-input-block">
						<input type="number" name="sort" value="{$detail.sort|default='0'}" placeholder="请输入排序值" class="layui-input" />
						<div class="layui-form-mid layui-word-aux">数值越小排序越靠前</div>
					</div>
				</div>
				
				<div class="layui-form-item">
					<label class="layui-form-label">状态</label>
					<div class="layui-input-block">
						<input type="radio" name="status" value="1" title="启用" {if condition="$detail.status == 1 || empty($detail)"}checked{/if} />
						<input type="radio" name="status" value="0" title="禁用" {if condition="$detail.status == 0"}checked{/if} />
					</div>
				</div>
				
				<div class="layui-form-item">
					<div class="layui-input-block">
						<button class="layui-btn" lay-submit lay-filter="addSubmit">保存</button>
						<button type="reset" class="layui-btn layui-btn-primary">重置</button>
						<button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>

{/block}
<!-- /主体 -->

{block name="style"}
<style>
.required::before {
	content: "*";
	color: #ff5722;
	margin-right: 4px;
}
</style>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','form'];
	function gouguInit() {
		var form = layui.form, tool = layui.tool;
		
		// 表单提交
		form.on('submit(addSubmit)', function(data){
			let callback = function (e) {
				layer.msg(e.msg);
				if (e.code == 0) {
					parent.layui.pageTable.reload();
					parent.layer.closeAll();
				}
			}
			tool.post("/material/category/add", data.field, callback);
			return false;
		});
	}
</script>
{/block}
<!-- /脚本 -->