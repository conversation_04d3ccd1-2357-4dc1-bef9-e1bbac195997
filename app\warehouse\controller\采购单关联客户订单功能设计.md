# 采购单关联客户订单功能设计

## 需求背景

用户反馈：
1. `http://tc.xinqiyu.cn:8830/customer/order/getRelatedDocuments?id=88` 接口中采购单没有显示
2. 需要在采购需求页面 `http://tc.xinqiyu.cn:8830/purchase/supply/cgxq` 新增采购单时，自动关联到客户订单

## 问题分析

### 1. 原始问题
- `getRelatedDocuments` 方法查询采购单时使用了正确的关联条件：
  ```php
  ->where('po.source_order_id', $id)
  ->where('po.source_type', 'customer_order')
  ```
- 但是采购单表 `oa_purchase_order` 中的 `source_order_id` 和 `source_type` 字段没有被正确设置

### 2. 数据流分析
```
客户订单审核 → 生成物料需求(material_requirement) → MRP采购建议 → 创建采购单
```

在创建采购单时，需要从物料需求记录中获取客户订单ID，并设置到采购单的关联字段中。

## 解决方案

### 1. 修复 getRelatedDocuments 方法

**文件**: `app/customer/controller/Order.php`

**修改内容**:
- 移除了物料需求单查询（按用户要求）
- 修正了采购单查询的字段名：`po.admin_id` → `po.created_by`
- 更新了注释编号

### 2. 修改采购单创建逻辑

**文件**: `app/purchase/controller/Supply.php`

**方法**: `processPurchaseRequirements_cgxq()`

**核心修改**:
```php
// 获取客户订单ID（从第一个产品的需求记录中获取）
$customerOrderId = 0;
if (!empty($param['products']) && !empty($param['products'][0]['detail_ids'])) {
    $firstDetailId = $param['products'][0]['detail_ids'][0];
    $materialRequirement = Db::name('material_requirement')
        ->where('id', $firstDetailId)
        ->field('order_id')
        ->find();
    if ($materialRequirement) {
        $customerOrderId = $materialRequirement['order_id'];
    }
}

// 在采购订单参数中添加关联字段
$orderParam = [
    // ... 其他字段
    'source_order_id' => $customerOrderId, // 关联客户订单ID
    'source_type' => 'customer_order' // 设置来源类型
];
```

### 3. 数据关联逻辑

**关联路径**:
```
采购需求数据(detail_ids) → material_requirement.id → material_requirement.order_id → customer_order.id
```

**关键字段**:
- `material_requirement.order_id`: 客户订单ID
- `oa_purchase_order.source_order_id`: 来源订单ID
- `oa_purchase_order.source_type`: 来源类型（'customer_order'）

## 技术实现细节

### 1. 采购单表结构
```sql
CREATE TABLE `oa_purchase_order` (
  -- ... 其他字段
  `source_order_id` int(11) DEFAULT '0' COMMENT '来源id',
  `source_type` varchar(255) DEFAULT NULL COMMENT '来源类型',
  -- ... 其他字段
);
```

### 2. 物料需求表结构
```sql
CREATE TABLE `oa_material_requirement` (
  -- ... 其他字段
  `order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `source_id` int(11) NOT NULL COMMENT '来源ID（订单明细ID）',
  `source_type` varchar(50) NOT NULL COMMENT '来源类型',
  -- ... 其他字段
);
```

### 3. 查询逻辑
```php
// 获取相关采购单
$purchaseOrders = Db::name('purchase_order')
    ->alias('po')
    ->leftJoin('admin a', 'po.created_by = a.id')
    ->where('po.source_order_id', $customerOrderId)
    ->where('po.source_type', 'customer_order')
    ->field('po.id, po.order_no, po.status, po.create_time, a.nickname as creator_name')
    ->select();
```

## 测试验证

### 1. 功能测试
1. 访问 `http://tc.xinqiyu.cn:8830/purchase/supply/cgxq`
2. 选择采购需求数据，批量处理创建采购单
3. 访问 `http://tc.xinqiyu.cn:8830/customer/order/getRelatedDocuments?id=88`
4. 验证采购单是否正确显示

### 2. 数据验证
```sql
-- 检查采购单是否正确关联客户订单
SELECT po.id, po.order_no, po.source_order_id, po.source_type, co.order_no as customer_order_no
FROM oa_purchase_order po
LEFT JOIN oa_customer_order co ON po.source_order_id = co.id
WHERE po.source_type = 'customer_order'
ORDER BY po.create_time DESC;
```

## 注意事项

1. **批量处理限制**: 前端已有逻辑确保同一批次只能处理同一客户订单的需求
2. **事务处理**: 采购单创建过程使用事务确保数据一致性
3. **错误处理**: 如果无法获取客户订单ID，`source_order_id` 将设置为0
4. **向后兼容**: 修改不影响现有的采购单数据和其他创建方式

## 后续优化建议

1. **多订单支持**: 如果需要支持跨订单的采购需求合并，需要调整关联逻辑
2. **审计日志**: 可以添加采购单创建来源的详细记录
3. **权限控制**: 可以根据客户订单权限控制采购单的查看权限
