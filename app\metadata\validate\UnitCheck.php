<?php
declare (strict_types = 1);

namespace app\metadata\validate;

use think\Validate;

class UnitCheck extends Validate
{
    protected $rule = [
        'name'      => 'require|max:20|unique:unit',
        'precision' => 'require|integer|between:0,6',
        'type'      => 'require|in:四舍五入,进位',
        'remark'    => 'max:100'
    ];

    protected $message = [
        'name.require'      => '单位名称不能为空',
        'name.max'          => '单位名称不能超过20个字符',
        'name.unique'       => '单位名称已存在',
        'precision.require' => '单位精度不能为空',
        'precision.integer' => '单位精度必须为整数',
        'precision.between' => '单位精度必须在0-6之间',
        'type.require'      => '含入类型不能为空',
        'type.in'           => '含入类型只能是四舍五入或进位',
        'remark.max'        => '备注不能超过100个字符'
    ];

    protected $scene = [
        'add'  => ['name', 'precision', 'type', 'remark'],
        'edit' => ['name', 'precision', 'type', 'remark']
    ];
}