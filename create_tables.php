<?php
// 临时脚本用于创建数据库表

require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 初始化ThinkPHP
$app = new \think\App();
$app->initialize();

try {
    // 读取SQL文件内容
    $sql = file_get_contents('app/material/sql/material_price_tables.sql');
    
    // 分割SQL语句
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            echo "执行SQL: " . substr($statement, 0, 50) . "...\n";
            Db::execute($statement);
            echo "成功\n";
        }
    }
    
    echo "所有表创建完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}