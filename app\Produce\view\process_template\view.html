{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>工艺路线详情</h3>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                            <tr>
                                <td width="120"><strong>工艺编号：</strong></td>
                                <td>{$info.template_no|default=''}</td>
                            </tr>
                            <tr>
                                <td><strong>工艺名称：</strong></td>
                                <td>{$info.name|default=''}</td>
                            </tr>
                            <tr>
                                <td><strong>备注：</strong></td>
                                <td>{$info.remark|default=''}</td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td>{$info.create_time|date='Y-m-d H:i:s'}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="layui-row" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <h4>工艺步骤</h4>
                    {if condition="!empty($info.step_list)"}
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="80">序号</th>
                                <th width="200">工序名称</th>
                                <th width="120">工序类型</th>
                                <th width="120">加工方式</th>
                                <th width="120">检验方式</th>
                                <th width="100">完成时间</th>
                                <th width="80">时间单位</th>
                                <th>描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="info.step_list" id="step" key="k"}
                            <tr>
                                <td>{$k}</td>
                                <td>{$step.name|default=''}</td>
                                <td>{$step.type|default='数据记录'}</td>
                                <td>{$step.processing_type|default='自制'}</td>
                                <td>{$step.inspection_method|default='免检'}</td>
                                <td>{$step.completion_time|default=0}</td>
                                <td>{$step.time_unit|default='天'}</td>
                                <td>{$step.description|default=''}</td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                    {else /}
                    <div class="layui-empty">
                        <div class="layui-empty-icon">
                            <i class="layui-icon layui-icon-template-1"></i>
                        </div>
                        <p class="layui-empty-text">暂无工艺步骤</p>
                    </div>
                    {/if}
                </div>
            </div>
            
            <div class="layui-row" style="margin-top: 30px;">
                <div class="layui-col-md12">
                    <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">
                        <i class="layui-icon layui-icon-return"></i> 返回
                    </button>
                    <button type="button" class="layui-btn" onclick="editTemplate()">
                        <i class="layui-icon layui-icon-edit"></i> 编辑
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 编辑工艺路线
        window.editTemplate = function() {
            tool.side("/Produce/processTemplate/add?id={$info.id}");
        }
    }
</script>
{/block}

{block name="style"}
<style>
.layui-empty {
    text-align: center;
    padding: 40px 0;
}
.layui-empty-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}
.layui-empty-text {
    color: #999;
    font-size: 14px;
}
</style>
{/block}