<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use think\facade\Db;

/**
 * 库存分配优先级管理类
 * 
 * 功能说明：
 * 1. 定义各业务类型的基础优先级
 * 2. 根据业务特征动态计算优先级
 * 3. 提供优先级配置管理功能
 */
class AllocationPriority
{
    // 基础优先级常量
    const EMERGENCY_PRODUCTION = 100;      // 紧急生产订单
    const NORMAL_PRODUCTION = 90;          // 正常生产订单
    const VIP_CUSTOMER_ORDER = 80;         // VIP客户订单
    const URGENT_CUSTOMER_ORDER = 70;      // 紧急客户订单
    const NORMAL_CUSTOMER_ORDER = 60;      // 普通客户订单
    const TRANSFER_ORDER = 50;             // 调拨单
    const QUALITY_CHECK = 40;              // 质检
    const PURCHASE_ORDER = 30;             // 采购订单（在途锁定）
    
    // 客户等级权重
    const CUSTOMER_LEVEL_WEIGHTS = [
        'vip' => 20,        // VIP客户
        'gold' => 15,       // 金牌客户
        'silver' => 10,     // 银牌客户
        'bronze' => 5,      // 铜牌客户
        'normal' => 0       // 普通客户
    ];
    
    // 紧急程度权重
    const URGENCY_WEIGHTS = [
        'emergency' => 20,  // 紧急
        'urgent' => 15,     // 加急
        'high' => 10,       // 高优先级
        'normal' => 0       // 正常
    ];
    
    /**
     * 获取业务类型的默认优先级
     * 
     * @param string $refType 业务类型
     * @return int 优先级值
     */
    public static function getDefaultPriority($refType)
    {
        $priorityMap = [
            'production_order' => self::NORMAL_PRODUCTION,
            'customer_order' => self::NORMAL_CUSTOMER_ORDER,
            'purchase_order' => self::PURCHASE_ORDER,
            'transfer' => self::TRANSFER_ORDER,
            'quality_check' => self::QUALITY_CHECK,
        ];
        
        return $priorityMap[$refType] ?? 50;
    }
    
    /**
     * 根据业务特征计算动态优先级
     * 
     * @param string $refType 业务类型
     * @param int $refId 业务ID
     * @param array $params 额外参数
     * @return int 计算后的优先级
     */
    public static function calculatePriority($refType, $refId, $params = [])
    {
        $basePriority = self::getDefaultPriority($refType);
        
        // 根据业务类型进行特殊处理
        switch ($refType) {
            case 'customer_order':
                $basePriority = self::calculateCustomerOrderPriority($refId, $basePriority, $params);
                break;
            case 'production_order':
                $basePriority = self::calculateProductionOrderPriority($refId, $basePriority, $params);
                break;
            case 'purchase_order':
                $basePriority = self::calculatePurchaseOrderPriority($refId, $basePriority, $params);
                break;
        }
        
        // 应用通用权重调整
        $basePriority = self::applyCommonWeights($basePriority, $params);
        
        // 确保优先级在合理范围内
        return max(1, min(100, $basePriority));
    }
    
    /**
     * 计算客户订单优先级
     */
    private static function calculateCustomerOrderPriority($orderId, $basePriority, $params)
    {
        // 获取订单信息
        $order = Db::name('order')->alias('o')
            ->join('customer c', 'o.customer_id = c.id')
            ->where('o.id', $orderId)
            ->field('o.*, c.level as customer_level, c.type as customer_type')
            ->find();
            
        if (!$order) {
            return $basePriority;
        }
        
        // 根据客户等级调整优先级
        $customerLevel = $order['customer_level'] ?? 'normal';
        $levelWeight = self::CUSTOMER_LEVEL_WEIGHTS[$customerLevel] ?? 0;
        $basePriority += $levelWeight;
        
        // VIP客户特殊处理
        if ($customerLevel === 'vip') {
            $basePriority = max($basePriority, self::VIP_CUSTOMER_ORDER);
        }
        
        // 根据订单金额调整（大订单优先）
        $orderAmount = $order['total_amount'] ?? 0;
        if ($orderAmount > 100000) {
            $basePriority += 10;
        } elseif ($orderAmount > 50000) {
            $basePriority += 5;
        }
        
        // 根据交货期调整（紧急交货优先）
        $deliveryDate = $order['delivery_date'] ?? 0;
        if ($deliveryDate > 0) {
            $daysToDelivery = ($deliveryDate - time()) / 86400;
            if ($daysToDelivery <= 1) {
                $basePriority += 15; // 1天内交货
            } elseif ($daysToDelivery <= 3) {
                $basePriority += 10; // 3天内交货
            } elseif ($daysToDelivery <= 7) {
                $basePriority += 5;  // 7天内交货
            }
        }
        
        return $basePriority;
    }
    
    /**
     * 计算生产订单优先级
     */
    private static function calculateProductionOrderPriority($orderId, $basePriority, $params)
    {
        // 获取生产订单信息
        $order = Db::name('production_order')->find($orderId);
        if (!$order) {
            return $basePriority;
        }
        
        // 根据生产计划开始时间调整
        $planStartDate = $order['plan_start_date'] ?? 0;
        if ($planStartDate > 0) {
            $daysToStart = ($planStartDate - time()) / 86400;
            if ($daysToStart <= 0) {
                $basePriority += 20; // 已到开始时间或延期
            } elseif ($daysToStart <= 1) {
                $basePriority += 15; // 1天内开始
            } elseif ($daysToStart <= 3) {
                $basePriority += 10; // 3天内开始
            }
        }
        
        // 根据生产订单类型调整
        $orderType = $order['type'] ?? 'normal';
        if ($orderType === 'emergency') {
            $basePriority = max($basePriority, self::EMERGENCY_PRODUCTION);
        }
        
        // 根据生产数量调整（大批量生产优先）
        $quantity = $order['quantity'] ?? 0;
        if ($quantity > 1000) {
            $basePriority += 5;
        }
        
        return $basePriority;
    }
    
    /**
     * 计算采购订单优先级
     */
    private static function calculatePurchaseOrderPriority($orderId, $basePriority, $params)
    {
        // 获取采购订单信息
        $order = Db::name('purchase_order')->find($orderId);
        if (!$order) {
            return $basePriority;
        }
        
        // 根据采购紧急程度调整
        $urgency = $order['urgency'] ?? 'normal';
        $urgencyWeight = self::URGENCY_WEIGHTS[$urgency] ?? 0;
        $basePriority += $urgencyWeight;
        
        // 根据预计到货时间调整
        $expectedDate = $order['expected_date'] ?? 0;
        if ($expectedDate > 0) {
            $daysToArrival = ($expectedDate - time()) / 86400;
            if ($daysToArrival <= 1) {
                $basePriority += 10; // 1天内到货
            } elseif ($daysToArrival <= 3) {
                $basePriority += 5;  // 3天内到货
            }
        }
        
        return $basePriority;
    }
    
    /**
     * 应用通用权重调整
     */
    private static function applyCommonWeights($basePriority, $params)
    {
        // 紧急程度权重
        if (isset($params['urgency'])) {
            $urgencyWeight = self::URGENCY_WEIGHTS[$params['urgency']] ?? 0;
            $basePriority += $urgencyWeight;
        }
        
        // 手动指定的优先级调整
        if (isset($params['priority_adjustment'])) {
            $basePriority += $params['priority_adjustment'];
        }
        
        // 特殊标记处理
        if (isset($params['is_emergency']) && $params['is_emergency']) {
            $basePriority += 20;
        }
        
        if (isset($params['is_vip']) && $params['is_vip']) {
            $basePriority += 15;
        }
        
        return $basePriority;
    }
    
    /**
     * 获取优先级配置
     */
    public static function getConfig($configKey = null)
    {
        $config = Db::name('inventory_allocation_config')
            ->where('is_active', 1)
            ->column('config_value', 'config_key');
            
        if ($configKey) {
            $value = $config[$configKey] ?? null;
            return $value ? json_decode($value, true) : null;
        }
        
        $result = [];
        foreach ($config as $key => $value) {
            $result[$key] = json_decode($value, true);
        }
        
        return $result;
    }
    
    /**
     * 更新优先级配置
     */
    public static function updateConfig($configKey, $configValue, $description = '')
    {
        $data = [
            'config_value' => json_encode($configValue),
            'update_time' => time()
        ];
        
        if ($description) {
            $data['config_desc'] = $description;
        }
        
        $exists = Db::name('inventory_allocation_config')
            ->where('config_key', $configKey)
            ->find();
            
        if ($exists) {
            return Db::name('inventory_allocation_config')
                ->where('config_key', $configKey)
                ->update($data);
        } else {
            $data['config_key'] = $configKey;
            $data['create_time'] = time();
            return Db::name('inventory_allocation_config')->insert($data);
        }
    }
    
    /**
     * 获取优先级说明文本
     */
    public static function getPriorityDescription($priority)
    {
        if ($priority >= 90) {
            return '极高优先级';
        } elseif ($priority >= 80) {
            return '高优先级';
        } elseif ($priority >= 60) {
            return '中等优先级';
        } elseif ($priority >= 40) {
            return '低优先级';
        } else {
            return '极低优先级';
        }
    }
}
