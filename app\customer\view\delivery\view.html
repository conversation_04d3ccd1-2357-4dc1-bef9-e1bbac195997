{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-row">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">发货基本信息</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="nob">
                        <colgroup>
                            <col width="120">
                            <col>
                            <col width="120">
                            <col>
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="layui-bg-gray">发货编号</td>
                                <td>{$delivery.delivery_no}</td>
                                <td class="layui-bg-gray">订单编号</td>
                                <td>{$delivery.order_no}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">客户名称</td>
                                <td>{$delivery.customer_name}</td>
                                <td class="layui-bg-gray">发货状态</td>
                                <td>
                                    <span class="{$delivery.actual_status_class}">{$delivery.actual_status_text}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">出库状态</td>
                                <td>
                                    <span class="{$delivery.outbound_status_class}">{$delivery.outbound_status_text}</span>
                                </td>
                                <td class="layui-bg-gray">出库单号</td>
                                <td>
                                    {if $delivery.outbound_no}
                                        <a href="/warehouse/outbound/detail.html?id={$delivery.outbound_id}" target="_blank" style="color: #1E9FFF;">{$delivery.outbound_no}</a>
                                    {else}
                                        <span class="layui-text-muted">-</span>
                                    {/if}
                                </td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">收货地址</td>
                                <td colspan="3">{$delivery.address}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">联系人</td>
                                <td>{$delivery.contact}</td>
                                <td class="layui-bg-gray">联系电话</td>
                                <td>{$delivery.phone}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">特殊要求</td>
                                <td colspan="3">{$delivery.remark}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">物流信息</div>
                <div class="layui-card-body">
                    <table class="layui-table" lay-skin="nob">
                        <colgroup>
                            <col width="120">
                            <col>
                            <col width="120">
                            <col>
                        </colgroup>
                        <tbody>
                            <tr>
                                <td class="layui-bg-gray">发货方式</td>
                                <td>
                                    {if $delivery.delivery_type == 1}
                                        公司物流
                                    {elseif $delivery.delivery_type == 2}
                                        外部物流
                                    {elseif $delivery.delivery_type == 3}
                                        客户自提
                                    {/if}
                                </td>
                                <td class="layui-bg-gray">物流公司</td>
                                <td>{$delivery.logistics_name}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">物流单号</td>
                                <td>{$delivery.logistics_no}</td>
                                <td class="layui-bg-gray">预计发货日期</td>
                                <td>{$delivery.expect_time}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">实际发货日期</td>
                                <td>{$delivery.delivery_time}</td>
                                <td class="layui-bg-gray">创建人</td>
                                <td>{$delivery.creator_name}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">创建时间</td>
                                <td>{$delivery.create_time}</td>
                                <td class="layui-bg-gray">处理人</td>
                                <td>{$delivery.handler_name}</td>
                            </tr>
                            <tr>
                                <td class="layui-bg-gray">处理时间</td>
                                <td colspan="3">{$delivery.handle_time}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card mt-10">
        <div class="layui-card-header">发货商品</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>商品名称</th>
                        <th>规格型号</th>
                        <th>单位</th>
                        <th>订单数量</th>
                        <th>本次发货数量</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="items" id="item"}
                    <tr>
                        <td>{$item.product.title}</td>
                        <td>{$item.product.material_code}</td>
                        <td>{$item.product.unit}</td>
                        <td>{$item.order_qty}</td>
                        <td>{$item.delivery_qty}</td>
                        <td>{$item.remark}</td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
        </div>
    </div>

    <div class="layui-card mt-10">
        <div class="layui-card-header">发货进度</div>
        <div class="layui-card-body">
            <ul class="layui-timeline">
                {volist name="logs" id="log"}
                <li class="layui-timeline-item">
                    <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                    <div class="layui-timeline-content layui-text">
                        <h3 class="layui-timeline-title">{$log.create_time}</h3>
                        <p>{$log.content}</p>
                    </div>
                </li>
                {/volist}
            </ul>
        </div>
    </div>

    {if $delivery.status == 0 && ($delivery.outbound_status == 'none' || $delivery.outbound_status <= 2)}
     <div class="layui-form-item">
        <div class="layui-input-block">
             <button type="button" class="layui-btn layui-btn-danger" id="btnCancel">取消发货</button>
        </div>
    </div>
    {elseif $delivery.outbound_status == 4}
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="button" class="layui-btn layui-btn-normal" id="btnComplete">完成发货</button>
        </div>
    </div>
    {/if}
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    
    function gouguInit() {
        var tool = layui.tool;
        var $ = layui.jquery;
        var layer = layui.layer;
        var form = layui.form;
        var laydate = layui.laydate;
        var table = layui.table;
        var layedit = layui.layedit;
        var laytree = layui.laytree;
        
        // 获取发货ID
        var deliveryId = getUrlParam('id');
        
        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        
        // 开始处理按钮
        $("#btnProcess").click(function() {
            tool.confirm('确定开始处理该发货指令吗？', function() {
                tool.post('/customer/delivery/process', {id: deliveryId}, function(res) {
                    if (res.code == 0) {
                        tool.msg('操作成功', function() {
                            location.reload();
                        });
                    } else {
                        tool.msg(res.msg);
                    }
                });
            });
        });
        
        // 完成发货按钮
        $("#btnComplete").click(function() {
        // 弹出层代码
    layer.open({
        type: 1,
        title: '完成发货',
        area: ['500px', '300px'],
        content: '<div class="p-10"><form class="layui-form" id="completeForm">' +
            '<div class="layui-form-item"><label class="layui-form-label">物流单号</label>' +
            '<div class="layui-input-block"><input type="text" name="logistics_no" required lay-verify="required" placeholder="请输入物流单号" class="layui-input"></div></div>' +
            '<div class="layui-form-item"><label class="layui-form-label">发货日期</label>' +
            '<div class="layui-input-block"><input type="text" id="delivery_time" name="delivery_time" required lay-verify="required" placeholder="请选择发货日期" class="layui-input"></div></div>' +
            '<div class="layui-form-item"><div class="layui-input-block">' +
            '<button class="layui-btn layui-btn-normal" lay-submit lay-filter="completeForm">确定</button>' +
            '<button type="button" class="layui-btn layui-btn-primary" id="btnCancelPopup">取消</button>' +
            '</div></div></form></div>',
        success: function(layero, index) {
            // 初始化日期选择器
            laydate.render({
                elem: '#delivery_time',
                type: 'date'
            });
            
            // 取消按钮
            $("#btnCancelPopup").click(function() {
                layer.close(index);
            });
            
            // 表单提交
            form.on('submit(completeForm)', function(data) {
                var formData = data.field;
                formData.id = deliveryId;
                
                // 使用 jQuery 的 ajax 方法
                $.post('/customer/delivery/complete', formData, function(res) {
                    if (res.code == 0) {
                        layer.close(index);
                        layer.msg('操作成功', function() {
                            location.reload();
                        });
                    } else {
                        layer.msg(res.msg || '操作失败');
                    }
                });
                
                return false;
            });
        }
    });
        });
        
        // 取消发货按钮
        $("#btnCancel").click(function() {
            
            

                // 弹出层配置
    layer.open({
        type: 1, // 基础窗口类型
        title: '取消发货',
        area: ['500px', '250px'],
        content: '<div class="p-10"><form class="layui-form" id="cancelForm">' +
            '<div class="layui-form-item"><label class="layui-form-label">取消原因</label>' +
            '<div class="layui-input-block"><textarea name="cancel_reason" required lay-verify="required" placeholder="请输入取消原因" class="layui-textarea"></textarea></div></div>' +
            '<div class="layui-form-item"><div class="layui-input-block">' +
            '<button class="layui-btn layui-btn-danger" lay-submit lay-filter="cancelForm">确定</button>' +
            '<button type="button" class="layui-btn layui-btn-primary" id="btnCancelPopup">取消</button>' +
            '</div></div></form></div>',
        success: function(layero, index) {
            // 取消按钮事件
            $("#btnCancelPopup").click(function() {
                layer.close(index);
            });
            
            // 表单提交
            form.on('submit(cancelForm)', function(data) {
                var formData = data.field;
                formData.id = deliveryId;
                
                // 使用 jQuery 的 AJAX 方法
                $.post('/customer/delivery/cancel', formData)
                 .done(function(res) {
                     if (res.code == 0) {
                         layer.close(index);
                         layer.msg('操作成功', {icon:1}, function(){
                             location.reload();
                         });
                     } else {
                         layer.msg(res.msg || '操作失败', {icon:2});
                     }
                 })
                 .fail(function() {
                     layer.msg('请求失败', {icon:2});
                 });

                return false;
            });
        }
    });
        });
    }
</script>
{/block} 