# 入库单反审核功能测试

## 测试目标
验证入库单反审核时，相关的分配需求和锁库状态是否正确恢复到原来的状态。

## 测试场景

### 场景1：有分配需求和锁定库存的反审核
1. **前置条件**：
   - 创建一个入库单并提交成功
   - 入库时触发了自动分配，产生了分配需求和锁定库存
   - 入库单状态为已入库（status = 1）

2. **执行反审核**：
   - 访问：`http://tc.xinqiyu.cn:8830/warehouse/Receipt/unapprove.html`
   - 提交入库单ID

3. **预期结果**：
   - 入库单状态变为草稿（status = 0）
   - 库存数量正确减少
   - 锁定库存被释放
   - 分配需求记录被删除
   - 释放的库存重新分配给其他等待需求

### 场景2：无分配需求的反审核
1. **前置条件**：
   - 入库单没有触发自动分配
   - 无相关的锁定库存

2. **执行反审核**：
   - 正常反审核流程

3. **预期结果**：
   - 反审核成功
   - 不会产生分配相关的错误

## 检查要点

### 1. 数据库状态检查
```sql
-- 检查入库单状态
SELECT id, receipt_no, status FROM oa_purchase_receipt WHERE id = ?;

-- 检查库存变化
SELECT * FROM oa_inventory_realtime WHERE product_id = ? AND warehouse_id = ?;

-- 检查锁定记录
SELECT * FROM oa_inventory_lock WHERE ref_type = 'purchase_receipt' AND ref_id = ?;

-- 检查分配需求
SELECT * FROM oa_inventory_allocation_request WHERE ref_type = 'purchase_receipt' AND ref_id = ?;

-- 检查分配历史
SELECT * FROM oa_inventory_allocation_history WHERE request_id IN (
    SELECT id FROM oa_inventory_allocation_request WHERE ref_type = 'purchase_receipt' AND ref_id = ?
);
```

### 2. 日志检查
查看系统日志，确认以下操作：
- 反审核开始日志
- 锁定记录查找日志
- 锁定释放日志
- 分配需求删除日志
- 库存重新分配日志

### 3. 业务逻辑检查
- 采购订单明细的收货数量是否正确减少
- 采购订单状态是否正确更新
- 质检状态是否正确重置

## 常见问题排查

### 1. 反审核失败
**可能原因**：
- 有后续出库记录
- 库存不足
- 锁定记录状态异常

**排查方法**：
- 检查出库记录：`SELECT * FROM oa_outbound_detail WHERE product_id = ? AND warehouse_id = ?`
- 检查库存状态：`SELECT * FROM oa_inventory_realtime WHERE product_id = ? AND warehouse_id = ?`
- 检查锁定状态：`SELECT * FROM oa_inventory_lock WHERE ref_type = 'purchase_receipt' AND ref_id = ?`

### 2. 分配需求未删除
**可能原因**：
- 分配服务调用失败
- 数据库事务回滚

**排查方法**：
- 查看错误日志
- 检查分配需求表：`SELECT * FROM oa_inventory_allocation_request WHERE ref_type = 'purchase_receipt' AND ref_id = ?`

### 3. 锁定库存未释放
**可能原因**：
- 锁定服务调用失败
- 锁定记录状态异常

**排查方法**：
- 查看锁定服务日志
- 检查锁定记录状态：`SELECT * FROM oa_inventory_lock WHERE ref_type = 'purchase_receipt' AND ref_id = ?`

## 测试步骤

### 步骤1：准备测试数据
1. 创建测试产品和仓库
2. 创建采购订单
3. 创建入库单并提交

### 步骤2：执行反审核
1. 记录反审核前的数据状态
2. 执行反审核操作
3. 记录反审核后的数据状态

### 步骤3：验证结果
1. 对比数据变化
2. 检查日志记录
3. 验证业务逻辑正确性

## 预期的日志输出

```
[INFO] 开始反审释放 {"ref_type":"purchase_receipt","ref_id":123,"operator_id":1,"reason":"采购入库单反审核"}
[INFO] 获取锁定记录 {"ref_type":"purchase_receipt","ref_id":123,"lock_count":2}
[INFO] 开始删除分配需求 {"ref_type":"purchase_receipt","ref_id":123}
[INFO] 查找待删除的分配需求 {"ref_type":"purchase_receipt","ref_id":123,"found_count":1}
[INFO] 执行删除分配需求 {"ref_type":"purchase_receipt","ref_id":123,"expected_count":1,"actual_deleted_count":1}
[INFO] 反审删除分配需求完成 {"ref_type":"purchase_receipt","ref_id":123,"deleted_requests_count":1,"deleted_history_count":1}
[INFO] 反审后重新分配库存 {"product_id":1,"warehouse_id":1,"quantity":10,"pending_requests":1}
```

## 成功标准

1. ✅ 反审核操作成功完成
2. ✅ 入库单状态正确回退
3. ✅ 库存数量正确减少
4. ✅ 锁定库存完全释放
5. ✅ 分配需求记录完全删除
6. ✅ 分配历史记录完全删除
7. ✅ 释放的库存重新分配给其他需求
8. ✅ 相关业务状态正确更新
9. ✅ 无数据不一致问题
10. ✅ 日志记录完整准确
