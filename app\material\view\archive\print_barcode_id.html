<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>打印条形码</title>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <style>
        @media print {
            @page {
                margin: 0;
                size: auto;
            }
            body {
                margin: 0;
                padding: 0;
            }
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .barcode-label {
            width: 350px;
            height: 220px;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px auto;
            display: flex;
            flex-direction: column;
            background: white;
            page-break-inside: avoid;
            font-family: "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
        }

        .label-header {
            text-align: center;
            margin-bottom: 10px;
        }

        .label-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .label-info {
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 8px;
            color: #333;
        }

        .label-info strong {
            display: inline-block;
            width: 60px;
            font-weight: bold;
            color: #000;
        }

        .barcode-container {
            text-align: center;
            margin: 10px 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .barcode-svg {
            margin: 0 auto;
        }

        .barcode-text {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
            letter-spacing: 1px;
        }

        .print-buttons {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        @media print {
            .print-buttons {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-buttons no-print">
            <button class="btn" onclick="printLabel()">打印</button>
            <button class="btn btn-secondary" onclick="window.close()">关闭</button>
        </div>

        <div class="barcode-label">
            <div class="label-header">
                <div class="label-title">{$material.title}</div>
            </div>
            
            <div class="label-info"><strong>物料名称:</strong> {$material.title}</div>
            <div class="label-info"><strong>物料编码:</strong> {$material.material_code}</div>
            <div class="label-info"><strong>物料型号:</strong> {$material.specs|default='-'}</div>
            
            <div class="barcode-container">
                <svg id="barcode" class="barcode-svg"></svg>
                <div class="barcode-text">{$material.material_code}</div>
            </div>
        </div>
    </div>

    <script>
        // 生成条形码
        document.addEventListener('DOMContentLoaded', function() {
            const barcodeContent = "{$barcodeContent}";
            
            try {
                JsBarcode("#barcode", barcodeContent, {
                    format: "CODE128",
                    width: 2,
                    height: 60,
                    displayValue: false,
                    margin: 0,
                    fontSize: 12,
                    textAlign: "center",
                    textPosition: "bottom",
                    background: "#ffffff",
                    lineColor: "#000000"
                });
            } catch (error) {
                console.error('条形码生成失败:', error);
                document.getElementById('barcode').innerHTML = '<text x="50%" y="50%" text-anchor="middle" fill="#666">条形码生成失败</text>';
            }
        });

        function printLabel() {
            // 获取标签内容
            var labelContent = document.querySelector('.barcode-label');
            
            // 创建新窗口用于打印
            var printWindow = window.open('', '_blank', 'width=800,height=600');
            
            // 构建打印页面内容
            var printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <title>打印标签</title>
                    <style>
                        @page {
                            margin: 0;
                            size: auto;
                        }
                        body {
                            margin: 0;
                            padding: 20px;
                            font-family: "Microsoft YaHei", Arial, sans-serif;
                            width: 100vw;
                            height: 100vh;
                            box-sizing: border-box;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .barcode-label {
                            width: 800px;
                            height: 600px;
                            border: none;
                            padding: 40px;
                            display: flex;
                            flex-direction: column;
                            background: white;
                            box-sizing: border-box;
                        }
                        .label-header {
                            text-align: center;
                            margin-bottom: 20px;
                        }
                        .label-title {
                            font-size: 28px;
                            font-weight: bold;
                            color: #333;
                            margin-bottom: 10px;
                        }
                        .label-info {
                            font-size: 24px;
                            line-height: 1.8;
                            margin-bottom: 15px;
                            color: #333;
                        }
                        .label-info strong {
                            display: inline-block;
                            width: 120px;
                            font-weight: bold;
                            color: #000;
                        }
                        .barcode-container {
                            text-align: center;
                            margin: 20px 0;
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                        }
                        .barcode-svg {
                            margin: 0 auto;
                            transform: scale(2);
                        }
                        .barcode-text {
                            font-size: 18px;
                            color: #666;
                            margin-top: 40px;
                            letter-spacing: 2px;
                        }
                    </style>
                </head>
                <body>
                    ${labelContent.outerHTML}
                </body>
                </html>
            `;
            
            // 写入内容到新窗口
            printWindow.document.write(printContent);
            printWindow.document.close();
            
            // 等待内容加载完成后打印
            printWindow.onload = function() {
                setTimeout(function() {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };
        }
    </script>
</body>
</html>
