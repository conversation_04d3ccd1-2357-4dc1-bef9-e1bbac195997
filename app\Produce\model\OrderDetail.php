<?php

declare (strict_types = 1);

namespace app\Produce\model;

use think\Model;

/**
 * 生产订单明细模型
 */
class OrderDetail extends Model
{
    // 设置当前模型对应的数据表名称
    protected $name = 'produce_order_detail';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 类型转换
    protected $type = [
        'quantity' => 'float',
        'completed_qty' => 'float',
    ];
    
    /**
     * 关联生产订单
     */
    public function order()
    {
        return $this->belongsTo('app\Produce\model\Order', 'order_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\engineering\model\Product', 'product_id', 'id');
    }
    
    /**
     * 关联销售订单明细
     */
    public function customerOrderDetail()
    {
        return $this->belongsTo('app\customer\model\OrderDetail', 'customer_order_detail_id', 'id');
    }
    
    /**
     * 关联BOM
     */
    public function bom()
    {
        return $this->belongsTo('app\engineering\model\BomMaster', 'bom_id', 'id');
    }
    
    /**
     * 关联工艺
     */
    public function process()
    {
        return $this->belongsTo('app\engineering\model\Process', 'process_id', 'id');
    }
    
    /**
     * 获取完成率
     */
    public function getCompletionRateAttr($value, $data)
    {
        if ($data['quantity'] <= 0) {
            return 0;
        }
        
        return round(($data['completed_qty'] / $data['quantity']) * 100, 2);
    }
    
    /**
     * 获取剩余数量
     */
    public function getRemainingQtyAttr($value, $data)
    {
        return max(0, $data['quantity'] - $data['completed_qty']);
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        if ($data['completed_qty'] <= 0) {
            return '未开始';
        } elseif ($data['completed_qty'] >= $data['quantity']) {
            return '已完成';
        } else {
            return '进行中';
        }
    }
    
    /**
     * 更新完成数量
     */
    public function updateCompletedQty($completedQty)
    {
        $this->completed_qty = $completedQty;
        $this->save();
        
        // 同步更新主订单的完成状态
        $this->updateOrderStatus();
        
        return true;
    }
    
    /**
     * 更新主订单状态
     */
    private function updateOrderStatus()
    {
        $order = $this->order;
        if (!$order) {
            return;
        }
        
        // 获取所有明细的完成情况
        $details = $order->details;
        $totalQty = 0;
        $completedQty = 0;
        
        foreach ($details as $detail) {
            $totalQty += $detail['quantity'];
            $completedQty += $detail['completed_qty'];
        }
        
        // 计算完成率
        $completionRate = $totalQty > 0 ? ($completedQty / $totalQty) : 0;
        
        // 更新订单状态
        $newStatus = $order->status;
        if ($completionRate >= 1) {
            $newStatus = 3; // 已完成
        } elseif ($completionRate > 0) {
            $newStatus = 2; // 生产中
        }
        
        if ($newStatus != $order->status) {
            $order->status = $newStatus;
            $order->save();
        }
    }
}
