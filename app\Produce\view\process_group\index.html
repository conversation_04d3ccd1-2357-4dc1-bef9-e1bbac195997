{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">工作组管理</li>
            </ul>
        </div> 
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:120px;">
            <select name="status">
                <option value="">选择状态</option>
                <option value="1">启用</option>
                <option value="0">禁用</option>
            </select>
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，工作组名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_group" lay-filter="table_group"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 添加工作组</span>
    </button>
  </div>
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, tool = layui.tool;
        
        layui.pageTable = table.render({
            elem: "#table_group"
            ,title: "工作组管理列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/Produce/processGroup/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,cols: [[ //表头
                {
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'name',
                    title: '工作组名称',
                    minWidth: 150,
                    templet: '<div><a data-href="/Produce/processGroup/view/id/{{d.id}}.html" class="side-a">{{d.name}}</a></div>'
                },{
                    field: 'process_count',
                    title: '工序数量',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return '<span class="layui-badge layui-bg-blue">' + (d.process_count || 0) + '</span>';
                    }
                },{
                    field: 'sort',
                    title: '排序',
                    align: 'center',
                    width: 80
                },{
                    field: 'status_name',
                    title: '状态',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        var className = d.status == 1 ? 'layui-bg-green' : 'layui-bg-gray';
                        return '<span class="layui-badge ' + className + '">' + d.status_name + '</span>';
                    }
                },{
                    field: 'create_time',
                    title: '创建时间',
                    align: 'center',
                    width: 160
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 200,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
                        var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
                        var btn3 = d.status == 1 ? 
                            '<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="disable">禁用</span>' :
                            '<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="enable">启用</span>';
                        return html+btn0+btn1+btn2+btn3+'</div>';
                    }                        
                }
            ]]
        });
        
        //表头工具栏事件
        table.on('toolbar(table_group)', function(obj){
            if (obj.event === 'add'){
                tool.side("/Produce/processGroup/add");
                return;
            }
        });    
            
        table.on('tool(table_group)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/Produce/processGroup/view?id="+data.id);
                return;
            }
            if (obj.event === 'edit') {
                tool.side("/Produce/processGroup/add?id="+data.id);
                return;
            }
            if (obj.event === 'del') {
                layer.confirm('确定要删除该工作组吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.del();
                        }
                    }
                    tool.post("/Produce/processGroup/delete", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'enable' || obj.event === 'disable') {
                var status = obj.event === 'enable' ? 1 : 0;
                var statusText = obj.event === 'enable' ? '启用' : '禁用';
                layer.confirm('确定要' + statusText + '该工作组吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/Produce/processGroup/updateStatus", { id: data.id, status: status }, callback);
                    layer.close(index);
                });
                return;
            }
        });
    }
</script>
{/block}