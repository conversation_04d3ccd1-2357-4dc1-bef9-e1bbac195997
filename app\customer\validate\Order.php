<?php
namespace app\customer\validate;

use think\Validate;

class Order extends Validate
{
    protected $rule = [
        'customer_id' => 'require|number',
        'order_type' => 'require|in:1,2',
        'delivery_date' => 'require|number',
        'items' => 'require|array',
        'items.*.product_id' => 'require|number',
        'items.*.quantity' => 'require|float|gt:0',
        'items.*.unit_price' => 'require|float|gt:0',
        'items.*.tax_rate' => 'require|float|egt:0|elt:100'
    ];
    
    protected $message = [
        'customer_id.require' => '请选择客户',
        'customer_id.number' => '客户ID格式错误',
        'order_type.require' => '请选择订单类型',
        'order_type.in' => '订单类型错误',
        'delivery_date.require' => '请选择交期',
        'delivery_date.number' => '交期格式错误',
        'items.require' => '请添加订单明细',
        'items.array' => '订单明细格式错误',
        'items.*.product_id.require' => '请选择产品',
        'items.*.product_id.number' => '产品ID格式错误',
        'items.*.quantity.require' => '请输入数量',
        'items.*.quantity.float' => '数量必须为数字',
        'items.*.quantity.gt' => '数量必须大于0',
        'items.*.unit_price.require' => '请输入单价',
        'items.*.unit_price.float' => '单价必须为数字',
        'items.*.unit_price.gt' => '单价必须大于0',
        'items.*.tax_rate.require' => '请输入税率',
        'items.*.tax_rate.float' => '税率必须为数字',
        'items.*.tax_rate.egt' => '税率不能小于0',
        'items.*.tax_rate.elt' => '税率不能大于100'
    ];
    
    protected $scene = [
        'add' => ['customer_id', 'order_type', 'delivery_date', 'items'],
        'edit' => ['customer_id', 'order_type', 'delivery_date', 'items']
    ];
} 