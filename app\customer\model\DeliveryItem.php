<?php
namespace app\customer\model;

use think\Model;

class DeliveryItem extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'customer_order_delivery_item';
    
    // 设置主键
    protected $pk = 'id';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    /**
     * 关联发货指令
     */
    public function delivery()
    {
        return $this->belongsTo('Delivery', 'delivery_id', 'id');
    }
    
    /**
     * 关联订单商品
     */
    public function orderDetail()
    {
        return $this->belongsTo('OrderDetail', 'order_item_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\\engineering\\model\\Product', 'product_id', 'id');
    }
} 