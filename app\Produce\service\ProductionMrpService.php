<?php

declare(strict_types=1);

namespace app\Produce\service;

use think\facade\Db;
use think\facade\Log;
use app\warehouse\service\InventoryLockService;

/**
 * 生产订单MRP物料需求计算服务
 */
class ProductionMrpService
{
    // 物料需求状态常量
    const STATUS_PENDING = 1;      // 待分配
    const STATUS_PARTIAL = 2;      // 部分分配
    const STATUS_ALLOCATED = 3;    // 完全分配
    const STATUS_LOCKED = 4;       // 已锁定

    // 生产订单物料状态常量
    const MATERIAL_STATUS_PENDING = 0;    // 待分配
    const MATERIAL_STATUS_PARTIAL = 1;    // 部分齐套
    const MATERIAL_STATUS_COMPLETE = 2;   // 完全齐套

    /**
     * 处理生产订单的物料需求
     * @param int $orderId 生产订单ID
     * @param int $productId 产品ID
     * @param float $quantity 生产数量
     * @param int $requiredDate 需求日期
     * @return array
     */
    public function processMaterialRequirements($orderId, $productId, $quantity, $requiredDate): array
    {
        try {
            // 确保数据类型正确
            $orderId = (int)$orderId;
            $productId = (int)$productId;
            $quantity = (float)$quantity;
            $requiredDate = (int)$requiredDate;

            Log::info('开始处理生产订单物料需求', [
                'order_id' => $orderId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'required_date' => date('Y-m-d', $requiredDate)
            ]);

            // 1. 清理已存在的物料需求记录
            $this->clearExistingRequirements($orderId);

            // 2. 多层级BOM展开
            $materialRequirements = $this->expandBomRecursive($productId, $quantity);

            if (empty($materialRequirements)) {
                Log::warning('产品没有BOM信息', ['product_id' => $productId]);
                return ['code' => 1, 'msg' => '产品没有BOM信息，无法计算物料需求'];
            }

            // 3. 处理每个物料需求
            $processedCount = 0;
            $allocationCount = 0;
            $lockCount = 0;

            foreach ($materialRequirements as $requirement) {
                // 添加订单ID信息到需求数组中
                $requirement['order_id'] = $orderId;

                // 保存物料需求记录
                $requirementId = $this->saveMaterialRequirement($orderId, $requirement, $requiredDate);

                if ($requirementId) {
                    // 处理库存分配
                    $result = $this->processInventoryAllocation($requirementId, $requirement);
                    
                    if ($result['status'] == self::STATUS_LOCKED) {
                        $lockCount++;
                    } elseif ($result['status'] == self::STATUS_ALLOCATED) {
                        $allocationCount++;
                    }
                    
                    $processedCount++;
                }
            }

            // 4. 更新生产订单状态
            $this->updateProductionOrderStatus($orderId);

            Log::info('生产订单物料需求处理完成', [
                'order_id' => $orderId,
                'total_materials' => $processedCount,
                'locked_materials' => $lockCount,
                'allocation_requests' => $allocationCount
            ]);

            return [
                'code' => 0,
                'msg' => '物料需求处理完成',
                'data' => [
                    'total_materials' => $processedCount,
                    'locked_materials' => $lockCount,
                    'allocation_requests' => $allocationCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('处理生产订单物料需求失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ['code' => 1, 'msg' => '处理物料需求失败：' . $e->getMessage()];
        }
    }

    /**
     * 多层级BOM展开（递归）
     * @param int $productId 产品ID
     * @param float $quantity 数量
     * @param int $level BOM层级
     * @param int $parentMaterialId 父物料ID
     * @return array
     */
    private function expandBomRecursive($productId, $quantity, $level = 1, $parentMaterialId = 0): array
    {
        $requirements = [];

        // 确保数据类型正确
        $productId = (int)$productId;
        $quantity = (float)$quantity;
        $level = (int)$level;
        $parentMaterialId = (int)$parentMaterialId;

        // 防止无限递归
        if ($level > 5) {
            Log::warning('BOM层级过深，停止展开', ['product_id' => $productId, 'level' => $level]);
            return $requirements;
        }

        // 获取当前产品的BOM
        $bomItems = $this->getBomItems($productId);

        foreach ($bomItems as $item) {
            // 计算需求量（含损耗）
            $requiredQty = $quantity * $item['quantity'] * (1 + ($item['loss_rate'] ?? 0) / 100);

            // 记录当前层级需求
            $requirements[] = [
                'material_id' => $item['material_id'],
                'material_code' => $item['material_code'] ?? '',
                'material_name' => $item['material_name'] ?? '',
                'material_specs' => $item['specifications'] ?? '',
                'material_unit' => $item['unit'] ?? '',
                'bom_level' => $level,
                'parent_material_id' => $parentMaterialId,
                'bom_quantity' => $item['quantity'],
                'loss_rate' => $item['loss_rate'] ?? 0,
                'required_quantity' => $requiredQty,
                'warehouse_id' => 0 // 默认仓库，后续可以根据策略选择
            ];

            // 检查是否还有下级BOM（递归）
            if ($this->hasProductBom($item['material_id'])) {
                $subRequirements = $this->expandBomRecursive(
                    $item['material_id'], 
                    $requiredQty, 
                    $level + 1, 
                    $item['material_id']
                );
                $requirements = array_merge($requirements, $subRequirements);
            }
        }

        return $requirements;
    }

    /**
     * 获取产品BOM明细
     * @param int $productId 产品ID
     * @return array
     */
    private function getBomItems($productId): array
    {
        try {
            $productId = (int)$productId;

            // 获取产品的BOM
            $bom = Db::name('material_bom')
                ->where('product_id', $productId)
                ->where('status', 1)
                ->where('delete_time', 0)
                ->order('version desc')
                ->find();

            if (!$bom) {
                return [];
            }

            // 获取BOM明细
            $bomItems = Db::name('material_bom_detail')
                ->where('bom_id', $bom['id'])
                ->where('delete_time', 0)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            return $bomItems;

        } catch (\Exception $e) {
            Log::error('获取BOM明细失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 检查产品是否有BOM
     * @param int $productId 产品ID
     * @return bool
     */
    private function hasProductBom($productId): bool
    {
        try {
            $productId = (int)$productId;

            $count = Db::name('material_bom')
                ->where('product_id', $productId)
                ->where('status', 1)
                ->where('delete_time', 0)
                ->count();

            return $count > 0;

        } catch (\Exception $e) {
            Log::error('检查产品BOM失败', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 保存物料需求记录
     * @param int $orderId 生产订单ID
     * @param array $requirement 需求信息
     * @param int $requiredDate 需求日期
     * @return int
     */
    private function saveMaterialRequirement($orderId, array $requirement, $requiredDate): int
    {
        try {
            $orderId = (int)$orderId;
            $requiredDate = (int)$requiredDate;

            // 获取生产订单信息
            $order = Db::name('produce_order')->where('id', $orderId)->find();
            if (!$order) {
                throw new \Exception('生产订单不存在');
            }

            $data = [
                'order_id' => $orderId,
                'order_no' => $order['order_no'],
                'material_id' => $requirement['material_id'],
                'material_code' => $requirement['material_code'],
                'material_name' => $requirement['material_name'],
                'material_specs' => $requirement['material_specs'],
                'material_unit' => $requirement['material_unit'],
                'bom_level' => $requirement['bom_level'],
                'parent_material_id' => $requirement['parent_material_id'],
                'bom_quantity' => $requirement['bom_quantity'],
                'loss_rate' => $requirement['loss_rate'],
                'required_quantity' => $requirement['required_quantity'],
                'allocated_quantity' => 0,
                'locked_quantity' => 0,
                'shortage_quantity' => $requirement['required_quantity'],
                'required_date' => $requiredDate,
                'status' => self::STATUS_PENDING,
                'allocation_request_id' => 0,
                'warehouse_id' => $requirement['warehouse_id'],
                'notes' => '',
                'create_time' => time(),
                'update_time' => time()
            ];

            return Db::name('produce_order_material_requirement')->insertGetId($data);

        } catch (\Exception $e) {
            Log::error('保存物料需求记录失败', [
                'order_id' => $orderId,
                'material_id' => $requirement['material_id'],
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 处理库存分配
     * @param int $requirementId 需求记录ID
     * @param array $requirement 需求信息
     * @return array
     */
    private function processInventoryAllocation($requirementId, array $requirement): array
    {
        try {
            $requirementId = (int)$requirementId;
            $materialId = $requirement['material_id'];
            $requiredQty = $requirement['required_quantity'];
            $warehouseId = $requirement['warehouse_id'] ?: 0;

            // 获取可用库存（使用现有服务）
            $inventoryStatus = app('app\warehouse\service\InventoryLockService')
                ->getProductInventoryStatus($materialId, $warehouseId);

            // 获取安全库存
            $safetyStock = $this->getSafetyStock($materialId);

            // 计算可用库存（总库存 - 已锁定 - 安全库存）
            $availableQty = max(0, $inventoryStatus['available_quantity'] - $safetyStock);

            Log::info('检查物料库存状态', [
                'material_id' => $materialId,
                'required_quantity' => $requiredQty,
                'available_quantity' => $availableQty,
                'safety_stock' => $safetyStock
            ]);

            if ($availableQty >= $requiredQty) {
                // 库存充足：直接锁定
                return $this->lockInventoryDirect($requirementId, $requirement);
            } else {
                // 库存不足：创建分配请求
                return $this->createAllocationRequest($requirementId, $requirement, $availableQty);
            }

        } catch (\Exception $e) {
            Log::error('处理库存分配失败', [
                'requirement_id' => $requirementId,
                'material_id' => $requirement['material_id'],
                'error' => $e->getMessage()
            ]);

            return [
                'status' => self::STATUS_PENDING,
                'message' => '处理库存分配失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 直接锁定库存
     * @param int $requirementId 需求记录ID
     * @param array $requirement 需求信息
     * @return array
     */
    private function lockInventoryDirect($requirementId, array $requirement): array
    {
        try {
            $lockResult = app('app\warehouse\service\InventoryLockService')->lockInventory([
                'product_id' => $requirement['material_id'],
                'quantity' => $requirement['required_quantity'],
                'warehouse_id' => $requirement['warehouse_id'] ?: 0,
                'ref_type' => 'production_order',
                'ref_id' => $requirement['order_id'] ?? 0,
                'notes' => "生产订单物料锁定 - {$requirement['material_name']}"
            ]);

            if ($lockResult['code'] == 0) {
                // 更新需求记录状态
                Db::name('produce_order_material_requirement')
                    ->where('id', $requirementId)
                    ->update([
                        'locked_quantity' => $requirement['required_quantity'],
                        'shortage_quantity' => 0,
                        'status' => self::STATUS_LOCKED,
                        'update_time' => time()
                    ]);

                Log::info('库存锁定成功', [
                    'requirement_id' => $requirementId,
                    'material_id' => $requirement['material_id'],
                    'locked_quantity' => $requirement['required_quantity']
                ]);

                return [
                    'status' => self::STATUS_LOCKED,
                    'message' => '库存锁定成功'
                ];
            } else {
                throw new \Exception($lockResult['msg']);
            }

        } catch (\Exception $e) {
            Log::error('库存锁定失败', [
                'requirement_id' => $requirementId,
                'material_id' => $requirement['material_id'],
                'error' => $e->getMessage()
            ]);

            return [
                'status' => self::STATUS_PENDING,
                'message' => '库存锁定失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建库存分配请求
     * @param int $requirementId 需求记录ID
     * @param array $requirement 需求信息
     * @param float $availableQty 可用库存量
     * @return array
     */
    private function createAllocationRequest($requirementId, array $requirement, $availableQty = 0): array
    {
        try {
            // 获取生产订单信息用于计算优先级
            $orderId = $requirement['order_id'] ?? 0;
            $order = Db::name('produce_order')->where('id', $orderId)->find();

            Log::info('创建库存分配请求 - 订单信息', [
                'requirement_order_id' => $orderId,
                'order_found' => !empty($order),
                'order_no' => $order['order_no'] ?? 'null',
                'material_id' => $requirement['material_id']
            ]);

            $allocationData = [
                'product_id' => $requirement['material_id'],
                'warehouse_id' => $requirement['warehouse_id'] ?: 0,
                'quantity' => $requirement['required_quantity'],
                'allocated_quantity' => 0,
                'ref_type' => 'production_order',
                'ref_id' => $orderId,
                'ref_no' => $order['order_no'] ?? '',
                'priority' => $this->calculatePriority($requirement, $order),
                'status' => 1, // 待分配
                'request_time' => time(),
                'notes' => "生产订单物料需求 - {$requirement['material_name']}",
                'created_by' => 0,
                'create_time' => time(),
                'update_time' => time()
            ];

            // 插入到现有的库存分配请求表
            $allocationRequestId = Db::name('inventory_allocation_request')->insertGetId($allocationData);

            // 更新物料需求记录
            $updateData = [
                'allocation_request_id' => $allocationRequestId,
                'status' => self::STATUS_PENDING,
                'update_time' => time()
            ];

            // 如果有部分库存可用，先锁定部分
            if ($availableQty > 0) {
                $partialLockResult = $this->lockPartialInventory($requirementId, $requirement, $availableQty);
                if ($partialLockResult['success']) {
                    $updateData['locked_quantity'] = $availableQty;
                    $updateData['shortage_quantity'] = $requirement['required_quantity'] - $availableQty;
                    $updateData['status'] = self::STATUS_PARTIAL;
                }
            }

            Db::name('produce_order_material_requirement')
                ->where('id', $requirementId)
                ->update($updateData);

            Log::info('创建库存分配请求成功', [
                'requirement_id' => $requirementId,
                'allocation_request_id' => $allocationRequestId,
                'material_id' => $requirement['material_id'],
                'required_quantity' => $requirement['required_quantity'],
                'available_quantity' => $availableQty
            ]);

            return [
                'status' => $availableQty > 0 ? self::STATUS_PARTIAL : self::STATUS_PENDING,
                'message' => '创建库存分配请求成功',
                'allocation_request_id' => $allocationRequestId
            ];

        } catch (\Exception $e) {
            Log::error('创建库存分配请求失败', [
                'requirement_id' => $requirementId,
                'material_id' => $requirement['material_id'],
                'error' => $e->getMessage()
            ]);

            return [
                'status' => self::STATUS_PENDING,
                'message' => '创建库存分配请求失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 部分锁定库存
     * @param int $requirementId 需求记录ID
     * @param array $requirement 需求信息
     * @param float $lockQty 锁定数量
     * @return array
     */
    private function lockPartialInventory($requirementId, array $requirement, $lockQty): array
    {
        try {
            $requirementId = (int)$requirementId;
            $lockQty = (float)$lockQty;

            $lockResult = app('app\warehouse\service\InventoryLockService')->lockInventory([
                'product_id' => $requirement['material_id'],
                'quantity' => $lockQty,
                'warehouse_id' => $requirement['warehouse_id'] ?: 0,
                'ref_type' => 'production_order',
                'ref_id' => $requirement['order_id'] ?? 0,
                'notes' => "生产订单物料部分锁定 - {$requirement['material_name']}"
            ]);

            if ($lockResult['code'] == 0) {
                Log::info('部分库存锁定成功', [
                    'requirement_id' => $requirementId,
                    'material_id' => $requirement['material_id'],
                    'locked_quantity' => $lockQty
                ]);

                return ['success' => true, 'message' => '部分库存锁定成功'];
            } else {
                throw new \Exception($lockResult['msg']);
            }

        } catch (\Exception $e) {
            Log::error('部分库存锁定失败', [
                'requirement_id' => $requirementId,
                'material_id' => $requirement['material_id'],
                'lock_quantity' => $lockQty,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => '部分库存锁定失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取物料安全库存
     * @param int $materialId 物料ID
     * @return float
     */
    private function getSafetyStock($materialId): float
    {
        try {
            $materialId = (int)$materialId;
            $product = Db::name('product')->where('id', $materialId)->find();
            return floatval($product['safety_stock'] ?? 0);
        } catch (\Exception $e) {
            Log::error('获取安全库存失败', [
                'material_id' => $materialId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 计算优先级
     * @param array $requirement 需求信息
     * @param array|null $order 生产订单信息
     * @return int
     */
    private function calculatePriority(array $requirement, ?array $order = null): int
    {
        // 基础优先级
        $basePriority = 50;

        // 根据生产订单优先级调整
        $orderPriority = $order['priority'] ?? 1;
        $orderBonus = $orderPriority * 10;

        // 根据BOM层级调整（层级越低优先级越高）
        $levelBonus = (6 - ($requirement['bom_level'] ?? 1)) * 5;

        // 根据需求日期调整（越紧急优先级越高）
        $dateBonus = $this->calculateDatePriority($requirement['required_date'] ?? time());

        return $basePriority + $orderBonus + $levelBonus + $dateBonus;
    }

    /**
     * 根据日期计算优先级加分
     * @param int $requiredDate 需求日期
     * @return int
     */
    private function calculateDatePriority($requiredDate): int
    {
        $requiredDate = (int)$requiredDate;
        $now = time();
        $daysDiff = ($requiredDate - $now) / 86400;

        if ($daysDiff <= 0) {
            return 30; // 已过期或当天
        } elseif ($daysDiff <= 3) {
            return 20; // 3天内
        } elseif ($daysDiff <= 7) {
            return 10; // 一周内
        } elseif ($daysDiff <= 30) {
            return 5;  // 一个月内
        } else {
            return 0;  // 超过一个月
        }
    }

    /**
     * 清理已存在的物料需求记录
     * @param int $orderId 生产订单ID
     */
    private function clearExistingRequirements($orderId): void
    {
        try {
            $orderId = (int)$orderId;

            // 删除物料需求记录
            Db::name('produce_order_material_requirement')
                ->where('order_id', $orderId)
                ->delete();

            Log::info('清理已存在的物料需求记录完成', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            Log::error('清理已存在的物料需求记录失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新生产订单状态
     * @param int $orderId 生产订单ID
     */
    private function updateProductionOrderStatus($orderId): void
    {
        try {
            $orderId = (int)$orderId;

            $requirements = Db::name('produce_order_material_requirement')
                ->where('order_id', $orderId)
                ->select();

            $totalCount = count($requirements);
            $allocatedCount = 0;
            $lockedCount = 0;

            foreach ($requirements as $req) {
                if (in_array($req['status'], [self::STATUS_ALLOCATED, self::STATUS_LOCKED])) {
                    $allocatedCount++;
                }
                if ($req['status'] == self::STATUS_LOCKED) {
                    $lockedCount++;
                }
            }

            // 计算物料状态
            $materialStatus = self::MATERIAL_STATUS_PENDING;
            if ($lockedCount == $totalCount && $totalCount > 0) {
                $materialStatus = self::MATERIAL_STATUS_COMPLETE;
            } elseif ($allocatedCount > 0) {
                $materialStatus = self::MATERIAL_STATUS_PARTIAL;
            }

            // 更新生产订单状态
            Db::name('produce_order')
                ->where('id', $orderId)
                ->update([
                    'material_status' => $materialStatus,
                    'update_time' => time()
                ]);

        } catch (\Exception $e) {
            Log::error('更新生产订单状态失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
