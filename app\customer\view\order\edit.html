{extend name="../../base/view/common/base" /}

{block name="body"}
<style>
/* 父子商品关系样式 */
.child-product {
    background-color: #f8f8f8 !important; 
    border-left: 3px solid #1E9FFF !important;
}
.layui-table-view .layui-table td.child-product-name,
.child-product-name {
    padding-left: 30px !important; 
    position: relative !important;
}
.layui-table-view .layui-table td.child-product-name:before,
.child-product-name:before {
    content: "└─" !important;
    position: absolute !important;
    left: 8px !important;
    color: #1E9FFF !important;
    font-weight: bold !important;
    font-size: 16px !important;
}
/* 父商品标记 */
.parent-product-marker {
    display: inline-block !important;
    background-color: #1E9FFF !important;
    color: white !important;
    font-size: 12px !important;
    padding: 0 5px !important;
    border-radius: 3px !important;
    margin-right: 5px !important;
    vertical-align: middle !important;
    z-index: 10 !important;
}
/* 子商品标记 */
.child-product-marker {
    display: inline-block !important;
    background-color: #FF9800 !important;
    color: white !important;
    font-size: 12px !important;
    padding: 0 5px !important;
    border-radius: 3px !important;
    margin-right: 5px !important;
    vertical-align: middle !important;
    z-index: 10 !important;
}
/* 确保删除按钮始终显示 */
.layui-btn-danger[lay-event="del"] {
    display: inline-block !important;
    visibility: visible !important;
}
/* 强制子商品行样式 */
.layui-table-view .layui-table tr.child-product td {
    background-color: #f8f8f8 !important;
}
/* 确保标记不被覆盖 */
.layui-table-cell .parent-product-marker,
.layui-table-cell .child-product-marker {
    position: relative !important;
    display: inline-block !important;
}
</style>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">编辑销售订单</div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="orderForm">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_no" value="{$order.order_no|default=''}" readonly class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="order_date" id="orderDate" placeholder="请选择订单日期" value="{$order.delivery_date|default=''}" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">客户</label>
                            <div class="layui-input-block">
                                <select name="customer_id" lay-verify="required" lay-filter="customer" lay-search>
                                    <option value="">请选择客户</option>
                                    {volist name=":get_customer_list()" id="vo"}
                                    <option value="{$vo.id}" {if $order.customer_id == $vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">订单类型</label>
                            <div class="layui-input-block">
                                <select name="order_type" lay-verify="required">
                                    <option value="">请选择订单类型</option>
                                    <option value="1" {if $order.order_type == 1}selected{/if}>现货单</option>
                                    <option value="2" {if $order.order_type == 2}selected{/if}>账期单</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
               
                
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">预计交期</label>
                            <div class="layui-input-block">
                                <input type="text" name="delivery_date" id="deliveryDate" placeholder="请选择交货日期" value="{$order.delivery_date|default=''}" lay-verify="required" class="layui-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">税率</label>
                            <div class="layui-input-block">
                                <select name="tax_rate" lay-verify="required" lay-filter="tax_rate">
                                    <option value="0" {if $order.tax_rate == 0}selected{/if}>不含税</option>
                                    <option value="3" {if $order.tax_rate == 3}selected{/if}>3%</option>
                                    <option value="9" {if $order.tax_rate == 9}selected{/if}>9%</option>
                                    <option value="13" {if $order.tax_rate == 13}selected{/if}>13%</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">备注说明</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入备注说明" class="layui-textarea">{$order.remark|default=''}</textarea>
                    </div>
                </div>
                
                <fieldset class="layui-elem-field">
                    <legend>商品明细</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <button type="button" class="layui-btn layui-btn-normal" id="btnAddProduct">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加商品
                                </button>
                                <button type="button" class="layui-btn layui-btn-danger" id="btnRemoveProduct">
                                    <i class="layui-icon layui-icon-delete"></i> 删除选中
                                </button>
                            </div>
                        </div>
                        
                        <table class="layui-table" id="productTable" lay-filter="productTable">
                            <thead>
                                <tr>
                                    <th lay-data="{type:'checkbox', fixed:'left'}"></th>
                                    <th lay-data="{field:'row_number', width:80, templet:function(d){
                                        // 只有父商品（非子商品）显示序号
                                        if(!d.is_child) {
                                            // 使用全局序号变量递增
                                            window.parent_index = (window.parent_index || 0) + 1;
                                            return window.parent_index;
                                        }
                                        return '';
                                    }}">序号</th>
                                    <th lay-data="{field:'material_code', width:180}">材料编码</th>
                                    <th lay-data="{field:'product_name', width:180}">商品名称</th>
                                    <th lay-data="{field:'product_specs', width:120}">规格型号</th>
                                    <th lay-data="{field:'unit', width:80}">单位</th>
                                    <th lay-data="{field:'quantity', width:100, edit:function(d){ return !d.is_child ? 'text' : false; }}">数量</th>
                                    <th lay-data="{field:'multiplier', width:80, edit:'text'}">每套/件</th>
                                    <th lay-data="{field:'price', width:120, edit:'text'}">单价</th>
                                    <th lay-data="{field:'tax_rate', width:100, edit:'text'}">税率(%)</th>
                                    <th lay-data="{field:'amount', width:120}">金额</th>
                                    <th lay-data="{field:'tax_amount', width:120}">税额</th>
                                    <th lay-data="{field:'total_amount', width:120}">价税合计</th>
                                    <th lay-data="{field:'notes', width:180, edit:'text'}">备注</th>
                                    <th lay-data="{field:'attachment', title:'附件', width:100, templet:'#attachmentTpl'}">附件</th>
                                    <th lay-data="{width:180, toolbar:'#productBar', fixed:'right'}">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 动态添加的商品行 -->
                            </tbody>
                        </table>
                        
                        <script type="text/html" id="productBar">
                            {{# if(d.is_child !== true){ }}
                            <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="addChild">添加子商品</a>
                            {{# } }}
                            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del" style="display: inline-block !important;">删除</a>
                        </script>

                        <script type="text/html" id="attachmentTpl">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" lay-event="upload">
                                {{# if(d.attachments && d.attachments.length > 0){ }}
                                <i class="layui-icon layui-icon-file"></i> 附件({{d.attachments.length}})
                                {{# } else if(d.attachment_id){ }}
                                <i class="layui-icon layui-icon-file"></i> 查看
                                {{# }else{ }}
                                <i class="layui-icon layui-icon-upload"></i> 上传
                                {{# } }}
                            </button>
                        </script>
                    </div>
                </fieldset>
                
                <div class="layui-form-item text-right">
                    <div class="layui-input-block">
                        <span>订单总金额: </span>
                        <span id="totalAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">总税额: </span>
                        <span id="totalTaxAmount" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                        <span style="margin-left: 20px;">价税合计: </span>
                        <span id="totalAmountWithTax" style="font-size: 18px; color: #FF5722; font-weight: bold;">￥0.00</span>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="hidden" name="__token__" value="{:token()}">
                        <input type="hidden" name="id" value="{$order.id}">
                        <button type="button" class="layui-btn" lay-submit lay-filter="saveOrder">保存订单</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="btnCancel">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 文件上传对话框 -->
<div id="fileUploadDialog" class="layui-form" style="display: none; padding: 20px;">
    <div class="layui-form-item">
        <div class="layui-upload-drag" id="fileUploadArea">
            <i class="layui-icon layui-icon-upload"></i>
            <p>点击上传或将文件拖拽到此处</p>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-upload-list">
            <input type="file" id="attachment-upload" name="file" style="display: none;">
            <div id="uploadedFileList" style="margin-top: 10px;">
                <!-- 文件项将动态添加到这里 -->
            </div>
            <div id="noFileInfo" style="text-align: center; padding: 20px; color: #999;">
                <i class="layui-icon layui-icon-file" style="font-size: 30px;"></i>
                <p>暂无附件</p>
            </div>
        </div>
    </div>
    <div class="layui-form-item" style="text-align: center;">
        <button type="button" class="layui-btn" id="btnConfirmUpload">确定</button>
        <button type="button" class="layui-btn layui-btn-primary" id="btnCancelUpload">取消</button>
    </div>
</div>

<!-- 隐藏的数据字段，用于存储当前编辑的产品ID -->
<input type="hidden" id="currentProductId">
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'uploadPlus'];
    
    // 订单明细数据
    var itemsData = JSON.parse('{$items|json_encode|raw}');
    
    // 如果itemsData未定义，设置为空数组
    if (typeof itemsData === 'undefined') {
        itemsData = [];
        console.warn("订单明细数据未定义，使用空数组");
    }
    
    // 定义全局计算总金额函数，供子页面调用
    window.calculateTotal = function(){
        var table = layui.table;
        var data = table.cache.productTable || [];
        var total = 0;
        var totalTax = 0;
        var totalWithTax = 0;
        
        for(var i = 0; i < data.length; i++){
            if(data[i].amount){
                total += parseFloat(data[i].amount);
            }
            if(data[i].tax_amount){
                totalTax += parseFloat(data[i].tax_amount);
            }
            if(data[i].total_amount){
                totalWithTax += parseFloat(data[i].total_amount);
            }
        }
        
        document.getElementById('totalAmount').innerText = '￥' + total.toFixed(2);
        document.getElementById('totalTaxAmount').innerText = '￥' + totalTax.toFixed(2);
        document.getElementById('totalAmountWithTax').innerText = '￥' + totalWithTax.toFixed(2);
        
        return {
            total: total,
            tax: totalTax,
            withTax: totalWithTax
        };
    };
    
    // 定义全局的styleChildProducts函数，供子页面调用
    window.styleChildProducts = function() {
        // 确保layui对象可用
        if (!layui || !layui.table) {
            console.error("layui对象不可用");
            return;
        }
        
        var table = layui.table;
        var tableData = table.cache.productTable || [];
        
        console.log("开始应用子商品样式，表格数据长度:", tableData.length);
        
        // 调试信息：输出所有行的is_child和parent_product_id
        tableData.forEach(function(item, index) {
            console.log("行", index, "- 商品:", item.product_name, 
                      "is_child:", item.is_child, 
                      "product_id:", item.product_id, 
                      "parent_product_id:", item.parent_product_id);
        });
        
        // 遍历表格数据，为子商品添加样式
        tableData.forEach(function(item, index) {
            if (item.is_child) {
                console.log("处理子商品样式 - 行", index, "商品:", item.product_name);
                
                var tr = $('#productTable').next().find('tr[data-index="' + index + '"]');
                if (tr.length === 0) {
                    console.error("未找到子商品对应的表格行:", index);
                    return;
                }
                
                // 添加子商品样式
                tr.addClass('child-product');
                
                // 为商品名称添加缩进和标识
                var productNameTd = tr.find('td[data-field="product_name"]').find('.layui-table-cell');
                if (productNameTd.length === 0) {
                    console.error("未找到子商品的商品名称单元格:", index);
                    return;
                }
                
                if (!productNameTd.find('.child-product-marker').length) {
                    // 添加子商品标记和缩进
                    console.log("为子商品添加标记:", item.product_name);
                    var newHtml = '<span class="child-product-marker">子品</span>' + productNameTd.html();
                    productNameTd.html(newHtml);
                    productNameTd.addClass('child-product-name');
                }
                
                // 确保操作列按钮正确显示
                var operationTd = tr.find('td[data-content="操作"]');
                if(operationTd.length > 0) {
                    operationTd.find('.layui-btn').show();
                    operationTd.find('.layui-btn-danger').css('display', 'inline-block !important');
                }
            } else {
                console.log("处理父商品样式 - 行", index, "商品:", item.product_name);
                
                // 为父商品添加标记
                var tr = $('#productTable').next().find('tr[data-index="' + index + '"]');
                if (tr.length === 0) {
                    console.error("未找到父商品对应的表格行:", index);
                    return;
                }
                
                var productNameTd = tr.find('td[data-field="product_name"]').find('.layui-table-cell');
                if (productNameTd.length === 0) {
                    console.error("未找到父商品的商品名称单元格:", index);
                    return;
                }
                
                if (!productNameTd.find('.parent-product-marker').length) {
                    // 添加父商品标记
                    console.log("为父商品添加标记:", item.product_name);
                    var newHtml = '<span class="parent-product-marker">主品</span>' + productNameTd.html();
                    productNameTd.html(newHtml);
                }
                
                // 确保操作列按钮正确显示
                var operationTd = tr.find('td[data-content="操作"]');
                if(operationTd.length > 0) {
                    operationTd.find('.layui-btn').show();
                    operationTd.find('.layui-btn-danger').css('display', 'inline-block !important');
                }
            }
        });
        
        // 调试信息
        console.log("样式应用完成，检查操作列按钮");
        $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
            console.log("第", i, "行操作列:", $(el).html());
            // 强制显示删除按钮
            $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
            $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
        });
    };
    
    // 定义全局的addChildToParent函数，供子页面调用
    window.addChildToParent = function(childProducts) {
        // 确保layui对象可用
        if (!layui || !layui.table) {
            console.error("layui对象不可用");
            return;
        }
        
        var table = layui.table;
        var tableData = table.cache.productTable || [];
        var parentData = window.currentParentData; // 使用全局变量存储父商品数据
        
        if (!parentData) {
            layer.msg('未找到父商品数据', {icon: 2});
            return;
        }
        
        // 添加调试信息
        console.log("添加子商品 - 父商品:", parentData.product_name, "product_id:", parentData.product_id);
        
        if (!parentData.product_id) {
            console.error("错误：父商品没有product_id");
            layer.msg('无法添加子商品：父商品标识符缺失', {icon: 2});
            return;
        }
        
        var parentIndex = -1;
        
        // 使用product_id查找父商品在当前表格数据中的索引（更可靠）
        for (var i = 0; i < tableData.length; i++) {
            if (tableData[i].product_id === parentData.product_id) {
                parentIndex = i;
                break;
            }
        }
        
        if (parentIndex === -1) {
            console.error("未找到父商品，父商品ID:", parentData.product_id);
            layer.msg('未找到父商品', {icon: 2});
            return;
        }
        
        // 插入子商品到父商品后面
        for (var i = 0; i < childProducts.length; i++) {
            var childProduct = childProducts[i];
            
            // 获取全局税率
            var taxRate = parseFloat($('select[name="tax_rate"]').val() || 0);
            
            // 确保父项数据中有倍数字段，如果没有则默认为1
            if(typeof parentData.multiplier === 'undefined') {
                parentData.multiplier = 1;
                console.log("父项没有倍数字段，设置默认值:", parentData.multiplier);
            }
            
            // 父项数量和倍数
            var parentQuantity = parseFloat(parentData.quantity) || 1;
            var parentMultiplier = parseFloat(parentData.multiplier) || 1;
            
            console.log("子项数量计算 - 父项数量:", parentQuantity, "父项倍数:", parentMultiplier);
            
            // 设置父子关系和缩进标识 - 使用product_id作为父子关系标识
            var newProduct = {
                product_id: childProduct.id,
                parent_product_id: parentData.product_id, // 使用product_id作为父商品标识
                product_name: childProduct.title,
                material_code: childProduct.material_code,
                product_specs: childProduct.specs,
                unit: childProduct.unit,
                // 子商品数量 = 父商品数量 × 子商品倍数
                multiplier: 1, // 子商品默认倍数为1
                quantity: parseFloat(parentData.quantity || 1), // 初始数量等于父项数量
                price: childProduct.purchase_price,
                tax_rate: taxRate,
                amount: childProduct.purchase_price,
                tax_amount: (childProduct.purchase_price * taxRate / 100).toFixed(2),
                total_amount: (parseFloat(childProduct.purchase_price) + parseFloat((childProduct.purchase_price * taxRate / 100).toFixed(2))).toFixed(2),
                is_child: true, // 标记为子商品
                attachments: [], // 附件数组，存储多个附件信息
                attachment_id: '', // 保留兼容旧代码
                delivery_date: parentData.delivery_date || '', // 继承父商品的交期
                notes: ''
            };
            
            console.log("添加子商品:", newProduct.product_name, "product_id:", newProduct.product_id, "parent_product_id:", newProduct.parent_product_id);
            
            // 插入到父商品后面
            tableData.splice(parentIndex + 1, 0, newProduct);
            parentIndex++; // 更新索引，确保下一个子商品插入位置正确
        }
        
        // 重新加载表格
        table.reload('productTable', {
            data: tableData,
            done: function() {
                console.log("表格重载完成");
                // 重新应用样式
                styleChildProducts();
                
                // 确保删除按钮显示
                setTimeout(function() {
                    $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
                        $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
                        $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
                    });
                }, 200);
            }
        });
        
        // 重新计算总金额
        window.calculateTotal();
    };
    
    function gouguInit() {
        var form = layui.form;
        var table = layui.table;
        var laydate = layui.laydate;
        var util = layui.util;
        var tool = layui.tool;
        var $ = layui.jquery;
        
        // 获取订单明细数据
        var orderItems = [];
        if(typeof itemsData !== 'undefined' && itemsData && itemsData.length > 0) {
            orderItems = itemsData;
            console.log("已加载订单明细数据:", orderItems.length, "条");
        } else {
            console.log("未找到订单明细数据");
        }
        
        // 需要处理明细数据，使其符合表格要求
        for (var i = 0; i < orderItems.length; i++) {
            // 确保数据字段和表格一致
            if (!orderItems[i].price && orderItems[i].unit_price) {
                orderItems[i].price = orderItems[i].unit_price;
            }
            
            if (!orderItems[i].total_amount) {
                var amount = parseFloat(orderItems[i].amount || 0);
                var taxAmount = parseFloat(orderItems[i].tax_amount || 0);
                orderItems[i].total_amount = (amount + taxAmount).toFixed(2);
            }
            
            // 确保is_child和parent_product_id属性正确设置
            if (orderItems[i].parent_product_id) {
                // 如果有parent_product_id，则一定是子商品
                orderItems[i].is_child = true;
                console.log("确认子商品标识 - 商品:", orderItems[i].product_name, 
                          "parent_product_id:", orderItems[i].parent_product_id, 
                          "is_child设置为:", orderItems[i].is_child);
            } else {
                // 如果没有parent_product_id，则一定是父商品
                orderItems[i].is_child = false;
                console.log("确认父商品标识 - 商品:", orderItems[i].product_name, 
                          "product_id:", orderItems[i].product_id, 
                          "is_child设置为:", orderItems[i].is_child);
            }
            
            // 添加LAY_TABLE_INDEX字段，避免表格操作错误
            orderItems[i].LAY_TABLE_INDEX = i;
        }
        
        // 渲染日期选择器
        laydate.render({
            elem: '#orderDate'
        });
        
        laydate.render({
            elem: '#deliveryDate'
        });
        
        // 初始化表格
        table.init('productTable', {
            limit: 1000,
            data: orderItems,
            done: function(res, curr, count){
                if(orderItems.length > 0) {
                    console.log("表格加载成功，共", orderItems.length, "条数据");
                } else {
                    console.log("表格数据为空");
                }
                calculateTotal();
                // 为子商品添加样式
                styleChildProducts();
                
                // 确保工具栏正确显示
                console.log("检查工具栏是否正确显示");
                setTimeout(function(){
                    $('#productTable').next().find('td[data-content="操作"]').each(function(i, el){
                        console.log("第", i, "行操作列内容:", $(el).html());
                        // 确保删除按钮显示
                        $(el).find('.layui-btn-danger').css('display', 'inline-block');
                    });
                }, 500);
            }
        });
        
        // 监听单元格编辑
        table.on('edit(productTable)', function(obj){
            var value = obj.value;
            var field = obj.field;
            var data = obj.data;
            
            // 数量、单价或税率变更时，计算金额
            if(field === 'quantity' || field === 'price'){
                // 将输入转为数值并验证
                var val = parseFloat(value);
                if(isNaN(val) || val < 0){
                    layer.msg('请输入有效的数字', {icon: 5});
                    // 恢复原值
                    $(this).val(data[field]);
                    return;
                }
                
                if(field === 'quantity'){
                    data.quantity = val;
                    
                    // 如果是父项，更新所有子项的数量
                    if(!data.is_child) {
                        updateChildrenQuantities(data);
                    }
                }else if(field === 'price'){
                    data.price = val;
                }
                
                // 获取当前选择的税率（如果行中未设置税率）
                if (!data.tax_rate) {
                    data.tax_rate = parseFloat($('select[name="tax_rate"]').val() || 0);
                }
                
                // 计算金额
                data.amount = (data.quantity * data.price).toFixed(2);
                data.tax_amount = (data.amount * data.tax_rate / 100).toFixed(2);
                data.total_amount = (parseFloat(data.amount) + parseFloat(data.tax_amount)).toFixed(2);
                
                // 更新表格这一行的数据
                obj.update({
                    quantity: data.quantity,
                    price: data.price,
                    tax_rate: data.tax_rate,
                    amount: data.amount,
                    tax_amount: data.tax_amount,
                    total_amount: data.total_amount
                });
                
                // 重新计算总金额
                calculateTotal();
            } else if(field === 'multiplier') {
                // 处理倍数变更
                var val = parseFloat(value);
                if(isNaN(val) || val <= 0){
                    layer.msg('倍数必须是大于0的数字', {icon: 5});
                    // 恢复默认值
                    $(this).val(data.multiplier || 1);
                    return;
                }
                
                // 更新倍数
                data.multiplier = val;
                
                if(data.is_child) {
                    // 如果是子项，查找其父项
                    var parentItem = null;
                    for(var i = 0; i < table.cache.productTable.length; i++) {
                        if(!table.cache.productTable[i].is_child && 
                           table.cache.productTable[i].product_id === data.parent_product_id) {
                            parentItem = table.cache.productTable[i];
                            break;
                        }
                    }
                    
                    if(parentItem) {
                        // 更新自身数量 = 父项数量 × 子项倍数
                        var parentQuantity = parseFloat(parentItem.quantity) || 0;
                        data.quantity = parentQuantity * data.multiplier;
                        
                        // 重新计算金额
                        data.amount = (data.quantity * data.price).toFixed(2);
                        data.tax_amount = (data.amount * data.tax_rate / 100).toFixed(2);
                        data.total_amount = (parseFloat(data.amount) + parseFloat(data.tax_amount)).toFixed(2);
                        
                        console.log("子项倍数变更 - 子项:", data.product_name, 
                                  "父项:", parentItem.product_name,
                                  "父项数量:", parentQuantity,
                                  "子项倍数:", data.multiplier,
                                  "新子项数量:", data.quantity);
                        
                        // 更新表格这一行的数据
                        obj.update({
                            multiplier: data.multiplier,
                            quantity: data.quantity,
                            amount: data.amount,
                            tax_amount: data.tax_amount,
                            total_amount: data.total_amount
                        });
                    }
                } else {
                    // 如果是父项，更新所有子项的数量
                    updateChildrenQuantities(data);
                    
                    // 更新表格这一行的数据
                    obj.update({
                        multiplier: data.multiplier
                    });
                }
                
                // 重新计算总金额
                calculateTotal();
            }
        });
        
        // 监听工具条
        table.on('tool(productTable)', function(obj){
            var data = obj.data;
            console.log("工具条事件触发:", obj.event, "数据:", data);
            
            if(obj.event === 'del'){
                // 删除商品
                layer.confirm('确定要删除此商品吗？', function(index){
                    obj.del();
                    calculateTotal();
                    layer.close(index);
                });
            } else if(obj.event === 'addChild') {
                // 添加子商品
                addChildProduct(data);
            } else if(obj.event === 'upload') {
                // 记录当前编辑的商品索引
                currentEditIndex = data.LAY_TABLE_INDEX;
                console.log("点击上传按钮，当前行索引:", currentEditIndex);
                
                // 保存当前商品ID到隐藏字段和localStorage
                var productId = data.product_id || '';
                $('#currentProductId').val(productId);
                try {
                    localStorage.setItem('currentProductId', productId);
                    window.currentProductId = productId;
                } catch (e) {
                    console.error("保存到localStorage失败:", e);
                }
                
                // 显示已有附件（如果有）
                if (data.attachments && data.attachments.length > 0) {
                    console.log("显示已有附件:", data.attachments);
                    initFileList(data.attachments);
                } else if (data.attachment_id) {
                    console.log("显示单个附件ID:", data.attachment_id);
                    // 兼容旧数据结构，创建一个附件对象
                    var attachmentObj = {
                        id: data.attachment_id,
                        attachment_id: data.attachment_id,
                        name: data.attachment_name || '未知文件',
                        attachment_name: data.attachment_name || '未知文件',
                        size: data.attachment_size || 0,
                        attachment_size: data.attachment_size || 0,
                        fileext: data.attachment_ext || '',
                        attachment_ext: data.attachment_ext || '',
                        url: data.attachment_url || '',
                        attachment_url: data.attachment_url || ''
                    };
                    initFileList([attachmentObj]);
                } else {
                    // 清空附件列表
                    $('#uploadedFileList .layui-file-item').remove();
                    $('#noFileInfo').show();
                }
                
                // 重置上传组件
                resetUploadComponent();
                
                // 打开上传对话框
                layer.open({
                    type: 1,
                    title: '上传附件 - ' + (data.product_name || ''),
                    area: ['500px', '550px'],
                    content: $('#fileUploadDialog'),
                    success: function(layero, index){
                        // 在对话框打开后的回调
                        console.log("上传对话框已打开，当前编辑索引:", currentEditIndex);
                    }
                });
            }
        });
        
        // 监听税率变更
        form.on('select(tax_rate)', function(data){
            var taxRate = parseFloat(data.value);
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            
            // 更新所有商品的税率和税额
            for(var i = 0; i < tableData.length; i++){
                var item = tableData[i];
                item.tax_rate = taxRate;
                item.tax_amount = (item.amount * taxRate / 100).toFixed(2);
                item.total_amount = (parseFloat(item.amount) + parseFloat(item.tax_amount)).toFixed(2);
            }
            
            // 重新加载表格
            table.reload('productTable', {
                data: tableData
            });
            
            // 重新计算总金额
            calculateTotal();
        });
        
        // 添加商品
        $('#btnAddProduct').on('click', function(){
            var taxRate = parseFloat($('select[name="tax_rate"]').val() || 0);
            
            tool.side('selectProduct', {
                title: '选择商品',
                width: '100%',
                success: function(layero, index){
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var table = iframeWindow.layui.table;
                    
                    // 监听选择事件
                    iframeWindow.layui.table.on('tool(productTable)', function(obj){
                        var data = obj.data;
                        if(obj.event === 'select'){
                            var table = layui.table;
                            var tableData = table.cache.productTable || [];
                            
                            // 检查是否已添加该商品
                            var exists = tableData.some(function(item){
                                return item.product_id === data.id;
                            });
                            
                            if(exists){
                                layer.msg('该商品已添加', {icon: 2});
                                return;
                            }
                            
                            // 使用当前选择的税率
                            var price = data.price || 0;
                            var amount = (parseFloat(price) * 1).toFixed(2);
                            var tax_amount = (amount * taxRate / 100).toFixed(2);
                            var total_amount = (parseFloat(amount) + parseFloat(tax_amount)).toFixed(2);
                            
                            // 添加新商品
                            tableData.push({
                                product_id: data.id,
                                product_name: data.title,
                                material_code: data.material_code,
                                product_specs: data.specs,
                                unit: data.unit,
                                quantity: 1,
                                multiplier: 1, // 默认倍数为1
                                price: price,
                                tax_rate: taxRate,
                                amount: amount,
                                tax_amount: tax_amount,
                                total_amount: total_amount,
                                delivery_date: '',
                                notes: '',
                                is_child: false // 明确标记为非子商品
                            });
                            
                            table.reload('productTable', {
                                data: tableData,
                                done: function() {
                                    // 重新应用样式
                                    styleChildProducts();
                                    calculateTotal();
                                }
                            });
                            
                            layer.close(index);
                        }
                    });
                }
            });
        });
        
        // 删除选中商品
        $('#btnRemoveProduct').on('click', function(){
            var table = layui.table;
            var checkStatus = table.checkStatus('productTable');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请选择要删除的商品', {icon: 2});
                return;
            }
            layer.confirm('确定要删除选中的商品吗？', function(index){
                var tableData = table.cache.productTable || [];
                var newData = tableData.filter(function(item){
                    return !data.some(function(selected){
                        return selected.LAY_TABLE_INDEX === item.LAY_TABLE_INDEX;
                    });
                });
                table.reload('productTable', {
                    data: newData,
                    done: function() {
                        // 重新应用样式
                        styleChildProducts();
                        calculateTotal();
                    }
                });
                layer.close(index);
            });
        });
        
        // 取消按钮
        $('#btnCancel').on('click', function(){
            // 检查是否在弹窗中
            if (typeof refreshParent === 'function') {
                // 在弹窗中，关闭当前弹窗
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            } else {
                // 不在弹窗中，使用原来的返回逻辑
                history.back();
            }
        });
        
        // 保存订单
        form.on('submit(saveOrder)', function(data){
            var table = layui.table;
            var tableData = table.cache.productTable || [];
            if(tableData.length === 0){
                layer.msg('请添加商品明细', {icon: 2});
                return false;
            }
            
            var formData = data.field;
            
            // 获取当前选择的税率
            var globalTaxRate = parseFloat(formData.tax_rate || 0);
            
            // 处理表格数据，将每行的税率设置为全局税率
            for(var i = 0; i < tableData.length; i++){
                // 清理不需要的字段
                delete tableData[i].LAY_TABLE_INDEX;
                delete tableData[i].LAY_CHECKED;
                
                tableData[i].tax_rate = globalTaxRate;
                // 重新计算税额和价税合计
                tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                // 确保单价字段正确
                tableData[i].unit_price = tableData[i].price;
            }
            
            // 确保所有子商品数量是根据父商品数量和倍数计算的
            var parentProducts = {};
            
            // 先收集所有父商品
            for(var i = 0; i < tableData.length; i++){
                if(!tableData[i].is_child){
                    parentProducts[tableData[i].product_id] = {
                        quantity: parseFloat(tableData[i].quantity) || 0
                    };
                    console.log("保存前收集父项:", tableData[i].product_name, 
                              "ID:", tableData[i].product_id, 
                              "数量:", parentProducts[tableData[i].product_id].quantity);
                }
            }
            
            // 更新所有子商品的数量
            for(var i = 0; i < tableData.length; i++){
                if(tableData[i].is_child && tableData[i].parent_product_id){
                    var parentProduct = parentProducts[tableData[i].parent_product_id];
                    if(parentProduct){
                        var oldQuantity = tableData[i].quantity;
                        var childMultiplier = parseFloat(tableData[i].multiplier) || 1;
                        var newQuantity = parentProduct.quantity * childMultiplier;
                        
                        // 更新子项数量
                        tableData[i].quantity = newQuantity;
                        
                        // 重新计算金额
                        tableData[i].amount = (tableData[i].quantity * tableData[i].price).toFixed(2);
                        tableData[i].tax_amount = (tableData[i].amount * globalTaxRate / 100).toFixed(2);
                        tableData[i].total_amount = (parseFloat(tableData[i].amount) + parseFloat(tableData[i].tax_amount)).toFixed(2);
                        
                        console.log("保存前更新子项:", tableData[i].product_name, 
                                  "父项ID:", tableData[i].parent_product_id, 
                                  "子项倍数:", childMultiplier,
                                  "旧数量:", oldQuantity, 
                                  "新数量:", newQuantity);
                    }
                }
            }
            
            formData.items = tableData;
            
            console.log("准备提交订单数据:", formData);
            
            tool.post('/customer/order/edit?id=' + formData.id, formData, function(res){
                if(res.code === 0){
                    layer.msg('保存成功', {icon: 1}, function(){
                        // 检查是否在弹窗中
                        if (typeof refreshParent === 'function') {
                            // 在弹窗中，刷新父页面并关闭当前弹窗
                            refreshParent();
                        } else {
                            // 不在弹窗中，使用原来的跳转逻辑
                            window.location.href = '/customer/order/view?id=' + formData.id;
                        }
                    });
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            });
            return false;
        });
        
        // 定义当前编辑的商品索引
        var currentEditIndex = -1;
        var currentFileInfo = null;
        var attachment = null;
        var upload_instance = null; // 添加上传实例全局变量
        
        // 初始页面加载时初始化上传组件
        $(function() {
            // 初始化上传组件
            resetUploadComponent();
            
            // 添加调试信息，检查关键元素
            console.log("DOM加载完成，检查关键元素:");
            console.log("- fileUploadDialog存在:", $('#fileUploadDialog').length > 0);
            console.log("- uploadedFileList存在:", $('#uploadedFileList').length > 0);
            
            // 绑定上传对话框按钮事件
            $('#btnConfirmUpload').on('click', function() {
                layer.closeAll();
            });
            
            $('#btnCancelUpload').on('click', function() {
                layer.closeAll();
            });
            
            // 为图片添加查看事件
            $(document).on('click', '.preview-image', function() {
                var imageUrl = $(this).data('url') || $(this).attr('src');
                viewImage(imageUrl);
            });
            
            // 为预览按钮添加点击事件
            $(document).on('click', '.btn-preview-file', function() {
                var fileUrl = $(this).data('url');
                var fileExt = $(this).parent().parent().find('.file-type').text().toLowerCase();
                var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
                
                if (isImage && fileUrl) {
                    viewImage(fileUrl);
                } else {
                    window.open(fileUrl, '_blank');
                }
            });
            
            // 为下载按钮添加点击事件
            $(document).on('click', '.btn-download-file', function() {
                var fileUrl = $(this).data('href');
                if (fileUrl) {
                    // 创建一个临时链接并触发点击
                    var a = document.createElement('a');
                    a.href = fileUrl;
                    a.download = $(this).parent().parent().find('.file-name').text();
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            });
            
            // 为删除按钮添加点击事件
            $(document).on('click', '.btn-delete-file', function() {
                var fileId = $(this).data('id');
                layer.confirm('确定要删除此附件吗？', {icon: 3}, function(index) {
                    // 从当前编辑行的attachments数组中删除
                    var table = layui.table;
                    var tableData = table.cache.productTable || [];
                    
                    if (currentEditIndex >= 0 && currentEditIndex < tableData.length) {
                        var row = tableData[currentEditIndex];
                        
                        // 确保attachments数组存在
                        if (row.attachments && row.attachments.length > 0) {
                            // 查找并删除匹配的附件
                            var newAttachments = row.attachments.filter(function(item) {
                                return item.id != fileId && item.attachment_id != fileId;
                            });
                            
                            row.attachments = newAttachments;
                            
                            // 如果没有附件了，清除主附件字段
                            if (newAttachments.length === 0) {
                                row.attachment_id = '';
                                row.attachment_name = '';
                                row.attachment_url = '';
                                row.attachment_ext = '';
                                row.attachment_size = 0;
                            } else {
                                // 否则使用第一个附件作为主附件
                                var firstAttachment = newAttachments[0];
                                row.attachment_id = firstAttachment.id || firstAttachment.attachment_id;
                                row.attachment_name = firstAttachment.name || firstAttachment.attachment_name;
                                row.attachment_url = firstAttachment.url || firstAttachment.attachment_url;
                                row.attachment_ext = firstAttachment.fileext || firstAttachment.attachment_ext;
                                row.attachment_size = firstAttachment.size || firstAttachment.attachment_size;
                            }
                            
                            // 更新表格
                            table.reload('productTable', {
                                data: tableData
                            });
                            
                            // 更新文件列表显示
                            if (newAttachments.length > 0) {
                                initFileList(newAttachments);
                            } else {
                                $('#uploadedFileList .layui-file-item').remove();
                                $('#noFileInfo').show();
                            }
                        }
                    }
                    
                    layer.close(index);
                });
            });
        });
        
        // 重置上传组件
        function resetUploadComponent() {
            console.log("重置上传组件");
            
            // 检查必要的依赖
            console.log("检查必要的依赖: uploadPlus -", (typeof layui.uploadPlus !== 'undefined' ? '已加载' : '未加载'));
            
            // 如果存在之前的上传实例，尝试销毁
            if (upload_instance) {
                try {
                    upload_instance.destroy && upload_instance.destroy();
                } catch(e) {
                    console.error("销毁上传实例失败:", e);
                }
            }
            
            // 检查上传区域是否存在
            if ($('#fileUploadArea').length === 0) {
                console.error("上传区域不存在，无法初始化上传组件");
                return;
            }
            
            // 优先使用uploadPlus模块
            if (typeof layui.uploadPlus !== 'undefined') {
                console.log("使用uploadPlus模块初始化上传组件");
                try {
                    // 移除旧的上传组件
                    $('#fileUploadArea').html('<div id="uploadBox"><input type="hidden" data-type="file" id="fileAttachmentId" value=""><div class="layui-upload-drag-desc"><i class="layui-icon layui-icon-upload"></i><p>点击上传，或将文件拖拽到此处</p></div></div>');
                    
                    // 初始化上传组件
                    var attachment = new layui.uploadPlus({
                        "target": 'fileUploadArea',
                        "targetBox": 'uploadBox',
                        "url": '/api/index/upload',
                        "attachment": {
                            "type": 1,
                            "uidDelete": true,
                            "ajaxSave": function(res) {
                                console.log("上传响应:", res);
                                layer.closeAll('loading');
                                
                                if (res.code == 0) {
                                    console.log("上传成功，文件ID:", res.data.id);
                                    
                                    // 尝试从多个地方获取当前编辑的行索引和商品ID
                                    var editIndex = currentEditIndex;
                                    console.log("初始editIndex值:", editIndex);
                                    var productId = '';
                                    
                                    // 1. 优先从隐藏字段获取
                                    productId = $('#currentProductId').val();
                                    console.log("从隐藏字段获取产品ID:", productId);
                                    
                                    // 2. 如果没有，尝试从全局变量获取
                                    if (!productId) {
                                        productId = window.currentProductId;
                                        console.log("从全局变量获取产品ID:", productId);
                                    }
                                    
                                    // 3. 最后尝试从本地存储获取
                                    if (!productId) {
                                        try {
                                            productId = localStorage.getItem('currentProductId');
                                            console.log("从本地存储获取产品ID:", productId);
                                        } catch (e) {
                                            console.error("从本地存储读取失败:", e);
                                        }
                                    }
                                    
                                    console.log("最终确定的索引:", editIndex, "产品ID:", productId);
                                    
                                    // 确保文件路径正确
                                    if (res.data.url && !res.data.url.startsWith('/') && !res.data.url.startsWith('http')) {
                                        res.data.url = '/' + res.data.url;
                                    }
                                    if (res.data.filepath && !res.data.filepath.startsWith('/') && !res.data.filepath.startsWith('http')) {
                                        res.data.filepath = '/' + res.data.filepath;
                                    }
                                    
                                    // 确保有url属性，如果没有则使用filepath
                                    if (!res.data.url && res.data.filepath) {
                                        res.data.url = res.data.filepath;
                                    }
                                    
                                    // 创建新的附件对象
                                    var newAttachment = {
                                        id: res.data.id,
                                        name: res.data.name,
                                        url: res.data.url || res.data.filepath,
                                        fileext: res.data.fileext,
                                        size: res.data.size
                                    };
                                    
                                    var tableData = table.cache.productTable || [];
                                    console.log("表格数据:", tableData);
                                    
                                    // 检查索引是否有效并在表格数据范围内
                                    if (editIndex !== undefined && editIndex !== null && editIndex >= 0 && editIndex < tableData.length) {
                                        console.log("找到有效行索引:", editIndex);
                                        
                                        // 更新指定行的附件信息
                                        if (tableData[editIndex]) {
                                            // 确保attachments数组存在
                                            if (!tableData[editIndex].attachments) {
                                                tableData[editIndex].attachments = [];
                                            }
                                            
                                            // 添加新附件到attachments数组，而不是覆盖
                                            tableData[editIndex].attachments.push(newAttachment);
                                            console.log("已添加新附件到attachments数组，现有附件数量:", tableData[editIndex].attachments.length);
                                            
                                            // 同时更新主附件字段（为了向后兼容）
                                            // 这里选择保留最新上传的附件作为主附件
                                            tableData[editIndex].attachment_id = res.data.id;
                                            tableData[editIndex].attachment_name = res.data.name;
                                            tableData[editIndex].attachment_url = res.data.url || res.data.filepath;
                                            tableData[editIndex].attachment_ext = res.data.fileext;
                                            tableData[editIndex].attachment_size = res.data.size;
                                            
                                            console.log("已更新表格第" + editIndex + "行的附件信息:", tableData[editIndex]);
                                        }
                                    } else {
                                        console.log("索引无效或超出范围，尝试通过产品ID查找或找到一个空行");
                                        
                                        // 如果有产品ID，尝试找到对应的行
                                        var foundIndex = -1;
                                        if (productId) {
                                            for (var i = 0; i < tableData.length; i++) {
                                                if (tableData[i] && tableData[i].product_id == productId) {
                                                    foundIndex = i;
                                                    console.log("通过产品ID找到行:", foundIndex);
                                                    break;
                                                }
                                            }
                                        }
                                        
                                        // 如果找到了产品ID对应的行
                                        if (foundIndex >= 0) {
                                            // 确保attachments数组存在
                                            if (!tableData[foundIndex].attachments) {
                                                tableData[foundIndex].attachments = [];
                                            }
                                            
                                            // 添加新附件到attachments数组
                                            tableData[foundIndex].attachments.push(newAttachment);
                                            console.log("已添加新附件到attachments数组，现有附件数量:", tableData[foundIndex].attachments.length);
                                            
                                            // 同时更新主附件字段（为了向后兼容）
                                            tableData[foundIndex].attachment_id = res.data.id;
                                            tableData[foundIndex].attachment_name = res.data.name;
                                            tableData[foundIndex].attachment_url = res.data.url || res.data.filepath;
                                            tableData[foundIndex].attachment_ext = res.data.fileext;
                                            tableData[foundIndex].attachment_size = res.data.size;
                                            
                                            console.log("已更新通过产品ID找到的行:", foundIndex, tableData[foundIndex]);
                                        } else {
                                            // 尝试找到第一个空行
                                            for (var i = 0; i < tableData.length; i++) {
                                                if (!tableData[i] || !tableData[i].product_id) {
                                                    tableData[i] = tableData[i] || {};
                                                    
                                                    // 确保attachments数组存在
                                                    if (!tableData[i].attachments) {
                                                        tableData[i].attachments = [];
                                                    }
                                                    
                                                    // 添加新附件到attachments数组
                                                    tableData[i].attachments.push(newAttachment);
                                                    console.log("已添加新附件到空行的attachments数组，现有附件数量:", tableData[i].attachments.length);
                                                    
                                                    // 同时更新主附件字段（为了向后兼容）
                                                    tableData[i].attachment_id = res.data.id;
                                                    tableData[i].attachment_name = res.data.name;
                                                    tableData[i].attachment_url = res.data.url || res.data.filepath;
                                                    tableData[i].attachment_ext = res.data.fileext;
                                                    tableData[i].attachment_size = res.data.size;
                                                    
                                                    console.log("已更新找到的空行:", i, tableData[i]);
                                                    foundIndex = i;
                                                    break;
                                                }
                                            }
                                        }
                                        
                                        // 如果没有找到合适的行，添加到最后
                                        if (foundIndex < 0) {
                                            console.log("未找到合适的行，将附件添加到新行");
                                            var newRow = {
                                                attachment_id: res.data.id,
                                                attachment_name: res.data.name,
                                                attachment_url: res.data.url || res.data.filepath,
                                                attachment_ext: res.data.fileext,
                                                attachment_size: res.data.size,
                                                attachments: [newAttachment] // 初始化attachments数组并添加新附件
                                            };
                                            tableData.push(newRow);
                                        }
                                    }
                                    
                                    // 更新表格
                                    table.reload('productTable', {
                                        data: tableData
                                    });
                                    
                                    console.log("表格已重新加载");
                                    
                                    // 确定当前行索引，用于显示所有附件
                                    var currentRowIndex = editIndex >= 0 ? editIndex : foundIndex;
                                    
                                    // 显示文件信息到上传弹窗中
                                    if (currentRowIndex >= 0 && tableData[currentRowIndex] && tableData[currentRowIndex].attachments) {
                                        // 显示所有附件，而不仅仅是新上传的附件
                                        console.log("显示所有附件:", tableData[currentRowIndex].attachments);
                                        initFileList(tableData[currentRowIndex].attachments);
                                    } else {
                                        // 如果找不到完整的attachments数组，至少显示新上传的附件
                                        console.log("显示上传文件:", newAttachment);
                                        initFileList([newAttachment]);
                                    }
                                    
                                    layer.msg('上传成功');
                                } else {
                                    console.error("上传失败或无效数据:", res);
                                    layer.msg(res.msg || '上传失败', {icon: 2});
                                }
                            }
                        }
                    });
                    
                    // 将attachment赋值给全局的upload_instance
                    upload_instance = attachment;
                    
                    console.log("上传组件初始化完成");
                    return attachment;
                } catch (e) {
                    console.error("初始化uploadPlus失败:", e);
                }
            }
            
            // fallback到常规upload模块
            if (typeof layui.upload !== 'undefined') {
                console.log("使用普通upload模块初始化上传组件");
                try {
                    var upload = layui.upload;
                    var attachment = upload.render({
                        elem: '#fileUploadArea',
                        accept: 'file',
                        field: 'file',
                        multiple: false,
                        auto: true,
                        headers: {
                            'X-Requested-With': null
                        },
                        url: '/api/index/upload',
                        before: function() {
                            layer.load();
                        },
                        done: function(res) {
                            layer.closeAll('loading');
                            
                            if (res.code == 0) {
                                console.log("上传成功 (普通upload):", res.data);
                                
                                // 创建与 uploadPlus 一致的数据结构
                                var resData = {
                                    code: 0,
                                    data: {
                                        id: res.data.id || res.data.attachment_id,
                                        name: res.data.name || res.data.attachment_name,
                                        url: res.data.url || res.data.filepath || res.data.attachment_url,
                                        fileext: res.data.fileext || res.data.attachment_ext,
                                        size: res.data.size || res.data.attachment_size
                                    }
                                };
                                
                                // 调用相同的处理逻辑
                                var ajaxSave = attachment.config.attachment.ajaxSave;
                                ajaxSave(resData);
                            } else {
                                layer.msg(res.msg || '上传失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.closeAll('loading');
                            layer.msg('上传发生错误', {icon: 2});
                        }
                    });
                    
                    // 设置附加属性，与 uploadPlus 保持兼容
                    attachment.config = attachment.config || {};
                    attachment.config.attachment = {
                        ajaxSave: function(res) {
                            // 这里实现与 uploadPlus 相同的响应处理逻辑
                            console.log("上传响应处理（普通upload）:", res);
                            // TODO: 添加相同的处理逻辑...
                        }
                    };
                    
                    upload_instance = attachment;
                    console.log("普通上传组件初始化完成");
                    return attachment;
                } catch (e) {
                    console.error("初始化普通upload失败:", e);
                }
            }
            
            console.error("无法初始化上传组件，所有可能的上传模块都不可用");
            return null;
        }
        
        // 初始化文件列表 - 支持多附件显示
        function initFileList(attachments) {
            console.log("初始化文件列表:", attachments);
            
            // 先清除现有的文件项
            $('#uploadedFileList .layui-file-item').remove();
            
            // 处理输入参数，支持单个对象或数组
            var fileItems = [];
            if (Array.isArray(attachments)) {
                fileItems = attachments;
            } else if (attachments && typeof attachments === 'object') {
                fileItems = [attachments];
            } else if (arguments.length >= 3) {
                // 兼容旧的调用方式 initFileList(id, name, size, ext, url)
                fileItems = [{
                    id: arguments[0],
                    name: arguments[1],
                    size: arguments[2],
                    fileext: arguments[3],
                    url: arguments[4]
                }];
            }
            
            if (fileItems.length > 0) {
                // 隐藏"暂无附件"提示
                $('#noFileInfo').hide();
                
                // 使用字符串拼接HTML，而不是DOM操作
                var fileListHtml = '';
                
                // 遍历所有文件项添加到列表
                fileItems.forEach(function(fileInfo) {
                    if (!fileInfo || (!fileInfo.id && !fileInfo.attachment_id)) return;
                    
                    // 确保获取正确的ID和其他属性
                    var attachmentId = fileInfo.id || fileInfo.attachment_id;
                    var attachmentName = fileInfo.name || fileInfo.attachment_name || '未知文件';
                    var attachmentSize = fileInfo.size || fileInfo.attachment_size || 0;
                    var attachmentExt = fileInfo.fileext || fileInfo.attachment_ext || '';
                    var attachmentUrl = fileInfo.url || fileInfo.attachment_url || fileInfo.filepath || '';
                    
                    // 确保URL以 / 或 http 开头
                    if (attachmentUrl && !attachmentUrl.startsWith('/') && !attachmentUrl.startsWith('http')) {
                        attachmentUrl = '/' + attachmentUrl;
                    }
                    
                    console.log("处理文件项: ID=", attachmentId, "名称=", attachmentName, "类型=", attachmentExt, "URL=", attachmentUrl);
                    
                    // 判断文件类型
                    var fileExt = (attachmentExt || '').toLowerCase();
                    var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].indexOf(fileExt) !== -1;
                    
                    // 构建预览内容
                    var previewContent = '';
                    if (isImage && attachmentUrl) {
                        console.log("显示图片预览:", attachmentUrl);
                        // 添加明确的点击提示和样式
                        previewContent = '<img src="' + attachmentUrl + '" alt="缩略图" data-url="' + attachmentUrl + '" class="preview-image" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain; cursor: zoom-in; transition: transform 0.2s;" title="点击查看大图">';
                    } else {
                        // 非图片类型，显示文件图标
                        var iconClass = getFileIconClass(fileExt);
                        previewContent = '<i class="layui-icon ' + iconClass + '" style="font-size: 30px; color: #999;"></i>';
                    }
                    
                    // 定义预览按钮文本和样式
                    var previewBtnText = isImage ? 
                        '<i class="layui-icon layui-icon-picture"></i> 查看大图' : 
                        '<i class="layui-icon layui-icon-file"></i> 文件信息';
                    
                    // 构建文件项HTML
                    var itemHtml = 
                        '<div class="layui-file-item" id="file-' + attachmentId + '" style="padding: 10px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; background-color: #fafafa;">' +
                            '<div class="file-preview" style="width: 60px; height: 60px; margin-right: 10px; text-align: center; overflow: hidden; border-radius: 3px; background-color: #f9f9f9; display: flex; align-items: center; justify-content: center;">' +
                                previewContent +
                            '</div>' +
                            '<div class="file-info" style="flex: 1;">' +
                                '<div class="file-name" style="font-weight: bold;">' + attachmentName + '</div>' +
                                '<div class="file-meta" style="color: #999; font-size: 12px;">' +
                                    '<span class="file-size">' + formatFileSize(attachmentSize) + '</span> · ' +
                                    '<span class="file-type">' + attachmentExt.toUpperCase() + '</span>' +
                                '</div>' +
                            '</div>' +
                            '<div class="file-actions">' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal btn-preview-file" data-id="' + attachmentId + '" data-url="' + attachmentUrl + '"' + (isImage ? ' data-action="view-image"' : '') + '>' +
                                    previewBtnText +
                                '</button>' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary btn-download-file" data-id="' + attachmentId + '" data-href="' + attachmentUrl + '">' +
                                    '<i class="layui-icon layui-icon-download-circle"></i> 下载' +
                                '</button>' +
                                '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger btn-delete-file" data-id="' + attachmentId + '">' +
                                    '<i class="layui-icon layui-icon-delete"></i> 删除' +
                                '</button>' +
                            '</div>' +
                        '</div>';
                    
                    fileListHtml += itemHtml;
                });
                
                // 一次性添加所有文件项到DOM
                $('#uploadedFileList').append(fileListHtml);
                
                // 确认DOM更新，检查是否正确添加了文件项
                var addedItems = $('#uploadedFileList .layui-file-item').length;
                console.log("已添加文件项数量:", addedItems);
            } else {
                // 显示"暂无附件"提示
                $('#noFileInfo').show();
            }
        }
        
        // 获取文件图标类
        function getFileIconClass(fileExt) {
            var iconMap = {
                // 文档类型
                'doc': 'layui-icon-file-b',
                'docx': 'layui-icon-file-b',
                'pdf': 'layui-icon-file-pdf',
                'txt': 'layui-icon-file-text',
                'rtf': 'layui-icon-file-text',
                
                // 表格类型
                'xls': 'layui-icon-file-excel',
                'xlsx': 'layui-icon-file-excel',
                'csv': 'layui-icon-file-excel',
                
                // 演示文稿
                'ppt': 'layui-icon-file-ppt',
                'pptx': 'layui-icon-file-ppt',
                
                // 压缩文件
                'zip': 'layui-icon-file-zip',
                'rar': 'layui-icon-file-zip',
                '7z': 'layui-icon-file-zip',
                'tar': 'layui-icon-file-zip',
                'gz': 'layui-icon-file-zip',
                
                // 音频
                'mp3': 'layui-icon-file-audio',
                'wav': 'layui-icon-file-audio',
                'wma': 'layui-icon-file-audio',
                'flac': 'layui-icon-file-audio',
                'midi': 'layui-icon-file-audio',
                
                // 视频
                'mp4': 'layui-icon-file-video',
                'avi': 'layui-icon-file-video',
                'mov': 'layui-icon-file-video',
                'wmv': 'layui-icon-file-video',
                'flv': 'layui-icon-file-video',
                'mpg': 'layui-icon-file-video',
                'mpeg': 'layui-icon-file-video',
                
                // 代码
                'html': 'layui-icon-file-code',
                'css': 'layui-icon-file-code',
                'js': 'layui-icon-file-code',
                'php': 'layui-icon-file-code',
                'java': 'layui-icon-file-code',
                'py': 'layui-icon-file-code',
                
                // 默认
                'default': 'layui-icon-file'
            };
            
            return iconMap[fileExt] || iconMap['default'];
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
        }
        
        // 添加一个通用的查看图片函数
        function viewImage(imageUrl) {
            console.log("查看大图:", imageUrl);
            if (!imageUrl) {
                layer.msg('图片路径无效', {icon: 2});
                return;
            }
            
            try {
                // 使用layer.photos
                layer.photos({
                    photos: {
                        "data": [{"src": imageUrl}]
                    },
                    anim: 5,
                    shade: 0.8,
                    closeBtn: 1,
                    shadeClose: true
                });
            } catch (e) {
                console.error("图片预览错误:", e, imageUrl);
                // 如果上述方法失败，直接在新窗口打开
                window.open(imageUrl, '_blank');
            }
        }
        
        // 添加子商品
        function addChildProduct(parentData) {
            // 使用product_id而非LAY_TABLE_INDEX
            var parentId = parentData.product_id;
            
            if (!parentId) {
                console.error("错误：父商品没有product_id");
                layer.msg('无法添加子商品：父商品标识符缺失', {icon: 2});
                return;
            }
            
            // 保存父商品数据到全局变量，以便全局的addChildToParent函数可以访问
            window.currentParentData = parentData;
            
            // 打开商品选择弹窗，传递product_id而非索引
            tool.side('selectChildProduct?parent_product_id=' + parentId, {
                title: '选择子商品',
                width: '100%',
                success: function(layero, index){
                    var iframeWindow = window['layui-layer-iframe' + index];
                    var table = iframeWindow.layui.table;
                    
                    // 设置父商品数据，供子页面使用
                    iframeWindow.parentProductData = parentData;
                }
            });
        }
        
        // 更新子项数量的辅助函数
        function updateChildrenQuantities(parentData) {
            if(!parentData || !parentData.product_id) return;
            
            var tableData = table.cache.productTable || [];
            var hasUpdates = false;
            
            // 父项数量
            var parentQuantity = parseFloat(parentData.quantity) || 0;
            
            console.log("更新子项数量 - 父项:", parentData.product_name, 
                      "父项ID:", parentData.product_id, 
                      "父项数量:", parentQuantity);
            
            // 记录子项更新前后的数据，用于调试
            var updateLog = [];
            
            // 遍历所有行，查找并更新子项
            for(var i = 0; i < tableData.length; i++) {
                var item = tableData[i];
                
                // 找到对应的子项
                if(item.is_child && item.parent_product_id === parentData.product_id) {
                    var oldQuantity = item.quantity;
                    // 使用子项自己的倍数
                    var childMultiplier = parseFloat(item.multiplier) || 1;
                    
                    // 计算新数量 = 父项数量 × 子项倍数
                    var newQuantity = parentQuantity * childMultiplier;
                    
                    // 更新数量和金额
                    item.quantity = newQuantity;
                    
                    // 重新计算金额
                    item.amount = (item.quantity * item.price).toFixed(2);
                    item.tax_amount = (item.amount * item.tax_rate / 100).toFixed(2);
                    item.total_amount = (parseFloat(item.amount) + parseFloat(item.tax_amount)).toFixed(2);
                    
                    updateLog.push({
                        name: item.product_name,
                        oldQuantity: oldQuantity,
                        newQuantity: newQuantity,
                        multiplier: childMultiplier
                    });
                    
                    hasUpdates = true;
                }
            }
            
            // 输出更新日志
            if(updateLog.length > 0) {
                console.log("子项数量更新明细:", updateLog);
            }
            
            // 如果有更新，重新加载表格
            if(hasUpdates) {
                console.log("有子项更新，重新加载表格");
                
                // 强制刷新表格 - 重建整个表格数据
                table.reload('productTable', {
                    data: tableData,
                    done: function() {
                        console.log("表格重载完成，子项数量已更新");
                        // 重新应用样式
                        styleChildProducts();
                        
                        // 确保删除按钮显示
                        setTimeout(function() {
                            $('#productTable').next().find('td[data-content="操作"]').each(function(i, el) {
                                $(el).find('.layui-btn-danger').css('display', 'inline-block !important');
                                $(el).find('.layui-btn-danger').attr('style', 'display: inline-block !important');
                            });
                        }, 200);
                    }
                });
                
                // 重新计算总金额
                calculateTotal();
            } else {
                console.log("没有找到需要更新的子项");
            }
        }
    }
</script>
{/block} 