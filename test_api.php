<?php
// 测试API接口
require_once 'vendor/autoload.php';

// 模拟请求参数
$_GET = [
    'name' => 'customer_delivery',
    'action_id' => '102',
    'page' => '1',
    'limit' => '50'
];

// 设置环境
define('APP_PATH', __DIR__ . '/app/');

// 简单测试数据库连接
try {
    $config = include 'config/database.php';
    $dsn = "mysql:host={$config['default']['hostname']};dbname={$config['default']['database']};charset={$config['default']['charset']}";
    $pdo = new PDO($dsn, $config['default']['username'], $config['default']['password']);
    
    // 测试查询
    $sql = "SELECT d.*, 
        o.id as outbound_id,
        o.outbound_no,
        o.status as outbound_status,
        o.outbound_time,
        CASE 
            WHEN o.status IS NULL THEN '未创建出库单'
            WHEN o.status = 0 THEN '草稿'
            WHEN o.status = 1 THEN '已提交'
            WHEN o.status = 2 THEN '已审核'
            WHEN o.status = 3 THEN '部分出库'
            WHEN o.status = 4 THEN '全部出库'
            WHEN o.status = 5 THEN '已取消'
            ELSE '未知状态'
        END as outbound_status_text
    FROM oa_customer_order_delivery d
    LEFT JOIN oa_outbound o ON o.ref_id = d.order_id AND o.ref_type = 'customer_order' AND o.ref_no LIKE CONCAT('%/', d.delivery_no)
    WHERE d.order_id = ? AND d.delete_time = 0
    ORDER BY d.id DESC
    LIMIT 50";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([102]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "查询结果：\n";
    echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}
