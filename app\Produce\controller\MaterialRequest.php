<?php

declare(strict_types=1);

namespace app\Produce\controller;

use think\facade\Db;
use think\facade\Log;
use think\facade\View;
use app\base\BaseController;

/**
 * 生产领料管理控制器
 */
class MaterialRequest extends BaseController
{
    /**
     * 领料单列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            // 搜索条件
            if (!empty($param['request_no'])) {
                $where[] = ['r.request_no', 'like', '%' . $param['request_no'] . '%'];
            }
            if (!empty($param['production_order_no'])) {
                $where[] = ['r.production_order_no', 'like', '%' . $param['production_order_no'] . '%'];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['r.status', '=', $param['status']];
            }
            if (!empty($param['start_time'])) {
                $where[] = ['r.create_time', '>=', strtotime($param['start_time'])];
            }
            if (!empty($param['end_time'])) {
                $where[] = ['r.create_time', '<=', strtotime($param['end_time']) + 86399];
            }
            
            // 系统使用物理删除，不需要delete_time条件
            
            $list = Db::name('production_material_request')
                ->alias('r')
                ->leftJoin('admin a', 'r.created_by = a.id')
                ->where($where)
                ->field([
                    'r.*',
                    'IFNULL(a.name, r.created_name) as created_name',
                    'CASE r.status
                        WHEN 0 THEN "待审核"
                        WHEN 1 THEN "已审核"
                        WHEN 2 THEN "已出库"
                        WHEN 3 THEN "已完成"
                        WHEN 4 THEN "已取消"
                        ELSE "未知"
                    END as status_text',
                    'CASE r.request_type
                        WHEN 1 THEN "正常领料"
                        WHEN 2 THEN "补料"
                        WHEN 3 THEN "超领"
                        ELSE "未知"
                    END as request_type_text'
                ])
                ->order('r.create_time desc')
                ->paginate([
                    'list_rows' => $param['limit'] ?? 10,
                    'page' => $param['page'] ?? 1
                ]);

            // 为每个领料单添加出库状态和超领标识
            $listData = $list->toArray();
            foreach ($listData['data'] as &$item) {
                $item['outbound_status'] = $this->getOutboundStatus($item['request_no']);
                $item['outbound_status_text'] = $this->getOutboundStatusText($item['outbound_status']);

                // 计算超领标识
                $excessInfo = $this->getExcessInfo($item['request_no'], $item['production_order_id']);
                $item['has_excess'] = $excessInfo['has_excess'];
                $item['excess_items'] = $excessInfo['excess_items'];

                // 调试日志
                \think\facade\Log::info('超领标识计算', [
                    'request_no' => $item['request_no'],
                    'production_order_id' => $item['production_order_id'],
                    'has_excess' => $item['has_excess'],
                    'excess_items' => $item['excess_items']
                ]);

                // 临时调试：在领料单号后面显示超领信息
                $item['request_no_debug'] = $item['request_no'] . ' [超领:' . $item['has_excess'] . ',项数:' . $item['excess_items'] . ']';
            }

            // 重新构造分页数据
            $list = new \think\paginator\driver\Bootstrap(
                $listData['data'],
                $listData['per_page'],
                $listData['current_page'],
                $listData['total'],
                false,
                [
                    'path' => request()->url(),
                    'query' => request()->param()
                ]
            );
                
            return table_assign(0, '', $list);
        }
        
        return View::fetch('index');
    }

    /**
     * 新增领料单页面
     */
    public function add()
    {
        if (request()->isPost()) {
            return $this->save();
        }
        
        // 获取仓库列表
        $warehouses = Db::name('warehouse')
            ->where('status', 1)
            ->field('id, name')
            ->select();
            
        View::assign('warehouses', $warehouses);
        return View::fetch('add');
    }

    /**
     * 获取未完成的生产订单列表
     */
    public function getProductionOrders()
    {
        try {
            $orders = Db::name('produce_order')
                ->where('status', 'in', [0, 1, 2]) // 待排产、已排产、生产中
                ->where('delete_time', 0) // 未删除的记录
                ->field([
                    'id',
                    'order_no',
                    'product_id',
                    'product_name',
                    'quantity',
                    'status',
                    'CASE status
                        WHEN 0 THEN "待排产"
                        WHEN 1 THEN "已排产"
                        WHEN 2 THEN "生产中"
                        ELSE "未知"
                    END as status_text'
                ])
                ->order('create_time desc')
                ->select();
                
            return json(['code' => 0, 'msg' => '获取成功', 'data' => $orders]);
            
        } catch (\Exception $e) {
            Log::error('获取生产订单列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 根据生产订单获取BOM物料
     */
    public function getBomMaterials()
    {
        try {
            $productionOrderId = input('production_order_id');
            
            if (empty($productionOrderId)) {
                throw new \Exception('生产订单ID不能为空');
            }
            
            // 获取生产订单信息
            $order = Db::name('produce_order')->where('id', $productionOrderId)->find();
            if (!$order) {
                throw new \Exception('生产订单不存在');
            }


            
            // 获取产品BOM
            $bom = Db::name('material_bom')
                ->where('product_id', $order['product_id'])
                ->where('status', 1)
                ->order('version desc')
                ->find();
                
            if (!$bom) {
                throw new \Exception('产品没有BOM信息');
            }

            // 调试BOM信息
            Log::info('BOM信息', [
                'product_id' => $order['product_id'],
                'bom_data' => $bom
            ]);
            
            // 获取BOM明细和库存信息 - 只获取一级物料
            $materials = Db::name('material_bom_detail')
                ->alias('bd')
                ->leftJoin('product p', 'bd.material_id = p.id')
                ->leftJoin('inventory_realtime i', 'bd.material_id = i.product_id')
                ->where('bd.bom_id', $bom['id'])
                ->where('bd.bom_level', 1)  // 只获取一级物料
                ->field([
                    'bd.material_id',
                    'p.material_code',
                    'p.title as material_name',
                    'p.specs as material_specs',
                    'p.unit',
                    'bd.quantity as bom_quantity',
                    'bd.loss_rate',
                    'COALESCE(SUM(i.available_quantity), 0) as available_stock'
                ])
                ->group('bd.material_id, p.id')
                ->order('bd.sort asc, bd.id asc')
                ->select();

            if (empty($materials)) {
                throw new \Exception('该产品的BOM没有配置物料明细');
            }
                
            // 计算标准需求数量和已领数量
            $processedMaterials = [];
            foreach ($materials as $material) {
                $bomQuantity = floatval($material['bom_quantity']);
                $orderQuantity = floatval($order['quantity']);
                $standardQuantity = $bomQuantity * $orderQuantity;

                // 计算已领数量：查询该生产订单已经领取的物料数量
                $receivedQuantity = $this->getReceivedQuantity($productionOrderId, $material['material_id']);

                $processedMaterials[] = [
                    'material_id' => $material['material_id'],
                    'material_code' => $material['material_code'],
                    'material_name' => $material['material_name'],
                    'material_specs' => $material['material_specs'],
                    'unit' => $material['unit'],
                    'bom_quantity' => round($bomQuantity, 4),
                    'loss_rate' => round(floatval($material['loss_rate'] ?: 0), 2),
                    'available_stock' => round(floatval($material['available_stock']), 4),
                    'standard_quantity' => round($standardQuantity, 4),
                    'received_quantity' => round($receivedQuantity, 4), // 已领数量
                    'remaining_quantity' => round($standardQuantity - $receivedQuantity, 4), // 剩余需求
                    'request_quantity' => round(max(0, $standardQuantity - $receivedQuantity), 4) // 默认申请剩余数量
                ];
            }
            $materials = $processedMaterials;
            


            return json([
                'code' => 0,
                'msg' => '获取成功',
                'data' => [
                    'order' => $order,
                    'materials' => $materials
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取BOM物料失败', [
                'production_order_id' => $productionOrderId ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 保存领料单
     */
    public function save()
    {
        $param = input('post.');
        
        // 验证必要参数
        if (empty($param['production_order_id'])) {
            return json(['code' => 1, 'msg' => '请选择生产订单']);
        }
        if (empty($param['materials']) || !is_array($param['materials'])) {
            return json(['code' => 1, 'msg' => '请添加领料物料']);
        }
        
        Db::startTrans();
        try {
            // 1. 生成领料单号
            $requestNo = $this->generateRequestNo();
            
            // 2. 获取生产订单信息
            $order = Db::name('produce_order')->where('id', $param['production_order_id'])->find();
            if (!$order) {
                throw new \Exception('生产订单不存在');
            }
            
            // 3. 计算汇总信息和获取锁定库存信息
            $totalItems = count($param['materials']);
            $totalQuantity = 0;
            $hasExcess = false;
            $excessItems = 0;
            $warehouseGroups = []; // 按仓库分组的物料

            foreach ($param['materials'] as &$material) {
                $requestQuantity = floatval($material['request_quantity']);
                $standardQuantity = floatval($material['standard_quantity']);

                if ($requestQuantity <= 0) {
                    throw new \Exception("物料【{$material['material_name']}】申请数量必须大于0");
                }

                $totalQuantity += $requestQuantity;

                // 检查是否超领
                if ($requestQuantity > $standardQuantity) {
                    $material['excess_quantity'] = round($requestQuantity - $standardQuantity, 4);
                    $material['is_excess'] = 1;
                    $hasExcess = true;
                    $excessItems++;
                } else {
                    $material['excess_quantity'] = 0;
                    $material['is_excess'] = 0;
                }

                // 获取该物料的锁定库存信息
                $lockedStocks = $this->getLockedStockByMaterial($material['material_id'], $param['production_order_id'], $requestQuantity);

                // 计算已锁定数量
                $lockedQuantity = 0;
                if (!empty($lockedStocks)) {
                    $lockedQuantity = array_sum(array_column($lockedStocks, 'quantity'));
                }

                // 如果锁定库存不够，尝试自动锁定不足的部分
                if ($lockedQuantity < $requestQuantity) {
                    $needLockQuantity = $requestQuantity - $lockedQuantity;

                    Log::info('需要补充锁定库存', [
                        'material_id' => $material['material_id'],
                        'material_name' => $material['material_name'],
                        'request_quantity' => $requestQuantity,
                        'locked_quantity' => $lockedQuantity,
                        'need_lock_quantity' => $needLockQuantity
                    ]);

                    $autoLockResult = $this->autoLockInventoryForMaterial($material['material_id'], $param['production_order_id'], $needLockQuantity);
                    if ($autoLockResult['success']) {
                        // 合并已有锁定和新锁定的库存
                        $lockedStocks = array_merge($lockedStocks, $autoLockResult['locked_stocks']);
                        Log::info('自动锁定库存成功', [
                            'material_id' => $material['material_id'],
                            'material_name' => $material['material_name'],
                            'need_lock_quantity' => $needLockQuantity,
                            'new_locked_stocks' => $autoLockResult['locked_stocks']
                        ]);
                    } else {
                        throw new \Exception("物料【{$material['material_name']}】库存不足，当前可用库存：{$autoLockResult['available_quantity']}，需求数量：{$needLockQuantity}");
                    }
                }

                $material['locked_stocks'] = $lockedStocks;

                // 按仓库分组
                foreach ($lockedStocks as $stock) {
                    $warehouseId = $stock['warehouse_id'];
                    if (!isset($warehouseGroups[$warehouseId])) {
                        $warehouseGroups[$warehouseId] = [
                            'warehouse_id' => $warehouseId,
                            'warehouse_name' => $stock['warehouse_name'],
                            'materials' => []
                        ];
                    }

                    $warehouseGroups[$warehouseId]['materials'][] = [
                        'material_id' => $material['material_id'],
                        'material_code' => $material['material_code'],
                        'material_name' => $material['material_name'],
                        'material_specs' => $material['material_specs'] ?? '',
                        'unit' => $material['unit'] ?? '',
                        'quantity' => $stock['quantity'],
                        'lock_id' => $stock['lock_id']
                    ];
                }
            }
            
            // 4. 保存主表 created_name
            $mainData = [
                'request_no' => $requestNo,
                'production_order_id' => $param['production_order_id'],
                'production_order_no' => $order['order_no'],
                'product_id' => $order['product_id'],
                'product_name' => $order['product_name'],
                'production_quantity' => $order['quantity'],
                'request_type' => $hasExcess ? 3 : 1, // 有超领则标记为超领类型
                'total_items' => $totalItems,
                'total_quantity' => round($totalQuantity, 4),
                'has_excess' => $hasExcess ? 1 : 0,
                'excess_items' => $excessItems,
                'status' => 1, // 直接设为已审核，因为基于锁定库存
                'notes' => $param['notes'] ?? '',
                'created_by' => $this->uid,
                'created_name' => get_admin($this->uid)['name'] ?? '',
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $requestId = Db::name('production_material_request')->insertGetId($mainData);

            // 5. 保存明细
            foreach ($param['materials'] as $material) {
                $detailData = [
                    'request_id' => $requestId,
                    'request_no' => $requestNo,
                    'material_id' => $material['material_id'],
                    'material_code' => $material['material_code'],
                    'material_name' => $material['material_name'],
                    'material_specs' => $material['material_specs'] ?? '',
                    'unit' => $material['unit'] ?? '',
                    'bom_quantity' => round(floatval($material['bom_quantity']), 4),
                    'standard_quantity' => round(floatval($material['standard_quantity']), 4),
                    'request_quantity' => round(floatval($material['request_quantity']), 4),
                    'excess_quantity' => round(floatval($material['excess_quantity']), 4),
                    'is_excess' => $material['is_excess'],
                    'create_time' => time(),
                    'update_time' => time()
                ];

                Db::name('production_material_request_detail')->insert($detailData);
            }

            // 6. 根据锁定库存按仓库创建出库单
            $outboundOrders = $this->createOutboundOrdersByWarehouse($warehouseGroups, $requestId, $requestNo, $order);

            // 7. 更新生产订单状态
            $this->updateProductionOrderStatus($param['production_order_id']);
            
            Db::commit();
            
            Log::info('创建领料单成功', [
                'request_id' => $requestId,
                'request_no' => $requestNo,
                'production_order_id' => $param['production_order_id'],
                'total_items' => $totalItems,
                'has_excess' => $hasExcess
            ]);
            
            return json([
                'code' => 0,
                'msg' => '领料单创建成功，等待审核',
                'data' => [
                    'id' => $requestId,
                    'request_no' => $requestNo
                ]
            ]);
            
        } catch (\Exception $e) {
            Db::rollback();
            
            Log::error('创建领料单失败', [
                'param' => $param,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 审核领料单
     */
    public function approve()
    {
        $id = input('id');
        $approveNotes = input('approve_notes', '');

        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        Db::startTrans();
        try {
            // 1. 获取领料单信息
            $request = Db::name('production_material_request')->where('id', $id)->find();
            if (!$request) {
                throw new \Exception('领料单不存在');
            }

            if ($request['status'] != 0) {
                throw new \Exception('领料单状态不正确，无法审核');
            }

            // 2. 再次检查库存（防止审核时库存发生变化）
            $details = Db::name('production_material_request_detail')
                ->where('request_id', $id)
                ->select();

            foreach ($details as $detail) {
                $availableStock = $this->getAvailableStock($detail['material_id'], $request['warehouse_id']);
                if ($availableStock < $detail['request_quantity']) {
                    throw new \Exception("物料【{$detail['material_name']}】库存不足，当前可用库存：{$availableStock}，申请数量：{$detail['request_quantity']}");
                }
            }

            // 3. 更新审核状态
            Db::name('production_material_request')
                ->where('id', $id)
                ->update([
                    'status' => 1, // 已审核
                    'approved_by' => $this->uid,
                    'approved_name' => session('admin.name'),
                    'approve_time' => time(),
                    'approve_notes' => $approveNotes,
                    'update_time' => time()
                ]);

            Db::commit();

            Log::info('审核领料单成功', [
                'request_id' => $id,
                'request_no' => $request['request_no'],
                'approved_by' => $this->uid,
                'approve_notes' => $approveNotes
            ]);

            return json(['code' => 0, 'msg' => '审核成功']);

        } catch (\Exception $e) {
            Db::rollback();

            Log::error('审核领料单失败', [
                'request_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 拒绝领料单
     */
    public function reject()
    {
        $id = input('id');
        $rejectNotes = input('reject_notes', '');

        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        try {
            $request = Db::name('production_material_request')->where('id', $id)->find();
            if (!$request) {
                throw new \Exception('领料单不存在');
            }

            if ($request['status'] != 0) {
                throw new \Exception('领料单状态不正确，无法拒绝');
            }

            // 更新为已取消状态
            Db::name('production_material_request')
                ->where('id', $id)
                ->update([
                    'status' => 4, // 已取消
                    'approved_by' => $this->uid,
                    'approved_name' => session('admin.name'),
                    'approve_time' => time(),
                    'approve_notes' => $rejectNotes,
                    'update_time' => time()
                ]);

            Log::info('拒绝领料单成功', [
                'request_id' => $id,
                'request_no' => $request['request_no'],
                'rejected_by' => $this->uid,
                'reject_notes' => $rejectNotes
            ]);

            return json(['code' => 0, 'msg' => '已拒绝领料单']);

        } catch (\Exception $e) {
            Log::error('拒绝领料单失败', [
                'request_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 查看领料单详情
     */
    public function view()
    {
        $id = input('id');

        if (empty($id)) {
            $this->error('参数错误');
        }

        // 获取主表信息，关联创建人和审核人信息
        $request = Db::name('production_material_request')
            ->alias('r')
            ->leftJoin('admin a1', 'r.created_by = a1.id')
            ->leftJoin('admin a2', 'r.approved_by = a2.id')
            ->where('r.id', $id)
            ->field([
                'r.*',
                'IFNULL(a1.name, r.created_name) as created_name',
                'IFNULL(a2.name, r.approved_name) as approved_name'
            ])
            ->find();
        if (!$request) {
            $this->error('领料单不存在');
        }

        // 获取明细信息，关联物料信息
        $details = Db::name('production_material_request_detail')
            ->alias('d')
            ->leftJoin('product p', 'd.material_id = p.id')
            ->where('d.request_id', $id)
            ->field([
                'd.*',
                'p.material_code',
                'p.title as material_name',
                'p.specs as material_specs',
                'p.unit'
            ])
            ->order('d.id asc')
            ->select()
            ->toArray();

        // 计算超领信息
        $excessInfo = $this->getExcessInfo($request['request_no'], $request['production_order_id']);
        $request['has_excess'] = $excessInfo['has_excess'];
        $request['excess_items'] = $excessInfo['excess_items'];

        // 为每个明细添加超领相关字段
        foreach ($details as &$detail) {
            $materialId = $detail['material_id'];

            // 从超领信息中获取该物料的详细信息
            $materialExcessInfo = $excessInfo['materials'][$materialId] ?? [];

            $detail['bom_quantity'] = $materialExcessInfo['bom_quantity'] ?? 0;
            $detail['standard_quantity'] = $materialExcessInfo['standard_quantity'] ?? 0;
            $detail['available_stock'] = $materialExcessInfo['available_stock'] ?? 0;
            $detail['excess_quantity'] = $materialExcessInfo['excess_quantity'] ?? 0;
            $detail['is_excess'] = $materialExcessInfo['is_excess'] ?? 0;
        }

        View::assign('request', $request);
        View::assign('details', $details);

        return View::fetch('view');
    }

    /**
     * 获取生产订单某个物料的已领数量
     * @param int $productionOrderId 生产订单ID
     * @param int $materialId 物料ID
     * @return float 已领数量
     */
    private function getReceivedQuantity($productionOrderId, $materialId)
    {
        try {
            // 查询该生产订单的所有领料单明细中该物料的实际出库数量
            $receivedQuantity = Db::name('production_material_request_detail')
                ->alias('d')
                ->leftJoin('production_material_request r', 'd.request_no = r.request_no')
                ->where('r.production_order_id', $productionOrderId)
                ->where('d.material_id', $materialId)
                ->sum('d.actual_quantity');

            // 添加调试日志
            \think\facade\Log::info("查询已领数量", [
                'production_order_id' => $productionOrderId,
                'material_id' => $materialId,
                'received_quantity' => $receivedQuantity
            ]);

            return floatval($receivedQuantity ?: 0);

        } catch (\Exception $e) {
            Log::error('获取已领数量失败', [
                'production_order_id' => $productionOrderId,
                'material_id' => $materialId,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * 获取领料单的超领信息
     * @param string $requestNo 领料单号
     * @param int $productionOrderId 生产订单ID
     * @return array 超领信息
     */
    private function getExcessInfo($requestNo, $productionOrderId)
    {
        try {
            \think\facade\Log::info('开始计算超领信息', [
                'request_no' => $requestNo,
                'production_order_id' => $productionOrderId
            ]);

            // 获取该领料单的所有明细
            $details = Db::name('production_material_request_detail')
                ->where('request_no', $requestNo)
                ->field('material_id, request_quantity, actual_quantity')
                ->select()
                ->toArray();

            \think\facade\Log::info('领料单明细', [
                'request_no' => $requestNo,
                'details_count' => count($details),
                'details' => $details
            ]);

            if (empty($details)) {
                return ['has_excess' => 0, 'excess_items' => 0, 'materials' => []];
            }

            // 获取生产订单信息
            $order = Db::name('produce_order')
                ->where('id', $productionOrderId)
                ->field('id, quantity, product_id')
                ->find();

            if (!$order) {
                return ['has_excess' => 0, 'excess_items' => 0, 'materials' => []];
            }

            // 获取BOM信息 - 只计算一级物料
            $bomMaterials = Db::name('material_bom_detail')
                ->where('product_id', $order['product_id'])
                ->where('bom_level', 1)  // 只获取一级物料
                ->where('delete_time', 0)  // 排除已删除的记录
                ->field('material_id, quantity as bom_quantity')
                ->select()
                ->toArray();

            \think\facade\Log::info('BOM信息', [
                'product_id' => $order['product_id'],
                'bom_materials_count' => count($bomMaterials),
                'bom_materials' => $bomMaterials
            ]);

            // 构建BOM数据映射
            $bomMap = [];
            foreach ($bomMaterials as $bom) {
                $bomMap[$bom['material_id']] = floatval($bom['bom_quantity']);
            }

            // 获取库存信息
            $stockMap = [];
            $materialIds = array_column($details, 'material_id');
            if (!empty($materialIds)) {
                $stocks = Db::name('inventory_realtime')
                    ->whereIn('product_id', $materialIds)
                    ->field('product_id, SUM(available_quantity) as available_stock')
                    ->group('product_id')
                    ->select()
                    ->toArray();

                foreach ($stocks as $stock) {
                    $stockMap[$stock['product_id']] = floatval($stock['available_stock']);
                }
            }

            // 计算当前领料单是否导致超领
            $excessItems = 0;
            $materialsInfo = [];

            foreach ($details as $detail) {
                $materialId = $detail['material_id'];
                $currentActualQuantity = floatval($detail['actual_quantity']);

                // 计算该物料在当前领料单之前的累计出库量
                $previousActualQuantity = Db::name('production_material_request_detail')
                    ->alias('d')
                    ->leftJoin('production_material_request r', 'd.request_no = r.request_no')
                    ->where('r.production_order_id', $productionOrderId)
                    ->where('d.material_id', $materialId)
                    ->where('r.create_time', '<', Db::name('production_material_request')->where('request_no', $requestNo)->value('create_time'))
                    ->sum('d.actual_quantity');

                $previousActualQuantity = floatval($previousActualQuantity);

                // 计算标准需求量
                $bomQuantity = $bomMap[$materialId] ?? 0;
                $standardQuantity = $bomQuantity * floatval($order['quantity']);

                // 判断当前领料单是否导致超领
                $beforeTotal = $previousActualQuantity; // 之前的累计量
                $afterTotal = $previousActualQuantity + $currentActualQuantity; // 加上当前领料单后的累计量

                // 计算超领数量
                $excessQuantity = max(0, $afterTotal - $standardQuantity);
                $isExcess = ($beforeTotal <= $standardQuantity && $afterTotal > $standardQuantity) ? 1 : 0;

                \think\facade\Log::info('超领判断', [
                    'request_no' => $requestNo,
                    'material_id' => $materialId,
                    'previous_total' => $beforeTotal,
                    'current_quantity' => $currentActualQuantity,
                    'after_total' => $afterTotal,
                    'standard_quantity' => $standardQuantity,
                    'excess_quantity' => $excessQuantity,
                    'is_excess' => $isExcess
                ]);

                // 如果之前没有超领，但加上当前领料单后超领了，则认为当前领料单导致了超领
                if ($isExcess) {
                    $excessItems++;
                }

                // 保存物料详细信息
                $materialsInfo[$materialId] = [
                    'bom_quantity' => $bomQuantity,
                    'standard_quantity' => $standardQuantity,
                    'available_stock' => $stockMap[$materialId] ?? 0,
                    'excess_quantity' => $excessQuantity,
                    'is_excess' => $isExcess
                ];
            }

            return [
                'has_excess' => $excessItems > 0 ? 1 : 0,
                'excess_items' => $excessItems,
                'materials' => $materialsInfo
            ];

        } catch (\Exception $e) {
            \think\facade\Log::error('获取超领信息失败', [
                'request_no' => $requestNo,
                'production_order_id' => $productionOrderId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return ['has_excess' => 0, 'excess_items' => 0, 'materials' => []];
        }
    }

    /**
     * 获取领料单的出库状态
     * @param string $requestNo 领料单号
     * @return int 出库状态：0=未出库,1=部分出库,2=全部出库
     */
    private function getOutboundStatus($requestNo)
    {
        try {
            // 直接查询该领料单对应的所有出库单
            $outbounds = Db::name('outbound')
                ->where('ref_type', 'production_material_request')
                ->where('ref_no', $requestNo)
                ->field('id, status, total_quantity, outbound_no')
                ->select()
                ->toArray();

            if (empty($outbounds)) {
                return 0; // 未出库
            }
            // 检查是否有已完成出库的单据
            $hasCompleted = false;
            $hasPartial = false;
            $hasCanceled = false;

            foreach ($outbounds as $outbound) {
                if ($outbound['status'] == 4) { // 全部出库
                    $hasCompleted = true;
                } elseif ($outbound['status'] == 3) { // 部分出库
                    $hasPartial = true;
                } elseif ($outbound['status'] == 5) { // 已取消
                    $hasCanceled = true;
                }
            }

            if ($hasCompleted) {
                return 2; // 全部出库
            } elseif ($hasPartial) {
                return 1; // 部分出库
            } elseif ($hasCanceled) {
                return 5; // 仓库取消
            } else {
                return 0; // 未出库
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('获取出库状态失败: ' . $e->getMessage() . ' - 领料单号: ' . $requestNo);
            return 0; // 默认未出库
        }
    }

    /**
     * 获取出库状态文本
     * @param int $status 出库状态
     * @return string 状态文本
     */
    private function getOutboundStatusText($status)
    {
        switch ($status) {
            case 0:
                return '未出库';
            case 1:
                return '部分出库';
            case 2:
                return '全部出库';
            case 5:
                return '仓库取消';
            default:
                return '未知';
        }
    }

    /**
     * 删除领料单
     */
    public function delete()
    {
        $id = input('id');

        if (empty($id)) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }

        try {
            $request = Db::name('production_material_request')->where('id', $id)->find();
            if (!$request) {
                throw new \Exception('领料单不存在');
            }

            if ($request['status'] != 0) {
                throw new \Exception('只能删除待审核状态的领料单');
            }

            // 物理删除
            Db::name('production_material_request')
                ->where('id', $id)
                ->delete();

            // 删除明细记录
            Db::name('production_material_request_detail')
                ->where('request_id', $id)
                ->delete();

            Log::info('删除领料单成功', [
                'request_id' => $id,
                'request_no' => $request['request_no'],
                'deleted_by' => $this->uid
            ]);

            return json(['code' => 0, 'msg' => '删除成功']);

        } catch (\Exception $e) {
            Log::error('删除领料单失败', [
                'request_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成领料单号
     */
    private function generateRequestNo(): string
    {
        $prefix = 'MR';
        $datePart = date('Ymd');
        $maxRetries = 10;

        for ($i = 0; $i < $maxRetries; $i++) {
            // 获取当天最大序号
            $maxSeq = Db::name('production_material_request')
                ->where('request_no', 'like', $prefix . $datePart . '%')
                ->max('request_no');

            if ($maxSeq) {
                $seq = intval(substr($maxSeq, -4)) + 1;
            } else {
                $seq = 1;
            }

            $requestNo = $prefix . $datePart . str_pad((string)$seq, 4, '0', STR_PAD_LEFT);

            // 检查是否已存在
            $exists = Db::name('production_material_request')
                ->where('request_no', $requestNo)
                ->count();

            if (!$exists) {
                return $requestNo;
            }

            // 如果存在，添加随机后缀重试
            usleep(10000); // 等待10毫秒
        }

        // 如果重试失败，使用时间戳后缀
        return $prefix . $datePart . substr((string)time(), -4);
    }

    /**
     * 获取可用库存
     */
    private function getAvailableStock(int $materialId, int $warehouseId = 0): float
    {
        try {
            $query = Db::name('inventory_realtime')
                ->where('product_id', $materialId);

            if ($warehouseId > 0) {
                $query->where('warehouse_id', $warehouseId);
            }

            $availableStock = $query->sum('available_quantity');

            return floatval($availableStock);

        } catch (\Exception $e) {
            Log::error('获取可用库存失败', [
                'material_id' => $materialId,
                'warehouse_id' => $warehouseId,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * 获取物料的锁定库存信息
     */
    private function getLockedStockByMaterial($materialId, $productionOrderId, $requestQuantity)
    {
       
        try {
            // 查找该物料在生产订单中的锁定库存
            $lockedStocks = Db::name('inventory_lock')
                ->alias('il')
                ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
                ->where('il.product_id', $materialId)
                ->where('il.ref_type', 'production_order')
                ->where('il.ref_id', $productionOrderId)
                ->where('il.status', 1) // 已锁定状态
                ->field('il.id as lock_id, il.warehouse_id, w.name as warehouse_name, il.quantity')
                ->order('il.create_time asc')
                ->select()
                ->toArray();

            if (empty($lockedStocks)) {
                return [];
            }

            // 按需求数量分配锁定库存
            $allocatedStocks = [];
            $remainingQuantity = $requestQuantity;

            foreach ($lockedStocks as $stock) {
                if ($remainingQuantity <= 0) {
                    break;
                }

                $allocateQuantity = min($remainingQuantity, $stock['quantity']);
                $allocatedStocks[] = [
                    'lock_id' => $stock['lock_id'],
                    'warehouse_id' => $stock['warehouse_id'],
                    'warehouse_name' => $stock['warehouse_name'],
                    'quantity' => $allocateQuantity
                ];

                $remainingQuantity -= $allocateQuantity;
            }

            return $allocatedStocks;

        } catch (\Exception $e) {
            Log::error('获取锁定库存失败', [
                'material_id' => $materialId,
                'production_order_id' => $productionOrderId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 自动锁定物料库存
     * @param int $materialId 物料ID
     * @param int $productionOrderId 生产订单ID
     * @param float $requestQuantity 需求数量
     * @return array 锁定结果
     */
    private function autoLockInventoryForMaterial($materialId, $productionOrderId, $requestQuantity)
    {
        try {
            // 获取生产订单信息
            $productionOrder = Db::name('produce_order')
                ->where('id', $productionOrderId)
                ->field('id, order_no')
                ->find();

            if (!$productionOrder) {
                throw new \Exception('生产订单不存在');
            }

            // 添加调试日志
            Log::info('开始自动锁定库存', [
                'material_id' => $materialId,
                'production_order_id' => $productionOrderId,
                'production_order_no' => $productionOrder['order_no'],
                'request_quantity' => $requestQuantity
            ]);

            // 查询该物料的可用库存（使用正确的ThinkPHP写法）
            $query = Db::name('inventory_realtime')
                ->alias('ir')
                ->leftJoin('warehouse w', 'ir.warehouse_id = w.id')
                ->where('ir.product_id', intval($materialId)) // 确保数据类型正确
                ->where('ir.available_quantity', '>', 0.0) // 使用数值而不是字符串
                ->field('ir.id, ir.warehouse_id, w.name as warehouse_name, ir.available_quantity')
                ->order('ir.available_quantity desc'); // 优先使用库存多的仓库

            $availableStocks = $query->select()->toArray();

            // 记录查询SQL和结果
            Log::info('库存查询详情', [
                'material_id' => $materialId,
                'material_id_int' => intval($materialId),
                'sql' => $query->getLastSql(),
                'available_stocks_count' => count($availableStocks),
                'available_stocks' => $availableStocks
            ]);

            // 无论是否找到，都查询所有库存记录进行调试
            $allStocks = Db::name('inventory_realtime')
                ->where('product_id', intval($materialId))
                ->field('id, product_id, warehouse_id, quantity, available_quantity, locked_quantity')
                ->select()
                ->toArray();

            // 查询产品表，确认物料ID是否存在
            $product = Db::name('product')
                ->where('id', intval($materialId))
                ->field('id, title, material_code')
                ->find();

            // 记录调试信息到日志
            Log::info('库存查询完成', [
                'material_id' => $materialId,
                'available_stocks_count' => count($availableStocks),
                'all_stocks_count' => count($allStocks),
                'product_exists' => $product ? true : false
            ]);

            Log::info('物料信息查询', [
                'material_id' => $materialId,
                'product_found' => $product ? 'yes' : 'no',
                'product_info' => $product
            ]);

            // 记录查询结果
            Log::info('库存查询结果', [
                'material_id' => $materialId,
                'available_stocks' => $availableStocks,
                'sql' => Db::getLastSql()
            ]);

            if (empty($availableStocks)) {
                // 查询该物料的总库存情况
                $totalStock = Db::name('inventory_realtime')
                    ->where('product_id', intval($materialId))
                    ->sum('available_quantity');

                return [
                    'success' => false,
                    'available_quantity' => floatval($totalStock),
                    'message' => '没有可用库存'
                ];
            }

            // 计算总可用库存
            $totalAvailable = array_sum(array_column($availableStocks, 'available_quantity'));

            if ($totalAvailable < $requestQuantity) {
                return [
                    'success' => false,
                    'available_quantity' => $totalAvailable,
                    'message' => '可用库存不足'
                ];
            }

            // 按需求数量分配并锁定库存
            $lockedStocks = [];
            $remainingQuantity = $requestQuantity;

            // 开启事务
            Db::startTrans();
            try {
                foreach ($availableStocks as $stock) {
                    if ($remainingQuantity <= 0) {
                        break;
                    }

                    $lockQuantity = min($remainingQuantity, $stock['available_quantity']);

                    // 创建库存锁定记录
                    $lockData = [
                        'product_id' => $materialId,
                        'warehouse_id' => $stock['warehouse_id'],
                        'quantity' => $lockQuantity,
                        'ref_type' => 'production_order',
                        'ref_id' => $productionOrderId,
                        'ref_no' => $productionOrder['order_no'], // 添加生产订单号
                        'status' => 1, // 已锁定
                        'created_by' => $this->uid,
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    $lockId = Db::name('inventory_lock')->insertGetId($lockData);

                    // 更新实时库存（减少可用库存，增加锁定库存）
                    $updateResult = Db::name('inventory_realtime')
                        ->where('id', $stock['id'])
                        ->dec('available_quantity', $lockQuantity)
                        ->inc('locked_quantity', $lockQuantity)
                        ->update();

                    if (!$updateResult) {
                        throw new \Exception("更新库存失败，仓库ID：{$stock['warehouse_id']}");
                    }

                    $lockedStocks[] = [
                        'lock_id' => $lockId,
                        'warehouse_id' => $stock['warehouse_id'],
                        'warehouse_name' => $stock['warehouse_name'],
                        'quantity' => $lockQuantity
                    ];

                    $remainingQuantity -= $lockQuantity;
                }

                // 提交事务
                Db::commit();

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                throw $e;
            }

            return [
                'success' => true,
                'locked_stocks' => $lockedStocks,
                'message' => '自动锁定成功'
            ];

        } catch (\Exception $e) {
            Log::error('自动锁定库存失败', [
                'material_id' => $materialId,
                'production_order_id' => $productionOrderId,
                'request_quantity' => $requestQuantity,
                'error' => $e->getMessage()
            ]);

            // 在异常情况下查询实际可用库存
            try {
                $actualStock = Db::name('inventory_realtime')
                    ->where('product_id', intval($materialId))
                    ->sum('available_quantity');
            } catch (\Exception $ex) {
                $actualStock = 0;
            }

            return [
                'success' => false,
                'available_quantity' => floatval($actualStock),
                'message' => '自动锁定失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试库存查询（临时调试方法）
     */
    public function testInventoryQuery()
    {
        $materialId = 2164; // 659上壳的ID

        // 查询该物料的所有库存
        $allStocks = Db::name('inventory_realtime')
            ->where('product_id', $materialId)
            ->field('id, product_id, warehouse_id, quantity, available_quantity, locked_quantity')
            ->select()
            ->toArray();

        // 查询可用库存（使用不同的条件测试）
        $availableStocks1 = Db::name('inventory_realtime')
            ->where('product_id', $materialId)
            ->where('available_quantity', '>', 0)
            ->field('id, product_id, warehouse_id, quantity, available_quantity, locked_quantity')
            ->select()
            ->toArray();

        $availableStocks2 = Db::name('inventory_realtime')
            ->where('product_id', $materialId)
            ->where('available_quantity', '>', 0.0)
            ->field('id, product_id, warehouse_id, quantity, available_quantity, locked_quantity')
            ->select()
            ->toArray();

        $availableStocks3 = Db::name('inventory_realtime')
            ->where('product_id', $materialId)
            ->where('available_quantity', '>', '0.00')
            ->field('id, product_id, warehouse_id, quantity, available_quantity, locked_quantity')
            ->select()
            ->toArray();

        // 查询产品信息
        $product = Db::name('product')
            ->where('id', $materialId)
            ->field('id, title, material_code')
            ->find();

        return json([
            'code' => 0,
            'msg' => '查询成功',
            'data' => [
                'material_id' => $materialId,
                'product_info' => $product,
                'all_stocks' => $allStocks,
                'available_stocks_int' => $availableStocks1,
                'available_stocks_float' => $availableStocks2,
                'available_stocks_string' => $availableStocks3,
                'total_available' => array_sum(array_column($allStocks, 'available_quantity'))
            ]
        ]);
    }

    /**
     * 按仓库创建出库单
     */
    private function createOutboundOrdersByWarehouse($warehouseGroups, $requestId, $requestNo, $order)
    {
        $outboundOrders = [];

        foreach ($warehouseGroups as $warehouseGroup) {
            try {
                // 生成出库单号
                $outboundNo = $this->generateOutboundNo();

                // 创建出库单主表
                $outboundData = [
                    'outbound_no' => $outboundNo,
                    'outbound_type' => 'production', // 生产出库
                    'ref_type' => 'production_material_request',
                    'ref_id' => $requestId,
                    'ref_no' => $requestNo,
                    'warehouse_id' => $warehouseGroup['warehouse_id'],
                    'department_id' => 0, // 生产部门ID，可以根据需要设置
                    'outbound_date' => date('Y-m-d'),
                    'total_quantity' => array_sum(array_column($warehouseGroup['materials'], 'quantity')),
                    'total_amount' => 0.00, // 生产领料通常不涉及金额
                    'status' => 2, // 已审核状态
                    'priority' => 1, // 普通优先级
                    'notes' => '生产领料自动出库',
                    'created_by' => $this->uid,
                    'create_time' => time(),
                    'update_time' => time()
                ];

                $outboundId = Db::name('outbound')->insertGetId($outboundData);

                // 创建出库单明细
                foreach ($warehouseGroup['materials'] as $material) {
                    $detailData = [
                        'outbound_id' => $outboundId,
                        'ref_detail_id' => 0, // 可以关联到领料单明细ID
                        'product_id' => $material['material_id'],
                        'product_name' => $material['material_name'],
                        'product_code' => $material['material_code'],
                        'specification' => $material['material_specs'] ?? '',
                        'unit' => $material['unit'],
                        'quantity' => $material['quantity'],
                        'unit_price' => 0.00, // 生产领料通常不涉及单价
                        'total_amount' => 0.00, // 生产领料通常不涉及金额
                        'batch_no' => '', // 批次号，可以根据需要设置
                        'location' => '', // 库位，可以根据需要设置
                        'quality_status' => 1, // 合格
                        'actual_quantity' => 0.00, // 实际出库数量，出库时填写
                        'shortage_quantity' => 0.00, // 缺货数量
                        'notes' => '锁定记录ID:' . $material['lock_id'], // 在备注中记录锁定记录ID
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    Db::name('outbound_detail')->insert($detailData);
                }

                $outboundOrders[] = [
                    'outbound_id' => $outboundId,
                    'outbound_no' => $outboundNo,
                    'warehouse_id' => $warehouseGroup['warehouse_id'],
                    'warehouse_name' => $warehouseGroup['warehouse_name']
                ];

                Log::info('创建生产领料出库单', [
                    'outbound_id' => $outboundId,
                    'outbound_no' => $outboundNo,
                    'warehouse_id' => $warehouseGroup['warehouse_id'],
                    'material_count' => count($warehouseGroup['materials'])
                ]);

            } catch (\Exception $e) {
                Log::error('创建出库单失败', [
                    'warehouse_id' => $warehouseGroup['warehouse_id'],
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        return $outboundOrders;
    }

    /**
     * 更新生产订单状态
     */
    private function updateProductionOrderStatus($productionOrderId)
    {
        try {
            // 检查生产订单的物料齐套状态
            $materialStatus = $this->checkProductionMaterialStatus($productionOrderId);

            // 更新生产订单的物料状态
            Db::name('produce_order')
                ->where('id', $productionOrderId)
                ->update([
                    'material_status' => $materialStatus,
                    'update_time' => time()
                ]);

            Log::info('更新生产订单物料状态', [
                'production_order_id' => $productionOrderId,
                'material_status' => $materialStatus
            ]);

        } catch (\Exception $e) {
            Log::error('更新生产订单状态失败', [
                'production_order_id' => $productionOrderId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查生产订单物料齐套状态
     */
    private function checkProductionMaterialStatus($productionOrderId)
    {
        // 这里可以根据实际业务逻辑检查物料齐套状态
        // 比如检查所有物料是否都已领料完成
        return 2; // 假设返回已齐套状态
    }

    /**
     * 生成出库单号
     */
    private function generateOutboundNo()
    {
        $prefix = 'CK';
        $date = date('Ymd');
        $maxRetries = 10;

        for ($i = 0; $i < $maxRetries; $i++) {
            // 查询当天最大的序号
            $maxNo = Db::name('outbound')
                ->where('outbound_no', 'like', $prefix . $date . '%')
                ->max('outbound_no');

            if ($maxNo) {
                $sequence = intval(substr($maxNo, -4)) + 1;
            } else {
                $sequence = 1;
            }

            $outboundNo = $prefix . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);

            // 检查是否已存在
            $exists = Db::name('outbound')
                ->where('outbound_no', $outboundNo)
                ->count();

            if (!$exists) {
                return $outboundNo;
            }

            // 如果存在，等待后重试
            usleep(10000); // 等待10毫秒
        }

        // 如果重试失败，使用时间戳后缀
        return $prefix . $date . substr((string)time(), -4);
    }
}
