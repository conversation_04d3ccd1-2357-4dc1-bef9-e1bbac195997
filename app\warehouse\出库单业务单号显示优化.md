# 出库单业务单号显示优化

## 优化目标
让仓库人员清楚地知道每个出库单是为了满足哪个具体的业务需求，通过显示对应的业务单号来提高工作效率和准确性。

## 实现方案

### 1. 添加业务单号列
在出库单列表中新增"业务单号"列，显示关联的业务单据编号。

```html
{field: 'ref_no', title: '业务单号', width: 150, templet: '#ref-no-tpl'}
```

### 2. 根据出库类型显示不同标签
通过模板根据`outbound_type`字段显示不同颜色和前缀的标签：

```html
<script type="text/html" id="ref-no-tpl">
    {{# if(d.ref_no && d.ref_no !== '') { }}
        {{# if(d.outbound_type === 'sales') { }}
            <span class="layui-badge layui-bg-blue" title="销售订单">销售: {{d.ref_no}}</span>
        {{# } else if(d.outbound_type === 'production') { }}
            <span class="layui-badge layui-bg-orange" title="生产领料">生产: {{d.ref_no}}</span>
        {{# } else if(d.outbound_type === 'transfer') { }}
            <span class="layui-badge layui-bg-cyan" title="调拨单">调拨: {{d.ref_no}}</span>
        {{# } else if(d.outbound_type === 'return') { }}
            <span class="layui-badge layui-bg-red" title="退货单">退货: {{d.ref_no}}</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray" title="其他业务">其他: {{d.ref_no}}</span>
        {{# } }}
    {{# } else { }}
        <span class="layui-text-muted">-</span>
    {{# } }}
</script>
```

## 显示效果

### 不同业务类型的标签样式

#### 销售出库
- **标签**：`销售: SC2025081000001`
- **颜色**：蓝色 (layui-bg-blue)
- **说明**：显示销售订单号，仓库知道是为客户发货

#### 生产出库
- **标签**：`生产: SC2025081000001`
- **颜色**：橙色 (layui-bg-orange)
- **说明**：显示生产订单号，仓库知道是为生产领料

#### 调拨出库
- **标签**：`调拨: DB2025081000001`
- **颜色**：青色 (layui-bg-cyan)
- **说明**：显示调拨单号，仓库知道是仓库间调拨

#### 退货出库
- **标签**：`退货: TH2025081000001`
- **颜色**：红色 (layui-bg-red)
- **说明**：显示退货单号，仓库知道是退货处理

#### 其他出库
- **标签**：`其他: QT2025081000001`
- **颜色**：灰色 (layui-bg-gray)
- **说明**：显示其他业务单号

### 无业务单号
- **显示**：`-`
- **样式**：灰色文字 (layui-text-muted)

## 业务价值

### 1. 提高工作效率
- **快速识别**：仓库人员一眼就能看出出库单的业务类型
- **减少错误**：明确的业务单号避免混淆
- **便于查找**：可以根据业务单号快速定位

### 2. 增强可追溯性
- **业务关联**：清楚地显示出库单与业务单据的关联关系
- **问题排查**：出现问题时可以快速追溯到源头业务
- **数据完整性**：确保每个出库都有明确的业务来源

### 3. 优化用户体验
- **视觉区分**：不同颜色的标签便于快速区分
- **信息丰富**：在有限的空间内展示更多有用信息
- **操作便捷**：鼠标悬停显示完整的业务类型说明

## 数据来源

### ref_no字段内容
根据`ref_type`字段确定业务单号的来源：

```php
// 生产领料
'ref_type' => 'production_material_request',
'ref_no' => 'SC2025081000001', // 生产订单号

// 销售出库
'ref_type' => 'customer_order',
'ref_no' => 'XS2025081000001', // 销售订单号

// 调拨出库
'ref_type' => 'transfer_order',
'ref_no' => 'DB2025081000001', // 调拨单号
```

### 控制器查询
确保查询时包含`ref_no`字段：

```php
->field('o.id, o.outbound_no, o.outbound_type, o.ref_type, o.ref_no, 
        o.outbound_date, o.total_quantity, o.total_amount, o.status, 
        c.name as customer_name, w.name as warehouse_name')
```

## 扩展功能

### 1. 点击跳转
可以考虑添加点击业务单号跳转到对应业务单据的功能：

```javascript
// 点击业务单号跳转到对应页面
$(document).on('click', '.ref-no-link', function() {
    var refType = $(this).data('ref-type');
    var refId = $(this).data('ref-id');
    
    switch(refType) {
        case 'customer_order':
            window.open('/customer/Order/detail?id=' + refId);
            break;
        case 'production_order':
            window.open('/Produce/Order/detail?id=' + refId);
            break;
        // 其他类型...
    }
});
```

### 2. 筛选功能
可以添加按业务单号筛选的功能：

```html
<div class="layui-inline">
    <label class="layui-form-label">业务单号</label>
    <div class="layui-input-inline">
        <input type="text" name="ref_no" placeholder="业务单号" class="layui-input">
    </div>
</div>
```

### 3. 批量操作
可以按业务类型进行批量操作：

```javascript
// 批量选择同一业务类型的出库单
function selectByType(outboundType) {
    // 选择逻辑
}
```

## 实施效果

### 仓库操作场景
1. **生产领料**：仓库看到"生产: SC2025081000001"，知道是为生产订单SC2025081000001准备物料
2. **销售发货**：仓库看到"销售: XS2025081000001"，知道是为销售订单XS2025081000001发货
3. **仓库调拨**：仓库看到"调拨: DB2025081000001"，知道是执行调拨单DB2025081000001

### 问题处理场景
1. **缺货处理**：根据业务单号快速联系相关部门
2. **质量问题**：根据业务单号追溯到具体的业务需求
3. **优先级处理**：根据业务类型和单号确定处理优先级

通过这个优化，仓库人员可以更高效、更准确地处理出库业务，减少错误和混淆。
