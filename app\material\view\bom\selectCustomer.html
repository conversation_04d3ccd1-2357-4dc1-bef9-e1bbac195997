{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}
</style>
{/block}

{block name="body"}
<div class="p-page">
    <!-- 搜索表单 -->
    <form class="layui-form gg-form-bar border-t border-x" lay-filter="searchForm">
        <div class="layui-input-inline" style="width:300px">
            <input type="text" name="keyword" placeholder="客户名称" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="reset">清空</button>
        </div>
    </form>

    <!-- 客户列表表格 -->
    <table class="layui-hide" id="customerTable" lay-filter="customerTable"></table>
</div>

<!-- 操作列模板 -->
<script type="text/html" id="tableBar">
    <a class="layui-btn layui-btn-xs" lay-event="select">选择</a>
</script>
{/block}

{block name="script"}
<script>
const moduleInit = ['tool'];
function gouguInit() {
    var table = layui.table, form = layui.form, tool = layui.tool;

    // 渲染表格
    var tableIns = table.render({
        elem: '#customerTable',
        url: '/material/bom/selectCustomer',
        cols: [[
            {field: 'id', width: 80, title: 'ID', align: 'center'},
            {field: 'name', width: 200, title: '客户名称'},
            {field: 'address', width: 350, title: '地址'},
             {field: 'belong_name', width: 100, title: '归属业务员', align: 'center'},
             {width: 100, title: '操作', templet: '#tableBar', fixed: 'right', align: 'center'}
        ]],
        page: true,
        limit: 10,
        text: {
            none: '暂无客户数据'
        }
    });

    // 监听搜索
    form.on('submit(search)', function(data) {
        tableIns.reload({
            where: data.field,
            page: {
                curr: 1
            }
        });
        return false;
    });

    // 监听表格工具条
    table.on('tool(customerTable)', function(obj) {
        var data = obj.data;
        if (obj.event === 'select') {
            // 选择客户
            selectCustomerCallback(data);
        }
    });

    // 客户选择回调函数
    window.selectCustomerCallback = function(customerData) {
        // 获取父窗口
        var parentWindow = parent.window;

        // 填充客户信息到父窗口的表单
        if (parentWindow.$) {
            parentWindow.$('input[name="customer_name"]').val(customerData.name);
            parentWindow.$('input[name="customer_id"]').val(customerData.id);

            // 触发父窗口的表单验证
            if (parentWindow.layui && parentWindow.layui.form) {
                parentWindow.layui.form.render();
            }
        }

        // 关闭当前弹窗
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);

        // 提示选择成功
        parent.layer.msg('客户选择成功', {icon: 1});
    };
}
</script>
{/block}
