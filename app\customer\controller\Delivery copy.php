<?php
namespace app\customer\controller;


use app\base\BaseController;
use app\customer\model\Order;
use app\customer\model\OrderDetail;
use app\customer\model\Delivery as DeliveryModel;
use app\customer\model\DeliveryItem;
use app\customer\model\DeliveryLog;
use app\warehouse\model\Outbound as OutboundModel;
use app\warehouse\model\OutboundDetail as OutboundDetailModel;
use app\warehouse\model\Inventory as InventoryModel;
use think\facade\Db;
use think\facade\Session;
use think\facade\Validate;
use think\facade\View;

class Delivery extends BaseController
{
    /**
     * 发货指令列表页
     */
    public function index()
    {
        return view();
    }
    
    /**
     * 获取发货指令列表
     */
    public function getList()
    {
        $param = $this->request->param();
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 10;
        
        $map = [];
        
        // 搜索条件
        if (!empty($param['delivery_no'])) {
            $map[] = ['delivery_no', 'like', '%' . $param['delivery_no'] . '%'];
        }
        
        if (!empty($param['order_no'])) {
            $map[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
        }
        
        if (isset($param['customer_id']) && $param['customer_id'] !== '') {
            $map[] = ['customer_id', '=', $param['customer_id']];
        }
        
        if (isset($param['status']) && $param['status'] !== '') {
            $map[] = ['status', '=', $param['status']];
        }
        
        // 获取数据
        $list = DeliveryModel::where($map)
            ->order('id', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
            
        return json(['code' => 0, 'msg' => 'ok', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    /**
     * 选择订单页面
     */
    public function selectOrder()
    {
        return view();
    }
    
    /**
     * 获取可发货订单列表
     */
    public function getOrderList()
    {
        $param = $this->request->param();
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 10;
        
        $map = [];
        $map[] = ['status', 'in', [1, 2, 3]]; // 已审核、部分发货、待补货的订单
        
        // 搜索条件
        if (!empty($param['order_no'])) {
            $map[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
        }
        
        if (isset($param['customer_id']) && $param['customer_id'] !== '') {
            $map[] = ['customer_id', '=', $param['customer_id']];
        }
        
        // 获取数据
        $list = Order::with(['customer'])
            ->where($map)
            ->order('id', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
        
        // 检查订单是否有可发货商品
        $orderIds = array_column($list['data'], 'id');
        $orderItems = OrderDetail::where('order_id', 'in', $orderIds)
            ->field('order_id, SUM(quantity) as total_qty, SUM(delivered_qty) as total_delivered')
            ->group('order_id')
            ->select()
            ->toArray();
        
        $orderItemMap = [];
        foreach ($orderItems as $item) {
            $orderItemMap[$item['order_id']] = [
                'total_qty' => $item['total_qty'],
                'total_delivered' => $item['total_delivered']
            ];
        }
        
        foreach ($list['data'] as &$order) {
            if (isset($orderItemMap[$order['id']])) {
                $itemInfo = $orderItemMap[$order['id']];
                $order['has_shippable'] = ($itemInfo['total_qty'] > $itemInfo['total_delivered']);
            } else {
                $order['has_shippable'] = false;
            }
        }
            
        return json(['code' => 0, 'msg' => 'ok', 'count' => $list['total'], 'data' => $list['data']]);
    }
    
    /**
     * 添加发货指令页面
     */
    public function add()
    {
        // 生成发货单号
        $delivery_no = 'FH' . date('YmdHis') . rand(1000, 9999);
       // $this->assign('delivery_no', $delivery_no);
        
        return view();
    }
    
    /**
     * 获取订单信息
     */
    public function getOrderInfo()
    {
        $id=request()->param('id/d');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $order = Order::with(['customer'])->find($id);
        if (!$order) {
            return json(['code' => 1, 'msg' => '订单不存在']);
        }
        
        return json(['code' => 0, 'msg' => 'ok', 'data' => ['order' => $order]]);
    }
    
    /**
     * 获取订单商品
     */
    public function getOrderItems()
    {
        $orderId = request()->param('order_id/d');
        if (!$orderId) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 添加调试日志
        \think\facade\Log::record('获取订单商品参数: order_id=' . $orderId, 'debug');
        
        $items = OrderDetail::alias('oi')
            ->join('product p', 'p.id = oi.product_id', 'LEFT')
            ->field('oi.*, p.title as product_name,p.material_code,p.unit,p.specs')
            ->where('oi.order_id', $orderId)
            ->select()
            ->toArray();
            
        // 调试输出结果
        \think\facade\Log::record('查询到订单商品数量: ' . count($items), 'debug');
        
        // 获取库存信息
        foreach ($items as &$item) {
            // 查询总库存
            $inventoryQty = \think\facade\Db::name('inventory')
                ->where('product_id', $item['product_id'])
                ->where('status', 1)
                ->sum('quantity') ?: 0;
            
            // 查询customer_order_delivery中所有与$orderId相同的ID
            $customerOrderDeliveryIds = \think\facade\Db::name('customer_order_delivery')
                ->where('order_id', $orderId)
                ->column('id');
            
                
            // 查询锁定库存
            $lockedQty = \think\facade\Db::name('inventory_lock')
                ->where('product_id', $item['product_id'])
                //->where('ref_type', 'customer_order_delivery')
                //->where('ref_id', 'in', $customerOrderDeliveryIds)
                ->where('status', 1)
                ->sum('quantity') ?: 0;

             // 本次锁定库存
            $locke_my = \think\facade\Db::name('inventory_lock')
            ->where('product_id', $item['product_id'])
            ->where('ref_type', 'customer_order_delivery')
            ->where('ref_id', 'in', $customerOrderDeliveryIds)
            ->where('status', 1)
            ->sum('quantity') ?: 0;
            
            // 计算可用库存
            $availableQty = $inventoryQty - $lockedQty;
            
            // 添加到返回数据中 
            $item['inventory_qty'] = $inventoryQty;
            $item['locked_qty'] = $lockedQty;
            $item['available_qty'] = $availableQty;
            $item['locke_my'] = $locke_my;
            
            // 查询customer_order_delivery_item本产品发货数量合计
            $deliveredQty = Db::name('customer_order_delivery_item')
                ->alias('d')
                ->join('customer_order_delivery c', 'd.delivery_id = c.id')
                ->where('d.product_id', $item['product_id'])
                ->where('d.order_id', $orderId)
                ->where('c.status', 1)
                ->sum('d.delivery_qty') ?: 0;
            
            $item['delivered_qty'] = $deliveredQty;
            //$item['delivered_qty'] = 
            

            
        }
        
        return json(['code' => 0, 'msg' => 'ok', 'data' => ['items' => $items]]);
    }
    
    /**
     * 保存发货指令
     */
    public function save()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }
        
        $param = request()->param();
        
        // 验证数据
        try {
            $deliveryData = $param['delivery']; //发货信息
            $items = $param['items']; //发货商品
            
            // 校验必要字段
            $validate = Validate::rule([
                'order_id' => 'require|number',
                'delivery_type' => 'require|number',
                'address' => 'require',
                'contact' => 'require',
                'phone' => 'require|mobile'
            ]);
            
            if (!$validate->check($deliveryData)) {
                return json(['code' => 1, 'msg' => $validate->getError()]);
            }
            
            // 检查订单是否存在
            $order = Order::find($deliveryData['order_id']);
            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }
           
            // 开始事务
            Db::startTrans();
            
          
            // 创建发货指令 delivery_no
            $delivery = new DeliveryModel();
            $delivery->order_id = $deliveryData['order_id'];
            $delivery->order_no = $deliveryData['order_no'];
            $delivery->delivery_no = $this->generateOrderNo();
            $delivery->customer_id = $deliveryData['customer_id'];
            $delivery->customer_name = $deliveryData['customer_name'];
            $delivery->address = $deliveryData['address'];
            $delivery->contact = $deliveryData['contact'];
            $delivery->phone = $deliveryData['phone'];
            $delivery->delivery_type = $deliveryData['delivery_type'];
            $delivery->logistics_id = isset($deliveryData['logistics_id']) ? $deliveryData['logistics_id'] : 0;
            $delivery->expect_time = $deliveryData['expect_time'];
            $delivery->remark = isset($deliveryData['remark']) ? $deliveryData['remark'] : '';
            $delivery->status = 0; // 待处理
            $delivery->creator_id = $this->uid;
            $delivery->save();
            
            
            // 创建发货商品
            foreach ($items as $item) {
                // 获取订单商品 
                $orderItem = OrderDetail::alias('oi')
                    ->join('product p', 'p.id = oi.product_id', 'LEFT')
                    ->field('oi.*, p.title as product_name,p.unit,p.specs,p.material_code')
                    ->where('oi.id', $item['order_item_id'])
                    ->find();
                
                if (!$orderItem) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '订单商品不存在']);
                }
                
           
                
              
                // 遍历原来的发货明细合计
                $totalDeliveredQty = DeliveryItem::where('order_item_id', $item['order_item_id'])
                    ->sum('delivery_qty');
                
                 
                // 验证发货数量
                $remainQty = $orderItem['quantity'] - $totalDeliveredQty;
                if ($item['delivery_qty'] > $remainQty) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' =>'警告：'. $orderItem['product_name'] .'-' . $orderItem['material_code'] . ' 【发货数量不能大于可发货数量】']);
                }
                
                // 如果本次发货数量为0就不写入发货商品明细
                if ($item['delivery_qty'] == 0) {
                    continue;
                }

                //判断库存是否充足
                $inventory = InventoryModel::where('product_id', $orderItem['product_id'])
                    ->where('quantity', '>=', $item['delivery_qty'])
                    ->find();
                if (!$inventory) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' =>'警告：'. $orderItem['product_name'].'-' . $orderItem['material_code'] . ' 【库存不足】']);
                }



                // 执行锁库存操作 
                $lockTime = time();
                $expireTime = $lockTime + (7 * 24 * 3600); // 7天后过期
                // 计算锁定的库存数量
                $lockQty = $item['delivery_qty'];
                // 获取库存对象
                $inventoryModel = new InventoryModel();
                $inventoryInstance = $inventoryModel->where('product_id', $orderItem['product_id'])
                    ->where('quantity', '>=', $lockQty)
                    ->where('available_quantity', '>=', $lockQty)
                    ->find();
                
                if (!$inventoryInstance) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '警告：' . $orderItem['product_name'] . '-' . $orderItem['material_code'] . ' 【可用库存不足，无法锁定】']);
                }
                
                // 设置锁定备注
                $remark = "发货单 [{$delivery->delivery_no}] 发货锁定库存";
                try {
                    // 执行锁库存操作
                    $result = $inventoryInstance->lockStock(
                        'customer_order_delivery', 
                        $delivery->id, 
                        $lockQty, 
                        $remark, 
                        $expireTime
                    );
                    // 在发货日志中记录锁库存操作
                    $lockLog = new DeliveryLog();
                    $lockLog->delivery_id = $delivery->id;
                    $lockLog->content = "锁定商品 [{$orderItem['product_name']}] 库存 {$lockQty} {$orderItem['unit_name']}，锁定期7天";
                    $lockLog->operator_id = $this->uid;
                    $lockLog->operator_name = Session::get('admin.username') ?? '';
                    $lockLog->save();
                    
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '库存锁定失败：' . $e->getMessage()]);
                }
                //锁库存结束

                // 创建发货商品
                $deliveryItem = new DeliveryItem();
                $deliveryItem->delivery_id = $delivery->id;
                $deliveryItem->order_id = $delivery->order_id;
                $deliveryItem->order_item_id = $item['order_item_id'];
                $deliveryItem->product_id = $orderItem['product_id'];
                $deliveryItem->product_name = $orderItem['product_name'];
                $deliveryItem->sku_id = $orderItem['sku_id'];
                $deliveryItem->sku_name = $orderItem['sku_name'] ?? '';
                $deliveryItem->unit_name = $orderItem['unit_name'] ?? '';
                $deliveryItem->order_qty = $orderItem['quantity'];
                $deliveryItem->delivery_qty = $item['delivery_qty'];
                $deliveryItem->remark = $item['remark'] ?? '';
                $deliveryItem->save();


            }
            
           
            // 生成销售出库单 
            $outboundData = [
                'outbound_no' => OutboundModel::generateOutboundNo(1),
                'related_bill_type' => 'customer_order_delivery',
                'related_bill_no' => $delivery->delivery_no,
                'customer_id' => $delivery->customer_id,
                'outbound_date' => date('Y-m-d'),
                'notes' => '由销售订单' . $delivery->delivery_no . '自动生成',
                'status' => 1, // 草稿
                'outbound_type' => 1, // 销售出库 
                'notes' =>$delivery->remark ,
                'created_by' =>$this->uid,
                'create_time' => time(),
                'update_time' => time()
            ];
          
            
            $outboundId = Db::name('warehouse_outbound')->insertGetId($outboundData);
            
            // 创建出库单明细
            foreach ($items as $item) {
                // 获取订单商品
                $orderItem = OrderDetail::where('id', $item['order_item_id'])->find();
                
                // 查询产品信息
                $product = Db::name('product')->where('id', $orderItem['product_id'])->find();
                
                // 查询可用库存并智能分配库位
                $inventories = Db::name('inventory')
                    ->where('product_id', $orderItem['product_id'])
                    ->where('quantity', '>', 0)
                    ->where('status', 1)
                    ->order('id asc') // 先进先出
                    ->select();
                
                $remainQty = $item['delivery_qty'];
                
                foreach ($inventories as $inventory) {
                    if ($remainQty <= 0) break;
                    
                    // 计算本次分配数量
                    $allocateQty = min($remainQty, $inventory['quantity']);
                    
                    // 创建出库单明细
                    $outboundItemData = [
                        'outbound_id' => $outboundId,
                        'warehouse_id' => $inventory['warehouse_id'],
                        'location_id' => $inventory['location_id'],
                        'product_id' => $orderItem['product_id'],
                        'product_code' => $product['material_code'] ?? '',
                        'product_name' => $product['title'] ?? '',
                        'spec' => $product['specs'] ?? '',
                        'unit' => $product['unit'] ?? '',
                        'quantity' => $allocateQty,
                        'price' => $orderItem['price'] ?? 0,
                        'amount' => ($orderItem['price'] ?? 0) * $allocateQty,
                        'batch_no' => $inventory['batch_no'] ?? '',
                        'production_date' => $inventory['production_date'] ?? null,
                        'expiry_date' => $inventory['expiry_date'] ?? null,
                        'notes' => $item['remark'] ?? '',
                        'sort_order' => 0,
                        'create_time' => time()
                    ];
                    
                    Db::name('warehouse_outbound_detail')->insert($outboundItemData);
                    
                    // 减少剩余需要分配的数量
                    $remainQty -= $allocateQty;
                }
                
                // 如果库存不足，创建一条记录但标记库存不足
                if ($remainQty > 0) {
                    $outboundItemData = [
                        'outbound_id' => $outboundId,
                        'warehouse_id' => 0, // 暂无可用仓库
                        'location_id' => 0, // 暂无可用库位
                        'product_id' => $orderItem['product_id'],
                        'product_code' => $product['material_code'] ?? '',
                        'product_name' => $product['title'] ?? '',
                        'spec' => $product['specs'] ?? '',
                        'unit' => $product['unit'] ?? '',
                        'quantity' => $remainQty,
                        'price' => $orderItem['price'] ?? 0,
                        'amount' => ($orderItem['price'] ?? 0) * $remainQty,
                        'batch_no' => '',
                        'notes' => '库存不足，需手动分配' . ($item['remark'] ? '，' . $item['remark'] : ''),
                        'sort_order' => 99, // 排在最后
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                    
                    Db::name('warehouse_outbound_detail')->insert($outboundItemData);
                }
            }
            
           
            // 添加发货日志 order_id
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '创建发货指令并生成销售出库单';
            $log->operator_id = $this->uid;
            $log->operator_name = '';
            $log->save();
            
            // 提交事务
            Db::commit();
            return json(['code' => 0, 'msg' => '发货指令创建成功', 'data' => ['delivery_id' => $delivery->id]]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage(),'data' => []]);
        }
    }
    
    /**
     * 查看发货指令
     */
    public function view()
    {
        $id = request()->param('id/d');
        if (!$id) {
            $this->error('参数错误');
        }
        
        // 获取发货指令        
        $delivery = DeliveryModel::withTrashed()->find($id);
        if (!$delivery) {
            $this->error('发货指令不存在');
        }
        
        // 获取发货商品
        $items = DeliveryItem::withTrashed()->with('product')->where('delivery_id', $id)->select();
        // 获取发货日志
        $logs = DeliveryLog::withTrashed()->where('delivery_id', $id)
            ->order('id', 'desc')
            ->select();
       
        return View::fetch('view', [
            'delivery' => $delivery,
            'items'    => $items,
            'logs'     => $logs
        ]);
        
        
    }
    
    /**
     * 开始处理发货指令
     */
    public function process()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }
        
        $id = $this->request->param('id/d');
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取发货指令
        $delivery = DeliveryModel::find($id);
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货指令不存在']);
        }
        
        // 检查状态
        if ($delivery->status != 0) {
            return json(['code' => 1, 'msg' => '发货指令状态不正确']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新状态
            $delivery->status = 1; // 处理中
            $delivery->handler_id = Session::get('user.id');
            $delivery->handler_name = Session::get('user.realname');
            $delivery->handle_time = date('Y-m-d H:i:s');
            $delivery->save();
            
            // 添加日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '开始处理发货指令';
            $log->operator_id = Session::get('user.id');
            $log->operator_name = Session::get('user.realname');
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '操作成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
    
    /**
     * 完成发货
     */
    public function complete()
    {
        if (!$this->request->isPost()) {
            return json(['code' =>.1, 'msg' => '请求方式错误']);
        }
        
        $param = $this->request->param();
        $id = $param['id'];
        $logistics_no = $param['logistics_no'];
        $delivery_time = $param['delivery_time'];
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取发货指令
        $delivery = DeliveryModel::find($id);
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货指令不存在']);
        }
        
        // 检查状态
        if ($delivery->status != 1) {
            return json(['code' => 1, 'msg' => '发货指令状态不正确']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新状态
            $delivery->status = 2; // 已完成
            $delivery->logistics_no = $logistics_no;
            $delivery->delivery_time = $delivery_time;
            $delivery->save();
            
            // 更新订单商品发货数量
            $deliveryItems = DeliveryItem::where('delivery_id', $id)->select();
            foreach ($deliveryItems as $item) {
                // 更新订单商品已发货数量
                $orderItem = OrderDetail::find($item->order_item_id);
                if ($orderItem) {
                    $orderItem->delivered_qty = $orderItem->delivered_qty + $item->delivery_qty;
                    $orderItem->save();
                }
            }
            
            // 检查订单是否全部发货完成
            $order = Order::find($delivery->order_id);
            if ($order) {
                $allDelivered = true;
                $hasPartDelivered = false;
                
                $orderItems = OrderDetail::where('order_id', $order->id)->select();
                foreach ($orderItems as $item) {
                    if ($item->delivered_qty < $item->quantity) {
                        $allDelivered = false;
                        if ($item->delivered_qty > 0) {
                            $hasPartDelivered = true;
                        }
                    }
                }
                
                // 更新订单状态
                if ($allDelivered) {
                    $order->status = 2; // 已发货
                } elseif ($hasPartDelivered) {
                    $order->status = 3; // 待补货
                }
                $order->save();
            }
            
            // 添加日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '完成发货，物流单号：' . $logistics_no;
            $log->operator_id = Session::get('user.id');
            $log->operator_name = Session::get('user.realname');
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '操作成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
    
    /**
     * 取消发货
     */
    public function cancel()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误', 'data' => []]);
        }
        
        $param = request()->param();
        $id = $param['id'];
        $cancel_reason = $param['cancel_reason'];
        
        if (!$id || !$cancel_reason) {
            return json(['code' => 1, 'msg' => '参数错误', 'data' => []]);
        }
        
        // 获取发货指令 
        $delivery = DeliveryModel::find($id);
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货指令不存在', 'data' => []]);
        }
        
        // 检查状态
        if ($delivery->status >= 2) {
            return json(['code' => 1, 'msg' => '发货指令状态不正确', 'data' => []]);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新状态
            // 软删除发货指令
           
            $delivery->status = 3; // 已取消
            $delivery->cancel_reason = $cancel_reason;
            $delivery->delete_time = time(); // 如果需要同时软删除
            $delivery->save();
            
            
            // 添加日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '取消发货，原因：' . $cancel_reason;
            $log->operator_id =$this->uid;
            $log->save();
            //同时删除出库单
            // 获取出库单
            $outbound = OutboundModel::where('related_bill_no', $delivery->delivery_no)->find();
            if ($outbound) {
                // 检查出库单状态
                if ($outbound->status == OutboundModel::STATUS_PENDING) {
                    throw new \Exception('出库单已出库，不允许取消发货');
                }
                // 软删除出库单
                $outbound->delete();
            }
             
            // 软删除出库单明细
            $outboundDetail = OutboundDetailModel::where('outbound_id', $outbound->id)->select();
            foreach ($outboundDetail as $item) {
                $item->delete();


                
            }

    
        // 解锁库存
        $inventoryModel = new InventoryModel();
        $inventoryInstances = $inventoryModel->where('id', 'in', function ($query) use ($delivery) {
            $query->name('inventory_lock')
                ->where('ref_type', 'customer_order_delivery')
                ->where('ref_id', $delivery->id)
                ->where('status', 1)
                ->field('inventory_id');
        })->select();

        foreach ($inventoryInstances as $inventoryInstance) {
            try {
                // 执行解锁操作
                $inventoryInstance->unlockStock(
                    'customer_order_delivery',
                    $delivery->id,
                    '发货单取消，自动解锁库存'
                );
            } catch (\Exception $e) {
                // 记录错误日志
                \think\facade\Log::error('解锁库存失败：'.$e->getMessage());
            }
        }
    //库存解锁完成
            



            Db::commit();
            return json(['code' => 0, 'msg' => '操作成功', 'data' => []]);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }

     /**
     * 生成订单号
     */
 

     private function generateOrderNo()
     {
         $prefix = 'FH' . date('Ymd');
         $maxRetries = 3; // 最大重试次数
         $retryCount = 0;
         
         while ($retryCount < $maxRetries) {
             // 开启事务
             Db::startTrans();
             try {
                 // 使用FOR UPDATE锁定查询结果
                 $maxNumber = Db::name('customer_order_delivery')
                     ->where('delivery_no', 'like', $prefix . '%')
                     ->order('id', 'desc')
                     ->lock(true)
                     ->value('delivery_no');
                 
                 if ($maxNumber) {
                     // 提取最后4位数字并加1
                     $lastNumber = intval(substr($maxNumber, -4));
                     $newNumber = $lastNumber + 1;
                 } else {
                     // 如果没有记录，从0001开始
                     $newNumber = 1;
                 }
                 
                 // 格式化为4位数字，不足补0
                 $newPlanNo = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
                 
                 // 直接尝试插入一条测试记录
                 Db::name('customer_order_delivery')->insert([
                     'delivery_no' => $newPlanNo,
                     'status' => -1, // 使用特殊状态标记为临时记录
                     'create_time' => time()
                 ]);
                 
                 // 如果插入成功，删除测试记录并提交事务
                 Db::name('customer_order_delivery')->where('delivery_no', $newPlanNo)->where('status', -1)->delete();
                 Db::commit();
                 
                 return $newPlanNo;
             } catch (\Exception $e) {
                 Db::rollback();
                 $retryCount++;
                 
                 if ($retryCount >= $maxRetries) {
                     throw new \Exception('生成编号失败，请重试');
                 }
                 
                 // 短暂延迟后重试
                 usleep(100000); // 延迟100毫秒
             }
         }
         
         throw new \Exception('生成编号失败，请重试');
     }
} 