<?php
namespace app\api\service;

use think\facade\Db;
use app\warehouse\model\Inventory;
use app\warehouse\controller\Outbound;
use app\api\service\InventoryReserveService;
use think\facade\Log;

/**
 * 库存清理服务类
 * 用于清理过期的库存记录（包括锁定和预占）
 */
class InventoryCleanService
{
    /**
     * 清理过期的库存记录（包括锁定和预占）
     * 
     * @return array 清理结果
     */
    public function cleanExpiredRecords()
    {
        // 记录开始时间
        $startTime = microtime(true);
        
        // 初始化日志文件
        $logFile = runtime_path() . 'log/inventory_clean_' . date('Ymd') . '.log';
        error_log('[INFO] 开始清理过期库存记录，时间: ' . date('Y-m-d H:i:s') . "\n", 3, $logFile);
        
        $result = [
            'success' => true,
            'message' => '清理成功',
            'data' => [
                'expired_locks' => 0,
                'expired_reserves' => 0,
                'execution_time' => 0
            ],
            'errors' => []
        ];
        
        // 步骤1：清理过期的库存锁定
        try {
            error_log('[INFO] 开始清理过期库存锁定记录...' . "\n", 3, $logFile);
            $lockResult = Outbound::cleanExpiredLocks();
            
            if ($lockResult === true) {
                error_log('[INFO] 过期库存锁定清理成功' . "\n", 3, $logFile);
                $result['data']['expired_locks'] = $lockResult === true ? 1 : $lockResult; // 更好的方式是修改cleanExpiredLocks返回清理的记录数
            } else {
                error_log('[ERROR] 过期库存锁定清理失败' . "\n", 3, $logFile);
                $result['errors'][] = '过期库存锁定清理失败';
                $result['success'] = false;
            }
        } catch (\Exception $e) {
            error_log('[ERROR] 清理过期库存锁定异常: ' . $e->getMessage() . "\n", 3, $logFile);
            error_log('[ERROR] 异常位置: ' . $e->getFile() . ':' . $e->getLine() . "\n", 3, $logFile);
            $result['errors'][] = '清理过期库存锁定异常: ' . $e->getMessage();
            $result['success'] = false;
        }
        
        // 步骤2：清理过期的库存预占
        try {
            error_log('[INFO] 开始清理过期库存预占记录...' . "\n", 3, $logFile);
            $reserveService = new InventoryReserveService();
            $reserveResult = $reserveService->checkExpiredReserves();
            
            if ($reserveResult['success']) {
                error_log('[INFO] 过期库存预占清理成功，共清理 ' . ($reserveResult['count'] ?? 0) . ' 条记录' . "\n", 3, $logFile);
                $result['data']['expired_reserves'] = $reserveResult['count'] ?? 0;
            } else {
                error_log('[ERROR] 过期库存预占清理失败: ' . ($reserveResult['message'] ?? '未知错误') . "\n", 3, $logFile);
                $result['errors'][] = '过期库存预占清理失败: ' . ($reserveResult['message'] ?? '未知错误');
                $result['success'] = false;
            }
        } catch (\Exception $e) {
            error_log('[ERROR] 清理过期库存预占异常: ' . $e->getMessage() . "\n", 3, $logFile);
            error_log('[ERROR] 异常位置: ' . $e->getFile() . ':' . $e->getLine() . "\n", 3, $logFile);
            $result['errors'][] = '清理过期库存预占异常: ' . $e->getMessage();
            $result['success'] = false;
        }
        
        // 记录结束时间和执行时间
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 3);
        error_log('[INFO] 清理过期库存记录完成，执行时间: ' . $executionTime . ' 秒' . "\n", 3, $logFile);
        $result['data']['execution_time'] = $executionTime;
        
        if (!empty($result['errors'])) {
            $result['message'] = '清理过程中出现错误，详情请查看日志';
        }
        
        return $result;
    }
} 