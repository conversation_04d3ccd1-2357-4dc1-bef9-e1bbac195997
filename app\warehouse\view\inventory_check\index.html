{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
        <div class="body-table layui-tab layui-tab-brief" lay-filter="tab">
            <ul class="layui-tab-title">
                <li class="layui-this">全部</li>
                <li>待盘点</li>
                <li>盘点中</li>
                <li>已完成</li>
                <li>已取消</li>
            </ul>
        </div>
    </div>
    <form class="layui-form gg-form-bar border-x" id="barsearchform">
        <div class="layui-input-inline" style="width:150px;">
            <select name="warehouse_id">
                <option value="">选择仓库</option>
                {volist name="warehouses" id="warehouse"}
                <option value="{$warehouse.id}">{$warehouse.name}</option>
                {/volist}
            </select>
        </div>
        <div class="layui-input-inline" style="width:175px;">
            <input type="text" class="layui-input" id="time_range" placeholder="选择时间范围" readonly name="time_range">
        </div>
        <div class="layui-input-inline" style="width:220px;">
            <input type="text" name="keywords" placeholder="输入关键字，盘点单号" class="layui-input" autocomplete="off" />
        </div>
        <div class="layui-input-inline" style="width:150px">
            <input type="hidden" name="tab" value="0" />
            <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
            <button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
        </div>
    </form>
    <table class="layui-hide" id="table_check" lay-filter="table_check"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="add">
        <span>+ 创建盘点单</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="batchStart">
        <span>批量开始</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="batchComplete">
        <span>批量完成</span>
    </button>
    <button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="statistics">
        <span>统计报表</span>
    </button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus','laydatePlus'];
    function gouguInit() {
        console.log('开始初始化库存盘点页面...');

        // 检查必要的模块是否加载
        if (!layui.tablePlus) {
            console.error('tablePlus模块未加载');
            layer.msg('页面初始化失败：tablePlus模块未加载', {icon: 2});
            return;
        }

        if (!layui.tool) {
            console.error('tool模块未加载');
            layer.msg('页面初始化失败：tool模块未加载', {icon: 2});
            return;
        }

        var table = layui.tablePlus, element = layui.element, tool = layui.tool, laydatePlus = layui.laydatePlus;
        console.log('模块加载成功:', {table: !!table, element: !!element, tool: !!tool, laydatePlus: !!laydatePlus});

        //tab切换
        element.on('tab(tab)', function(data){
            var statusMap = {0: "", 1: "0", 2: "1", 3: "2", 4: "3"};
            $('[name="tab"]').val(data.index);
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({where:{status:statusMap[data.index]},page:{curr:1}});
            return false;
        });

        //日期范围
        var time_range = new laydatePlus({'target':'time_range'});

        layui.pageTable = table.render({
            elem: "#table_check"
            ,title: "盘点单列表"
            ,toolbar: "#toolbarDemo"
            ,url: "/warehouse/inventory_check/index"
            ,page: true
            ,limit: 20
            ,cellMinWidth: 80
            ,height: 'full-152'
            ,parseData: function(res){ //res 即为原始返回的数据
                console.log('返回的数据:', res);
                if (res.code !== 0) {
                    layer.msg(res.msg || '数据加载失败');
                }
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.count, //解析数据长度
                    "data": res.data //解析数据列表
                };
            }
            ,done: function(res, curr, count){
                console.log('表格渲染完成:', res);
                if (res.code !== 0) {
                    layer.msg(res.msg || '数据加载失败', {icon: 2});
                }
            }
            ,cols: [[ //表头
                {
                    type: 'checkbox',
                    width: 50
                },{
                    field: 'id',
                    title: 'ID',
                    align: 'center',
                    width: 80
                },{
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    width: 100,
                    templet: function (d) {
                        var statusClass = {
                            0: 'layui-bg-gray', // 待盘点
                            1: 'layui-bg-orange', // 盘点中
                            2: 'layui-bg-green', // 已完成
                            3: 'layui-bg-red' // 已取消
                        };
                        var statusText = {
                            0: '待盘点',
                            1: '盘点中',
                            2: '已完成',
                            3: '已取消'
                        };
                        var html = '<span class="layui-badge ' + statusClass[d.status] + '">'+statusText[d.status]+'</span>';
                        return html;
                    }
                },{
                    field: 'check_no',
                    title: '盘点单号',
                    align: 'center',
                    width: 150,
                    templet: function(d) {
                        return '<div><a href="javascript:;" class="side-a" data-href="/warehouse/inventory_check/detail/id/' + d.id + '.html">' + d.check_no + '</a></div>';
                    }
                },{
                    field: 'warehouse_name',
                    title: '仓库',
                    align: 'center',
                    width: 120,
                    templet: function(d) {
                        return d.warehouse ? d.warehouse.name : (d.warehouse_name || '');
                    }
                },{
                    field: 'total_products',
                    title: '产品总数',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return d.total_products || 0;
                    }
                },{
                    field: 'checked_products',
                    title: '已盘点',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return d.checked_products || 0;
                    }
                },{
                    field: 'creator_name',
                    title: '创建人',
                    align: 'center',
                    width: 100,
                    templet: function(d) {
                        return d.creator ? d.creator.nickname : (d.creator_name || '');
                    }
                },{
                    field: 'create_time',
                    title: '创建时间',
                    align: 'center',
                    width: 160,
                    templet: function(d) {
                        if (d.create_time) {
                            var date = new Date(d.create_time * 1000);
                            return date.getFullYear() + '-' +
                                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                                   String(date.getDate()).padStart(2, '0') + ' ' +
                                   String(date.getHours()).padStart(2, '0') + ':' +
                                   String(date.getMinutes()).padStart(2, '0');
                        }
                        return '';
                    }
                },{
                    field: 'notes',
                    title: '备注',
                    minWidth: 120
                },{
                    field: 'right',
                    fixed:'right',
                    title: '操作',
                    width: 200,
                    align: 'center',
                    ignoreExport:true,
                    templet: function (d) {
                        var html = '<div class="layui-btn-group">';
                        var btn0='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</span>';
                        var btn1='<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
                        var btn2='<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="cancel">取消</span>';
                        var btn3='<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="start">开始</span>';
                        var btn4='<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="complete">完成</span>';
                        var btn5='<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="check">盘点</span>';

                        switch(d.status){
                            case 0: // 待盘点
                                return html+btn0+btn1+btn2+btn3+'</div>';
                            case 1: // 盘点中
                                return html+btn0+btn2+btn5+btn4+'</div>';
                            case 2: // 已完成
                                return html+btn0+'</div>';
                            case 3: // 已取消
                                return html+btn0+'</div>';
                            default:
                                return html+btn0+'</div>';
                        }
                    }
                }
            ]]
        });

        console.log('表格初始化完成');


        //表头工具栏事件
        table.on('toolbar(table_check)', function(obj){
            if (obj.event === 'add'){
                tool.side("/warehouse/inventory_check/add", "创建盘点单");
                return;
            }
            if (obj.event === 'batchStart'){
                var checkStatus = table.checkStatus('table_check');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要开始的盘点单');
                    return;
                }
                var ids = [];
                for(var i = 0; i < data.length; i++){
                    ids.push(data[i].id);
                }
                layer.confirm('确定要批量开始选中的盘点单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/inventory_check/batchStart", { ids: ids }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'batchComplete'){
                var checkStatus = table.checkStatus('table_check');
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要完成的盘点单');
                    return;
                }
                var ids = [];
                for(var i = 0; i < data.length; i++){
                    ids.push(data[i].id);
                }
                layer.confirm('确定要批量完成选中的盘点单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            layui.pageTable.reload();
                        }
                    }
                    tool.post("/warehouse/inventory_check/batchComplete", { ids: ids }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'statistics'){
                tool.side("/warehouse/inventory_check/statistics", "盘点统计");
                return;
            }
        });

        table.on('tool(table_check)',function (obj) {
            var data = obj.data;
            if (obj.event === 'view') {
                tool.side("/warehouse/inventory_check/detail?id="+data.id, "盘点单详情");
                return;
            }
            if (obj.event === 'edit') {
                tool.side("/warehouse/inventory_check/add?id="+data.id, "编辑盘点单");
                return;
            }
            if (obj.event === 'cancel') {
                layer.confirm('确定要取消该盘点单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 3, status_text: '已取消'});
                        }
                    }
                    tool.post("/warehouse/inventory_check/cancel", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'start') {
                layer.confirm('确定要开始该盘点单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 1, status_text: '盘点中'});
                        }
                    }
                    tool.post("/warehouse/inventory_check/start", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'complete') {
                layer.confirm('确定要完成该盘点单吗?', { icon: 3, title: '提示' }, function (index) {
                    let callback = function (e) {
                        layer.msg(e.msg);
                        if (e.code == 0) {
                            obj.update({status: 2, status_text: '已完成'});
                        }
                    }
                    tool.post("/warehouse/inventory_check/complete", { id: data.id }, callback);
                    layer.close(index);
                });
                return;
            }
            if (obj.event === 'check') {
                tool.side("/warehouse/inventory_check/detail?id="+data.id, "执行盘点");
                return;
            }
        });

        // 表单搜索
        layui.form.on('submit(table-search)', function(data){
            var formData = data.field;
            layui.pageTable.reload({
                where: formData,
                page: {
                    curr: 1
                }
            });
            return false;
        });

        // 重置搜索
        layui.form.on('submit(table-reset)', function(data){
            $("#barsearchform")[0].reset();
            layui.pageTable.reload({
                where: {},
                page: {
                    curr: 1
                }
            });
            return false;
        });
    }
</script>
{/block}
