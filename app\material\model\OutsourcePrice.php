<?php

namespace app\material\model;

use think\Model;

class OutsourcePrice extends Model
{    
    protected $table = 'material_outsource_price';
    protected $pk = 'id';
    
    // 关联外协商（复用供应商表）
    public function supplier()
    {
        return $this->belongsTo(\app\purchase\model\Supplier::class, 'supplier_id', 'id');
    }
    
    // 关联物料
    public function material()
    {
        return $this->belongsTo(Archive::class, 'material_id', 'id');
    }
}











