{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>调拨统计</h3>
        </div>
        <div class="layui-card-body">
            <!-- 统计概览 -->
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">今日调拨单</div>
                            <div class="text-primary" style="font-size:24px;font-weight:bold;" id="today_count">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">本月调拨单</div>
                            <div class="text-success" style="font-size:24px;font-weight:bold;" id="month_count">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">待审核</div>
                            <div class="text-warning" style="font-size:24px;font-weight:bold;" id="pending_count">0</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body text-center">
                            <div class="text-muted">已完成</div>
                            <div class="text-info" style="font-size:24px;font-weight:bold;" id="completed_count">0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tab切换 -->
            <div class="layui-tab layui-tab-brief" lay-filter="statistics_tab" style="margin-top:20px;">
                <ul class="layui-tab-title">
                    <li class="layui-this">按仓库统计</li>
                    <li>按产品统计</li>
                    <li>按时间统计</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 按仓库统计 -->
                    <div class="layui-tab-item layui-show">
                        <table class="layui-hide" id="table_warehouse_stats" lay-filter="table_warehouse_stats"></table>
                    </div>
                    <!-- 按产品统计 -->
                    <div class="layui-tab-item">
                        <table class="layui-hide" id="table_product_stats" lay-filter="table_product_stats"></table>
                    </div>
                    <!-- 按时间统计 -->
                    <div class="layui-tab-item">
                        <div class="layui-row">
                            <div class="layui-col-md12">
                                <div id="chart_time_stats" style="height:400px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool','tablePlus'];
    function gouguInit() {
        var table = layui.tablePlus, element = layui.element, tool = layui.tool;
        
        // 加载统计数据
        loadStatistics();
        
        // 按仓库统计表格
        var warehouseTable = table.render({
            elem: "#table_warehouse_stats"
            ,title: "按仓库统计"
            ,url: "/warehouse/InventoryTransferController/statistics"
            ,page: false
            ,cellMinWidth: 80
            ,height: 400
            ,parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.warehouse_stats ? res.data.warehouse_stats.length : 0,
                    "data": res.data.warehouse_stats || []
                };
            }
            ,cols: [[ //表头
                {
                    field: 'warehouse_name',
                    title: '仓库名称',
                    align: 'left',
                    width: 200
                },{ 
                    field: 'transfer_out_count', 
                    title: '调出次数', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#FF5722;font-weight:bold;">' + (d.transfer_out_count || 0) + '</span>';
                    }
                },{ 
                    field: 'transfer_in_count', 
                    title: '调入次数', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#5FB878;font-weight:bold;">' + (d.transfer_in_count || 0) + '</span>';
                    }
                },{ 
                    field: 'total_out_amount', 
                    title: '调出金额', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#FF5722;">¥' + (d.total_out_amount || 0).toFixed(2) + '</span>';
                    }
                },{ 
                    field: 'total_in_amount', 
                    title: '调入金额', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#5FB878;">¥' + (d.total_in_amount || 0).toFixed(2) + '</span>';
                    }
                },{ 
                    field: 'net_amount', 
                    title: '净调拨金额', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        var netAmount = (d.total_in_amount || 0) - (d.total_out_amount || 0);
                        var color = netAmount >= 0 ? '#5FB878' : '#FF5722';
                        var sign = netAmount >= 0 ? '+' : '';
                        return '<span style="color:' + color + ';font-weight:bold;">' + sign + '¥' + netAmount.toFixed(2) + '</span>';
                    }
                }
            ]]
        });
        
        // 按产品统计表格
        var productTable = table.render({
            elem: "#table_product_stats"
            ,title: "按产品统计"
            ,url: "/warehouse/InventoryTransferController/statistics"
            ,page: false
            ,cellMinWidth: 80
            ,height: 400
            ,parseData: function(res) {
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.product_stats ? res.data.product_stats.length : 0,
                    "data": res.data.product_stats || []
                };
            }
            ,cols: [[ //表头
                {
                    field: 'product_name',
                    title: '产品名称',
                    align: 'left',
                    width: 200,
                    templet: function (d) {
                        var html = '<div style="line-height:18px;">';
                        html += '<div style="font-weight:bold;">' + (d.product_name || '') + '</div>';
                        html += '<div style="color:#999;font-size:12px;">' + (d.material_code || '') + '</div>';
                        html += '</div>';
                        return html;
                    }
                },{ 
                    field: 'transfer_count', 
                    title: '调拨次数', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#1E9FFF;font-weight:bold;">' + (d.transfer_count || 0) + '</span>';
                    }
                },{ 
                    field: 'total_quantity', 
                    title: '调拨总量', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#5FB878;font-weight:bold;">' + (d.total_quantity || 0) + ' ' + (d.unit || '') + '</span>';
                    }
                },{ 
                    field: 'total_amount', 
                    title: '调拨总额', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        return '<span style="color:#FF5722;font-weight:bold;">¥' + (d.total_amount || 0).toFixed(2) + '</span>';
                    }
                },{ 
                    field: 'avg_price', 
                    title: '平均单价', 
                    align: 'center', 
                    width: 120,
                    templet: function (d) {
                        var avgPrice = d.total_quantity > 0 ? (d.total_amount / d.total_quantity) : 0;
                        return '<span>¥' + avgPrice.toFixed(2) + '</span>';
                    }
                }
            ]]
        });
        
        // Tab切换事件
        element.on('tab(statistics_tab)', function(data){
            if (data.index === 0) {
                warehouseTable.reload();
            } else if (data.index === 1) {
                productTable.reload();
            } else if (data.index === 2) {
                loadTimeChart();
            }
        });
        
        // 加载统计概览数据
        function loadStatistics() {
            $.get('/warehouse/InventoryTransferController/statistics', function(res) {
                if (res.code === 0 && res.data.overview) {
                    var overview = res.data.overview;
                    $('#today_count').text(overview.today_count || 0);
                    $('#month_count').text(overview.month_count || 0);
                    $('#pending_count').text(overview.pending_count || 0);
                    $('#completed_count').text(overview.completed_count || 0);
                }
            }, 'json');
        }
        
        // 加载时间统计图表
        function loadTimeChart() {
            // 这里可以使用 ECharts 或其他图表库
            // 暂时显示提示信息
            $('#chart_time_stats').html('<div style="text-align:center;padding-top:150px;color:#999;">时间统计图表功能待开发</div>');
        }
    }
</script>
{/block}
