<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use app\warehouse\model\InventoryCheck;
use app\warehouse\model\InventoryCheckDetail;
use app\warehouse\model\InventoryRealtime;
use app\warehouse\model\InventoryTransaction;
use think\facade\Db;
use think\Exception;

/**
 * 库存盘点服务类
 */
class InventoryCheckService
{
    /**
     * 获取盘点单列表
     */
    public function getCheckList($params = [])
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        try {
            // 直接使用原生查询，避免关联问题
            $query = InventoryCheck::alias('ic')
                ->leftJoin('oa_warehouse w', 'ic.warehouse_id = w.id')
                ->leftJoin('admin a', 'ic.created_by = a.id')
                ->field('ic.*, w.name as warehouse_name, a.nickname as creator_name')
                ->order('ic.id desc');

            // 处理搜索条件
            if (!empty($params['keywords'])) {
                $query->where('ic.check_no|ic.notes', 'like', '%' . $params['keywords'] . '%');
            }

            if (!empty($params['warehouse_id'])) {
                $query->where('ic.warehouse_id', $params['warehouse_id']);
            }

            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('ic.status', $params['status']);
            }

            if (!empty($params['time_range'])) {
                $timeRange = explode(' - ', $params['time_range']);
                if (count($timeRange) == 2) {
                    $startTime = strtotime($timeRange[0] . ' 00:00:00');
                    $endTime = strtotime($timeRange[1] . ' 23:59:59');
                    $query->whereBetween('ic.create_time', [$startTime, $endTime]);
                }
            }

            $result = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return $result;
        } catch (\Exception $e) {
            // 如果oa_warehouse表不存在，尝试使用warehouse表
            try {
                $query = InventoryCheck::alias('ic')
                    ->leftJoin('warehouse w', 'ic.warehouse_id = w.id')
                    ->leftJoin('admin a', 'ic.created_by = a.id')
                    ->field('ic.*, w.name as warehouse_name, a.nickname as creator_name')
                    ->order('ic.id desc');

                // 处理搜索条件
                if (!empty($params['keywords'])) {
                    $query->where('ic.check_no|ic.notes', 'like', '%' . $params['keywords'] . '%');
                }

                if (!empty($params['warehouse_id'])) {
                    $query->where('ic.warehouse_id', $params['warehouse_id']);
                }

                if (isset($params['status']) && $params['status'] !== '') {
                    $query->where('ic.status', $params['status']);
                }

                if (!empty($params['time_range'])) {
                    $timeRange = explode(' - ', $params['time_range']);
                    if (count($timeRange) == 2) {
                        $startTime = strtotime($timeRange[0] . ' 00:00:00');
                        $endTime = strtotime($timeRange[1] . ' 23:59:59');
                        $query->whereBetween('ic.create_time', [$startTime, $endTime]);
                    }
                }

                return $query->paginate([
                    'list_rows' => $limit,
                    'page' => $page
                ]);
            } catch (\Exception $e2) {
                // 如果都失败了，返回空的分页结果
                return new \think\Paginate([], $limit, $page, 0);
            }
        }
    }
    
    /**
     * 创建盘点单
     */
    public function createCheck($data, $userId)
    {
        Db::startTrans();
        try {
            // 生成盘点单号
            $checkNo = InventoryCheck::generateCheckNo();
            
            // 创建盘点单
            $checkData = [
                'check_no' => $checkNo,
                'warehouse_id' => $data['warehouse_id'],
                'check_date' => $data['check_date'] ?? time(),
                'status' => InventoryCheck::STATUS_PENDING,
                'notes' => $data['notes'] ?? '',
                'created_by' => $userId
            ];
            
            $check = InventoryCheck::create($checkData);
            
            // 如果指定了产品，创建盘点明细
            if (!empty($data['products']) && is_array($data['products'])) {
                $this->createCheckDetails($check->id, $data['products'], $data['warehouse_id']);
            }
            
            Db::commit();
            return $check;
        } catch (\Exception $e) {
            Db::rollback();
            throw new Exception('创建盘点单失败：' . $e->getMessage());
        }
    }
    
    /**
     * 创建盘点明细
     */
    private function createCheckDetails($checkId, $products, $warehouseId)
    {
        $details = [];
        
        foreach ($products as $productId) {
            // 获取系统库存
            $inventory = InventoryRealtime::where([
                ['product_id', '=', $productId],
                ['warehouse_id', '=', $warehouseId]
            ])->find();
            
            $systemQuantity = $inventory ? $inventory->total_quantity : 0;
            
            // 获取产品信息
            $product = Db::name('oa_product')->where('id', $productId)->find();
            if (!$product) {
                $product = Db::name('product')->where('id', $productId)->find();
            }
            
            $details[] = [
                'check_id' => $checkId,
                'product_id' => $productId,
                'system_quantity' => $systemQuantity,
                'actual_quantity' => 0,
                'difference' => 0,
                'unit' => $product['unit'] ?? '',
                'cost_price' => $product['purchase_price'] ?? 0,
                'create_time' => time(),
                'update_time' => time()
            ];
        }
        
        if (!empty($details)) {
            InventoryCheckDetail::insertAll($details);
            
            // 更新盘点单的产品总数
            InventoryCheck::where('id', $checkId)->update([
                'total_products' => count($details)
            ]);
        }
    }
    
    /**
     * 获取盘点单详情
     */
    public function getCheckDetail($id)
    {
        $check = InventoryCheck::with(['warehouse', 'creator', 'approver'])
            ->find($id);
        
        if (!$check) {
            throw new Exception('盘点单不存在');
        }
        
        $details = InventoryCheckDetail::with(['product'])
            ->where('check_id', $id)
            ->order('id asc')
            ->select();
        
        return [
            'check' => $check,
            'details' => $details
        ];
    }
    
    /**
     * 更新盘点明细
     */
    public function updateCheckDetail($detailId, $actualQuantity, $notes = '')
    {
        $detail = InventoryCheckDetail::find($detailId);
        if (!$detail) {
            throw new Exception('盘点明细不存在');
        }
        
        // 计算差异
        $difference = $actualQuantity - $detail->system_quantity;
        
        $detail->actual_quantity = $actualQuantity;
        $detail->difference = $difference;
        $detail->notes = $notes;
        $detail->save();
        
        // 更新盘点单的已盘点产品数
        $this->updateCheckProgress($detail->check_id);
        
        return $detail;
    }
    
    /**
     * 更新盘点进度
     */
    private function updateCheckProgress($checkId)
    {
        $checkedCount = InventoryCheckDetail::where([
            ['check_id', '=', $checkId],
            ['actual_quantity', '>', 0]
        ])->count();
        
        InventoryCheck::where('id', $checkId)->update([
            'checked_products' => $checkedCount
        ]);
    }
    
    /**
     * 完成盘点
     */
    public function completeCheck($id, $userId)
    {
        Db::startTrans();
        try {
            $check = InventoryCheck::find($id);
            if (!$check) {
                throw new Exception('盘点单不存在');
            }
            
            if ($check->status != InventoryCheck::STATUS_CHECKING) {
                throw new Exception('只有盘点中的单据才能完成');
            }
            
            // 获取所有盘点明细
            $details = InventoryCheckDetail::where('check_id', $id)->select();
            
            foreach ($details as $detail) {
                if ($detail->difference != 0) {
                    // 调整库存
                    $this->adjustInventory($detail, $userId);
                }
            }
            
            // 更新盘点单状态
            $check->status = InventoryCheck::STATUS_COMPLETED;
            $check->approved_by = $userId;
            $check->approved_time = time();
            $check->save();
            
            Db::commit();
            return $check;
        } catch (\Exception $e) {
            Db::rollback();
            throw new Exception('完成盘点失败：' . $e->getMessage());
        }
    }
    
    /**
     * 调整库存
     */
    private function adjustInventory($detail, $userId)
    {
        $inventory = InventoryRealtime::where([
            ['product_id', '=', $detail->product_id],
            ['warehouse_id', '=', $detail->check->warehouse_id]
        ])->find();
        
        if (!$inventory) {
            // 创建库存记录
            $inventory = InventoryRealtime::create([
                'product_id' => $detail->product_id,
                'warehouse_id' => $detail->check->warehouse_id,
                'total_quantity' => $detail->actual_quantity,
                'available_quantity' => $detail->actual_quantity,
                'locked_quantity' => 0
            ]);
        } else {
            // 更新库存
            $inventory->total_quantity = $detail->actual_quantity;
            $inventory->available_quantity = $detail->actual_quantity - $inventory->locked_quantity;
            $inventory->save();
        }
        
        // 记录库存流水
        InventoryTransaction::create([
            'product_id' => $detail->product_id,
            'warehouse_id' => $detail->check->warehouse_id,
            'transaction_type' => $detail->difference > 0 ? 'check_in' : 'check_out',
            'quantity' => abs($detail->difference),
            'unit_price' => $detail->cost_price,
            'total_amount' => abs($detail->difference) * $detail->cost_price,
            'ref_type' => 'inventory_check',
            'ref_id' => $detail->check_id,
            'notes' => '库存盘点调整',
            'created_by' => $userId
        ]);
    }
    
    /**
     * 取消盘点
     */
    public function cancelCheck($id, $userId)
    {
        $check = InventoryCheck::find($id);
        if (!$check) {
            throw new Exception('盘点单不存在');
        }
        
        if ($check->status == InventoryCheck::STATUS_COMPLETED) {
            throw new Exception('已完成的盘点单不能取消');
        }
        
        $check->status = InventoryCheck::STATUS_CANCELLED;
        $check->save();
        
        return $check;
    }
    
    /**
     * 开始盘点
     */
    public function startCheck($id)
    {
        $check = InventoryCheck::find($id);
        if (!$check) {
            throw new Exception('盘点单不存在');
        }
        
        if ($check->status != InventoryCheck::STATUS_PENDING) {
            throw new Exception('只有待盘点的单据才能开始');
        }
        
        $check->status = InventoryCheck::STATUS_CHECKING;
        $check->save();
        
        return $check;
    }
}
