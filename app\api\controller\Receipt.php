<?php
declare(strict_types = 1);

namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;
use app\warehouse\model\ReceiptInspectionLog as ReceiptInspectionLogModel;

/**
 * 收货单API控制器
 */
class Receipt extends BaseController
{
    /**
     * 获取质检记录
     * 
     * @return \think\Response
     */
    public function getInspectionLogs()
    {
        // 获取参数
        $param = get_params();
        
        // 验证参数
        if (empty($param['receipt_id'])) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        try {
            // 查询条件
            $where = [];
            
            // 如果指定了明细ID，查询该明细的质检记录
            if (!empty($param['detail_id'])) {
                $where[] = ['o.source_id', '=', $param['detail_id']];
            } else {
                // 如果没有指定明细ID，则查询与该收货单相关的所有质检记录
                // 先获取该收货单的所有明细ID
                $detailIds = Db::name('purchase_receipt_detail')
                    ->where('receipt_id', $param['receipt_id'])
                    ->column('id');
                if (!empty($detailIds)) {
                    $where[] = ['o.source_id', 'in', $detailIds];
                }
            }
            
            // 添加调试信息
            trace('查询条件：' . json_encode($where), 'debug');
            
            // 查询质检单 item_id
            $inspectOrders = Db::name('inspect_order')
                ->alias('o')
                ->join('inspect_order_item i', 'i.inspect_id = o.id')
                ->join('inspect_item item', 'item.id = i.item_id', 'LEFT')
                ->join('admin a', 'a.id = o.inspector_id', 'LEFT')
                ->where('o.source_type', 1)  // 来源类型：1=来料检验
                ->where($where)
                ->field([
                    'o.id as order_id',
                    'o.inspect_no',
                    'o.create_time',
                    'o.status',
                    'o.result as inspect_result',
                    'o.notes as remark',
                    'o.product_name',
                    'o.inspect_time',
                    'i.id as item_id',
                    'i.result as item_result',
                    'i.qualified_qty',
                    'i.unqualified_qty',
                    'i.notes as item_notes',
                    'item.name as item_name',
                    'item.method',
                    'item.standard',
                    'a.name as inspector_name'
                ])
                ->buildSql();  // 先获取SQL语句用于调试
                
            trace('SQL语句：' . $inspectOrders, 'debug');
            
            // 执行查询
            $inspectOrders = Db::name('inspect_order')
                ->alias('o')
                ->join('inspect_order_item i', 'i.inspect_id = o.id')
                ->join('inspect_item item', 'item.id = i.item_id', 'LEFT')
                ->join('admin a', 'a.id = o.inspector_id', 'LEFT')
                ->where('o.source_type', 1)
                ->where($where)
                ->field([
                    'o.id as order_id',
                    'o.inspect_no',
                    'o.create_time',
                    'o.status',
                    'o.result as inspect_result',
                    'o.notes as remark',
                    'o.product_name',
                    'o.inspect_time',
                    'i.id as item_id',
                    'i.result as item_result',
                    'i.qualified_qty',
                    'i.unqualified_qty',
                    'i.notes as item_notes',
                    'item.name as item_name',
                    'item.method',
                    'item.standard',
                    'a.name as inspector_name'
                ])
                ->select()
                ->toArray();
                
            trace('查询结果：' . json_encode($inspectOrders), 'debug');

            $data = [];
            foreach ($inspectOrders as $order) {
                // 获取质检图片
                $images = Db::name('inspect_image')
                    ->where('inspect_id', $order['order_id'])
                    ->where('detail_id', $order['item_id'])
                    ->column('image_url');
                
                // 获取质检缺陷
                $defects = Db::name('inspect_defect')
                    ->alias('d')
                    ->where('d.status', 1)  // 只获取启用的缺陷项目
                    ->column('d.name as defect_desc');  // 直接获取所有可用的缺陷项目名称

                // 构建状态文本
                $statusText = '';
                switch ($order['status']) {
                    case 1:
                        $statusText = '待检验';
                        break;
                    case 2:
                        $statusText = '检验中';
                        break;
                    case 3:
                        $statusText = '已完成';
                        break;
                    default:
                        $statusText = '未知';
                }

                // 构建检验结果文本
                $resultText = '';
                switch ($order['inspect_result']) {
                    case 1:
                        $resultText = '合格';
                        break;
                    case 2:
                        $resultText = '不合格';
                        break;
                    default:
                        $resultText = '未知';
                }

                // 构建项目结果文本
                $itemResultText = '';
                switch ($order['item_result']) {
                    case 0:
                        $itemResultText = '未检验';
                        break;
                    case 1:
                        $itemResultText = '合格';
                        break;
                    case 2:
                        $itemResultText = '不合格';
                        break;
                    default:
                        $itemResultText = '未知';
                }

                $data[] = [
                    'inspection_time' => $order['inspect_time'] ? date('Y-m-d H:i:s', $order['inspect_time']) : date('Y-m-d H:i:s', $order['create_time']),
                    'inspect_no' => $order['inspect_no'],
                    'inspector_name' => $order['inspector_name'] ?: '未知',
                    'status_text' => $statusText,
                    'product_name' => $order['product_name'],
                    'inspect_result' => $resultText,
                    'item_name' => $order['item_name'],
                    'method' => $order['method'],
                    'standard' => $order['standard'],
                    'item_result' => $itemResultText,
                    'qualified_qty' => $order['qualified_qty'],
                    'unqualified_qty' => $order['unqualified_qty'],
                    'remark' => $order['remark'],
                    'item_notes' => $order['item_notes'],
                    'image_urls' => $images,
                    'defects' => $defects
                ];
            }
            
            return json(['code' => 0, 'msg' => 'success', 'data' => $data]);
        } catch (\Exception $e) {
            // 记录错误信息
            trace('质检记录查询错误: ' . $e->getMessage(), 'error');
            return json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
} 