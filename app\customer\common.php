<?php


/**
======================
 *模块数据获取公共文件
======================
 */
use think\facade\Db;

//客户查看编辑数据权限判断
function customer_auth($uid,$customer_id)
{
	$customer =  Db::name('Customer')->where(['id' => $customer_id])->find();
	if($customer['belong_uid']==0){
		return $customer;
	}
	//是否是客户管理员
    $auth = isAuth($uid,'customer_admin','conf_1');
	$role= 0;
	if($auth==1){
		//可见部门数据
		$dids = get_role_departments($uid);
		if(in_array($customer['belong_did'],$dids)){
			$role= 1;
		}
	}
	else if($auth==0){
		$auth_array=[];
		if(!empty($customer['share_ids'])){
			$share_ids = explode(",",$customer['share_ids']);
			$auth_array = array_merge($auth_array,$share_ids);
		}	
		array_push($auth_array,$customer['belong_uid']);
		//部门负责人
		$dids = get_leader_departments($uid);
		if(in_array($uid,$auth_array) || in_array($customer['belong_did'],$dids)){
			$role= 1;
		}
	}
	if($role == 0){
		throw new \think\exception\HttpException(405, '无权限访问');
	}
	else{
		return $customer;
	}
}

//读取联系人
function customer_contact($cid)
{
    $contact = Db::name('CustomerContact')->where(['delete_time' => 0,'cid'=>$cid])->select()->toArray();
    return $contact;
}

//读取销售机会
function customer_chance($cid)
{
    $chance = Db::name('CustomerChance')->where(['delete_time' => 0,'cid'=>$cid])->select()->toArray();
    return $chance;
}

//获取客户列表 2025.3.25
function get_customer_list()
{
    $where = [];
    $where[] = ['delete_time', '=', 0];
    //是否是客户管理员
    $uid = session('tc_admin');
    $auth = isAuth($uid,'customer_admin','conf_1');

    if($auth == 0){
        $where[] = ['admin_id','=',$uid];
    }
    

    $list = Db::name('Customer')
        ->where($where)
        ->field('id,name')
        ->order('id desc')
        ->select();
    return $list;
}

//获取客户模块产品列表
function get_customer_product_list()
{
    $where = [];
    $where[] = ['delete_time', '=', 0];
    $where[] = ['status', '=', 1];
    $list = Db::name('Product')
        ->where($where)
        ->field('id,title ,material_code,specs,unit')
        ->order('id desc')
        ->select();
    return $list;
}