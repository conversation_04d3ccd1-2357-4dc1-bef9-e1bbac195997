{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<div class="layui-card border-x border-t" style="margin-bottom:0; box-shadow:0 0 0 0 rgb(5 32 96 / 0%)">
		<div class="body-table">
			<div class="layui-row">
				<div class="layui-col-md3">
					<div class="category-tree" style="padding: 0; border-right: 1px solid #e6e6e6; height: 100%; background: #fafafa;">
						<div class="tree-header" style="padding: 15px 15px 10px 15px; border-bottom: 1px solid #e6e6e6; background: #f8f8f8;">
							<div style="display: flex; justify-content: space-between; align-items: center;">
								<span style="font-weight: bold; color: #333; font-size: 14px;">分类名称</span>
								<button class="layui-btn layui-btn-xs layui-btn-danger" id="addCategoryBtn" style="height: 24px; line-height: 24px;">新增分类</button>
							</div>
						</div>
						<div class="tree-content" style="padding: 10px 0;">
							<ul id="categoryTree" class="category-list">
								<li data-id="" class="category-item active">
									<div class="category-content">
										<span class="expand-placeholder"></span>
										<span class="category-name">物料</span>
									</div>
								</li>
								{volist name="categoryTree" id="category"}
								<li data-id="{$category.id}" class="category-item {$category.has_children ? 'has-children' : ''}" data-level="0" data-loaded="false">
									<div class="category-content">
										{if condition="$category.has_children"}
										<i class="expand-icon layui-icon layui-icon-right"></i>
										{else /}
										<span class="expand-placeholder"></span>
										{/if}
										<span class="category-name">{$category.title}</span>
										<div class="category-actions">
											<button class="action-btn add-sub-btn" title="添加下级分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-add-circle"></i>
											</button>
											<button class="action-btn edit-btn" title="编辑分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-edit"></i>
											</button>
											<button class="action-btn delete-btn" title="删除分类" data-id="{$category.id}">
												<i class="layui-icon layui-icon-close"></i>
											</button>
										</div>
									</div>
								</li>
								{/volist}
							</ul>
						</div>
					</div>
				</div>
				<div class="layui-col-md9">
					<form class="layui-form gg-form-bar border-x" id="barsearchform" style="border-left: none;">
						<div class="layui-input-inline" style="width:150px;">
							<input type="text" name="keywords" placeholder="输入关键字" class="layui-input" autocomplete="off" />
						</div>
						<div class="layui-input-inline" style="width:150px">
							<input type="hidden" name="category_id" value="" />
							<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
							<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
						</div>
					</form>
					<table class="layui-hide" id="table_archive" lay-filter="table_archive"></table>
				</div>
			</div>
		</div> 
	</div>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	 
	<button class="layui-btn layui-btn-sm" lay-event="addMaterial">
		<span>新增物料</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="import">
		<span>导入</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="export">
		<span>导出</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="delete">
		<span>删除</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="batchModify">
		<span>批量修改</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="printBarcode">
		<span>批量打印二维码</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="printLabel">
		<span>批量条码标签</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="update">
		<span>更新</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="importCategory">
		<span>导入分类</span>
	</button>
	<button class="layui-btn layui-btn-sm layui-btn-primary" lay-event="importSubCategory">
		<span>导入子分类</span>
	</button>
  </div>
</script>

{/block}
<!-- /主体 -->

{block name="style"}
<style>
.category-tree {
	height: calc(100vh - 200px);
	overflow-y: auto;
}
.tree-header {
	position: sticky;
	top: 0;
	z-index: 10;
}
.tree-content {
	max-height: calc(100vh - 280px);
	overflow-y: auto;
}
.category-list {
	list-style: none;
	margin: 0;
	padding: 0;
}
.category-item {
	cursor: pointer;
	transition: all 0.2s;
	border-bottom: 1px solid #f0f0f0;
	font-size: 14px;
	color: #333;
	line-height: 1.5;
	position: relative;
}
.category-content {
	padding: 10px 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.category-item:hover .category-content {
	background-color: #f5f5f5;
}
.category-item:hover .category-actions {
	opacity: 1;
	visibility: visible;
}
/* 子分类默认样式 */
.sub-category .category-content {
	background-color: #fafafa;
}
.sub-category:hover .category-content {
	background-color: #f0f0f0;
}

/* 多级分类缩进 */
.category-item[data-level="1"] .category-content {
	padding-left: 35px;
}
.category-item[data-level="2"] .category-content {
	padding-left: 55px;
}
.category-item[data-level="3"] .category-content {
	padding-left: 75px;
}
.category-item[data-level="4"] .category-content {
	padding-left: 95px;
}

/* 选中状态样式 - 只改变文字颜色，保持背景不变 */
.category-tree li.category-item.active > .category-content {
	color: #ff5722 !important;
	font-weight: 500 !important;
}

/* 选中状态下的分类名称颜色 - 更强的选择器 */
.category-tree li.category-item.active > .category-content .category-name {
	color: #ff5722 !important;
	font-weight: 500 !important;
}

/* 特别针对子分类的选中状态 */
.category-tree .sub-category-list li.category-item.active > .category-content,
.category-tree .sub-category-list li.category-item.active > .category-content .category-name {
	color: #ff5722 !important;
	font-weight: 500 !important;
}

/* 顶级分类选中状态 */
li.category-item.active[data-level="0"] > .category-content,
li.category-item.active:not([data-level]) > .category-content {
	padding-left: 15px !important;
}

/* 各级子分类选中状态的左边距保持原样 */
li.category-item.active[data-level="1"] > .category-content {
	padding-left: 35px !important;
}
li.category-item.active[data-level="2"] > .category-content {
	padding-left: 55px !important;
}
li.category-item.active[data-level="3"] > .category-content {
	padding-left: 75px !important;
}
li.category-item.active[data-level="4"] > .category-content {
	padding-left: 95px !important;
}
.category-actions {
	display: flex;
	gap: 2px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.2s;
}
.action-btn {
	width: 20px;
	height: 20px;
	border: 1px solid #d9d9d9;
	background: #fff;
	border-radius: 50%;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;
	color: #666;
}
.action-btn:hover {
	transform: scale(1.05);
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.add-sub-btn:hover {
	background-color: #52c41a;
	border-color: #52c41a;
	color: #fff;
}
.edit-btn:hover {
	background-color: #1890ff;
	border-color: #1890ff;
	color: #fff;
}
.delete-btn:hover {
	background-color: #ff4d4f;
	border-color: #ff4d4f;
	color: #fff;
}
.action-btn i {
	font-size: 10px;
}
.category-item.expandable {
	position: relative;
}
.expand-icon {
	margin-right: 8px;
	font-size: 12px;
	color: #999;
	transition: transform 0.2s;
	cursor: pointer;
	width: 16px;
	text-align: center;
}
.expand-placeholder {
	width: 24px;
	display: inline-block;
}
.category-item.expanded .expand-icon {
	transform: rotate(90deg);
}
.sub-category-list {
	list-style: none;
	margin: 0;
	padding: 0;
}
.has-children > .category-content .expand-icon:hover {
	color: #333;
}
.category-name {
	flex: 1;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 10px;
}
.p-page .layui-col-md3 {
	padding-right: 0;
}
.p-page .layui-col-md9 {
	padding-left: 0;
}
</style>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool, form = layui.form;
		
		// 展开/收起分类
		$(document).on('click', '.expand-icon', function(e) {
			e.stopPropagation();
			var $item = $(this).closest('.category-item');
			var $subList = $item.find('> .sub-category-list');
			var categoryId = $item.data('id');
			var isLoaded = $item.data('loaded');

			if ($item.hasClass('expanded')) {
				// 收起分类
				$item.removeClass('expanded');
				$subList.slideUp(200);
			} else {
				// 展开分类
				if (!isLoaded && categoryId) {
					// 如果还没有加载过子分类，则异步加载
					loadSubCategories($item, categoryId);
				} else {
					// 已经加载过，直接展开
					$item.addClass('expanded');
					$subList.slideDown(200);
				}
			}
		});

		// 异步加载子分类
		function loadSubCategories($parentItem, parentId) {
			// 显示加载状态
			var $expandIcon = $parentItem.find('.expand-icon');
			$expandIcon.removeClass('layui-icon-right').addClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop');

			tool.get("/material/category/getChildren", {parent_id: parentId}, function(res) {
				$expandIcon.removeClass('layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop').addClass('layui-icon-right');

				if (res.code === 0 && res.data && res.data.length > 0) {
					// 构建子分类HTML
					var subHtml = '<ul class="sub-category-list" style="display: none;">';
					var parentLevel = parseInt($parentItem.data('level')) || 0;
					var subLevel = parentLevel + 1;

					res.data.forEach(function(subCategory) {
						var hasChildrenClass = subCategory.has_children ? 'has-children' : '';
						var expandIcon = subCategory.has_children ?
							'<i class="expand-icon layui-icon layui-icon-right"></i>' :
							'<span class="expand-placeholder"></span>';

						subHtml += '<li data-id="' + subCategory.id + '" class="category-item sub-category ' + hasChildrenClass + '" data-level="' + subLevel + '" data-loaded="false">';
						subHtml += '  <div class="category-content">';
						subHtml += '    ' + expandIcon;
						subHtml += '    <span class="category-name">' + subCategory.title + '</span>';
						subHtml += '    <div class="category-actions">';
						subHtml += '      <button class="action-btn add-sub-btn" title="添加下级分类" data-id="' + subCategory.id + '">';
						subHtml += '        <i class="layui-icon layui-icon-add-circle"></i>';
						subHtml += '      </button>';
						subHtml += '      <button class="action-btn edit-btn" title="编辑分类" data-id="' + subCategory.id + '">';
						subHtml += '        <i class="layui-icon layui-icon-edit"></i>';
						subHtml += '      </button>';
						subHtml += '      <button class="action-btn delete-btn" title="删除分类" data-id="' + subCategory.id + '">';
						subHtml += '        <i class="layui-icon layui-icon-close"></i>';
						subHtml += '      </button>';
						subHtml += '    </div>';
						subHtml += '  </div>';
						subHtml += '</li>';
					});
					subHtml += '</ul>';

					// 添加子分类到DOM
					$parentItem.append(subHtml);
					$parentItem.data('loaded', true);

					// 展开分类
					$parentItem.addClass('expanded');
					$parentItem.find('> .sub-category-list').slideDown(200);
				} else {
					// 没有子分类，移除展开图标
					$expandIcon.replaceWith('<span class="expand-placeholder"></span>');
					$parentItem.removeClass('has-children');
				}
			});
		}
		
		// 防止重复请求的标志
		var isLoadingCategory = false;

		// 分类点击事件
		$(document).on('click', '.category-item', function(e) {
			// 阻止事件冒泡和默认行为
			e.stopPropagation();
			e.preventDefault();

			// 如果正在加载，直接返回
			if (isLoadingCategory) {
				return false;
			}

			// 如果点击的是操作按钮或展开图标，不触发分类选择
			if ($(e.target).closest('.category-actions').length > 0 || $(e.target).hasClass('expand-icon')) {
				return false;
			}

			// 防止重复点击同一个分类
			var categoryId = $(this).data('id');
			var currentCategoryId = $('[name="category_id"]').val();

			// 处理空的categoryId（根分类"物料"）
			if (categoryId === '' || categoryId === undefined) {
				categoryId = '';
			}

			if (categoryId == currentCategoryId) {
				return false;
			}

			// 设置加载标志
			isLoadingCategory = true;

			// 移除所有分类的active状态
			$('.category-item').removeClass('active');
			// 添加当前分类的active状态
			$(this).addClass('active');

			// 判断是否为子分类（通过检查是否有 sub-category 类或者 data-level 大于0）
			var isSubCategory = $(this).hasClass('sub-category') || ($(this).data('level') && $(this).data('level') > 0);
			var includeChildren = !isSubCategory; // 父级分类包含子分类，子分类不包含

			// 调试信息
			console.log('Selected category:', $(this).find('.category-name').text(), 'ID:', categoryId);
			console.log('Is sub category:', isSubCategory, 'Include children:', includeChildren);

			$('[name="category_id"]').val(categoryId);

			// 构建查询参数
			var whereParams = {};
			if (categoryId !== '') {
				whereParams.category_id = categoryId;
				if (!includeChildren) {
					whereParams.include_children = false;
				}
			}
			// 如果categoryId为空，则查询所有数据（不添加category_id参数）

			// 重载表格数据
			layui.pageTable.reload({
				where: whereParams,
				page: {curr: 1},
				done: function() {
					// 请求完成后重置标志
					isLoadingCategory = false;
				}
			});

			return false;
		});
		
		// 添加下级分类
		$(document).on('click', '.add-sub-btn', function(e) {
			e.stopPropagation();
			var parentId = $(this).data('id');
			var parentName = $(this).closest('.category-item').find('.category-name').text();
			
			// 打开添加下级分类弹窗
			layer.open({
				type: 1,
				title: '新增分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="addSubCategoryForm">
							<input type="hidden" name="pid" value="${parentId}" />
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label">父级分类</label>
								<div class="layui-input-block">
									<input type="text" value="${parentName}" class="layui-input" readonly style="background: #f5f5f5;" />
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveSubCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '350px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定添加下级分类表单提交事件
					form.on('submit(saveSubCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", data.field, callback);
						return false;
					});
				}
			});
		});
		
		// 编辑分类
		$(document).on('click', '.edit-btn', function(e) {
			e.stopPropagation();
			var categoryId = $(this).data('id');
			var categoryName = $(this).closest('.category-item').find('.category-name').text();
			
			// 打开编辑分类弹窗
			layer.open({
				type: 1,
				title: '编辑分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="editCategoryForm">
							<input type="hidden" name="id" value="${categoryId}" />
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" value="${categoryName}" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveEditCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '300px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定编辑表单提交事件
					form.on('submit(saveEditCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", data.field, callback);
						return false;
					});
				}
			});
		});
		
		// 删除分类
		$(document).on('click', '.delete-btn', function(e) {
			e.stopPropagation();
			var categoryId = $(this).data('id');
			var categoryName = $(this).closest('.category-item').find('.category-name').text();
			
			layer.confirm('确定要删除分类"' + categoryName + '"吗？', { 
				icon: 3, 
				title: '删除确认' 
			}, function (index) {
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						// 重新加载页面
						location.reload();
					}
				}
				tool.post("/material/category/delete", { id: categoryId }, callback);
				layer.close(index);
			});
		});
		
		// 新增分类弹窗处理
		$(document).on('click', '#addCategoryBtn', function(e) {
			e.stopPropagation();
			
			// 打开新增分类弹窗
			layer.open({
				type: 1,
				title: '新增分类',
				content: `
					<div style="padding: 20px;">
						<form class="layui-form" lay-filter="newCategoryForm">
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类编号</label>
								<div class="layui-input-block">
									<input type="text" name="category_code" placeholder="使用系统编号" class="layui-input" readonly style="background: #f5f5f5;" />
									<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" style="position: absolute; right: 5px; top: 5px;">自动</button>
								</div>
							</div>
							<div class="layui-form-item">
								<label class="layui-form-label" style="color: #ff5722;">* 分类名称</label>
								<div class="layui-input-block">
									<input type="text" name="category_name" placeholder="请输入分类名称" class="layui-input" lay-verify="required" />
									<div class="layui-form-mid layui-word-aux" style="color: #ff5722;">请输入分类名称</div>
								</div>
							</div>
							<div class="layui-form-item" style="margin-top: 30px;">
								<div class="layui-input-block" style="text-align: right;">
									<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
									<button class="layui-btn layui-btn-danger" lay-submit lay-filter="saveNewCategoryBtn">提交</button>
								</div>
							</div>
						</form>
					</div>
				`,
				area: ['500px', '300px'],
				btn: false,
				closeBtn: 1,
				success: function(layero, index) {
					// 重新渲染表单
					form.render();
					
					// 绑定新增分类表单提交事件
					form.on('submit(saveNewCategoryBtn)', function(data) {
						let callback = function (e) {
							layer.msg(e.msg);
							if (e.code == 0) {
								layer.close(index);
								// 重新加载分类列表
								location.reload();
							}
						}
						tool.post("/material/category/add", {name: data.field.category_name, status: 1}, callback);
						return false;
					});
				}
			});
		});
		
		layui.pageTable = table.render({
			elem: "#table_archive"
			,title: "物料档案列表"
			,toolbar: "#toolbarDemo"
			,url: "/material/archive/index"
			,page: true
			,limit: 20
			,cellMinWidth: 80
			,height: 'full-152'
			,cols: [[ //表头
				{type: 'checkbox', fixed: 'left'},
				{
					field: 'material_image',
					title: '物料图片',
					align: 'center',
					width: 80,
					templet: function(d) {
						return '<div class="material-image" style="width:40px;height:40px;background:#f5f5f5;border-radius:4px;display:flex;align-items:center;justify-content:center;"><i class="layui-icon layui-icon-picture" style="font-size:20px;color:#ccc;"></i></div>';
					}
				},{
					field: 'title',
					title: '物料名称',
					minWidth: 200
				},{
					field: 'material_code',
					title: '物料编码',
					align: 'center',
					width: 150,
					sort: true
				},{
					field: 'cate',
					title: '产品分类',
					align: 'center',
					width: 150,
					sort: true
				}
				,{
					field: 'specs',
					title: '规格型号',
					align: 'center',
					width: 200
				},{
					field: 'unit',
					title: '单位',
					align: 'center',
					width: 120
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 280,
					align: 'center',
					ignoreExport:true,
					templet: function (d) {
						var html = '<div class="layui-btn-group">';
 						html += '<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						html += '<span class="layui-btn layui-btn-xs" lay-event="detail">详情</span>';
						html += '<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
						html += '<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="printBarcode">打印二维码</span>';
						html += '<span class="layui-btn layui-btn-primary layui-btn-xs" lay-event="printBarcodeID">打印条形码</span>';
						html += '</div>';
						return html;
					}						
				}
			]]
		});
		
		//表头工具栏事件
		table.on('toolbar(table_archive)', function(obj){
			if (obj.event === 'add'){
				// 新增分类
				return;
			}
			if (obj.event === 'addMaterial'){
				tool.side("/material/archive/add");
				return;
			}
			if (obj.event === 'import'){
				tool.side("/material/archive/import");
				return;
			}
			if (obj.event === 'export'){
				var checkStatus = table.checkStatus('table_archive');
				var data = checkStatus.data;
				if(data.length === 0){
					layer.msg('请选择要导出的数据');
					return;
				}
				// 导出逻辑
				return;
			}
			if (obj.event === 'printBarcode'){
				var checkStatus = table.checkStatus('table_archive');
				var data = checkStatus.data;
				if(data.length === 0){
					layer.msg('请选择要打印二维码的物料');
					return;
				}
				var ids = [];
				layui.each(data, function(i, item) {
					ids.push(item.id);
				});
				var url = '/material/archive/batchPrintBarcode?ids=' + ids.join(',');
				layer.open({
					type: 2,
					title: '批量打印二维码',
					area: ['1200px', '800px'],
					content: url,
					btn: false,
					closeBtn: 1
				});
				return;
			}
			if (obj.event === 'delete'){
				var checkStatus = table.checkStatus('table_archive');
				var data = checkStatus.data;
				if(data.length === 0){
					layer.msg('请选择要删除的数据');
					return;
				}
				layer.confirm('确定要删除选中的物料档案吗?', { icon: 3, title: '提示' }, function (index) {
					var ids = [];
					layui.each(data, function(i, item) {
						ids.push(item.id);
					});
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/material/archive/batchDelete", { ids: ids.join(',') }, callback);
					layer.close(index);
				});
				return;
			}
		});	
			
		table.on('tool(table_archive)',function (obj) {
			var data = obj.data;
			if (obj.event === 'view') {
				// 复制物料
				tool.side("/material/archive/add?copy_id="+data.id);
				return;
			}
			if (obj.event === 'edit') {
				tool.side("/material/archive/add?id="+data.id);
				return;
			}
			if (obj.event === 'detail') {
				tool.side("/material/archive/view?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除该物料档案吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.post("/material/archive/delete", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'printBarcode') {
				// 打印二维码
				var url = '/material/archive/printBarcode?id=' + data.id;
				layer.open({
					type: 2,
					title: '打印二维码',
					area: ['900px', '600px'],
					content: url,
					btn: false,
					closeBtn: 1
				});
				return;
			}
			if (obj.event === 'printBarcodeID') {
				// 打印条形码
				var url = '/material/archive/printBarcodeID?id=' + data.id;
				layer.open({
					type: 2,
					title: '打印条形码',
					area: ['900px', '600px'],
					content: url,
					btn: false,
					closeBtn: 1
				});
				return;
			}
			if (obj.event === 'downloadLabel') {
				// 下载条码号
				layer.msg('下载条码号功能开发中...');
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->