{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<div class="p-page" style="height:100%; box-sizing: border-box;">
	<form class="layui-form gg-form-bar border-t border-x" lay-filter="barsearchform">
		<div class="layui-input-inline" style="width:300px;">
			<input type="text" class="layui-input" id="diff_time" placeholder="保养日期" readonly name="diff_time">
		</div>
		<div class="layui-input-inline" style="width:300px">
			<input type="text" name="keywords" placeholder="输入关键字" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="protectTable" lay-filter="protectTable"></table>
</div>
<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
  	<button class="layui-btn layui-btn-sm tool-add" type="button" data-href="/adm/car/protect_add">+ 添加保养记录</button>
  </div>
</script>
{/block}
<!-- /主体 -->
{block name="copyright"}{/block}
<!-- 脚本 -->
{block name="script"}
	<script>
	const moduleInit = ['tool','tablePlus','laydatePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool,form = layui.form,laydatePlus = layui.laydatePlus;	
		var diff_time = new laydatePlus({'target':'diff_time'});		
		
		layui.protectTable = table.render({
			elem: '#protectTable'
			,toolbar: '#toolbarDemo'
			,title:'保养记录列表'
			,url: "/adm/car/protect_list"
			,is_excel: true
			,page: true
			,cellMinWidth: 60
			,cols: [[
				{field:'id',width:80, title: 'ID号', align:'center'}
				,{field:'repair_time',title: '保养日期',align: 'center',width: 100}
				,{field:'amount',width:100, title: '保养费用(元)', align:'center',style:"color:#16b777"}
				,{field:'car',width:120,title: '车辆名称'}
				,{field:'name',width:100, title: '车牌号码', align:'center'}
				,{field:'content',minWidth:300,title: '保养内容'}
				,{field:'handled_name',title: '跟进人',align: 'center',width: 80}
				,{field:'create_time',title: '记录时间',align: 'center',width: 130}
				,{width:120,fixed:'right', title: '操作', align:'center',templet: function(d){
					var html='<div class="layui-btn-group"><a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a><a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="view">详情</a><a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a></div>';
					return html;
				}}
			]]
		});
		
		table.on('tool(protectTable)',function (obj) {
			var that = this;
			if(obj.event === 'edit'){					
				tool.side("/adm/car/protect_add?id="+obj.data.id);
			}
			if(obj.event === 'view'){					
				tool.side("/adm/car/protect_view?id="+obj.data.id);
			}
			if(obj.event === 'del'){
				layer.confirm('确定要删除该保养记录吗?', {icon: 3, title:'提示'}, function(index){
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.protectTable.reload();
						}
					}
					tool.post("/adm/car/protect_del", {id: obj.data.id}, callback);
					layer.close(index);
				});
				return;
			}
		});
	}
	</script>
{/block}
<!-- /脚本 -->