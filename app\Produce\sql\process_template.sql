-- 工艺路线模板表
CREATE TABLE IF NOT EXISTS `oa_process_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_no` varchar(50) NOT NULL DEFAULT '' COMMENT '工艺模板编号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工艺名称',
  `remark` text COMMENT '备注',
  `steps` text COMMENT '工艺步骤JSON数据',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_no` (`template_no`),
  KEY `idx_name` (`name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工艺路线模板表';

-- 为生产订单表添加工艺模板关联字段（如果不存在）
ALTER TABLE `oa_produce_order` 
ADD COLUMN `process_template_id` int(11) NOT NULL DEFAULT '0' COMMENT '工艺模板ID';

-- 添加索引
ALTER TABLE `oa_produce_order` 
ADD KEY `idx_process_template_id` (`process_template_id`);

-- 插入示例数据
INSERT INTO `oa_process_template` (`template_no`, `name`, `remark`, `steps`, `create_time`, `update_time`) VALUES
('GYLX202412010001', '东升', '', '[{"step":1,"name":"下料","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":2,"name":"CNC","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":3,"name":"车床","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":4,"name":"火花机","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":5,"name":"表面处理","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":6,"name":"质检","type":"数据记录","completion_time":0,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""}]', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('GYLX202412010002', '金水工艺流线', '', '[{"step":1,"name":"粗车","type":"数据记录","completion_time":1,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":2,"name":"清洗研磨","type":"数据记录","completion_time":1,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""},{"step":3,"name":"电镀","type":"数据记录","completion_time":1,"time_unit":"天","processing_type":"外协","inspection_method":"免检","description":""},{"step":4,"name":"全检","type":"数据记录","completion_time":1,"time_unit":"天","processing_type":"自制","inspection_method":"免检","description":""}]', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
