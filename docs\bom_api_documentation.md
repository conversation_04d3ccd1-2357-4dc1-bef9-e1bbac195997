# BOM管理API文档

## 接口地址
`POST /material/bom/add`

## 功能说明
添加或编辑BOM（物料清单）信息，支持多级BOM结构和物料层级管理。

## 请求参数

### 基础信息字段

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | int | 否 | BOM ID，编辑时传入，新增时不传或传0 | 123 |
| bom_code | string | 是 | BOM编号，系统自动生成或手动输入 | BOM202508020001 |
| bom_name | string | 是 | BOM名称 | S-18音响BOM |
| product_id | int | 是 | 产品ID，必须传入有效的产品ID | 2165 |
| product_code | string | 否 | 产品编号，自动填充 | P001 |
| product_name | string | 是 | 产品名称 | S-18音响SS6 |
| customer_id | int | 否 | 客户ID，选择客户后自动填充 | 456 |
| customer_name | string | 否 | 客户名称 | 深圳科技有限公司 |
| remark | string | 否 | 备注信息 | 特殊要求说明 |

### 物料清单字段 (materials数组)

每个物料项包含以下字段：

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| material_id | int | 是 | 物料ID，必须传入有效的物料ID | 789 |
| material_code | string | 否 | 物料编号 | M001 |
| material_name | string | 否 | 物料名称 | 电阻器 |
| material_category | string | 否 | 物料分类 | 电子元件 |
| specifications | string | 否 | 规格 | 10K欧姆 |
| material_source | string | 是 | 物料来源：自购、自制、委外 | 自购 |
| bom_level | int | 否 | BOM等级，1=一级，2=二级，默认1 | 1 |
| parent_material_id | int | 否 | 父级物料ID，0表示顶级，默认0 | 0 |
| quantity | decimal | 否 | 用量，默认1 | 2.5 |
| loss_rate | decimal | 否 | 损耗率(%)，默认0 | 5.0 |

## 请求示例

### 新增BOM
```json
{
  "bom_code": "BOM202508020001",
  "bom_name": "S-18音响BOM",
  "product_id": 2165,
  "product_code": "P001",
  "product_name": "S-18音响SS6",
  "customer_id": 456,
  "customer_name": "深圳科技有限公司",
  "remark": "标准配置",
  "materials": [
    {
      "material_id": 789,
      "material_code": "M001",
      "material_name": "电阻器",
      "material_category": "电子元件",
      "specifications": "10K欧姆",
      "material_source": "自购",
      "bom_level": 1,
      "parent_material_id": 0,
      "quantity": 2,
      "loss_rate": 5.0
    },
    {
      "material_id": 790,
      "material_code": "M002", 
      "material_name": "电容器",
      "material_category": "电子元件",
      "specifications": "100uF",
      "material_source": "自购",
      "bom_level": 1,
      "parent_material_id": 0,
      "quantity": 1,
      "loss_rate": 3.0
    }
  ]
}
```

### 编辑BOM
```json
{
  "id": 123,
  "bom_code": "BOM202508020001",
  "bom_name": "S-18音响BOM(修改版)",
  "product_id": 2165,
  "product_code": "P001",
  "product_name": "S-18音响SS6",
  "customer_id": 456,
  "customer_name": "深圳科技有限公司",
  "remark": "修改后的配置",
  "materials": [
    {
      "material_id": 789,
      "material_code": "M001",
      "material_name": "电阻器",
      "material_category": "电子元件",
      "specifications": "10K欧姆",
      "material_source": "自购",
      "bom_level": 1,
      "parent_material_id": 0,
      "quantity": 3,
      "loss_rate": 5.0
    }
  ]
}
```

## 响应格式

### 成功响应
```json
{
  "code": 0,
  "msg": "保存成功"
}
```

### 失败响应
```json
{
  "code": 1,
  "msg": "错误信息描述"
}
```

## 数据库表结构

### 主表：oa_material_bom
- 存储BOM基础信息
- 包含产品信息、客户信息等

### 明细表：oa_material_bom_detail  
- 存储BOM物料明细
- 支持多级BOM结构
- 包含物料信息、用量、损耗率等

### 序列表：oa_material_bom_sequence
- 管理BOM编号自动生成
- 按日期递增序号

## 注意事项

1. **必填字段验证**：bom_code、bom_name、product_name、product_id为必填
2. **物料ID验证**：materials数组中每个物料的material_id必须有效
3. **BOM编号唯一性**：新增时会检查bom_code是否重复
4. **事务处理**：使用数据库事务确保数据一致性
5. **多级BOM**：支持通过bom_level和parent_material_id构建层级结构
6. **编辑模式**：编辑时会先删除原有明细，再插入新明细

## 手工导入SQL

请执行以下SQL文件创建数据库表：
```sql
-- 执行文件：database/bom_tables_oa.sql
-- 包含完整的表结构、索引和初始数据
```
