<?php
declare (strict_types = 1);

namespace app\common\service;

use think\facade\Db;

/**
 * 库存查询公共服务
 * 提供统一的库存查询接口，方便各模块复用
 */
class InventoryQueryService
{
    /**
     * 获取产品的可用库存数量
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID（可选，默认为0表示所有仓库）
     * @return array [
     *     'total_quantity' => 总库存,
     *     'available_quantity' => 可用库存,
     *     'locked_quantity' => 锁定库存
     * ]
     */
    public static function getProductAvailableQuantity($productId, $warehouseId = 0)
    {
        if (empty($productId) || $productId <= 0) {
            return [
                'total_quantity' => 0,
                'available_quantity' => 0,
                'locked_quantity' => 0
            ];
        }

        try {
            // 构建查询条件
            $where = [['product_id', '=', $productId]];
            if (!empty($warehouseId) && $warehouseId > 0) {
                $where[] = ['warehouse_id', '=', $warehouseId];
            }

            // 查询库存数据
            $inventoryData = Db::name('inventory_realtime')
                ->where($where)
                ->field('
                    SUM(IFNULL(quantity, 0)) as total_quantity,
                    SUM(IFNULL(available_quantity, 0)) as available_quantity,
                    SUM(IFNULL(locked_quantity, 0)) as locked_quantity
                ')
                ->find();

            return [
                'total_quantity' => floatval($inventoryData['total_quantity'] ?? 0),
                'available_quantity' => floatval($inventoryData['available_quantity'] ?? 0),
                'locked_quantity' => floatval($inventoryData['locked_quantity'] ?? 0)
            ];

        } catch (\Exception $e) {
            // 记录错误日志
            trace('获取产品库存失败: ' . $e->getMessage(), 'error');
            
            return [
                'total_quantity' => 0,
                'available_quantity' => 0,
                'locked_quantity' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查产品是否有足够的可用库存
     * 
     * @param int $productId 产品ID
     * @param float $requiredQuantity 需要的数量
     * @param int $warehouseId 仓库ID（可选，默认为0表示所有仓库）
     * @return bool 是否有足够库存
     */
    public static function hasEnoughStock($productId, $requiredQuantity, $warehouseId = 0)
    {
        $inventory = self::getProductAvailableQuantity($productId, $warehouseId);
        return $inventory['available_quantity'] >= $requiredQuantity;
    }

    /**
     * 批量获取多个产品的库存状态
     * 
     * @param array $productIds 产品ID数组
     * @param int $warehouseId 仓库ID（可选，默认为0表示所有仓库）
     * @return array 以产品ID为键的库存数据数组
     */
    public static function getBatchProductInventory($productIds, $warehouseId = 0)
    {
        if (empty($productIds)) {
            return [];
        }

        try {
            // 构建查询条件
            $where = [['product_id', 'in', $productIds]];
            if (!empty($warehouseId) && $warehouseId > 0) {
                $where[] = ['warehouse_id', '=', $warehouseId];
            }

            // 查询库存数据
            $inventoryList = Db::name('inventory_realtime')
                ->where($where)
                ->field('
                    product_id,
                    SUM(IFNULL(quantity, 0)) as total_quantity,
                    SUM(IFNULL(available_quantity, 0)) as available_quantity,
                    SUM(IFNULL(locked_quantity, 0)) as locked_quantity
                ')
                ->group('product_id')
                ->select();

            // 转换为以产品ID为键的数组
            $result = [];
            foreach ($inventoryList as $item) {
                $result[$item['product_id']] = [
                    'total_quantity' => floatval($item['total_quantity']),
                    'available_quantity' => floatval($item['available_quantity']),
                    'locked_quantity' => floatval($item['locked_quantity'])
                ];
            }

            // 补充没有库存记录的产品
            foreach ($productIds as $productId) {
                if (!isset($result[$productId])) {
                    $result[$productId] = [
                        'total_quantity' => 0,
                        'available_quantity' => 0,
                        'locked_quantity' => 0
                    ];
                }
            }

            return $result;

        } catch (\Exception $e) {
            // 记录错误日志
            trace('批量获取产品库存失败: ' . $e->getMessage(), 'error');
            
            // 返回空库存数据
            $result = [];
            foreach ($productIds as $productId) {
                $result[$productId] = [
                    'total_quantity' => 0,
                    'available_quantity' => 0,
                    'locked_quantity' => 0,
                    'error' => $e->getMessage()
                ];
            }
            return $result;
        }
    }

    /**
     * 获取产品在指定仓库的详细库存信息
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return array 详细库存信息
     */
    public static function getProductWarehouseInventory($productId, $warehouseId)
    {
        if (empty($productId) || empty($warehouseId)) {
            return [
                'id' => 0,
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'quantity' => 0,
                'available_quantity' => 0,
                'locked_quantity' => 0,
                'unit' => '',
                'cost_price' => 0
            ];
        }

        try {
            $inventory = Db::name('inventory_realtime')
                ->where('product_id', $productId)
                ->where('warehouse_id', $warehouseId)
                ->find();

            if ($inventory) {
                return [
                    'id' => $inventory['id'],
                    'product_id' => $inventory['product_id'],
                    'warehouse_id' => $inventory['warehouse_id'],
                    'quantity' => floatval($inventory['quantity']),
                    'available_quantity' => floatval($inventory['available_quantity']),
                    'locked_quantity' => floatval($inventory['locked_quantity']),
                    'unit' => $inventory['unit'] ?? '',
                    'cost_price' => floatval($inventory['cost_price'] ?? 0)
                ];
            } else {
                return [
                    'id' => 0,
                    'product_id' => $productId,
                    'warehouse_id' => $warehouseId,
                    'quantity' => 0,
                    'available_quantity' => 0,
                    'locked_quantity' => 0,
                    'unit' => '',
                    'cost_price' => 0
                ];
            }

        } catch (\Exception $e) {
            trace('获取产品仓库库存失败: ' . $e->getMessage(), 'error');
            
            return [
                'id' => 0,
                'product_id' => $productId,
                'warehouse_id' => $warehouseId,
                'quantity' => 0,
                'available_quantity' => 0,
                'locked_quantity' => 0,
                'unit' => '',
                'cost_price' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
