{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
    <style>
        .production-plan-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            padding: 15px;
            background: #fff;
            border-bottom: 1px solid #e6e6e6;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            background: #f5f5f5;
        }
        
        .left-panel {
            width: 350px;
            background: #fff;
            border-right: 1px solid #e6e6e6;
            display: flex;
            flex-direction: column;
        }
        
        .right-panel {
            flex: 1;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        
        .panel-header {
            padding: 15px;
            background: #f8f8f8;
            border-bottom: 1px solid #e6e6e6;
            font-weight: bold;
        }
        
        .panel-content {
            flex: 1;
            overflow: auto;
        }
        
        .order-list {
            padding: 10px;
        }
        
        .order-item {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s;
        }
        
        .order-item:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }

        .order-item.order-overdue {
            border-left: 4px solid #ff4757;
            background: #fff5f5;
        }

        .order-item.order-overdue:hover {
            border-color: #ff4757;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.2);
        }

        .overdue-badge {
            background: #ff4757;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
        }
        
        .order-item.dragging {
            opacity: 0.5;
        }
        
        .order-no {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 5px;
        }
        
        .order-info {
            font-size: 12px;
            color: #666;
            line-height: 1.5;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
            color: #fff;
            margin-left: 5px;
        }
        
        .priority-1 { background: #f5222d; }
        .priority-2 { background: #fa8c16; }
        .priority-3 { background: #fadb14; color: #333; }
        .priority-4 { background: #52c41a; }
        .priority-5 { background: #1890ff; }
        
        .gantt-container {
            padding: 15px;
            overflow: auto;
        }
        
        .gantt-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .gantt-chart {
            min-height: 400px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            background: #fff;
        }
        
        .stats-panel {
            padding: 15px;
            background: #f8f8f8;
            border-top: 1px solid #e6e6e6;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .filter-form {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .date-range-picker {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .gantt-timeline {
            display: flex;
            border-bottom: 1px solid #e6e6e6;
            background: #fafafa;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .gantt-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            min-height: 40px;
            align-items: center;
        }
        
        .gantt-row:hover {
            background: #f5f5f5;
        }
        
        .gantt-cell {
            min-width: 80px;
            padding: 8px;
            border-right: 1px solid #f0f0f0;
            text-align: center;
            font-size: 12px;
        }
        
        .gantt-bar {
            height: 24px;
            border-radius: 4px;
            position: relative;
            margin: 8px 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 11px;
            cursor: pointer;
        }
        
        .status-pending { background: #fadb14; color: #333; }
        .status-progress { background: #1890ff; }
        .status-completed { background: #52c41a; }
        .status-delayed { background: #f5222d; }
        
        .progress-text {
            font-weight: bold;
        }

        /* 工序详情提示框样式 */
        .process-tooltip {
            position: absolute;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 12px;
            z-index: 1000;
            min-width: 300px;
            max-width: 400px;
            font-size: 12px;
            line-height: 1.5;
            display: none;
        }

        .tooltip-header {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
            padding-bottom: 6px;
            border-bottom: 1px solid #f0f0f0;
        }

        .tooltip-date {
            color: #666;
            font-size: 11px;
            margin-bottom: 8px;
        }

        .process-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .process-item:last-child {
            border-bottom: none;
        }

        .process-name {
            font-weight: 500;
            color: #333;
        }

        .process-data {
            display: flex;
            gap: 8px;
            font-size: 11px;
        }

        .data-item {
            padding: 2px 6px;
            border-radius: 2px;
            color: #fff;
        }

        .data-reported { background: #1890ff; }
        .data-qualified { background: #52c41a; }
        .data-work-time { background: #fa8c16; }

        .tooltip-summary {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #f0f0f0;
            color: #666;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="production-plan-container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="filter-form">
                <div class="filter-item">
                    <label>状态：</label>
                    <select id="statusFilter" class="layui-input" style="width: 120px;">
                        <option value="">全部状态</option>
                        <option value="0">待开始</option>
                        <option value="1">生产中</option>
                        <option value="2">已完成</option>
                        <option value="3">已延期</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>客户：</label>
                    <input type="text" id="customerFilter" class="layui-input" placeholder="客户订单号" style="width: 150px;">
                </div>
                <div class="filter-item">
                    <label>搜索：</label>
                    <input type="text" id="keywordFilter" class="layui-input" placeholder="订单号/产品名称" style="width: 150px;">
                </div>
                <div class="filter-item date-range-picker">
                    <label>时间范围：</label>
                    <input type="text" id="dateRange" class="layui-input" placeholder="选择日期范围" style="width: 250px;">
                </div>
                <div class="filter-item date-range-picker" style="display: none;">
                    <input type="text" id="startDate" class="layui-input" placeholder="开始日期" style="width: 120px;">
                    <span>至</span>
                    <input type="text" id="endDate" class="layui-input" placeholder="结束日期" style="width: 120px;">
                </div>
                <div class="filter-item">
                    <button class="layui-btn layui-btn-primary" onclick="window.searchOrders()">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" onclick="window.autoSchedule()">
                        <i class="layui-icon layui-icon-engine"></i> 自动排产
                    </button>
                    <button class="layui-btn layui-btn-warm" onclick="window.batchSchedule()">
                        <i class="layui-icon layui-icon-list"></i> 批量排产
                    </button>
                    <button class="layui-btn layui-btn-primary" onclick="window.refreshData()">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 左侧待排产订单列表 -->
            <div class="left-panel">
                <div class="panel-header">
                    待排产订单
                    <span class="layui-badge layui-bg-blue" id="unscheduledCount">0</span>
                </div>
                <div class="panel-content">
                    <div class="order-list" id="orderList">
                        <!-- 订单列表将通过JavaScript动态生成 -->
                    </div>
                </div>
                
                <!-- 统计面板 -->
                <div class="stats-panel">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="todayOrders">0</div>
                            <div class="stat-label">今日订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="weekOrders">0</div>
                            <div class="stat-label">本周订单</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="completionRate">0%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧甘特图 -->
            <div class="right-panel">
                <div class="panel-header">
                    生产计划甘特图
                    <div class="gantt-header" style="float: right;">
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="window.changeView('day')">日视图</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="window.changeView('week')">周视图</button>
                        <button class="layui-btn layui-btn-xs layui-btn-primary" onclick="window.changeView('month')">月视图</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="gantt-container">
                        <div id="ganttChart" class="gantt-chart">
                            <!-- 甘特图将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工序详情提示框 -->
    <div id="processTooltip" class="process-tooltip"></div>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
    // 全局变量
    let currentView = 'day';
    let selectedOrders = [];
    let ganttData = [];

    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;

        // 初始化日期选择器
        var today = new Date();
        var endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
        var todayStr = today.toISOString().split('T')[0];
        var endDateStr = endDate.toISOString().split('T')[0];

        // 设置初始值到输入框
        document.getElementById('startDate').value = todayStr;
        document.getElementById('endDate').value = endDateStr;

        // 使用layui的laydate而不是laydatePlus，支持范围选择
        layui.use(['laydate'], function(){
            var laydate = layui.laydate;

            // 开始日期选择器
            laydate.render({
                elem: '#startDate',
                value: todayStr,
                done: function(value) {
                    window.refreshGanttChart();
                }
            });

            // 结束日期选择器
            laydate.render({
                elem: '#endDate',
                value: endDateStr,
                done: function(value) {
                    window.refreshGanttChart();
                }
            });

            // 添加范围选择器（可选）
            laydate.render({
                elem: '#dateRange',
                type: 'date',
                range: '至',
                done: function(value, date, endDate) {
                    if (value) {
                        var dates = value.split(' 至 ');
                        if (dates.length === 2) {
                            document.getElementById('startDate').value = dates[0];
                            document.getElementById('endDate').value = dates[1];
                            window.refreshGanttChart();
                        }
                    }
                }
            });
        });

        // 初始化页面
        init();
    }

    // 全局函数定义
    function init() {
        window.loadUnscheduledOrders();
        window.loadScheduledPlans();
        window.loadDailyStats();
    }

    // 全局函数 - 加载待排产订单
    window.loadUnscheduledOrders = function() {
        const params = {
            status: document.getElementById('statusFilter').value,
            customer: document.getElementById('customerFilter').value,
            keyword: document.getElementById('keywordFilter').value
        };

        layui.tool.get('/Produce/ProductionPlan/getUnscheduledOrders', params, function(res) {
            console.log('待排产订单API响应:', res);
            if (res.code === 0) {
                window.renderOrderList(res.data);
                document.getElementById('unscheduledCount').textContent = res.data.length;
            } else {
                layui.layer.msg(res.msg);
                console.error('获取待排产订单失败:', res.msg);
            }
        });
    }

    // 全局函数 - 加载已排产计划
    window.loadScheduledPlans = function() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        const params = {
            start_date: startDate,
            end_date: endDate,
            status: document.getElementById('statusFilter').value
        };

        layui.tool.get('/Produce/ProductionPlan/getScheduledPlans', params, function(res) {
            console.log('已排产计划API响应:', res);
            if (res.code === 0) {
                ganttData = res.data;
                console.log('甘特图数据:', ganttData);
                window.renderGanttChart();
            } else {
                layui.layer.msg(res.msg);
                console.error('获取已排产计划失败:', res.msg);
            }
        });
    }

    // 全局函数 - 加载每日统计
    window.loadDailyStats = function() {
        const today = new Date().toISOString().split('T')[0];
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());

        layui.tool.get('/Produce/ProductionPlan/getDailyStats', {
            start_date: weekStart.toISOString().split('T')[0],
            end_date: today
        }, function(res) {
            if (res.code === 0) {
                window.updateStatsDisplay(res.data);
            }
        });
    }

    // 全局函数 - 更新统计显示
    window.updateStatsDisplay = function(statsData) {
        const today = new Date().toISOString().split('T')[0];
        const todayStats = statsData.find(s => s.date === today) || {};

        let weekTotal = 0;
        let weekCompleted = 0;

        statsData.forEach(stat => {
            weekTotal += stat.total_orders;
            weekCompleted += stat.completed_orders;
        });

        document.getElementById('todayOrders').textContent = todayStats.total_orders || 0;
        document.getElementById('weekOrders').textContent = weekTotal;
        document.getElementById('completionRate').textContent =
            weekTotal > 0 ? Math.round(weekCompleted / weekTotal * 100) + '%' : '0%';
    }

    // 全局函数 - 渲染订单列表
    window.renderOrderList = function(orders) {
        const orderList = document.getElementById('orderList');
        let html = '';

        orders.forEach(order => {
            const priorityClass = `priority-${order.priority}`;
            const deliveryDate = new Date(order.delivery_date * 1000).toLocaleDateString();

            // 逾期标识
            const overdueClass = order.is_overdue ? 'order-overdue' : '';
            const overdueText = order.is_overdue ? `<span class="overdue-badge">逾期${order.overdue_days}天</span>` : '';

            html += `
                <div class="order-item ${overdueClass}" draggable="true" data-order-id="${order.id}"
                     ondragstart="window.handleDragStart(event)" ondragend="window.handleDragEnd(event)">
                    <div class="order-no">
                        ${order.order_no}
                        <span class="priority-badge ${priorityClass}">P${order.priority}</span>
                        ${overdueText}
                    </div>
                    <div class="order-info">
                        <div>产品：${order.product_name}</div>
                        <div>数量：${order.quantity}</div>
                        <div>交期：${deliveryDate}</div>
                        <div>预计：${order.estimated_days || 1}天</div>
                        <div>评分：${(order.priority_score || 0).toFixed(1)}</div>
                    </div>
                    <div style="margin-top: 8px;">
                        <input type="checkbox" class="order-checkbox" value="${order.id}"
                               onchange="window.updateSelectedOrders()">
                        <label style="margin-left: 5px; font-size: 12px;">选择</label>
                    </div>
                </div>
            `;
        });

        orderList.innerHTML = html;
    }

    // 全局函数 - 渲染甘特图
    window.renderGanttChart = function() {
        console.log('开始渲染甘特图, 数据量:', ganttData.length);
        const ganttChart = document.getElementById('ganttChart');

        // 获取日期值，如果为空则使用默认值
        let startDateValue = document.getElementById('startDate').value;
        let endDateValue = document.getElementById('endDate').value;

        if (!startDateValue) {
            startDateValue = new Date().toISOString().split('T')[0];
            document.getElementById('startDate').value = startDateValue;
        }
        if (!endDateValue) {
            const defaultEndDate = new Date();
            defaultEndDate.setDate(defaultEndDate.getDate() + 30);
            endDateValue = defaultEndDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDateValue;
        }

        const startDate = new Date(startDateValue);
        const endDate = new Date(endDateValue);

        console.log('时间范围:', startDate, '到', endDate);

        // 生成时间轴
        const timeline = window.generateTimeline(startDate, endDate);

        let html = '<div class="gantt-timeline">';
        html += '<div class="gantt-cell" style="min-width: 200px;">订单信息</div>';
        timeline.forEach(date => {
            html += `<div class="gantt-cell">${window.formatDate(date)}</div>`;
        });
        html += '</div>';

        // 生成甘特条
        ganttData.forEach(plan => {
            html += '<div class="gantt-row">';
            html += `
                <div class="gantt-cell" style="min-width: 200px; text-align: left;">
                    <div style="font-weight: bold; color: #1890ff;">${plan.order_no}</div>
                    <div style="font-size: 11px; color: #666;">${plan.product_name}</div>
                    <div style="font-size: 11px; color: #666;">数量: ${plan.quantity}</div>
                </div>
            `;

            timeline.forEach(date => {
                const dateStr = window.formatDateStr(date);
                const planStart = plan.start_date;
                const planEnd = plan.end_date;

                let cellContent = '';
                if (dateStr >= planStart && dateStr <= planEnd) {
                    const statusClass = window.getStatusClass(plan.status, plan.is_delayed);
                    cellContent = `
                        <div class="gantt-bar ${statusClass}"
                             onclick="window.showPlanDetail(${plan.id})"
                             onmouseover="window.showProcessTooltip(event, ${plan.order_id}, '${dateStr}')"
                             onmouseout="window.hideProcessTooltip()"
                             title="${plan.order_no} - ${plan.progress}%">
                            <span class="progress-text">${plan.progress}%</span>
                        </div>
                    `;
                }

                html += `<div class="gantt-cell">${cellContent}</div>`;
            });

            html += '</div>';
        });

        ganttChart.innerHTML = html;

        // 添加拖放支持
        window.addDropZoneSupport();
    }

    // 全局函数 - 生成时间轴
    window.generateTimeline = function(startDate, endDate) {
        const timeline = [];
        const current = new Date(startDate);

        while (current <= endDate) {
            timeline.push(new Date(current));
            current.setDate(current.getDate() + 1);
        }

        return timeline;
    }

    // 全局函数 - 格式化日期显示
    window.formatDate = function(date) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
        return `${month}/${day}<br>${weekDay}`;
    }

    // 全局函数 - 格式化日期字符串
    window.formatDateStr = function(date) {
        return date.toISOString().split('T')[0];
    }

    // 全局函数 - 获取状态样式类
    window.getStatusClass = function(status, isDelayed) {
        if (isDelayed) return 'status-delayed';

        switch (status) {
            case 0: return 'status-pending';
            case 1: return 'status-progress';
            case 2: return 'status-completed';
            case 3: return 'status-delayed';
            default: return 'status-pending';
        }
    }

    // 全局函数 - 刷新甘特图
    window.refreshGanttChart = function() {
        window.loadScheduledPlans();
    }

    // 全局函数 - 添加拖放区域支持
    window.addDropZoneSupport = function() {
        const ganttCells = document.querySelectorAll('.gantt-cell');

        ganttCells.forEach(cell => {
            cell.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.backgroundColor = '#e6f7ff';
            });

            cell.addEventListener('dragleave', function(e) {
                this.style.backgroundColor = '';
            });

            cell.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.backgroundColor = '';

                const orderId = e.dataTransfer.getData('text/plain');
                const cellIndex = Array.from(this.parentNode.children).indexOf(this);

                if (cellIndex > 0) { // 排除第一列（订单信息列）
                    const timeline = window.generateTimeline(
                        new Date(document.getElementById('startDate').value),
                        new Date(document.getElementById('endDate').value)
                    );
                    const targetDate = timeline[cellIndex - 1];

                    if (targetDate) {
                        window.showScheduleDialog(orderId, window.formatDateStr(targetDate));
                    }
                }
            });
        });
    }

    // 全局函数 - 显示排产对话框
    window.showScheduleDialog = function(orderId, defaultDate) {
        layui.layer.open({
            type: 1,
            title: '排产设置',
            content: `
                <div style="padding: 20px;">
                    <form class="layui-form" lay-filter="scheduleForm">
                        <input type="hidden" name="order_id" value="${orderId}" />
                        <div class="layui-form-item">
                            <label class="layui-form-label">开始日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="start_date" class="layui-input"
                                       id="scheduleStartDate" value="${defaultDate}" required />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">结束日期</label>
                            <div class="layui-input-block">
                                <input type="text" name="end_date" class="layui-input"
                                       id="scheduleEndDate" required />
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">优先级</label>
                            <div class="layui-input-block">
                                <select name="priority">
                                    <option value="1">低</option>
                                    <option value="2">较低</option>
                                    <option value="3" selected>中等</option>
                                    <option value="4">较高</option>
                                    <option value="5">高</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea name="notes" class="layui-textarea" placeholder="排产备注"></textarea>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="submitSchedule">确定排产</button>
                                <button type="button" class="layui-btn layui-btn-primary" onclick="layui.layer.closeAll()">取消</button>
                            </div>
                        </div>
                    </form>
                </div>
            `,
            area: ['500px', '400px'],
            success: function() {
                // 使用layui.use动态加载需要的模块
                layui.use(['laydate', 'form'], function(){
                    var laydate = layui.laydate;
                    var form = layui.form;

                    // 初始化日期选择器
                    laydate.render({
                        elem: '#scheduleStartDate',
                        done: function(value) {
                            // 自动设置结束日期（假设1天生产周期）
                            const endDate = new Date(value);
                            endDate.setDate(endDate.getDate() + 1);
                            document.getElementById('scheduleEndDate').value = window.formatDateStr(endDate);
                        }
                    });

                    laydate.render({
                        elem: '#scheduleEndDate'
                    });

                    form.render();

                    // 监听表单提交
                    form.on('submit(submitSchedule)', function(data) {
                        window.savePlan(data.field);
                        return false;
                    });
                });
            }
        });
    }

    // 全局函数 - 保存排产计划
    window.savePlan = function(formData) {
        layui.tool.post('/Produce/ProductionPlan/savePlan', formData, function(res) {
            layui.layer.msg(res.msg);
            if (res.code === 0) {
                layui.layer.closeAll();
                window.loadUnscheduledOrders();
                window.loadScheduledPlans();
                window.loadDailyStats();
            }
        });
    }
















    // 全局函数 - 切换视图
    window.changeView = function(view) {
        currentView = view;
        // 这里可以实现不同视图的逻辑
        layui.layer.msg(`切换到${view === 'day' ? '日' : view === 'week' ? '周' : '月'}视图`);
    }

    // 全局函数 - 显示计划详情
    window.showPlanDetail = function(planId) {
        // 这里可以实现显示计划详情的逻辑
        layui.layer.msg('显示计划详情: ' + planId);
    }

    // 全局函数 - 搜索订单
    window.searchOrders = function() {
        window.loadUnscheduledOrders();
        window.loadScheduledPlans();
    }

    // 全局函数 - 自动排产
    window.autoSchedule = function() {
        layui.layer.confirm('确定要执行自动排产吗？', {icon: 3, title: '提示'}, function(index) {
            const params = {
                start_date: document.getElementById('startDate').value,
                max_daily_orders: 5
            };

            layui.tool.post('/Produce/ProductionPlan/autoSchedule', params, function(res) {
                layui.layer.msg(res.msg);
                if (res.code === 0) {
                    window.refreshData();
                }
            });

            layui.layer.close(index);
        });
    }

    // 全局函数 - 批量排产
    window.batchSchedule = function() {
        if (selectedOrders.length === 0) {
            layui.layer.msg('请先选择要排产的订单');
            return;
        }

        layui.layer.confirm(`确定要批量排产选中的 ${selectedOrders.length} 个订单吗？`, {icon: 3, title: '提示'}, function(index) {
            const params = {
                order_ids: selectedOrders,
                start_date: document.getElementById('startDate').value,
                max_daily_orders: 5
            };

            layui.tool.post('/Produce/ProductionPlan/batchSchedule', params, function(res) {
                layui.layer.msg(res.msg);
                if (res.code === 0) {
                    selectedOrders = [];
                    window.refreshData();
                }
            });

            layui.layer.close(index);
        });
    }

    // 全局函数 - 刷新数据
    window.refreshData = function() {
        window.loadUnscheduledOrders();
        window.loadScheduledPlans();
        window.loadDailyStats();
    }

    // 全局函数 - 拖拽开始
    window.handleDragStart = function(event) {
        event.dataTransfer.setData('text/plain', event.target.dataset.orderId);
        event.target.classList.add('dragging');
    }

    // 全局函数 - 拖拽结束
    window.handleDragEnd = function(event) {
        event.target.classList.remove('dragging');
    }

    // 全局函数 - 更新选中订单
    window.updateSelectedOrders = function() {
        selectedOrders = [];
        document.querySelectorAll('.order-checkbox:checked').forEach(checkbox => {
            selectedOrders.push(parseInt(checkbox.value));
        });
    }

    // 全局函数 - 显示工序详情提示框
    window.showProcessTooltip = function(event, orderId, date) {
        const tooltip = document.getElementById('processTooltip');

        // 获取工序详情数据
        layui.tool.post('/Produce/ProductionPlan/getOrderProcessDetails', {
            order_id: orderId,
            date: date
        }, function(res) {
            if (res.code === 0) {
                const data = res.data;
                let html = `
                    <div class="tooltip-header">
                        ${data.order.order_no} - ${data.order.product_name}
                    </div>
                    <div class="tooltip-date">
                        报工日期：${data.date} | 订单数量：${data.order.quantity}件
                    </div>
                `;

                if (data.processes.length > 0) {
                    data.processes.forEach(process => {
                        const statusColor = process.status === '已完成' ? '#52c41a' :
                                          process.status === '进行中' ? '#1890ff' : '#d9d9d9';

                        html += `
                            <div class="process-item">
                                <div class="process-name">
                                    ${process.step_no}. ${process.process_name}
                                    <span style="color: ${statusColor}; font-size: 10px; margin-left: 5px;">${process.status}</span>
                                </div>
                                <div class="process-data">
                                    <span class="data-item data-reported">${process.daily_quantity}</span>
                                    <span class="data-item data-qualified">${process.daily_qualified}</span>
                                    <span class="data-item data-work-time">${process.daily_work_time}h</span>
                                </div>
                            </div>
                        `;
                    });

                    html += `
                        <div class="tooltip-summary">
                            当日总计：报工 ${data.summary.total_daily_reported}件，合格 ${data.summary.total_daily_qualified}件
                        </div>
                    `;
                } else {
                    html += `
                        <div style="color: #999; text-align: center; padding: 10px;">
                            当日无报工记录
                        </div>
                    `;
                }

                tooltip.innerHTML = html;

                // 定位提示框
                const rect = event.target.getBoundingClientRect();
                tooltip.style.left = (rect.left + window.scrollX + 10) + 'px';
                tooltip.style.top = (rect.top + window.scrollY - 10) + 'px';
                tooltip.style.display = 'block';
            }
        });
    }

    // 全局函数 - 隐藏工序详情提示框
    window.hideProcessTooltip = function() {
        const tooltip = document.getElementById('processTooltip');
        tooltip.style.display = 'none';
    }
</script>
{/block}
