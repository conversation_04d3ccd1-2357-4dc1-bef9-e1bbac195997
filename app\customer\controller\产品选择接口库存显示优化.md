# 产品选择接口库存显示优化

## 需求描述
在客户订单的产品选择页面（http://tc.xinqiyu.cn:8830/customer/Order/get_product_datalist.html），需要：
1. 修正库存数量显示不正确的问题
2. 增加"锁定库存数"列
3. 增加"等待分配数"列

## 问题分析
原有的产品选择接口 `get_product_datalist` 调用了 `ProductModel` 的 `datalist` 方法，但该方法没有查询库存信息，导致库存数量显示为空或不正确。

## 解决方案

### 1. 修改ProductModel的datalist方法
**文件**: `app/engineering/model/Product.php`

在 `datalist` 方法中增加库存信息查询：
```php
// 查询库存信息
$inventoryInfo = $this->getProductInventoryInfo($item['id']);
$item['stock'] = $inventoryInfo['total_stock']; // 库存数量
$item['locked_stock'] = $inventoryInfo['locked_stock']; // 锁定库存数
$item['pending_allocation'] = $inventoryInfo['pending_allocation']; // 等待分配数
$item['available_stock'] = $inventoryInfo['available_stock']; // 可用库存
```

### 2. 新增getProductInventoryInfo方法
该方法负责查询产品的库存信息：

**查询逻辑**：
1. 优先从 `inventory_realtime` 表查询实时库存
2. 如果实时库存表无数据，则从 `inventory` 表查询
3. 计算各种库存数量：
   - 总库存数量 (total_stock)
   - 锁定库存数 (locked_stock)
   - 等待分配数 (pending_allocation)
   - 可用库存 (available_stock)

**字段映射**：
- `inventory_realtime` 表：
  - quantity → total_stock
  - locked_quantity → locked_stock
  - available_quantity → available_stock

- `inventory` 表：
  - quantity → total_stock
  - locked_quantity → locked_stock
  - available_quantity → available_stock

- `inventory_allocation_request` 表（等待分配数）：
  - SUM(quantity - allocated_quantity) → pending_allocation
  - 查询条件：status IN (1,2) 表示待分配和部分分配状态

### 3. 修改前端页面显示
**文件**: `app/customer/view/order/selectProduct.html`

在表格列定义中增加新列：
```javascript
{field: 'stock', title: '库存数量', width: 100, align: 'center', templet: function(d){
    return d.stock || 0;
}},
{field: 'locked_stock', title: '锁定库存数', width: 120, align: 'center', templet: function(d){
    return '<span style="color: #ff5722;">' + (d.locked_stock || 0) + '</span>';
}},
{field: 'pending_allocation', title: '等待分配数', width: 120, align: 'center', templet: function(d){
    return '<span style="color: #ff9800;">' + (d.pending_allocation || 0) + '</span>';
}}
```

**样式说明**：
- 锁定库存数：红色显示 (#ff5722)
- 等待分配数：橙色显示 (#ff9800)

## 数据库表结构

### inventory_realtime 表
```sql
- quantity: 总库存数量
- available_quantity: 可用数量
- locked_quantity: 锁定数量
```

### inventory 表
```sql
- quantity: 总库存数量
- available_quantity: 可用数量
- locked_quantity: 锁定数量
- allocated_quantity: 已分配数量
```

## 测试验证
1. 访问产品选择页面：http://tc.xinqiyu.cn:8830/customer/order/selectProduct
2. 检查表格是否正确显示：
   - 库存数量列
   - 锁定库存数列（红色）
   - 等待分配数列（橙色）
3. 验证数据准确性

## 注意事项
1. 如果数据库中没有库存数据，所有库存相关字段将显示为0
2. 需要确保 `inventory_realtime` 或 `inventory` 表中有相应的库存数据
3. 异常处理：如果查询失败，返回默认值0，避免页面报错
