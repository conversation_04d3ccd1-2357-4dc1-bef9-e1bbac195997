-- 仓库测试数据
-- 请根据实际的表名执行对应的SQL语句

-- 如果表名是 warehouse
INSERT INTO `warehouse` (`id`, `code`, `name`, `address`, `contact_name`, `contact_phone`, `type`, `is_default`, `status`, `description`, `created_by`, `create_time`, `update_time`) VALUES
(1, 'WH001', '主仓库', '北京市朝阳区xxx路xxx号', '张三', '13800138001', 1, 1, 1, '主要存储仓库', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'WH002', '分仓库A', '北京市海淀区xxx路xxx号', '李四', '13800138002', 1, 0, 1, '分仓库A', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'WH003', '分仓库B', '北京市西城区xxx路xxx号', '王五', '13800138003', 1, 0, 1, '分仓库B', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 如果表名是 oa_warehouse
INSERT INTO `oa_warehouse` (`id`, `code`, `name`, `address`, `contact_name`, `contact_phone`, `type`, `is_default`, `status`, `description`, `created_by`, `create_time`, `update_time`) VALUES
(1, 'WH001', '主仓库', '北京市朝阳区xxx路xxx号', '张三', '13800138001', 1, 1, 1, '主要存储仓库', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'WH002', '分仓库A', '北京市海淀区xxx路xxx号', '李四', '13800138002', 1, 0, 1, '分仓库A', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'WH003', '分仓库B', '北京市西城区xxx路xxx号', '王五', '13800138003', 1, 0, 1, '分仓库B', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 产品测试数据（如果需要测试产品搜索功能）
-- 如果表名是 product
INSERT INTO `product` (`id`, `title`, `material_code`, `specs`, `unit`, `purchase_price`, `status`, `delete_time`, `create_time`, `update_time`) VALUES
(1, '测试产品A', 'P001', '规格A', '个', 10.50, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '测试产品B', 'P002', '规格B', '箱', 25.80, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '测试产品C', 'P003', '规格C', 'kg', 15.20, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 如果表名是 oa_product
INSERT INTO `oa_product` (`id`, `title`, `material_code`, `specs`, `unit`, `purchase_price`, `status`, `delete_time`, `create_time`, `update_time`) VALUES
(1, '测试产品A', 'P001', '规格A', '个', 10.50, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '测试产品B', 'P002', '规格B', '箱', 25.80, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '测试产品C', 'P003', '规格C', 'kg', 15.20, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
