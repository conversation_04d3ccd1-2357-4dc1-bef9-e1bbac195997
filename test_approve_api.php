<?php
// 测试审批API的简单脚本
// 用于验证返回格式

// 模拟测试数据
$test_cases = [
    [
        'name' => '审批通过测试',
        'data' => ['id' => 110, 'action' => 'approve', 'content' => '测试审批通过'],
        'expected' => ['code' => 0, 'msg' => '审批通过成功']
    ],
    [
        'name' => '审批拒绝测试',
        'data' => ['id' => 110, 'action' => 'reject', 'content' => '测试拒绝原因'],
        'expected' => ['code' => 0, 'msg' => '审批拒绝成功']
    ],
    [
        'name' => '反审核测试',
        'data' => ['id' => 110, 'action' => 'unapprove', 'content' => '测试反审核'],
        'expected' => ['code' => 0, 'msg' => '反审核成功']
    ]
];

echo "采购订单审批API测试用例：\n";
echo "=========================\n";

foreach ($test_cases as $case) {
    echo "测试：" . $case['name'] . "\n";
    echo "请求数据：" . json_encode($case['data'], JSON_UNESCAPED_UNICODE) . "\n";
    echo "期望返回：" . json_encode($case['expected'], JSON_UNESCAPED_UNICODE) . "\n";
    echo "API地址：POST /purchase/order/selfApprove\n";
    echo "------------------------\n";
}

echo "\n注意事项：\n";
echo "1. 所有返回都应该是标准JSON格式\n";
echo "2. 成功时 code=0，失败时 code=1\n";
echo "3. 操作记录应该显示在详情页面的操作记录标签页中\n";
echo "4. 日志应该包含操作人、操作时间、操作意见等信息\n";
?>
