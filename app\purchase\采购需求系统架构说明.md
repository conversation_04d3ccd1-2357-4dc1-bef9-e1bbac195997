# 采购需求系统架构说明

## 系统概述
采购需求系统用于统一管理和显示所有需要采购的物料，包括销售订单缺货商品和生产订单所需物料。

## 数据来源

### 1. 销售订单缺货 (Sales Shortage)
**数据表**：`oa_customer_order_detail`
**筛选条件**：
- `inventory_status = 2` (缺货状态)
- `gap > 0` (有缺口数量)
- 关联的销售订单已审核 (`customer_order.check_status = 2`)

**关键字段**：
- `product_id`: 产品ID
- `product_name`: 产品名称
- `quantity`: 订单数量
- `gap`: 缺口数量
- `order_id`: 关联销售订单ID

### 2. 生产物料缺货 (Production Material Shortage)
**数据表**：`oa_produce_order_material_requirement`
**筛选条件**：
- `shortage_quantity > 0` (有缺口数量)
- `status IN (1,2)` (待分配或部分分配)
- 关联的生产订单状态正常 (`produce_order.status IN (1,2,3)`)

**关键字段**：
- `material_id`: 物料ID
- `material_name`: 物料名称
- `required_quantity`: 需求总量
- `shortage_quantity`: 缺口数量
- `order_id`: 关联生产订单ID

## API接口

### 接口地址
`GET /api/test/getmrppurchase_suggestion`

### 请求参数
| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| page | int | 页码 | 1 |
| limit | int | 每页数量 | 15 |
| business_no | string | 订单号搜索 | SC202508110001 |
| product_name | string | 产品名称搜索 | 螺丝 |
| salesman_name | string | 业务员搜索 | 张三 |

### 返回数据结构
```json
{
  "code": 0,
  "msg": "success",
  "count": 100,
  "data": [
    {
      "id": 1,
      "product_id": 123,
      "product_name": "产品名称",
      "product_code": "P001",
      "business_no": "SC202508110001",
      "quantity": 100,
      "gap": 50,
      "source_type": "sales_shortage|production_shortage",
      "types": "销售缺货|生产缺料",
      "main_product_name": "主产品名称",
      "salesman_name": "业务员",
      "stock_quantity": 20,
      "stock_available": 15,
      "in_transit_quantity": 30,
      "in_transit_available": 25,
      "remaining_gap": 10,
      "create_time": "2025-08-11 10:00:00"
    }
  ]
}
```

## 前端页面

### 页面路径
`http://domain/purchase/supply/cgxq`

### 主要功能
1. **数据展示**：表格形式显示所有采购需求
2. **搜索筛选**：支持按订单号、产品名称、业务员搜索
3. **批量处理**：选择多个需求进行批量采购处理
4. **详情查看**：点击详情查看具体需求信息

### 表格列说明
| 列名 | 说明 | 数据来源 |
|------|------|----------|
| ID | 记录ID | 数据库主键 |
| 创建时间 | 需求创建时间 | create_time |
| 单号 | 订单号 | business_no |
| 业务员 | 负责业务员 | salesman_name |
| 产品编码 | 物料编码 | product_code |
| 产品名称 | 物料名称 | product_name |
| 需求类型 | 销售缺货/生产缺料 | source_type |
| 关联信息 | 所属订单产品 | main_product_name |
| 需求数量 | 总需求量 | quantity |
| 缺口数量 | 实际缺口 | gap |
| 在库 | 库存数量(可用) | stock_quantity(stock_available) |
| 在途 | 在途数量(可用) | in_transit_quantity(in_transit_available) |
| 剩余缺口 | 扣除库存在途后缺口 | remaining_gap |
| 状态 | 需求状态 | status_text |

## 业务流程

### 1. 数据生成
- **销售缺货**：销售订单审核后，系统检查库存，标记缺货商品
- **生产缺料**：生产订单创建后，系统计算物料需求，标记缺少物料

### 2. 采购处理
- 采购人员查看采购需求列表
- 根据缺口数量、库存情况制定采购计划
- 批量选择需求，创建采购订单

### 3. 状态更新
- 采购订单创建后，更新需求状态
- 物料到货后，更新库存和需求状态

## 技术架构

### 后端
- **框架**：ThinkPHP 6.0
- **数据库**：MySQL
- **API**：RESTful接口

### 前端
- **框架**：Layui
- **组件**：Table、Form、Layer
- **交互**：AJAX异步请求

## 系统优势

1. **数据统一**：整合销售和生产两种需求来源
2. **信息完整**：提供库存、在途、缺口等完整信息
3. **操作便捷**：支持搜索、筛选、批量处理
4. **实时准确**：基于实时库存和需求计算
5. **决策支持**：为采购决策提供全面数据支持

## 注意事项

1. **数据同步**：确保库存数据实时更新
2. **权限控制**：采购人员权限管理
3. **状态管理**：需求处理状态的准确维护
4. **性能优化**：大数据量时的查询优化
