<?php
namespace app\customer\controller;


use app\base\BaseController;
use app\customer\model\Order;
use app\customer\model\OrderDetail;
use app\customer\model\Delivery as DeliveryModel;
use app\customer\model\DeliveryItem;
use app\customer\model\DeliveryLog;
use app\warehouse\model\Outbound as OutboundModel;
use app\warehouse\model\OutboundDetail as OutboundDetailModel;
use app\warehouse\model\Inventory as InventoryModel;
use think\facade\Db;
use think\facade\Session;
use think\facade\Validate;
use think\facade\View;
use app\warehouse\service\InventoryService;

class Delivery extends BaseController
{
    /**
     * 发货指令列表页
     */
    public function index()
    {
        if (request()->isAjax()) {
            return $this->getList();
        }
        return view();
    }
    
    /**
     * 获取发货指令列表
     */
    public function getList()
    {
        $param = request()->param();
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 10;
        
        $map = [];
        
        // 搜索条件
        if (!empty($param['delivery_no'])) {
            $map[] = ['delivery_no', 'like', '%' . $param['delivery_no'] . '%'];
        }
        
        if (!empty($param['order_no'])) {
            $map[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
        }
        
        if (isset($param['customer_id']) && $param['customer_id'] !== '') {
            $map[] = ['customer_id', '=', $param['customer_id']];
        }
        
        if (isset($param['status']) && $param['status'] !== '') {
            $map[] = ['status', '=', $param['status']];
        }
        
        // 获取数据，包含关联信息
        $list = DeliveryModel::with(['customer', 'creator', 'handler'])
            ->where($map)
            ->order('id', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page]);

        // 处理数据格式
        $items = [];
        foreach ($list->items() as $item) {
            $data = $item->toArray();
            $data['customer_name'] = $item->customer ? $item->customer->name : '';
            $data['creator_name'] = $item->creator ? $item->creator->name : '';
            $data['handler_name'] = $item->handler ? $item->handler->name : '';

            // 处理创建时间
            if ($item->create_time) {
                if (is_numeric($item->create_time)) {
                    $data['create_time'] = date('Y-m-d H:i:s', $item->create_time);
                } else {
                    $data['create_time'] = $item->create_time;
                }
            } else {
                $data['create_time'] = '';
            }

            // 处理处理时间
            if ($item->handle_time) {
                if (is_numeric($item->handle_time)) {
                    $data['handle_time'] = date('Y-m-d H:i:s', $item->handle_time);
                } else {
                    $data['handle_time'] = $item->handle_time;
                }
            } else {
                $data['handle_time'] = '';
            }

            // 获取关联的出库单状态信息
            $outboundInfo = $this->getOutboundStatusInfo($item->id);
            $data = array_merge($data, $outboundInfo);

            $items[] = $data;
        }

        return json(['code' => 0, 'msg' => 'ok', 'count' => $list->total(), 'data' => $items]);
    }

    /**
     * 获取出库单状态信息
     * @param int $deliveryId 发货单ID
     * @return array
     */
    private function getOutboundStatusInfo($deliveryId)
    {
        // 先获取发货单信息
        $delivery = \think\facade\Db::name('customer_order_delivery')->where('id', $deliveryId)->find();
        if (!$delivery) {
            return [
                'outbound_status' => 'none',
                'outbound_status_text' => '发货单不存在',
                'outbound_status_class' => 'layui-badge layui-bg-gray',
                'outbound_no' => '',
                'outbound_operator' => '',
                'outbound_time' => '',
                'outbound_progress' => '发货单异常'
            ];
        }

        // 查询关联的出库单信息 - 通过多种方式匹配确保准确性
        $outbound = \think\facade\Db::name('outbound')
            ->alias('o')
            ->leftJoin('admin a1', 'o.created_by = a1.id')
            ->leftJoin('admin a2', 'o.outbound_by = a2.id')
            ->leftJoin('admin a3', 'o.approved_by = a3.id')
            ->field('o.id as outbound_id, o.outbound_no, o.status as outbound_status,
                     o.outbound_time, o.approved_time, o.total_quantity, o.total_amount,
                     a1.nickname as outbound_creator,
                     a2.nickname as outbound_operator,
                     a3.nickname as outbound_approver')
            ->where('o.ref_type', 'customer_order')
            ->where('o.ref_id', $delivery['order_id']) // 订单ID匹配
            ->where(function($query) use ($delivery) {
                // 通过ref_no或notes字段匹配发货单号
                $query->whereOr('o.ref_no', 'like', '%/' . $delivery['delivery_no'])
                      ->whereOr('o.notes', 'like', '%' . $delivery['delivery_no'] . '%');
            })
            ->order('o.id DESC') // 取最新的出库单
            ->find();

        if (!$outbound) {
            return [
                'outbound_status' => 'none',
                'outbound_status_text' => '未生成出库单',
                'outbound_status_class' => 'layui-badge layui-bg-gray',
                'outbound_no' => '',
                'outbound_operator' => '',
                'outbound_time' => '',
                'outbound_progress' => '等待生成出库单'
            ];
        }

        // 根据出库单状态返回相应信息
        $statusInfo = $this->getOutboundStatusMapping($outbound['outbound_status']);

        return [
            'outbound_id' => $outbound['outbound_id'],
            'outbound_no' => $outbound['outbound_no'],
            'outbound_status' => $outbound['outbound_status'],
            'outbound_status_text' => $statusInfo['text'],
            'outbound_status_class' => $statusInfo['class'],
            'outbound_operator' => $outbound['outbound_operator'] ?: $outbound['outbound_creator'],
            'outbound_approver' => $outbound['outbound_approver'],
            'outbound_time' => $outbound['outbound_time'] ? date('Y-m-d H:i:s', $outbound['outbound_time']) : '',
            'outbound_progress' => $statusInfo['progress']
        ];
    }

    /**
     * 获取出库单状态映射
     * @param int $status
     * @return array
     */
    private function getOutboundStatusMapping($status)
    {
        $mapping = [
            0 => [
                'text' => '草稿',
                'class' => 'layui-badge layui-bg-gray',
                'progress' => '仓库准备中'
            ],
            1 => [
                'text' => '已提交',
                'class' => 'layui-badge layui-bg-orange',
                'progress' => '等待审核'
            ],
            2 => [
                'text' => '已审核',
                'class' => 'layui-badge layui-bg-blue',
                'progress' => '等待出库'
            ],
            3 => [
                'text' => '部分出库',
                'class' => 'layui-badge layui-bg-cyan',
                'progress' => '正在出库'
            ],
            4 => [
                'text' => '全部出库',
                'class' => 'layui-badge layui-bg-green',
                'progress' => '出库完成'
            ],
            5 => [
                'text' => '已取消',
                'class' => 'layui-badge layui-bg-red',
                'progress' => '出库取消'
            ]
        ];

        return $mapping[$status] ?? [
            'text' => '未知状态',
            'class' => 'layui-badge layui-bg-gray',
            'progress' => '状态异常'
        ];
    }

    /**
     * 根据出库状态确定发货状态
     * @param mixed $outboundStatus
     * @return int
     */
    private function getDeliveryStatusByOutbound($outboundStatus)
    {
        if ($outboundStatus === 'none') {
            return 0; // 等待生成出库单
        } elseif ($outboundStatus == 0 || $outboundStatus == 1) {
            return 1; // 等待仓库操作
        } elseif ($outboundStatus == 2) {
            return 2; // 等待仓库操作（已审核）
        } elseif ($outboundStatus == 3) {
            return 3; // 正在出库
        } elseif ($outboundStatus == 4) {
            return 4; // 出库完成
        } elseif ($outboundStatus == 5) {
            return 5; // 已取消
        }
        return 0; // 默认等待生成出库单
    }

    /**
     * 获取发货状态文本
     * @param int $status
     * @return string
     */
    private function getDeliveryStatusText($status)
    {
        $texts = [
            0 => '等待生成出库单',
            1 => '等待仓库操作',
            2 => '等待仓库操作',
            3 => '正在出库',
            4 => '出库完成',
            5 => '已取消'
        ];
        return $texts[$status] ?? '未知状态';
    }

    /**
     * 获取发货状态样式类
     * @param int $status
     * @return string
     */
    private function getDeliveryStatusClass($status)
    {
        $classes = [
            0 => 'layui-badge layui-bg-gray',
            1 => 'layui-badge layui-bg-orange',
            2 => 'layui-badge layui-bg-orange',
            3 => 'layui-badge layui-bg-blue',
            4 => 'layui-badge layui-bg-green',
            5 => 'layui-badge layui-bg-red'
        ];
        return $classes[$status] ?? 'layui-badge layui-bg-gray';
    }

    /**
     * 测试方法 - 用于调试
     */
    public function test()
    {
        try {
            // 测试基本查询
            $count = DeliveryModel::count();

            // 测试关联查询
            $list = DeliveryModel::with(['customer', 'creator', 'handler'])
                ->limit(5)
                ->select();

            $result = [];
            foreach ($list as $item) {
                $result[] = [
                    'id' => $item->id,
                    'delivery_no' => $item->delivery_no,
                    'customer' => $item->customer ? $item->customer->name : 'No Customer',
                    'creator' => $item->creator ? $item->creator->name : 'No Creator',
                    'handler' => $item->handler ? $item->handler->name : 'No Handler'
                ];
            }

            return json([
                'code' => 0,
                'msg' => 'test success',
                'data' => [
                    'total_count' => $count,
                    'sample_data' => $result
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 1,
                'msg' => 'test failed: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 选择订单页面
     */
    public function selectOrder()
    {
        if (request()->isAjax()) {
            return $this->getOrderList();
        }
        return view();
    }
    
    /**
     * 获取可发货订单列表
     */
    public function getOrderList()
    {
        $param = request()->param();
        $page = isset($param['page']) ? intval($param['page']) : 1;
        $limit = isset($param['limit']) ? intval($param['limit']) : 10;
        
        $map = [];
        $map[] = ['status', 'in', [1, 2, 3]]; // 已审核、部分发货、待补货的订单
        $map[] = ['delete_time', '=', 0]; // 未删除的订单
        
        // 搜索条件
        if (!empty($param['order_no'])) {
            $map[] = ['order_no', 'like', '%' . $param['order_no'] . '%'];
        }
        
        if (isset($param['customer_id']) && $param['customer_id'] !== '') {
            $map[] = ['customer_id', '=', $param['customer_id']];
        }
        
        // 获取数据
        $list = Order::with(['customer'])
            ->where($map)
            ->order('id', 'desc')
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
        
        // 检查订单是否有可发货商品
        $orderIds = array_column($list['data'], 'id');

        // 获取订单商品总数量
        $orderItems = OrderDetail::where('order_id', 'in', $orderIds)
            ->where('delete_time', '=', 0)
            ->field('order_id, SUM(quantity) as total_qty')
            ->group('order_id')
            ->select()
            ->toArray();

        // 获取已发货数量
        $deliveredItems = Db::name('customer_order_delivery_item')
            ->alias('di')
            ->join('customer_order_delivery d', 'd.id = di.delivery_id')
            ->where('di.order_id', 'in', $orderIds)
            ->where('d.status', 'in', [1, 2]) // 已出库、已完成状态的发货单
            ->where('d.delete_time', '=', 0)
            ->where('di.delete_time', '=', 0)
            ->field('di.order_id, SUM(di.delivery_qty) as total_delivered')
            ->group('di.order_id')
            ->select();

        $orderItemMap = [];
        foreach ($orderItems as $item) {
            $orderItemMap[$item['order_id']] = [
                'total_qty' => $item['total_qty'],
                'total_delivered' => 0
            ];
        }

        foreach ($deliveredItems as $item) {
            if (isset($orderItemMap[$item['order_id']])) {
                $orderItemMap[$item['order_id']]['total_delivered'] = $item['total_delivered'];
            }
        }
        
        $filteredData = [];
        foreach ($list['data'] as $order) {
            if (isset($orderItemMap[$order['id']])) {
                $itemInfo = $orderItemMap[$order['id']];
                $order['has_shippable'] = ($itemInfo['total_qty'] > $itemInfo['total_delivered']);
                $order['total_qty'] = $itemInfo['total_qty'];
                $order['total_delivered'] = $itemInfo['total_delivered'];
            } else {
                $order['has_shippable'] = false;
                $order['total_qty'] = 0;
                $order['total_delivered'] = 0;
            }

            // 添加客户名称
            $order['customer_name'] = $order['customer'] ? $order['customer']['name'] : '';

            // 格式化日期
            $order['order_date'] = $order['create_time'] ? date('Y-m-d', (int)$order['create_time']) : '';
            $order['delivery_date'] = $order['delivery_date'] ? date('Y-m-d', (int)$order['delivery_date']) : '';

            // 只返回有可发货商品的订单
            if ($order['has_shippable']) {
                $filteredData[] = $order;
            }
        }

        return json(['code' => 0, 'msg' => 'ok', 'count' => count($filteredData), 'data' => $filteredData]);
    }
    
    /**
     * 添加发货指令页面
     */
    public function add()
    {
        // 生成发货单号
        $delivery_no = 'FH' . date('YmdHis') . rand(1000, 9999);
       // $this->assign('delivery_no', $delivery_no);
        
        return view();
    }
    
    /**
     * 获取订单信息
     */
    public function getOrderInfo()
    {
        $id=request()->param('id/d');
        
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        $order = Order::with(['customer'])->find($id);
        if (!$order) {
            return json(['code' => 1, 'msg' => '订单不存在']);
        }
        
        return json(['code' => 0, 'msg' => 'ok', 'data' => ['order' => $order]]);
    }
    
    /**
     * 获取订单商品
     */
    public function getOrderItems()
    {
        $orderId = request()->param('order_id/d');
        if (!$orderId) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        //inventory_lock
        
        $items = OrderDetail::alias('oi')
            ->join('product p', 'p.id = oi.product_id', 'LEFT')
            ->field('oi.*, p.title as product_name,p.material_code,p.unit,p.specs')
            ->where('oi.order_id', $orderId)
            ->order('oi.id ASC') // 按照订单明细ID排序，保持原始顺序
            ->select()
            ->toArray();
            
      
        
        // 添加显示所需的字段，保持原始数据顺序
        $sequence = 1;

        // 先收集所有主品信息
        $mainProducts = [];
        foreach ($items as $item) {
            if (!$item['parent_product_id'] || $item['parent_product_id'] == 0) {
                $mainProducts[$item['product_id']] = $item;
            }
        }

        // 为每个商品添加显示字段
        foreach ($items as &$item) {
            // 确保parent_product_id是整数类型
            $item['parent_product_id'] = (int)$item['parent_product_id'];

            if (!$item['parent_product_id'] || $item['parent_product_id'] == 0) {
                // 主品
                $item['is_child'] = false;
                $item['sequence_number'] = $sequence++;

                // 计算子品数量
                $childCount = 0;
                foreach ($items as $checkItem) {
                    if ($checkItem['parent_product_id'] == $item['product_id']) {
                        $childCount++;
                    }
                }
                $item['child_count'] = $childCount;
                $item['has_children'] = $childCount > 0;
                $item['product_number'] = $childCount > 0 ? $childCount : '';
            } else {
                // 子品
                $item['is_child'] = true;
                $item['sequence_number'] = '';
                $item['child_count'] = 0;
                $item['has_children'] = false;
                $item['product_number'] = '';
                $item['ratio'] = number_format($item['quantity'], 2);

                // 设置父商品名称
                if (isset($mainProducts[$item['parent_product_id']])) {
                    $item['parent_product_name'] = $mainProducts[$item['parent_product_id']]['product_name'];
                } else {
                    $item['parent_product_name'] = '';
                }
            }
        }

        // 获取库存信息
        foreach ($items as &$item) {
            // 查询总库存（使用新的实时库存表）
            $inventoryInfo = \think\facade\Db::name('inventory_realtime')
                ->where('product_id', $item['product_id'])
                ->field('SUM(quantity) as total_qty, SUM(available_quantity) as available_qty, SUM(locked_quantity) as locked_qty')
                ->find();

            $totalQty = $inventoryInfo['total_qty'] ?: 0; // 总库存
            $systemAvailableQty = $inventoryInfo['available_qty'] ?: 0; // 系统可用库存
            $systemLockedQty = $inventoryInfo['locked_qty'] ?: 0; // 系统锁定库存
            
            // 查询customer_order_delivery中所有与$orderId相同的ID
            $customerOrderDeliveryIds = \think\facade\Db::name('customer_order_delivery')
                ->where('order_id', $orderId)
                ->column('id');
            
                
            // 查询当前订单明细的锁定库存 - 多种策略查找
            $currentOrderLocked = 0;

            // 策略1：通过订单明细ID查找
            $lockByDetail = \think\facade\Db::name('inventory_lock')
                ->where('product_id', $item['product_id'])
                ->where('ref_type', 'customer_order_detail')
                ->where('ref_id', $item['id'])
                ->where('status', 1) // 锁定中状态
                ->sum('quantity') ?: 0;

            // 策略2：通过订单ID查找
            $lockByOrder = \think\facade\Db::name('inventory_lock')
                ->where('product_id', $item['product_id'])
                ->where('ref_type', 'customer_order')
                ->where('ref_id', $orderId)
                ->where('status', 1) // 锁定中状态
                ->sum('quantity') ?: 0;

            // 策略3：通过订单号查找
            $orderInfo = \think\facade\Db::name('customer_order')->where('id', $orderId)->find();
            $lockByOrderNo = 0;
            if ($orderInfo && $orderInfo['order_no']) {
                $lockByOrderNo = \think\facade\Db::name('inventory_lock')
                    ->where('product_id', $item['product_id'])
                    ->where('ref_no', $orderInfo['order_no'])
                    ->where('status', 1) // 锁定中状态
                    ->sum('quantity') ?: 0;
            }

            // 取最大值作为当前订单锁定数量
            $currentOrderLocked = max($lockByDetail, $lockByOrder, $lockByOrderNo);

            // 查询所有相关的锁定记录用于调试
            $allLockRecords = \think\facade\Db::name('inventory_lock')
                ->where('product_id', $item['product_id'])
                ->where('status', 1) // 只查锁定中的记录
                ->select()
                ->toArray();

            // 详细调试信息
            \think\facade\Log::info('锁定库存查询详情', [
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'],
                'order_id' => $orderId,
                'order_detail_id' => $item['id'],
                'order_no' => $orderInfo['order_no'] ?? '',
                'lock_by_detail' => $lockByDetail,
                'lock_by_order' => $lockByOrder,
                'lock_by_order_no' => $lockByOrderNo,
                'final_locked' => $currentOrderLocked,
                'all_lock_records' => $allLockRecords
            ]);

            // 查询当前订单的分配需求
            $allocationRequest = \think\facade\Db::name('inventory_allocation_request')
                ->where('product_id', $item['product_id'])
                ->where('ref_type', 'customer_order_detail')
                ->where('ref_id', $item['id'])
                ->where('status', 'in', [1, 2]) // 待分配、部分分配
                ->find();

            // 计算真正可用于当前订单的库存
            // 可用库存 = 系统可用库存 + 当前订单已锁定库存
            $realAvailableQty = $systemAvailableQty + $currentOrderLocked;

            // 注意：锁定库存已经包含了分配的库存，不需要重复计算
            // 分配记录只是用于跟踪分配状态，实际库存操作通过锁定记录完成

            // 显示真实的可用库存，不限制在订单需求数量内
            // 用户需要看到真实的库存情况来做决策
            $orderNeedQty = $item['quantity']; // 订单需求数量
            $maxAvailableQty = $realAvailableQty; // 显示真实可用库存

            // 添加到返回数据中
            $item['inventory_qty'] = $totalQty; // 总库存
            $item['system_available_qty'] = $systemAvailableQty; // 系统可用库存
            $item['system_locked_qty'] = $systemLockedQty; // 系统锁定库存
            $item['current_order_locked'] = $currentOrderLocked; // 当前订单锁定库存
            $item['available_qty'] = $maxAvailableQty; // 真正可用于当前订单的库存
            $item['allocation_status'] = $allocationRequest ? $allocationRequest['status'] : 0; // 分配状态

            // 添加调试信息
            \think\facade\Log::info('库存计算详情', [
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'],
                'order_id' => $orderId,
                'order_detail_id' => $item['id'],
                'total_qty' => $totalQty,
                'system_available_qty' => $systemAvailableQty,
                'system_locked_qty' => $systemLockedQty,
                'current_order_locked' => $currentOrderLocked,
                'real_available_qty' => $realAvailableQty,
                'order_need_qty' => $orderNeedQty,
                'final_available_qty' => $maxAvailableQty
            ]);
            
            // 查询customer_order_delivery_item本产品发货数量合计
            $deliveredQty = Db::name('customer_order_delivery_item')
                ->alias('d')
                ->join('customer_order_delivery c', 'd.delivery_id = c.id')
                ->where('d.product_id', $item['product_id'])
                ->where('d.order_id', $orderId)
                ->where('d.delete_time',0)
                ->where('c.status', 'in', [0,1,2])
                ->sum('d.delivery_qty') ?: 0;
            
            $item['delivered_qty'] = $deliveredQty;
            //$item['delivered_qty'] = 
            

            
        }
        
        // 调试输出
        \think\facade\Log::info('发货订单商品数据:', $items);

        return json(['code' => 0, 'msg' => 'ok', 'data' => ['items' => $items]]);
    }

    
    /**
     * 保存发货指令
     */
    public function save()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }
        
        $param = request()->param();
        
        // 验证数据
        try {
            $deliveryData = $param['delivery']; //发货信息
            $items = $param['items']; //发货商品
            
            // 校验必要字段
            $validate = Validate::rule([
                'order_id' => 'require|number',
                'delivery_type' => 'require|number',
                'address' => 'require',
                'contact' => 'require',
                'phone' => 'require|mobile'
            ]);
            
            if (!$validate->check($deliveryData)) {
                return json(['code' => 1, 'msg' => $validate->getError()]);
            }
            
            // 检查订单是否存在
            $order = Order::find($deliveryData['order_id']);
            if (!$order) {
                return json(['code' => 1, 'msg' => '订单不存在']);
            }

            //检查订单类型/检查付完款才可发货
            if ($order->order_type == 1) {
                if ($order->pay_status != 2 && $order->delivery_flag != 1) {
                    return json(['code' => 1, 'msg' => '警告：订单未付完货款，不能发货！！！']);
                }
            }
          
            // 开始事务
            Db::startTrans();
            
            // 创建发货指令
            $delivery = new DeliveryModel();
            $delivery->order_id = $deliveryData['order_id'];
            $delivery->order_no = $deliveryData['order_no'];
            $delivery->delivery_no = $this->generateOrderNo();
            $delivery->customer_id = $deliveryData['customer_id'];
            $delivery->customer_name = $deliveryData['customer_name'];
            $delivery->address = $deliveryData['address'];
            $delivery->contact = $deliveryData['contact'];
            $delivery->phone = $deliveryData['phone'];
            $delivery->delivery_type = $deliveryData['delivery_type'];
            $delivery->logistics_id = isset($deliveryData['logistics_id']) ? $deliveryData['logistics_id'] : 0;
            $delivery->expect_time = $deliveryData['expect_time'];
            $delivery->remark = isset($deliveryData['remark']) ? $deliveryData['remark'] : '';
            $delivery->status = 0; // 待处理
            $delivery->creator_id = $this->uid;
            $delivery->save();
            
            // 构建出库单数据
            $outboundItems = [];
            
            // 处理每个发货商品
            foreach ($items as $item) {
                // 获取订单商品 
                $orderItem = OrderDetail::alias('oi')
                    ->join('product p', 'p.id = oi.product_id', 'LEFT')
                    ->field('oi.*, p.title as product_name,p.unit,p.specs,p.material_code')
                    ->where('oi.id', $item['order_item_id'])
                    ->find();
                
                if (!$orderItem) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '订单商品不存在']);
                }
                
                // 计算已发货数量（排除已取消的发货单）
                $deliveryQuery = DeliveryItem::alias('di')
                    ->join('customer_order_delivery d', 'd.id = di.delivery_id', 'LEFT')
                    ->where('di.order_item_id', $item['order_item_id'])
                    ->where('d.status', '<>', 3); // 排除已取消的发货单

                // 记录查询SQL用于调试
                $sql = $deliveryQuery->fetchSql(true)->sum('di.delivery_qty');
                \think\facade\Log::info('已发货数量查询SQL', ['sql' => $sql]);

                $totalDeliveredQty = $deliveryQuery->sum('di.delivery_qty') ?: 0;

                // 确保数据类型正确
                $orderQuantity = floatval($orderItem['quantity']);
                $totalDeliveredQty = floatval($totalDeliveredQty);
                $currentDeliveryQty = floatval($item['delivery_qty']);
                $remainQty = $orderQuantity - $totalDeliveredQty;

                // 添加调试日志
                \think\facade\Log::info('发货数量验证', [
                    'product_name' => $orderItem['product_name'],
                    'material_code' => $orderItem['material_code'],
                    'order_quantity' => $orderQuantity,
                    'total_delivered' => $totalDeliveredQty,
                    'remain_qty' => $remainQty,
                    'current_delivery_qty' => $currentDeliveryQty,
                    'order_item_id' => $item['order_item_id']
                ]);

                if ($currentDeliveryQty > $remainQty) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' =>'警告：'. $orderItem['product_name'] .'-' . $orderItem['material_code'] . ' 【发货数量不能大于可发货数量，订单数量：'.$orderQuantity.'，已发货：'.$totalDeliveredQty.'，可发货：'.$remainQty.'】']);
                }
                
                // 验证库存是否足够（使用新的实时库存表）
                $inventoryQty = \think\facade\Db::name('inventory_realtime')
                    ->where('product_id', $orderItem['product_id'])
                    ->sum('available_quantity') ?: 0;

                if ($item['delivery_qty'] > $inventoryQty) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => '警告：'. $orderItem['product_name'] .'-' . $orderItem['material_code'] . ' 【库存不足，当前库存：'.$inventoryQty.'，发货数量：'.$item['delivery_qty'].'】']);
                }
                
                // 如果本次发货数量为0就不写入发货商品明细
                if ($item['delivery_qty'] == 0) {
                    continue;
                }

                // 创建发货商品
                $deliveryItem = new DeliveryItem();
                $deliveryItem->delivery_id = $delivery->id;
                $deliveryItem->order_id = $delivery->order_id;
                $deliveryItem->order_item_id = $item['order_item_id'];
                $deliveryItem->product_id = $orderItem['product_id'];
                $deliveryItem->product_name = $orderItem['product_name'];
                $deliveryItem->sku_id = $orderItem['sku_id'];
                $deliveryItem->sku_name = $orderItem['sku_name'] ?? '';
                $deliveryItem->unit_name = $orderItem['unit_name'] ?? '';
                $deliveryItem->order_qty = $orderItem['quantity'];
                $deliveryItem->delivery_qty = $item['delivery_qty'];
                $deliveryItem->remark = $item['remark'] ?? '';
                $deliveryItem->save();

                // 将商品添加到出库单数据中
                $outboundItems[] = [
                    'product_id' => $orderItem['product_id'],
                    'product_name' => $orderItem['product_name'],
                    'product_code' => $orderItem['material_code'],
                    'specification' => $orderItem['specs'],
                    'unit' => $orderItem['unit'],
                    'quantity' => $item['delivery_qty'],
                    'unit_price' => $orderItem['price'] ?? 0.00,
                    'total_amount' => ($orderItem['price'] ?? 0.00) * $item['delivery_qty'],
                    'notes' => $item['remark'] ?? ''
                ];
            }
            
            // 参照领料单模式，直接创建出库单
            if (!empty($outboundItems)) {
                $outboundResult = $this->createOutboundFromDelivery($delivery->id, $delivery->delivery_no, $outboundItems);
                if (!$outboundResult['success']) {
                    Db::rollback();
                    return json(['code' => 1, 'msg' => $outboundResult['message']]);
                }
            }
            
            // 添加发货日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '创建发货指令并生成销售出库单';
            $log->operator_id = $this->uid;
            $log->operator_name = '';
            $log->save();
            
            // 提交事务
            Db::commit();
            return json(['code' => 0, 'msg' => '发货指令创建成功，已生成销售出库单', 'data' => ['delivery_id' => $delivery->id]]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage(),'data' => []]);
        }
    }
    
    /**
     * 查看发货指令
     */
    public function view()
    {
        $id = request()->param('id/d');
        if (!$id) {
            $this->error('参数错误');
        }
        
        // 获取发货指令
        $delivery = DeliveryModel::withTrashed()->find($id);
        if (!$delivery) {
            $this->error('发货指令不存在');
        }

        // 获取出库状态信息
        $outboundInfo = $this->getOutboundStatusInfo($id);

        // 根据出库状态更新发货状态显示
        $delivery = $delivery->toArray();
        $delivery = array_merge($delivery, $outboundInfo);

        // 根据出库状态确定发货状态
        $delivery['actual_status'] = $this->getDeliveryStatusByOutbound($outboundInfo['outbound_status']);
        $delivery['actual_status_text'] = $this->getDeliveryStatusText($delivery['actual_status']);
        $delivery['actual_status_class'] = $this->getDeliveryStatusClass($delivery['actual_status']);

        // 获取发货商品
        $items = DeliveryItem::withTrashed()->with('product')->where('delivery_id', $id)->select();
        // 获取发货日志
        $logs = DeliveryLog::withTrashed()->where('delivery_id', $id)
            ->order('id', 'desc')
            ->select();

        return View::fetch('view', [
            'delivery' => $delivery,
            'items'    => $items,
            'logs'     => $logs
        ]);
        
        
    }
    
    /**
     * 开始处理发货指令
     */
    public function process()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        $id = request()->param('id/d');
        if (!$id) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取发货指令
        $delivery = DeliveryModel::find($id);
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货指令不存在']);
        }
        
        // 检查状态
        if ($delivery->status != 0) {
            return json(['code' => 1, 'msg' => '发货指令状态不正确']);
        }
        
        // 开始事务
        Db::startTrans();
        try {
            // 更新状态
            $delivery->status = 1; // 处理中
            $delivery->handler_id = session('tc_admin');
            $delivery->handler_name = session('tc_admin');
            $delivery->handle_time = date('Y-m-d H:i:s');
            $delivery->save();
            
            // 添加日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '开始处理发货指令';
            $log->operator_id = session('tc_admin');
            $log->operator_name = session('tc_admin');
            $log->save();
            
            Db::commit();
            return json(['code' => 0, 'msg' => '操作成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }
    
    /**
     * 完成发货
     */
    public function complete()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误']);
        }

        $param = request()->param();
        $id = $param['id'] ?? '';
        $logistics_no = $param['logistics_no'] ?? '';
        $delivery_time = $param['delivery_time'] ?? '';

        if (!$id) {
            return json(['code' => 1, 'msg' => '发货单ID不能为空']);
        }

        if (!$logistics_no) {
            return json(['code' => 1, 'msg' => '物流单号不能为空']);
        }

        if (!$delivery_time) {
            return json(['code' => 1, 'msg' => '发货日期不能为空']);
        }

        // 获取发货单
        $delivery = Db::name('customer_order_delivery')->where('id', $id)->find();
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货单不存在']);
        }

        // 开始事务
        Db::startTrans();
        try {
            // 1. 更新发货单状态、物流单号和发货日期
            Db::name('customer_order_delivery')
                ->where('id', $id)
                ->update([
                    'status' => 2, // 已完成
                    'logistics_no' => $logistics_no,
                    'delivery_time' => $delivery_time,
                    'handler_id' => $this->uid,
                    'handler_name' => session('admin.nickname'),
                    'handle_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);

            // 2. 检查订单是否全部发货完成
            $this->checkAndUpdateOrderStatus($delivery['order_id']);

            Db::commit();
            return json(['code' => 0, 'msg' => '发货完成']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查并更新订单状态
     * @param int $orderId 订单ID
     */
    private function checkAndUpdateOrderStatus($orderId)
    {
        // 获取订单所有商品的总数量
        $orderTotalQty = Db::name('customer_order_detail')
            ->where('order_id', $orderId)
            ->sum('quantity');

        // 获取所有已完成发货的数量
        $deliveredTotalQty = Db::name('customer_order_delivery_item')
            ->alias('di')
            ->join('customer_order_delivery d', 'di.delivery_id = d.id')
            ->where('di.order_id', $orderId)
            ->where('d.status', 2) // 只统计已完成的发货
            ->sum('di.delivery_qty');

        // 如果已发货数量 >= 订单总数量，则订单完成发货
        if ($deliveredTotalQty >= $orderTotalQty) {
            Db::name('customer_order')
                ->where('id', $orderId)
                ->update([
                    'status' => 2, // 已发货
                    'update_time' => date('Y-m-d H:i:s')
                ]);
        }
    }
    
    /**
     * 取消发货
     */
    public function cancel()
    {
        if (!request()->isPost()) {
            return json(['code' => 1, 'msg' => '请求方式错误', 'data' => []]);
        }
        
        $id = request()->param('id/d');
        $cancel_reason = request()->param('reason', ''); // 前端发送的参数名是reason

        if (!$id) {
            return json(['code' => 1, 'msg' => '发货单ID不能为空', 'data' => []]);
        }

        if (empty($cancel_reason)) {
            return json(['code' => 1, 'msg' => '取消原因不能为空', 'data' => []]);
        }
        
        // 获取发货指令 
        $delivery = DeliveryModel::find($id);
        if (!$delivery) {
            return json(['code' => 1, 'msg' => '发货指令不存在', 'data' => []]);
        }
        
        // 获取关联的出库单信息，检查是否可以撤销
        $outboundInfo = $this->getOutboundStatusInfo($id);

        // 检查出库状态，如果已经开始出库则不允许撤销
        if ($outboundInfo['outbound_status'] == 3 || $outboundInfo['outbound_status'] == 4) {
            return json(['code' => 1, 'msg' => '仓库已开始出库，不允许撤销发货', 'data' => []]);
        }

        // 检查发货状态
        if ($delivery->status >= 2) {
            return json(['code' => 1, 'msg' => '发货指令状态不正确', 'data' => []]);
        }

        // 开始事务
        Db::startTrans();
        try {
            // 更新发货单状态
            $delivery->status = 3; // 已取消
            $delivery->cancel_reason = $cancel_reason;
            $delivery->delete_time = time();
            $delivery->save();

            // 添加日志
            $log = new DeliveryLog();
            $log->delivery_id = $delivery->id;
            $log->content = '取消发货，原因：' . $cancel_reason;
            $log->operator_id = $this->uid;
            $log->save();

            // 删除关联的出库单（如果存在）
            if ($outboundInfo['outbound_id']) {
                // 删除出库单明细
                \think\facade\Db::name('outbound_detail')
                    ->where('outbound_id', $outboundInfo['outbound_id'])
                    ->delete();

                // 删除出库单主表
                \think\facade\Db::name('outbound')
                    ->where('id', $outboundInfo['outbound_id'])
                    ->delete();

                \think\facade\Log::info('撤销发货时删除出库单', [
                    'delivery_id' => $id,
                    'outbound_id' => $outboundInfo['outbound_id'],
                    'outbound_no' => $outboundInfo['outbound_no']
                ]);
            }

    
        // 解锁库存 - 基于销售订单的统一管理
        \think\facade\Log::info('开始解锁库存', ['delivery_id' => $delivery->id, 'order_id' => $delivery->order_id]);

        // 查找与此发货单相关的所有锁定记录（包括预锁定转使用和额外锁定）
        $lockRecords = \think\facade\Db::name('inventory_lock')
            ->where('ref_type', 'customer_order')
            ->where('ref_id', $delivery->order_id)
            ->where('status', 'in', [1, 2]) // 锁定中或已使用
            ->where(function($query) use ($delivery) {
                $query->where('notes', 'like', '%发货单自动锁定：' . $delivery->id . '%')
                      ->whereOr('notes', 'like', '%发货单使用:' . $delivery->delivery_no . '%')
                      ->whereOr('notes', 'like', '%发货单额外锁定:' . $delivery->delivery_no . '%');
            })
            ->select();

        \think\facade\Log::info('找到锁定记录', [
            'delivery_id' => $delivery->id,
            'delivery_no' => $delivery->delivery_no,
            'lock_count' => count($lockRecords),
            'lock_records' => $lockRecords
        ]);

        if (empty($lockRecords)) {
            \think\facade\Log::warning('未找到需要解锁的库存记录', [
                'delivery_id' => $delivery->id,
                'delivery_no' => $delivery->delivery_no
            ]);
        }

        foreach ($lockRecords as $lockRecord) {
            try {
                if ($lockRecord['status'] == 2) {
                    // 已使用的记录：直接释放
                    $updateResult = \think\facade\Db::name('inventory_lock')
                        ->where('id', $lockRecord['id'])
                        ->update([
                            'status' => 3, // 已释放
                            'notes' => $lockRecord['notes'] . '; 发货单取消释放',
                            'update_time' => time()
                        ]);
                } else {
                    // 锁定中的记录：恢复为预锁定状态
                    $updateResult = \think\facade\Db::name('inventory_lock')
                        ->where('id', $lockRecord['id'])
                        ->update([
                            'status' => 1, // 恢复预锁定
                            'ref_no' => str_replace('/发货单:' . $delivery->delivery_no, '', $lockRecord['ref_no']),
                            'notes' => str_replace('; 发货单使用:' . $delivery->delivery_no . ',数量:' . $lockRecord['quantity'], '', $lockRecord['notes']),
                            'update_time' => time()
                        ]);
                }

                // 更新实时库存表，释放锁定数量
                $inventoryUpdateResult = \think\facade\Db::name('inventory_realtime')
                    ->where('product_id', $lockRecord['product_id'])
                    ->where('warehouse_id', $lockRecord['warehouse_id'])
                    ->dec('locked_quantity', $lockRecord['quantity'])
                    ->inc('available_quantity', $lockRecord['quantity'])
                    ->update(['update_time' => time()]);

                \think\facade\Log::info('解锁库存成功', [
                    'delivery_id' => $delivery->id,
                    'lock_id' => $lockRecord['id'],
                    'product_id' => $lockRecord['product_id'],
                    'warehouse_id' => $lockRecord['warehouse_id'],
                    'quantity' => $lockRecord['quantity'],
                    'original_status' => $lockRecord['status'],
                    'lock_update_result' => $updateResult,
                    'inventory_update_result' => $inventoryUpdateResult
                ]);

            } catch (\Exception $e) {
                // 记录错误日志
                \think\facade\Log::error('解锁库存失败', [
                    'delivery_id' => $delivery->id,
                    'lock_record' => $lockRecord,
                    'error' => $e->getMessage()
                ]);
            }
        }
    //库存解锁完成
            



            Db::commit();
            return json(['code' => 0, 'msg' => '操作成功', 'data' => []]);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '系统错误：' . $e->getMessage(), 'data' => []]);
        }
    }

     /**
     * 生成订单号
     */
 

     private function generateOrderNo()
     {
         $prefix = 'FH' . date('Ymd');
         $maxRetries = 3; // 最大重试次数
         $retryCount = 0;
         
         while ($retryCount < $maxRetries) {
             // 开启事务
             Db::startTrans();
             try {
                 // 使用FOR UPDATE锁定查询结果
                 $maxNumber = Db::name('customer_order_delivery')
                     ->where('delivery_no', 'like', $prefix . '%')
                     ->order('id', 'desc')
                     ->lock(true)
                     ->value('delivery_no');
                 
                 if ($maxNumber) {
                     // 提取最后4位数字并加1
                     $lastNumber = intval(substr($maxNumber, -4));
                     $newNumber = $lastNumber + 1;
                 } else {
                     // 如果没有记录，从0001开始
                     $newNumber = 1;
                 }
                 
                 // 格式化为4位数字，不足补0
                 $newPlanNo = $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
                 
                 // 直接尝试插入一条测试记录
                 Db::name('customer_order_delivery')->insert([
                     'delivery_no' => $newPlanNo,
                     'status' => -1, // 使用特殊状态标记为临时记录
                     'create_time' => time()
                 ]);
                 
                 // 如果插入成功，删除测试记录并提交事务
                 Db::name('customer_order_delivery')->where('delivery_no', $newPlanNo)->where('status', -1)->delete();
                 Db::commit();
                 
                 return $newPlanNo;
             } catch (\Exception $e) {
                 Db::rollback();
                 $retryCount++;
                 
                 if ($retryCount >= $maxRetries) {
                     throw new \Exception('生成编号失败，请重试');
                 }
                 
                 // 短暂延迟后重试
                 usleep(100000); // 延迟100毫秒
             }
         }
         
         throw new \Exception('生成编号失败，请重试');
     }

    /**
     * 从发货单创建出库单（参照领料单模式）
     * @param int $deliveryId 发货单ID
     * @param string $deliveryNo 发货单号
     * @param array $outboundItems 出库商品数据
     * @return array 创建结果
     */
    private function createOutboundFromDelivery($deliveryId, $deliveryNo, $outboundItems)
    {
        try {
            // 获取发货单和订单信息
            $delivery = \think\facade\Db::name('customer_order_delivery')->where('id', $deliveryId)->find();
            if (!$delivery) {
                throw new \Exception('发货单不存在');
            }

            // 获取关联的销售订单信息
            $order = \think\facade\Db::name('customer_order')->where('id', $delivery['order_id'])->find();
            if (!$order) {
                throw new \Exception('关联的销售订单不存在');
            }

            // 检查并创建库存锁定
            $this->checkAndCreateInventoryLocks($order['id'], $deliveryId, $outboundItems);

            // 生成出库单号
            $outboundNo = $this->generateOutboundNo();

            // 计算总数量和总金额
            $totalQuantity = array_sum(array_column($outboundItems, 'quantity'));
            $totalAmount = array_sum(array_column($outboundItems, 'total_amount'));

            // 创建出库单主表
            $outboundData = [
                'outbound_no' => $outboundNo,
                'outbound_type' => 'sales', // 销售出库
                'ref_type' => 'customer_order',
                'ref_id' => $order['id'], // 关联销售订单ID
                'ref_no' => $order['order_no'] . '/' . $deliveryNo, // 销售订单号/发货单号
                'customer_id' => $order['customer_id'], // 客户ID
                'warehouse_id' => 0, // 由仓库人员决定具体仓库
                'department_id' => 0,
                'outbound_date' => date('Y-m-d'),
                'total_quantity' => $totalQuantity,
                'total_amount' => $totalAmount,
                'status' => 2, // 已审核状态，可以直接出库
                'priority' => 2, // 中等优先级
                'notes' => '销售发货出库，发货单号：' . $deliveryNo,
                'created_by' => $this->uid,
                'create_time' => time(),
                'update_time' => time()
            ];

            $outboundId = \think\facade\Db::name('outbound')->insertGetId($outboundData);

            // 创建出库单明细
            foreach ($outboundItems as $item) {
                $detailData = [
                    'outbound_id' => $outboundId,
                    'ref_detail_id' => 0,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_code' => $item['product_code'],
                    'specification' => $item['specification'],
                    'unit' => $item['unit'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_amount' => $item['total_amount'],
                    'batch_no' => '',
                    'location' => '',
                    'quality_status' => 1, // 合格
                    'actual_quantity' => 0.00, // 实际出库数量，出库时填写
                    'shortage_quantity' => 0.00, // 缺货数量
                    'notes' => $item['notes'],
                    'create_time' => time(),
                    'update_time' => time()
                ];

                \think\facade\Db::name('outbound_detail')->insert($detailData);
            }

            \think\facade\Log::info('创建销售发货出库单', [
                'delivery_id' => $deliveryId,
                'delivery_no' => $deliveryNo,
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
                'customer_id' => $order['customer_id'],
                'outbound_id' => $outboundId,
                'outbound_no' => $outboundNo,
                'total_quantity' => $totalQuantity
            ]);

            return [
                'success' => true,
                'outbound_id' => $outboundId,
                'outbound_no' => $outboundNo
            ];

        } catch (\Exception $e) {
            \think\facade\Log::error('创建发货出库单失败', [
                'delivery_id' => $deliveryId,
                'delivery_no' => $deliveryNo,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '创建出库单失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成出库单号（参照领料单模式）
     */
    private function generateOutboundNo()
    {
        $prefix = 'CK';
        $date = date('Ymd');
        $maxRetries = 10;

        for ($i = 0; $i < $maxRetries; $i++) {
            // 查询当天最大的序号
            $maxNo = \think\facade\Db::name('outbound')
                ->where('outbound_no', 'like', $prefix . $date . '%')
                ->max('outbound_no');

            if ($maxNo) {
                $sequence = intval(substr($maxNo, -4)) + 1;
            } else {
                $sequence = 1;
            }

            $outboundNo = $prefix . $date . str_pad((string)$sequence, 4, '0', STR_PAD_LEFT);

            // 检查是否已存在
            $exists = \think\facade\Db::name('outbound')
                ->where('outbound_no', $outboundNo)
                ->count();

            if (!$exists) {
                return $outboundNo;
            }

            // 如果存在，等待后重试
            usleep(10000); // 等待10毫秒
        }

        // 如果重试失败，使用时间戳后缀
        return $prefix . $date . substr((string)time(), -4);
    }

    /**
     * 检查并创建库存锁定
     * @param int $orderId 订单ID
     * @param int $deliveryId 发货单ID
     * @param array $outboundItems 出库商品数据
     * @throws \Exception
     */
    private function checkAndCreateInventoryLocks($orderId, $deliveryId, $outboundItems)
    {
        foreach ($outboundItems as $item) {
            // 检查是否已有该产品的库存锁定
            $existingLock = \think\facade\Db::name('inventory_lock')
                ->where('ref_type', 'customer_order')
                ->where('ref_id', $orderId)
                ->where('product_id', $item['product_id'])
                ->where('status', 1) // 锁定中状态
                ->sum('quantity') ?: 0;

            // 如果已锁定数量不足，需要补充锁定
            $needLockQty = $item['quantity'] - $existingLock;

            if ($needLockQty > 0) {
                // 检查可用库存是否充足
                $availableQty = \think\facade\Db::name('inventory_realtime')
                    ->where('product_id', $item['product_id'])
                    ->sum('available_quantity') ?: 0;

                if ($needLockQty > $availableQty) {
                    throw new \Exception($item['product_name'] . ' 库存不足，无法锁定。需要锁定：' . $needLockQty . '，可用库存：' . $availableQty);
                }

                // 创建库存锁定记录
                $this->createInventoryLock($orderId, $deliveryId, $item, $needLockQty);
            }
        }
    }

    /**
     * 创建库存锁定记录
     * @param int $orderId 订单ID
     * @param int $deliveryId 发货单ID
     * @param array $item 商品信息
     * @param float $quantity 锁定数量
     * @throws \Exception
     */
    private function createInventoryLock($orderId, $deliveryId, $item, $quantity)
    {
        try {
            // 获取订单信息和发货单号
            $order = \think\facade\Db::name('customer_order')->where('id', $orderId)->find();
            $deliveryNo = \think\facade\Db::name('customer_order_delivery')->where('id', $deliveryId)->value('delivery_no');

            \think\facade\Log::info('开始库存锁定处理', [
                'order_id' => $orderId,
                'delivery_id' => $deliveryId,
                'delivery_no' => $deliveryNo,
                'product_id' => $item['product_id'],
                'quantity' => $quantity
            ]);

            // 1. 查找销售订单的预锁定记录
            $orderLocks = \think\facade\Db::name('inventory_lock')
                ->where('ref_type', 'customer_order')
                ->where('ref_id', $orderId)
                ->where('product_id', $item['product_id'])
                ->where('status', 1) // 预锁定状态
                ->order('id ASC')
                ->select();

            \think\facade\Log::info('查找订单预锁定记录', [
                'order_id' => $orderId,
                'product_id' => $item['product_id'],
                'delivery_quantity' => $quantity,
                'found_locks' => count($orderLocks),
                'lock_details' => $orderLocks
            ]);

            $remainingQuantity = $quantity;

            // 2. 优先使用订单预锁定的库存
            foreach ($orderLocks as $lock) {
                if ($remainingQuantity <= 0) break;

                $useQuantity = min($remainingQuantity, $lock['quantity']);

                if ($useQuantity == $lock['quantity']) {
                    // 完全使用这个锁定记录
                    \think\facade\Db::name('inventory_lock')
                        ->where('id', $lock['id'])
                        ->update([
                            'status' => 2, // 已使用
                            'ref_no' => $lock['ref_no'] . '/发货单:' . $deliveryNo,
                            'notes' => $lock['notes'] . '; 发货单使用:' . $deliveryNo . ',数量:' . $useQuantity,
                            'update_time' => time()
                        ]);
                } else {
                    // 部分使用，需要拆分锁定记录
                    // 1. 更新原记录为剩余数量
                    \think\facade\Db::name('inventory_lock')
                        ->where('id', $lock['id'])
                        ->update([
                            'quantity' => $lock['quantity'] - $useQuantity,
                            'update_time' => time()
                        ]);

                    // 2. 创建新记录为使用数量
                    $usedLockData = [
                        'product_id' => $lock['product_id'],
                        'warehouse_id' => $lock['warehouse_id'],
                        'quantity' => $useQuantity,
                        'ref_type' => 'customer_order',
                        'ref_id' => $orderId,
                        'ref_no' => $lock['ref_no'] . '/发货单:' . $deliveryNo,
                        'status' => 2, // 已使用
                        'notes' => '发货单使用:' . $deliveryNo . ',数量:' . $useQuantity,
                        'created_by' => $this->uid,
                        'create_time' => time(),
                        'update_time' => time()
                    ];
                    \think\facade\Db::name('inventory_lock')->insert($usedLockData);
                }

                $remainingQuantity -= $useQuantity;

                \think\facade\Log::info('使用订单预锁定库存', [
                    'lock_id' => $lock['id'],
                    'product_id' => $item['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'use_quantity' => $useQuantity,
                    'remaining_quantity' => $remainingQuantity
                ]);
            }

            // 3. 如果预锁定不够，需要额外锁定库存
            if ($remainingQuantity > 0) {
                \think\facade\Log::warning('预锁定库存不足，需要额外锁定', [
                    'product_id' => $item['product_id'],
                    'remaining_quantity' => $remainingQuantity
                ]);

                // 查询可用库存进行额外锁定
                $availableStocks = \think\facade\Db::name('inventory_realtime')
                    ->where('product_id', $item['product_id'])
                    ->where('available_quantity', '>', 0)
                    ->order('available_quantity desc')
                    ->select();

                foreach ($availableStocks as $stock) {
                    if ($remainingQuantity <= 0) break;

                    $lockQuantity = min($remainingQuantity, $stock['available_quantity']);

                    // 创建额外的库存锁定记录
                    $lockData = [
                        'product_id' => $item['product_id'],
                        'warehouse_id' => $stock['warehouse_id'],
                        'quantity' => $lockQuantity,
                        'ref_type' => 'customer_order',
                        'ref_id' => $orderId,
                        'ref_no' => ($order['order_no'] ?? '') . '/发货单:' . $deliveryNo,
                        'status' => 2, // 直接标记为已使用
                        'notes' => '发货单额外锁定:' . $deliveryNo . ',数量:' . $lockQuantity,
                        'created_by' => $this->uid,
                        'create_time' => time(),
                        'update_time' => time()
                    ];

                    $lockId = \think\facade\Db::name('inventory_lock')->insertGetId($lockData);

                    // 更新实时库存
                    \think\facade\Db::name('inventory_realtime')
                        ->where('id', $stock['id'])
                        ->dec('available_quantity', $lockQuantity)
                        ->inc('locked_quantity', $lockQuantity)
                        ->update();

                    $remainingQuantity -= $lockQuantity;

                    \think\facade\Log::info('创建额外库存锁定', [
                        'lock_id' => $lockId,
                        'product_id' => $item['product_id'],
                        'warehouse_id' => $stock['warehouse_id'],
                        'quantity' => $lockQuantity
                    ]);
                }
            }

            if ($remainingQuantity > 0) {
                throw new \Exception($item['product_name'] . ' 库存不足，无法完成发货，缺少数量：' . $remainingQuantity);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('创建库存锁定失败', [
                'order_id' => $orderId,
                'delivery_id' => $deliveryId,
                'product_id' => $item['product_id'],
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}