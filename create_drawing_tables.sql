-- 创建图纸表
CREATE TABLE IF NOT EXISTS `oa_drawing` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `drawing_no` varchar(50) NOT NULL DEFAULT '' COMMENT '图纸编号',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '工序名称',
  `product_name` varchar(100) NOT NULL DEFAULT '' COMMENT '产品名称',
  `material_name` varchar(100) NOT NULL DEFAULT '' COMMENT '物料名称',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '分类ID',
  `file_path` varchar(500) NOT NULL DEFAULT '' COMMENT '文件路径',
  `file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件名称',
  `file_size` int(11) NOT NULL DEFAULT '0' COMMENT '文件大小(字节)',
  `file_type` varchar(50) NOT NULL DEFAULT '' COMMENT '文件类型',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_drawing_no` (`drawing_no`),
  KEY `idx_name` (`name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图纸表';

-- 创建图纸分类表
CREATE TABLE IF NOT EXISTS `oa_drawing_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级ID',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` text COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图纸分类表';

-- 插入示例分类数据
INSERT INTO `oa_drawing_category` (`name`, `parent_id`, `sort`, `create_time`, `update_time`) VALUES
('折弯', 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('铜接', 0, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('冲压', 0, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('涂覆', 0, 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('焊接', 0, 5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('产品分类', 0, 6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('s-18置物架s6', 6, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('测试-1', 6, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('test', 6, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('304H', 6, 4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('八字花铜螺母', 0, 7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('床身', 11, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('胶链', 11, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('113-0400000044', 11, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入示例图纸数据
INSERT INTO `oa_drawing` (`drawing_no`, `name`, `product_name`, `material_name`, `category_id`, `create_time`, `update_time`) VALUES
('TZ202412280001', '折弯', '方通架', '-', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 为生产订单表添加图纸关联字段（如果不存在）
ALTER TABLE `oa_produce_order` 
ADD COLUMN `drawing_id` int(11) NOT NULL DEFAULT '0' COMMENT '图纸ID';

-- 添加索引
ALTER TABLE `oa_produce_order` 
ADD KEY `idx_drawing_id` (`drawing_id`);