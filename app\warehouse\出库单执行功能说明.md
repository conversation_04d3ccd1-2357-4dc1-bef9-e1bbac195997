# 出库单执行功能说明

## 功能概述
为已审核的出库单增加"执行出库"功能，让仓库人员可以查看具体的出库产品信息，确认实际出库数量，并执行真正的库存扣减操作。

## 业务流程

### 1. 出库单状态流转
```
草稿(0) → 已提交(1) → 已审核(2) → 部分出库(3) → 全部出库(4)
                                    ↓
                                已取消(5)
```

### 2. 执行出库条件
- **已审核状态(2)**：可以开始执行出库
- **部分出库状态(3)**：可以继续执行剩余出库

### 3. 操作流程
1. 在出库单列表中，已审核的出库单显示"执行出库"按钮
2. 点击按钮打开出库执行页面
3. 查看出库明细，确认库存情况
4. 输入实际出库数量
5. 确认执行出库操作
6. 系统扣减库存并更新状态

## 功能特点

### 1. 智能操作按钮
根据出库单状态显示不同的操作按钮：

```html
{{# if(d.status == 2) { }}
    <!-- 已审核状态 -->
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="execute">执行出库</a>
{{# } else if(d.status == 3) { }}
    <!-- 部分出库状态 -->
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="execute">继续出库</a>
{{# } else if(d.status == 4) { }}
    <!-- 全部出库状态 -->
    <span class="layui-text-muted">已完成</span>
{{# } }}
```

### 2. 详细的出库信息展示
- **基本信息**：出库单号、类型、日期、状态
- **业务关联**：业务单号、客户信息、优先级
- **明细信息**：产品编码、名称、规格、单位

### 3. 库存状态检查
- **计划数量**：原计划出库数量
- **已出库**：已经出库的数量
- **剩余数量**：还需要出库的数量
- **当前库存**：实时库存数量
- **库存不足提醒**：红色显示库存不足的产品

### 4. 灵活的数量控制
- **手动输入**：可以手动输入实际出库数量
- **全部出库**：一键填入所有剩余数量
- **清空数量**：一键清空所有出库数量
- **数量验证**：自动限制最大出库数量

### 5. 实时库存扣减
- **库存更新**：实时扣减库存数量
- **状态更新**：根据出库完成情况更新状态
- **日志记录**：记录详细的库存变动日志

## 技术实现

### 1. 控制器方法

#### execute() - 执行出库页面
```php
public function execute()
{
    // 获取出库单信息
    $outbound = OutboundModel::with(['customer'])->find($id);
    
    // 检查状态是否可以执行出库
    if (!in_array($outbound->status, [2, 3])) {
        $this->error('当前状态不允许执行出库');
    }
    
    // 获取出库明细和实时库存
    $details = Db::name('outbound_detail')
        ->leftJoin('product p', 'od.product_id = p.id')
        ->field('od.*, p.name as product_name, p.code as product_code')
        ->where('od.outbound_id', $id)
        ->select();
    
    // 获取每个产品的当前库存
    foreach ($details as &$detail) {
        $inventory = Db::name('inventory')
            ->where('product_id', $detail['product_id'])
            ->where('warehouse_id', $outbound->warehouse_id)
            ->find();
        $detail['current_stock'] = $inventory ? $inventory['available_quantity'] : 0;
        $detail['remaining_quantity'] = $detail['quantity'] - $detail['actual_quantity'];
    }
}
```

#### doExecute() - 执行出库操作
```php
private function doExecute()
{
    Db::startTrans();
    try {
        foreach ($details as $detail) {
            // 检查库存是否充足
            if ($inventory['available_quantity'] < $actualQuantity) {
                throw new \Exception("产品库存不足");
            }
            
            // 更新明细的实际出库数量
            Db::name('outbound_detail')->update([
                'actual_quantity' => Db::raw('actual_quantity + ' . $actualQuantity)
            ]);
            
            // 更新库存
            Db::name('inventory')->update([
                'quantity' => Db::raw('quantity - ' . $actualQuantity),
                'available_quantity' => Db::raw('available_quantity - ' . $actualQuantity)
            ]);
            
            // 记录库存变动日志
            Db::name('inventory_log')->insert([...]);
        }
        
        // 更新出库单状态
        $newStatus = ($totalActualQuantity >= $totalPlannedQuantity) ? 4 : 3;
        Db::name('outbound')->update(['status' => $newStatus]);
        
        Db::commit();
    } catch (\Exception $e) {
        Db::rollback();
        return json(['code' => 1, 'msg' => $e->getMessage()]);
    }
}
```

### 2. 前端交互

#### 数量输入验证
```javascript
$(document).on('input', '.actual-qty-input', function() {
    var value = parseFloat($(this).val()) || 0;
    var remaining = parseFloat($(this).data('remaining'));
    var stock = parseFloat($(this).data('stock'));
    var maxQty = Math.min(remaining, stock);

    if (value > maxQty) {
        $(this).val(maxQty);
        if (stock < remaining) {
            layer.msg('库存不足，最大可出库数量：' + stock);
        }
    }
});
```

#### 批量操作
```javascript
// 全部出库
window.fillAllQuantity = function() {
    $('.actual-qty-input').each(function() {
        var remaining = parseFloat($(this).data('remaining'));
        var stock = parseFloat($(this).data('stock'));
        var maxQty = Math.min(remaining, stock);
        $(this).val(maxQty);
    });
}
```

### 3. 数据表更新

#### outbound_detail表
```sql
-- 新增字段记录实际出库数量
ALTER TABLE `oa_outbound_detail` 
ADD COLUMN `actual_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '实际出库数量';
```

#### outbound表
```sql
-- 新增字段记录出库操作信息
ALTER TABLE `oa_outbound` 
ADD COLUMN `outbound_by` int(11) DEFAULT '0' COMMENT '出库操作人ID',
ADD COLUMN `outbound_time` int(11) DEFAULT '0' COMMENT '出库时间';
```

## 业务价值

### 1. 精确库存管理
- **实时扣减**：只有确认出库时才扣减库存
- **部分出库**：支持分批次出库
- **库存准确**：避免预扣库存与实际出库不符

### 2. 操作可控性
- **二次确认**：审核后还需要执行确认
- **数量灵活**：可以调整实际出库数量
- **异常处理**：库存不足时可以部分出库

### 3. 数据可追溯
- **操作记录**：记录谁在什么时候执行了出库
- **库存日志**：详细记录每次库存变动
- **状态跟踪**：清楚的状态流转记录

### 4. 用户体验
- **界面友好**：清晰的信息展示和操作提示
- **操作便捷**：支持批量操作和快捷按钮
- **实时反馈**：即时的库存检查和错误提示

## 使用场景

### 1. 正常出库
1. 审核通过的出库单
2. 仓库人员执行出库
3. 确认所有数量后一次性出库完成

### 2. 部分出库
1. 某些产品库存不足
2. 先出库有库存的产品
3. 等补货后继续出库剩余产品

### 3. 分批出库
1. 大批量出库单
2. 分多次执行出库操作
3. 逐步完成整个出库任务

通过这个执行出库功能，仓库管理变得更加精确和可控，确保了库存数据的准确性和业务操作的规范性。
