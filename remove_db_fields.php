<?php
// 临时脚本用于删除数据库字段

try {
    $pdo = new PDO('mysql:host=*************;port=3305;dbname=sem_erp_oa', 'root', 'tongchou8541');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "连接数据库成功\n";
    
    // 删除 material_validity 字段
    try {
        $pdo->exec("ALTER TABLE `oa_material_archive` DROP COLUMN `material_validity`");
        echo "成功删除 material_validity 字段\n";
    } catch (Exception $e) {
        echo "删除 material_validity 字段失败或字段不存在: " . $e->getMessage() . "\n";
    }
    
    // 删除 cost_calculation 字段
    try {
        $pdo->exec("ALTER TABLE `oa_material_archive` DROP COLUMN `cost_calculation`");
        echo "成功删除 cost_calculation 字段\n";
    } catch (Exception $e) {
        echo "删除 cost_calculation 字段失败或字段不存在: " . $e->getMessage() . "\n";
    }
    
    echo "数据库字段删除完成！\n";
    
} catch (Exception $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}