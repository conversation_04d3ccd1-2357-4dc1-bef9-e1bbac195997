<?php
declare (strict_types = 1);

namespace app\warehouse\controller;

use app\base\BaseController;
use app\warehouse\service\InventoryCheckService;
use think\facade\Request;
use think\facade\View;

/**
 * 库存盘点控制器
 */
class InventoryCheck extends BaseController
{
    protected $service;
    
    public function __construct()
    {
        parent::__construct();
        $this->service = new InventoryCheckService();
    }
    
    /**
     * 盘点单列表
     */
    public function index()
    {
        if (Request::isAjax()) {
            try {
                $params = Request::param();

                // 检查表是否存在
                $tableExists = false;
                try {
                    \think\facade\Db::query("SELECT 1 FROM oa_inventory_check LIMIT 1");
                    $tableExists = true;
                } catch (\Exception $e) {
                    // 表不存在，返回提示信息
                    return json([
                        'code' => 1,
                        'msg' => '数据表不存在，请先执行SQL初始化脚本',
                        'count' => 0,
                        'data' => []
                    ]);
                }

                if ($tableExists) {
                    $result = $this->service->getCheckList($params);

                    return json([
                        'code' => 0,
                        'msg' => '查询成功',
                        'count' => $result->total(),
                        'data' => $result->items()
                    ]);
                }

            } catch (\Exception $e) {
                return json([
                    'code' => 1,
                    'msg' => '查询失败：' . $e->getMessage(),
                    'count' => 0,
                    'data' => []
                ]);
            }
        }

        // 加载仓库列表供页面使用
        $warehouses = $this->getWarehouseList();
        View::assign('warehouses', $warehouses);

        return View::fetch('inventory_check/index');
    }

    /**
     * 测试页面
     */
    public function test()
    {
        if (Request::isAjax()) {
            return json([
                'code' => 0,
                'msg' => '测试成功',
                'count' => 2,
                'data' => [
                    [
                        'id' => 1,
                        'check_no' => 'PD20241204001',
                        'warehouse_name' => '测试仓库1',
                        'status' => 0,
                        'total_products' => 10,
                        'checked_products' => 0,
                        'creator_name' => '测试用户',
                        'create_time' => time(),
                        'notes' => '测试盘点单1'
                    ],
                    [
                        'id' => 2,
                        'check_no' => 'PD20241204002',
                        'warehouse_name' => '测试仓库2',
                        'status' => 1,
                        'total_products' => 15,
                        'checked_products' => 8,
                        'creator_name' => '测试用户',
                        'create_time' => time(),
                        'notes' => '测试盘点单2'
                    ]
                ]
            ]);
        }

        return View::fetch('inventory_check/test');
    }

    /**
     * 获取仓库列表（内部方法）
     */
    private function getWarehouseList()
    {
        try {
            $warehouses = \think\facade\Db::name('oa_warehouse')
                ->where('status', 1)
                ->field('id, name')
                ->order('id asc')
                ->select();
        } catch (\Exception $e) {
            try {
                $warehouses = \think\facade\Db::name('warehouse')
                    ->where('status', 1)
                    ->field('id, name')
                    ->order('id asc')
                    ->select();
            } catch (\Exception $e2) {
                $warehouses = [];
            }
        }

        return $warehouses;
    }
    
    /**
     * 创建盘点单
     */
    public function add()
    {
        if (Request::isPost()) {
            $params = Request::post();

            // 验证数据
            $validate = [
                'warehouse_id' => 'require|integer|gt:0',
            ];

            $this->validate($params, $validate);

            try {
                $check = $this->service->createCheck($params, $this->uid);
                return json(['code' => 0, 'msg' => '创建成功', 'data' => $check]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }

        // 加载仓库列表供页面使用
        $warehouses = $this->getWarehouseList();
        View::assign('warehouses', $warehouses);

        return View::fetch('inventory_check/add');
    }
    
    /**
     * 盘点单详情
     */
    public function detail()
    {
        $id = Request::param('id');
        
        try {
            $detail = $this->service->getCheckDetail($id);
            
            if (Request::isAjax()) {
                return json(['code' => 0, 'msg' => '查询成功', 'data' => $detail]);
            }
            
            View::assign('detail', $detail);
            return View::fetch('inventory_check/detail');
        } catch (\Exception $e) {
            if (Request::isAjax()) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
            
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 开始盘点
     */
    public function start()
    {
        if (Request::isPost()) {
            $id = Request::param('id');
            
            try {
                $check = $this->service->startCheck($id);
                return json(['code' => 0, 'msg' => '开始盘点成功', 'data' => $check]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 更新盘点明细
     */
    public function updateDetail()
    {
        if (Request::isPost()) {
            $params = Request::post();
            
            // 验证数据
            $validate = [
                'detail_id' => 'require|integer|gt:0',
                'actual_quantity' => 'require|float|egt:0',
            ];
            
            $this->validate($params, $validate);
            
            try {
                $detail = $this->service->updateCheckDetail(
                    $params['detail_id'],
                    $params['actual_quantity'],
                    $params['notes'] ?? ''
                );
                
                return json(['code' => 0, 'msg' => '更新成功', 'data' => $detail]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 完成盘点
     */
    public function complete()
    {
        if (Request::isPost()) {
            $id = Request::param('id');
            
            try {
                $check = $this->service->completeCheck($id, $this->uid);
                return json(['code' => 0, 'msg' => '完成盘点成功', 'data' => $check]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 取消盘点
     */
    public function cancel()
    {
        if (Request::isPost()) {
            $id = Request::param('id');
            
            try {
                $check = $this->service->cancelCheck($id, $this->uid);
                return json(['code' => 0, 'msg' => '取消成功', 'data' => $check]);
            } catch (\Exception $e) {
                return json(['code' => 1, 'msg' => $e->getMessage()]);
            }
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取仓库列表
     */
    public function getWarehouses()
    {
        if (Request::isAjax()) {
            try {
                $warehouses = \think\facade\Db::name('oa_warehouse')
                    ->where('status', 1)
                    ->field('id, name')
                    ->order('id asc')
                    ->select();
            } catch (\Exception $e) {
                try {
                    $warehouses = \think\facade\Db::name('warehouse')
                        ->where('status', 1)
                        ->field('id, name')
                        ->order('id asc')
                        ->select();
                } catch (\Exception $e2) {
                    return json(['code' => 1, 'msg' => '仓库表不存在或无数据']);
                }
            }
            
            return json(['code' => 0, 'data' => $warehouses]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 获取产品列表
     */
    public function getProducts()
    {
        if (Request::isAjax()) {
            $keyword = Request::param('keyword', '', 'trim');
            $warehouseId = Request::param('warehouse_id', 0);
            
            $where = [
                ['status', '=', 1],
                ['delete_time', '=', 0]
            ];
            
            if (!empty($keyword)) {
                $where[] = ['title|material_code', 'like', '%' . $keyword . '%'];
            }
            
            try {
                $products = \think\facade\Db::name('oa_product')
                    ->where($where)
                    ->field('id, title, material_code, specs, unit, purchase_price as cost_price')
                    ->limit(20)
                    ->order('id desc')
                    ->select();
            } catch (\Exception $e) {
                try {
                    $products = \think\facade\Db::name('product')
                        ->where($where)
                        ->field('id, title, material_code, specs, unit, purchase_price as cost_price')
                        ->limit(20)
                        ->order('id desc')
                        ->select();
                } catch (\Exception $e2) {
                    return json(['code' => 1, 'msg' => '产品表不存在或无数据']);
                }
            }
            
            // 获取库存信息
            if ($warehouseId > 0) {
                foreach ($products as &$product) {
                    try {
                        $inventory = \think\facade\Db::name('oa_inventory_realtime')
                            ->where([
                                ['product_id', '=', $product['id']],
                                ['warehouse_id', '=', $warehouseId]
                            ])
                            ->value('quantity');

                        $product['system_quantity'] = $inventory ?: 0;
                    } catch (\Exception $e) {
                        $product['system_quantity'] = 0;
                    }
                }
            }

            return json(['code' => 0, 'data' => $products]);
        }
        
        return json(['code' => 1, 'msg' => '请求方式错误']);
    }
    
    /**
     * 批量开始盘点
     */
    public function batchStart()
    {
        if (Request::isPost()) {
            $ids = Request::param('ids');

            if (empty($ids) || !is_array($ids)) {
                return json(['code' => 1, 'msg' => '请选择要开始的盘点单']);
            }

            $successCount = 0;
            $errorMessages = [];

            foreach ($ids as $id) {
                try {
                    $this->service->startCheck($id);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorMessages[] = "ID {$id}: " . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                $msg = "成功开始 {$successCount} 个盘点单";
                if (!empty($errorMessages)) {
                    $msg .= "，失败：" . implode('; ', $errorMessages);
                }
                return json(['code' => 0, 'msg' => $msg]);
            } else {
                return json(['code' => 1, 'msg' => '批量开始失败：' . implode('; ', $errorMessages)]);
            }
        }

        return json(['code' => 1, 'msg' => '请求方式错误']);
    }

    /**
     * 批量完成盘点
     */
    public function batchComplete()
    {
        if (Request::isPost()) {
            $ids = Request::param('ids');

            if (empty($ids) || !is_array($ids)) {
                return json(['code' => 1, 'msg' => '请选择要完成的盘点单']);
            }

            $successCount = 0;
            $errorMessages = [];

            foreach ($ids as $id) {
                try {
                    $this->service->completeCheck($id, $this->uid);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorMessages[] = "ID {$id}: " . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                $msg = "成功完成 {$successCount} 个盘点单";
                if (!empty($errorMessages)) {
                    $msg .= "，失败：" . implode('; ', $errorMessages);
                }
                return json(['code' => 0, 'msg' => $msg]);
            } else {
                return json(['code' => 1, 'msg' => '批量完成失败：' . implode('; ', $errorMessages)]);
            }
        }

        return json(['code' => 1, 'msg' => '请求方式错误']);
    }

    /**
     * 盘点统计
     */
    public function statistics()
    {
        if (Request::isAjax()) {
            $params = Request::param();

            // 这里可以添加统计逻辑
            $stats = [
                'total_checks' => 0,
                'pending_checks' => 0,
                'completed_checks' => 0,
                'total_difference_amount' => 0
            ];

            return json(['code' => 0, 'data' => $stats]);
        }

        // 加载仓库列表供页面使用
        $warehouses = $this->getWarehouseList();
        View::assign('warehouses', $warehouses);

        return View::fetch('inventory_check/statistics');
    }
}
