{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}
<form class="layui-form p-4">
	<h3 class="pb-3">供应商详情</h3>
	<table class="layui-table layui-table-form">
		<tr>
			<td class="layui-td-gray-2">供应商名称</td>
			<td>{$detail.title}</td>
			<td class="layui-td-gray-2">供应商电话</td>
			<td>{$detail.phone}</td>
		</tr>
		<tr>
			<td class="layui-td-gray-2">供应商地址</td>
			<td>{$detail.address}</td>
			<td class="layui-td-gray-2">供应商邮箱</td>
			<td>{$detail.email}</td>
		</tr>
		<tr>
			<td class="layui-td-gray" style="vertical-align:top;">供应商简介</td>
			<td colspan="3">{$detail.content}</td>
		</tr>
		{notempty name="$detail.file_ids"}
		<tr>
			<td class="layui-td-gray-2">
				<div class="layui-input-inline">相关附件</div>
			</td>
			<td colspan="3" style="line-height:inherit">
				<div class="layui-row" id="uploadBox">
					{volist name="$detail.file_array" id="vo"}
						<div class="layui-col-md4" id="uploadImg{$vo.id}">{:file_card($vo,'view')}</div>
					{/volist}
				</div>
			</td>
		</tr>
		{/notempty}
		<tr>
			<td colspan="6"><strong>联系人信息</strong><span class="layui-btn layui-btn-xs add-contact ml-3">+ 新增联系人</span></td>
		</tr>
		<tr>
			<td colspan="6"><table class="layui-hide" id="contact" lay-filter="contact"></table></td>
		</tr>
	</table>
</form>
{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
const supplier_id = '{$detail.id}';
const moduleInit = ['tool'];
function gouguInit() {
	let tool = layui.tool, table = layui.table;

	layui.pageTable = table.render({
		elem: '#contact',
		title: '联系人列表',
		cellMinWidth:80,
		url: '/contract/api/get_supplier_contact',
		where:{'supplier_id':supplier_id},
		page: true, //开启分页
		limit: 20,
		cols: [[
			{field:'id',width:80, title: 'ID号', align:'center'}
			,{field:'name',width:100,title: '联系人姓名', align:'center'}
			,{field:'status', title: '性别',width:80,align:'center',templet: function(d){
				var html='未知';
				var html1='<span class="green">男</span>';
				var html2='<span class="blue">女</span>';
				if(d.sex==1){
					return html1;
				}
				if(d.sex==2){
					return html2;
				}
				else{
					return html;
				}
			}}
			,{field:'mobile',width:100,title: '手机号码', align:'center'}
			,{field:'qq',width:100,title: 'QQ号码', align:'center'}
			,{field:'wechat',width:100,title: '微信号码', align:'center'}
			,{field:'email',title: '电子邮箱', align:'center'}
			,{field:'position',width:120,title: '担任职位', align:'center'}
			,{field:'department',width:120,title: '部门', align:'center'}
			,{fixed:'right',width:190,title: '操作', align:'center',templet: function(d){
				var html = '<div class="layui-btn-group">';
				var btn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
				var btn1='<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="set">设为首要联系人</a>';
				var btn2='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>';
				if(d.is_default==1){
					return '<span class="red" style="margin-right:10px;">首要联系人</span>'+btn;
				}
				else{
					return html+btn+btn1+btn2+'</div>';
				}				
			}}
		]]
	});
	$('.add-contact').on('click',function(){
		tool.side('/contract/supplier/contact_add?sid='+supplier_id);
	});
	
	table.on('tool(contact)', function(obj){
		var data = obj.data; //获得当前行数据
		var layEvent = obj.event;		 
		if(layEvent === 'edit'){ //编辑
			let url = '/contract/supplier/contact_add?id='+data.id;
			tool.side(url);
		}
		if(layEvent === 'set'){ //查看
			layer.confirm('确定要设置该联系人为首要联系人吗?', {icon: 3, title:'提示'}, function(index){
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						layui.pageTable.reload();
					}
				}
				tool.delete("/contract/api/set_supplier_contact",{"id":data.id},callback);
				layer.close(index);
			});
		}
		if(layEvent === 'del'){ //删除
			layer.confirm('确定要删除该联系人吗?', {icon: 3, title:'提示'}, function(index){
				let callback = function (e) {
					layer.msg(e.msg);
					if (e.code == 0) {
						layui.pageTable.reload();
					}
				}
				tool.delete('/contract/supplier/contact_del',{"id":data.id},callback);
				layer.close(index);
			});
		}
		return false;
	})
}
</script>
{/block}
<!-- /脚本 -->