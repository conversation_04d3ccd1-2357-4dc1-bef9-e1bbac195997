{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <span style="color: #FF5722;">图纸详情</span>
            <div class="layui-btn-group" style="float: right;">
                <button type="button" class="layui-btn layui-btn-sm" onclick="editDrawing({$info.id})">编辑</button>
                <button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="deleteDrawing({$info.id})">删除</button>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space20">
                <!-- 基本信息 -->
                <div class="layui-col-md8">
                    <table class="layui-table" lay-skin="nob">
                        <colgroup>
                            <col width="120">
                            <col>
                        </colgroup>
                        <tbody>
                            <tr>
                                <td><strong>图纸编号：</strong></td>
                                <td>{$info.drawing_no}</td>
                            </tr>
                            <tr>
                                <td><strong>工序名称：</strong></td>
                                <td>{$info.name}</td>
                            </tr>
                            <tr>
                                <td><strong>产品名称：</strong></td>
                                <td>{$info.product_name|default='-'}</td>
                            </tr>
                            <tr>
                                <td><strong>物料名称：</strong></td>
                                <td>{$info.material_name|default='-'}</td>
                            </tr>
                            <tr>
                                <td><strong>分类：</strong></td>
                                <td>{$info.category_name|default='未分类'}</td>
                            </tr>
                            <tr>
                                <td><strong>创建时间：</strong></td>
                                <td>{$info.create_time_format}</td>
                            </tr>
                            <tr>
                                <td><strong>更新时间：</strong></td>
                                <td>{$info.update_time_format}</td>
                            </tr>
                            {if condition="$info.remark"}
                            <tr>
                                <td><strong>备注：</strong></td>
                                <td>{$info.remark}</td>
                            </tr>
                            {/if}
                        </tbody>
                    </table>
                </div>
                
                <!-- 文件信息 -->
                <div class="layui-col-md4">
                    <div class="file-info-card">
                        <div class="file-info-header">
                            <i class="layui-icon layui-icon-file" style="font-size: 24px; color: #FF5722;"></i>
                            <span>图纸文件</span>
                        </div>
                        <div class="file-info-body">
                            {if condition="$info.file_path"}
                            <div class="file-item">
                                <div class="file-name">{$info.file_name|default='图纸文件'}</div>
                                <div class="file-size">{$info.file_size_format}</div>
                                <div class="file-type">{$info.file_type|upper}</div>
                                <div class="file-actions">
                                    <a href="{$info.file_path}" target="_blank" class="layui-btn layui-btn-primary layui-btn-sm">
                                        <i class="layui-icon layui-icon-eye"></i> 预览
                                    </a>
                                    <a href="{$info.file_path}" download class="layui-btn layui-btn-normal layui-btn-sm">
                                        <i class="layui-icon layui-icon-download-circle"></i> 下载
                                    </a>
                                </div>
                            </div>
                            {else}
                            <div class="no-file">
                                <i class="layui-icon layui-icon-file" style="font-size: 48px; color: #ccc;"></i>
                                <p>暂无图纸文件</p>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图纸预览区域 -->
            {if condition="$info.file_path && in_array(strtolower($info.file_type), ['jpg', 'jpeg', 'png', 'gif', 'bmp'])"}
            <div class="layui-card" style="margin-top: 20px;">
                <div class="layui-card-header">图纸预览</div>
                <div class="layui-card-body" style="text-align: center;">
                    <img src="{$info.file_path}" alt="图纸预览" style="max-width: 100%; max-height: 600px; border: 1px solid #e6e6e6;">
                </div>
            </div>
            {/if}
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var tool = layui.tool;
        
        // 编辑图纸
        window.editDrawing = function(id) {
            tool.side('/Produce/drawing/add?id=' + id, '编辑图纸', '800px', '600px');
        };
        
        // 删除图纸
        window.deleteDrawing = function(id) {
            layer.confirm('确定要删除这个图纸吗？', {icon: 3, title: '提示'}, function(index) {
                tool.post('/Produce/drawing/delete', {id: id}, function(res) {
                    layer.msg(res.msg);
                    if (res.code === 0) {
                        // 关闭当前页面并刷新父页面
                        if (parent.window.drawingTableIns) {
                            parent.window.drawingTableIns.reload();
                        }
                        var frameIndex = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(frameIndex);
                    }
                });
                layer.close(index);
            });
        };
    }
</script>
{/block}

{block name="style"}
<style>
.file-info-card {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    overflow: hidden;
}

.file-info-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
}

.file-info-body {
    padding: 20px;
}

.file-item {
    text-align: center;
}

.file-name {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    word-break: break-all;
}

.file-size {
    color: #666;
    margin-bottom: 5px;
}

.file-type {
    color: #FF5722;
    font-weight: bold;
    margin-bottom: 15px;
}

.file-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.no-file {
    text-align: center;
    padding: 30px 0;
    color: #999;
}

.no-file p {
    margin-top: 10px;
    font-size: 14px;
}

/* 表格样式优化 */
.layui-table tbody tr td {
    padding: 12px 15px;
    border-bottom: 1px solid #f2f2f2;
}

.layui-table tbody tr:last-child td {
    border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layui-col-md8, .layui-col-md4 {
        width: 100% !important;
        margin-bottom: 20px;
    }
    
    .file-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .file-actions .layui-btn {
        width: 120px;
    }
}
</style>
{/block}