<?php
declare (strict_types = 1);

namespace app\warehouse\model;

use think\Model;

/**
 * 库存锁定模型
 */
class InventoryLock extends Model
{
    // 设置表名
    protected $name = 'inventory_lock';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 状态常量
    const STATUS_LOCKED = 1;    // 锁定中
    const STATUS_USED = 2;      // 已使用
    const STATUS_RELEASED = 3;  // 已释放
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'product_id'   => 'int',
        'warehouse_id' => 'int',
        'quantity'     => 'float',
        'ref_type'     => 'string',
        'ref_id'       => 'int',
        'ref_no'       => 'string',
        'status'       => 'int',
        'notes'        => 'string',
        'created_by'   => 'int',
        'create_time'  => 'int',
        'update_time'  => 'int',
    ];
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\product\model\Product', 'product_id', 'id');
    }
    
    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo('app\warehouse\model\Warehouse', 'warehouse_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\user\model\User', 'created_by', 'id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : $this->status;
        $statusArray = [
            self::STATUS_LOCKED => '锁定中',
            self::STATUS_USED => '已使用',
            self::STATUS_RELEASED => '已释放'
        ];
        
        return isset($statusArray[$status]) ? $statusArray[$status] : '未知';
    }
    
    /**
     * 获取状态数组
     */
    public static function getStatusArr()
    {
        return [
            self::STATUS_LOCKED => '锁定中',
            self::STATUS_USED => '已使用',
            self::STATUS_RELEASED => '已释放'
        ];
    }

    /**
     * 获取关联类型数组
     */
    public static function getRefTypeArr()
    {
        return [
            'order' => '销售订单',
            'production' => '生产订单',
            'transfer' => '调拨单',
            'outbound' => '出库单',
            'manual' => '手动锁定',
            'other' => '其他'
        ];
    }
    
    /**
     * 创建库存锁定记录
     * 
     * @param array $data 锁定数据
     * @return InventoryLock
     */
    public static function createLock($data)
    {
        $lockData = [
            'product_id' => $data['product_id'],
            'warehouse_id' => $data['warehouse_id'],
            'quantity' => $data['quantity'],
            'ref_type' => $data['ref_type'],
            'ref_id' => $data['ref_id'],
            'ref_no' => $data['ref_no'] ?? '',
            'status' => self::STATUS_LOCKED,
            'notes' => $data['notes'] ?? '',
            'created_by' => $data['created_by'] ?? 0
        ];
        
        return self::create($lockData);
    }
    
    /**
     * 释放锁定
     * 
     * @param int $lockId 锁定记录ID
     * @return bool
     * @throws \Exception
     */
    public static function releaseLock($lockId)
    {
        $lock = self::find($lockId);
        if (!$lock) {
            throw new \Exception('锁定记录不存在');
        }
        
        if ($lock->status != self::STATUS_LOCKED) {
            throw new \Exception('锁定记录状态不正确，当前状态: ' . $lock->status_text);
        }
        
        $lock->status = self::STATUS_RELEASED;
        return $lock->save();
    }
    
    /**
     * 使用锁定库存
     * 
     * @param int $lockId 锁定记录ID
     * @return bool
     * @throws \Exception
     */
    public static function useLock($lockId)
    {
        $lock = self::find($lockId);
        if (!$lock) {
            throw new \Exception('锁定记录不存在');
        }
        
        if ($lock->status != self::STATUS_LOCKED) {
            throw new \Exception('锁定记录状态不正确，当前状态: ' . $lock->status_text);
        }
        
        $lock->status = self::STATUS_USED;
        return $lock->save();
    }
    
    /**
     * 根据关联信息获取锁定记录
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @param int $status 状态（可选）
     * @return \think\Collection
     */
    public static function getLocksByRef($refType, $refId, $status = null)
    {
        $where = [
            'ref_type' => $refType,
            'ref_id' => $refId
        ];
        
        if ($status !== null) {
            $where['status'] = $status;
        }
        
        return self::where($where)
            ->with(['product', 'warehouse'])
            ->select();
    }
    
    /**
     * 批量释放锁定
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @return int 释放的记录数
     */
    public static function batchRelease($refType, $refId)
    {
        return self::where([
            'ref_type' => $refType,
            'ref_id' => $refId,
            'status' => self::STATUS_LOCKED
        ])->update(['status' => self::STATUS_RELEASED]);
    }
    
    /**
     * 批量使用锁定库存
     * 
     * @param string $refType 关联类型
     * @param int $refId 关联ID
     * @return int 使用的记录数
     */
    public static function batchUse($refType, $refId)
    {
        return self::where([
            'ref_type' => $refType,
            'ref_id' => $refId,
            'status' => self::STATUS_LOCKED
        ])->update(['status' => self::STATUS_USED]);
    }
    
    /**
     * 获取产品在指定仓库的锁定数量
     * 
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @return float
     */
    public static function getLockedQuantity($productId, $warehouseId)
    {
        return self::where([
            'product_id' => $productId,
            'warehouse_id' => $warehouseId,
            'status' => self::STATUS_LOCKED
        ])->sum('quantity') ?: 0;
    }
    
    /**
     * 搜索器：产品ID
     */
    public function searchProductIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('product_id', $value);
        }
    }
    
    /**
     * 搜索器：仓库ID
     */
    public function searchWarehouseIdAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('warehouse_id', $value);
        }
    }
    
    /**
     * 搜索器：关联类型
     */
    public function searchRefTypeAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('ref_type', $value);
        }
    }
    
    /**
     * 搜索器：状态
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '' && $value !== null) {
            $query->where('status', $value);
        }
    }
    
    /**
     * 搜索器：关键字（产品名称、关联单号）
     */
    public function searchKeywordsAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where(function($q) use ($value) {
                $q->whereOr('ref_no', 'like', '%' . $value . '%')
                  ->whereOr('notes', 'like', '%' . $value . '%');
            });
        }
    }
}
