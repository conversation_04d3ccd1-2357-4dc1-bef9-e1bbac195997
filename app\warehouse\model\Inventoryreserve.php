<?php
namespace app\warehouse\model;

use think\Model;

/**
 * 库存预占模型
 */
class Inventoryreserve extends Model
{
    protected $name = 'oa_inventory_reserve';
    protected $pk = 'id';
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 软删除
    use \think\model\concern\SoftDelete;
    protected $deleteTime = 'delete_time';
    protected $defaultSoftDelete = 0;
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'int',
        'inventory_id'      => 'int',
        'product_id'        => 'int',
        'warehouse_id'      => 'int',
        'location_id'       => 'int',
        'batch_no'          => 'string',
        'quantity'          => 'decimal',
        'used_quantity'     => 'decimal',
        'ref_type'          => 'string',
        'ref_id'            => 'int',
        'ref_no'            => 'string',
        'receipt_id'        => 'int',
        'status'            => 'int',
        'reserve_time'      => 'int',
        'expire_time'       => 'int',
        'created_by'        => 'int',
        'create_time'       => 'int',
        'update_time'       => 'int',
        'delete_time'       => 'int'
    ];
    
    // 状态常量
    const STATUS_INVALID = 0;  // 无效
    const STATUS_VALID = 1;    // 有效
    const STATUS_USED = 2;     // 已使用
    const STATUS_EXPIRED = 3;  // 已过期
    
    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusTexts = [
            self::STATUS_INVALID => '无效',
            self::STATUS_VALID => '有效',
            self::STATUS_USED => '已使用',
            self::STATUS_EXPIRED => '已过期'
        ];
        
        return $statusTexts[$data['status']] ?? '未知';
    }
    
    /**
     * 关联库存记录
     */
    public function inventory()
    {
        return $this->belongsTo(InventoryRealtime::class, 'inventory_id', 'id');
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo('app\api\model\Product', 'product_id', 'id');
    }
    
    /**
     * 关联仓库
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id', 'id');
    }
    
    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo('app\api\model\Admin', 'created_by', 'id');
    }
    
    /**
     * 检查是否过期
     */
    public function isExpired()
    {
        return $this->expire_time > 0 && $this->expire_time < time();
    }
    
    /**
     * 获取剩余数量
     */
    public function getRemainingQuantity()
    {
        return $this->quantity - $this->used_quantity;
    }
    
    /**
     * 使用预占数量
     */
    public function useQuantity($quantity)
    {
        if ($quantity <= 0) {
            return false;
        }
        
        $remainingQty = $this->getRemainingQuantity();
        if ($quantity > $remainingQty) {
            return false;
        }
        
        $this->used_quantity += $quantity;
        
        // 如果完全使用，更新状态
        if ($this->used_quantity >= $this->quantity) {
            $this->status = self::STATUS_USED;
        }
        
        return $this->save();
    }
    
    /**
     * 释放预占数量
     */
    public function releaseQuantity($quantity)
    {
        if ($quantity <= 0) {
            return false;
        }
        
        if ($quantity > $this->used_quantity) {
            $quantity = $this->used_quantity;
        }
        
        $this->used_quantity -= $quantity;
        
        // 如果有剩余，恢复有效状态
        if ($this->used_quantity < $this->quantity && $this->status == self::STATUS_USED) {
            $this->status = self::STATUS_VALID;
        }
        
        return $this->save();
    }
    
    /**
     * 过期处理
     */
    public function expire()
    {
        $this->status = self::STATUS_EXPIRED;
        return $this->save();
    }
}
