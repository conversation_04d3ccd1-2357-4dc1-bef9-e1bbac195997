# MRP采购建议API重构文档

## 概述
将`getmrppurchase_suggestion` API接口从`material_requirement`表改为`produce_order_material_requirement`表，不再进行BOM展开，直接使用生产订单的物料需求数据。

## 修改内容

### 1. 数据源变更
**原来**：`oa_material_requirement`表（销售订单BOM展开后的物料需求）
**现在**：`oa_produce_order_material_requirement`表（生产订单物料需求）

### 2. 查询逻辑调整

#### 原查询逻辑
```php
// 获取已审核销售订单
$checkedOrderIds = Db::name('customer_order')
    ->where('check_status', 2)
    ->column('id');

// 查询BOM展开的物料需求
$query = Db::name('material_requirement')
    ->alias('mr')
    ->join('product p', 'p.id = mr.product_id')
    ->leftJoin('customer_order_detail cod', 'cod.id = mr.source_id')
    ->leftJoin('customer_order co', 'co.id = cod.order_id');
```

#### 新查询逻辑
```php
// 获取已审核生产订单
$checkedOrderIds = Db::name('produce_order')
    ->where('status', 'in', [1, 2, 3])  // 已排产、生产中、已完成
    ->column('id');

// 查询生产订单物料需求
$query = Db::name('produce_order_material_requirement')
    ->alias('pomr')
    ->join('product p', 'p.id = pomr.material_id')
    ->leftJoin('produce_order po', 'po.id = pomr.order_id')
    ->leftJoin('admin au', 'au.id = po.create_uid');
```

### 3. 字段映射调整

| 原字段 | 新字段 | 说明 |
|--------|--------|------|
| `mr.product_id` | `pomr.material_id` | 物料ID |
| `mr.quantity` | `pomr.required_quantity` | 需求数量 |
| `mr.gap` | `pomr.shortage_quantity` | 缺口数量 |
| `mr.source_type` | - | 不再需要（都是生产订单） |
| `mr.source_id` | `pomr.order_id` | 关联订单ID |
| `co.order_no` | `po.order_no` | 业务单号 |

### 4. 状态处理调整

#### 原状态逻辑
```php
$item['status_text'] = $item['status'] == 0 ? '未处理' : '已处理';
```

#### 新状态逻辑
```php
$statusMap = [
    1 => '待分配',
    2 => '部分分配', 
    3 => '完全分配',
    4 => '已锁定'
];
$item['status_text'] = $statusMap[$item['status']] ?? '未知状态';
```

### 5. 主/BOM标识调整

#### 原逻辑（基于source_type判断）
```php
if ($item['source_type'] == 'customer_order_detail') {
    if ($item['product_id'] == $item['main_product_id']) {
        $item['main_or_bom'] = '主';
    } else {
        $item['main_or_bom'] = $item['main_product_name'];
    }
}
```

#### 新逻辑（基于BOM层级）
```php
if ($item['bom_level'] == 1) {
    $item['main_or_bom'] = '直接物料';
    $item['item_type'] = '直接物料';
} else {
    $item['main_or_bom'] = '第' . $item['bom_level'] . '级物料';
    $item['item_type'] = 'BOM物料';
}
$item['main_or_bom'] .= ' (' . $item['main_product_name'] . ')';
```

## 业务逻辑变化

### 1. 数据来源
- **原来**：基于销售订单，通过BOM展开生成物料需求
- **现在**：基于生产订单，直接使用已计算好的物料需求

### 2. 过滤条件
- **原来**：`mr.gap < mr.quantity` （有缺口的物料）
- **现在**：`pomr.shortage_quantity > 0` （有缺口的物料）

### 3. 状态管理
- **原来**：简单的处理/未处理状态
- **现在**：详细的分配状态（待分配、部分分配、完全分配、已锁定）

## 优势

1. **数据准确性**：直接使用生产订单的物料需求，避免重复BOM展开
2. **性能提升**：减少复杂的关联查询和BOM计算
3. **状态清晰**：更详细的物料分配状态管理
4. **业务对齐**：与生产计划流程更好地集成

## 测试要点

- [ ] API接口正常返回数据
- [ ] 字段映射正确
- [ ] 搜索功能正常
- [ ] 分页功能正常
- [ ] 库存数据计算正确
- [ ] 状态显示正确
- [ ] 主/BOM标识正确显示
