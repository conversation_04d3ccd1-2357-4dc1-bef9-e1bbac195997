<?php
/**
 * @copyright Copyright (c) 2021 勾股工作室
 * @license https://opensource.org/licenses/Apache-2.0
 * @link https://www.gougucms.com
 */

declare (strict_types = 1);

namespace app\Produce\model;

use think\Model;

class ProcessModel extends Model
{
    protected $table = 'oa_produce_process';
    protected $pk = 'id';

    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'code'            => 'string',
        'name'            => 'string', 
        'group_id'        => 'int',
        'report_user'     => 'string',
        'standard_price'  => 'decimal',
        'efficiency'      => 'decimal',
        'pricing_method'  => 'int',
        'quantity_ratio'  => 'decimal',
        'description'     => 'text',
        'serial_number'   => 'string',
        'admin_id'        => 'int',
        'create_time'     => 'int',
        'update_time'     => 'int',
        'delete_time'     => 'int'
    ];
}