<?php
declare (strict_types = 1);

namespace app\warehouse\service;
use app\base\BaseController;
use think\facade\Db;
use think\Exception;
use app\warehouse\model\InventoryLock;
use app\warehouse\model\InventoryRealtime;
use app\warehouse\service\InventoryLockServiceNew;
use app\warehouse\service\InventoryRealtimeService;

/**
 * 库存分配与锁定统一服务25.8.5
 * 
 * 功能说明：
 * 1. 统一的库存分配与锁定接口
 * 2. 入库时的自动分配功能
 * 3. 反审时的锁定释放功能
 * 4. 基于优先级的智能分配算法
 */
class InventoryAllocationService extends BaseController
{
    /**
     * 统一的库存分配与锁定接口
     *
     * @param array $request 分配请求参数
     * @return array 分配结果
     * @throws Exception
     */
    public function allocateAndLock($request)
    {
        $productId = $request['product_id'];
        $warehouseId = $request['warehouse_id'];
        $requestQty = $request['quantity'];
        $refType = $request['ref_type'];
        $refId = $request['ref_id'];
        $refNo = $request['ref_no'] ?? '';
        $priority = $request['priority'] ?? $this->getDefaultPriority($refType);
        $notes = $request['notes'] ?? '';
        $createdBy = $request['created_by'] ?? 0;

        // 参数验证
        if ($requestQty <= 0) {
            throw new Exception('请求数量必须大于0');
        }

        // 🔧 检查重复分配需求的逻辑需要修改
        // 问题：同一个订单中同一个产品可能出现多次（不同的订单明细行）
        // 解决：不应该简单地检查产品重复，而应该允许同一产品的多次分配需求
        // 只在真正的重复操作（如重复审核）时才阻止

        // 暂时注释掉重复检查，改为在订单审核层面处理重复审核问题
        /*
        $existingRequest = Db::name('inventory_allocation_request')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', 'in', [1, 2, 3]) // 活跃状态
            ->find();

        if ($existingRequest) {
            return [
                'code' => 1,
                'status' => 'duplicate',
                'msg' => '该业务已存在分配需求，请勿重复操作',
                'existing_request_id' => $existingRequest['id']
            ];
        }
        */

        // 1. 检查可用库存
        $availableQty = $this->getAvailableStock($productId, $warehouseId);

        if ($availableQty >= $requestQty) {
            // 2. 库存充足，直接分配并锁定
            return $this->directAllocateAndLock($request);
        } else if ($availableQty > 0) {
            // 3. 库存不足但有部分库存，部分分配
            return $this->partialAllocateAndLock($request, $availableQty);
        } else {
            // 4. 无库存，创建分配需求等待
            return $this->createAllocationRequest($request);
        }
    }
    
    /**
     * 入库触发的自动分配与锁定
     *
     * @param int $productId 产品ID
     * @param int $warehouseId 仓库ID
     * @param float $inboundQty 入库数量
     * @return array 分配结果
     */
    public function autoAllocateOnInbound($productId, $warehouseId, $inboundQty)
    {
        // 1. 首先获取指定仓库的待分配需求
        $pendingRequests = $this->getPendingAllocationRequests($productId, $warehouseId);

        // 2. 如果指定仓库没有待分配需求，查找其他仓库的需求并调整仓库
        if (empty($pendingRequests)) {
            $pendingRequests = $this->getPendingAllocationRequestsAnyWarehouse($productId);

            // 调整需求的仓库为实际入库仓库
            if (!empty($pendingRequests)) {
                $this->adjustAllocationWarehouse($pendingRequests, $warehouseId);
                trace('发现跨仓库分配需求，已调整仓库：产品ID=' . $productId . ', 调整到仓库ID=' . $warehouseId . ', 需求数量=' . count($pendingRequests), 'info');
            }
        }

        if (empty($pendingRequests)) {
            return [
                'code' => 0,
                'msg' => '无待分配需求',
                'total_allocated' => 0,
                'allocation_details' => []
            ];
        }
        
        $remainingQty = $inboundQty;
        $allocationResults = [];
        
        Db::startTrans();
        try {
            // 2. 按优先级逐个分配并锁定
            foreach ($pendingRequests as $request) {
                if ($remainingQty <= 0) break;
                
                $pendingQty = $request['quantity'] - $request['allocated_quantity'];
                $allocateQty = min($remainingQty, $pendingQty);
                
                // 执行分配
                $result = $this->executeAllocation($request, $allocateQty);
                $allocationResults[] = $result;
                
                $remainingQty -= $allocateQty;
            }
            
            Db::commit();
            
            return [
                'code' => 0,
                'msg' => '自动分配完成',
                'total_allocated' => $inboundQty - $remainingQty,
                'remaining_stock' => $remainingQty,
                'allocation_details' => $allocationResults
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 反审释放锁定库存
     *
     * @param array $params 反审参数
     * @return array 释放结果
     */
    public function releaseOnReverseAudit($params)
    {
        $refType = $params['ref_type'];
        $refId = $params['ref_id'];
        $operatorId = $params['operator_id'];
        $reason = $params['reason'] ?? '反审释放';

        // 🔧 添加调试日志
        \think\facade\Log::info('开始反审释放', [
            'ref_type' => $refType,
            'ref_id' => $refId,
            'operator_id' => $operatorId,
            'reason' => $reason
        ]);

        // 1. 检查是否允许反审
        $this->validateReverseAudit($refType, $refId);

        // 2. 对于入库单反审核，使用特殊的恢复逻辑
        if ($refType === 'purchase_receipt') {
            return $this->handleReceiptReverseAudit($refId, $operatorId, $reason);
        }

        // 3. 对于其他类型，使用原有的锁定释放逻辑
        $lockRecords = $this->getActiveLockRecords($refType, $refId);

        // 🔧 添加调试日志
        \think\facade\Log::info('获取锁定记录', [
            'ref_type' => $refType,
            'ref_id' => $refId,
            'lock_count' => count($lockRecords)
        ]);

        if (empty($lockRecords)) {
            // 即使没有锁定记录，也要删除分配需求
            $this->deleteAllocationRequests($refType, $refId);
            return ['code' => 0, 'msg' => '无锁定记录，但已清理分配需求'];
        }
        
        // 3. 检查每个锁定记录的可释放性
        $releaseResults = [];
        $hasError = false;
        $totalReleasedQty = 0;
        
        Db::startTrans();
        try {
            foreach ($lockRecords as $lock) {
                $releaseResult = $this->processLockRelease($lock, $reason, $operatorId);
                $releaseResults[] = $releaseResult;
                
                if ($releaseResult['code'] !== 0) {
                    $hasError = true;
                } else {
                    $totalReleasedQty += $lock['quantity'];
                }
            }
            
            if ($hasError) {
                Db::rollback();
                return ['code' => 1, 'msg' => '部分锁定释放失败', 'details' => $releaseResults];
            }
            
            // 4. 记录反审日志
            $this->logReverseAudit($refType, $refId, $lockRecords, $reason, $operatorId);
            
            // 5. 删除对应的分配需求记录（反审=撤销，应该删除而不是取消）
            \think\facade\Log::info('开始删除分配需求', [
                'ref_type' => $refType,
                'ref_id' => $refId
            ]);
            $this->deleteAllocationRequests($refType, $refId);
            
            // 6. 释放成功后，重新触发分配（给其他等待的需求）
            // 🔧 修复：只有在有其他等待需求时才重新分配，避免无意义的分配
            $this->redistributeReleasedStock($lockRecords, $refType, $refId);
            
            Db::commit();
            
            return [
                'code' => 0, 
                'msg' => '锁定释放成功', 
                'total_released' => $totalReleasedQty,
                'details' => $releaseResults
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取可用库存数量
     */
    private function getAvailableStock($productId, $warehouseId)
    {
        $inventory = Db::name('inventory_realtime')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->field('available_quantity')
            ->find();

        return $inventory ? floatval($inventory['available_quantity']) : 0;
    }
    
    /**
     * 获取业务类型的默认优先级
     */
    private function getDefaultPriority($refType)
    {
        $priorityMap = [
            'production_order' => 90,      // 生产订单
            'customer_order' => 60,        // 客户订单
            'purchase_order' => 50,        // 采购订单
            'transfer' => 50,              // 调拨单
            'quality_check' => 40,         // 质检
        ];
        
        return $priorityMap[$refType] ?? 50;
    }
    
    /**
     * 直接分配并锁定（库存充足的情况）
     */
    private function directAllocateAndLock($request)
    {
        $lockService = new InventoryLockServiceNew();
        
        try {
            $lockResult = $lockService->lockInventory([
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'quantity' => $request['quantity'],
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'notes' => $request['notes'],
                'created_by' => $request['created_by'] ?? 0
            ]);
            
            return [
                'code' => 0,
                'status' => 'success',
                'msg' => '库存分配并锁定成功',
                'allocated_quantity' => $request['quantity'],
                'lock_id' => $lockResult['lock_record']['id']
            ];
            
        } catch (Exception $e) {
            throw new Exception('库存锁定失败：' . $e->getMessage());
        }
    }
    
    /**
     * 部分分配并锁定（库存不足但有部分库存）
     */
    private function partialAllocateAndLock($request, $availableQty)
    {
        Db::startTrans();
        try {
            // 1. 锁定可用库存
            $lockResult = $this->directAllocateAndLock(array_merge($request, [
                'quantity' => $availableQty
            ]));
            
            // 2. 创建剩余数量的分配需求
            $remainingQty = $request['quantity'] - $availableQty;
            $allocationRequest = $this->createAllocationRequest(array_merge($request, [
                'quantity' => $remainingQty
            ]));
            
            Db::commit();
            
            return [
                'code' => 0,
                'status' => 'partial',
                'msg' => '部分库存分配成功，剩余数量等待分配',
                'allocated_quantity' => $availableQty,
                'pending_quantity' => $remainingQty,
                'lock_id' => $lockResult['lock_id'],
                'request_id' => $allocationRequest['request_id']
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 创建分配需求记录（无库存的情况）
     */
    private function createAllocationRequest($request)
    {
        // 检查是否已存在相同的分配请求
        $existingRequest = Db::name('inventory_allocation_request')
            ->where('product_id', $request['product_id'])
            ->where('warehouse_id', $request['warehouse_id'])
            ->where('ref_type', $request['ref_type'])
            ->where('ref_id', $request['ref_id'])
            ->where('status', 'in', [1, 2]) // 待分配或部分分配
            ->find();

        if ($existingRequest) {
            // 如果已存在，更新数量而不是创建新记录
            $newQuantity = $existingRequest['quantity'] + $request['quantity'];
            Db::name('inventory_allocation_request')
                ->where('id', $existingRequest['id'])
                ->update([
                    'quantity' => $newQuantity,
                    'update_time' => time(),
                    'notes' => $existingRequest['notes'] . '; 重复审核更新数量+' . $request['quantity']
                ]);

            return [
                'code' => 0,
                'status' => 'pending',
                'msg' => '库存不足，已更新现有分配需求',
                'allocated_quantity' => 0,
                'pending_quantity' => $newQuantity,
                'request_id' => $existingRequest['id']
            ];
        }

        $data = [
            'product_id' => $request['product_id'],
            'warehouse_id' => $request['warehouse_id'],
            'quantity' => $request['quantity'],
            'allocated_quantity' => 0,
            'ref_type' => $request['ref_type'],
            'ref_id' => $request['ref_id'],
            'ref_no' => $request['ref_no'],
            'priority' => $request['priority'] ?? $this->getDefaultPriority($request['ref_type']),
            'status' => 1, // 待分配
            'request_time' => time(),
            'notes' => $request['notes'],
            'created_by' => $request['created_by'] ?? 0,
            'create_time' => time(),
            'update_time' => time()
        ];

        $requestId = Db::name('inventory_allocation_request')->insertGetId($data);

        return [
            'code' => 0,
            'status' => 'pending',
            'msg' => '库存不足，已创建分配需求等待库存到货',
            'allocated_quantity' => 0,
            'pending_quantity' => $request['quantity'],
            'request_id' => $requestId
        ];
    }

    /**
     * 获取待分配的需求列表（按优先级排序）
     */
    private function getPendingAllocationRequests($productId, $warehouseId)
    {
        return Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->where('quantity', '>', Db::raw('allocated_quantity'))
            ->order('priority DESC, request_time ASC')
            ->select()
            ->toArray();
    }

    /**
     * 获取任意仓库的待分配需求列表（用于跨仓库分配）
     */
    private function getPendingAllocationRequestsAnyWarehouse($productId)
    {
        return Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->where('status', 'in', [1, 2]) // 待分配和部分分配
            ->where('quantity', '>', Db::raw('allocated_quantity'))
            ->order('priority DESC, request_time ASC')
            ->select()
            ->toArray();
    }

    /**
     * 调整分配需求的仓库
     */
    private function adjustAllocationWarehouse($requests, $newWarehouseId)
    {
        Db::startTrans();
        try {
            foreach ($requests as &$request) {
                $oldWarehouseId = $request['warehouse_id'];

                // 更新数据库中的仓库ID
                Db::name('inventory_allocation_request')
                    ->where('id', $request['id'])
                    ->update([
                        'warehouse_id' => $newWarehouseId,
                        'update_time' => time(),
                        'notes' => ($request['notes'] ?? '') . ' [仓库调整：' . $oldWarehouseId . '→' . $newWarehouseId . ']'
                    ]);

                // 更新内存中的数据
                $request['warehouse_id'] = $newWarehouseId;

                trace('调整分配需求仓库：需求ID=' . $request['id'] . ', 原仓库=' . $oldWarehouseId . ', 新仓库=' . $newWarehouseId, 'info');
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('调整分配仓库失败：' . $e->getMessage());
        }
    }

    /**
     * 执行具体的分配操作
     */
    private function executeAllocation($request, $allocateQty)
    {
        try {
            // 1. 锁定库存
            $lockService = new InventoryLockServiceNew();
            $lockResult = $lockService->lockInventory([
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'quantity' => $allocateQty,
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'notes' => '自动分配锁定：' . $request['notes'],
                'created_by' => $request['created_by']
            ]);

            // 2. 更新分配需求记录
            $newAllocatedQty = $request['allocated_quantity'] + $allocateQty;
            $newStatus = ($newAllocatedQty >= $request['quantity']) ? 3 : 2; // 完全分配或部分分配

            Db::name('inventory_allocation_request')
                ->where('id', $request['id'])
                ->update([
                    'allocated_quantity' => $newAllocatedQty,
                    'status' => $newStatus,
                    'update_time' => time()
                ]);

            // 3. 记录分配历史
            Db::name('inventory_allocation_history')->insert([
                'request_id' => $request['id'],
                'product_id' => $request['product_id'],
                'warehouse_id' => $request['warehouse_id'],
                'allocated_quantity' => $allocateQty,
                'lock_id' => $lockResult['lock_record']['id'],
                'allocation_type' => 'auto',
                'allocation_source' => 'inbound',
                'notes' => '入库自动分配',
                'created_by' => $request['created_by'],
                'create_time' => time()
            ]);

            return [
                'code' => 0,
                'request_id' => $request['id'],
                'ref_type' => $request['ref_type'],
                'ref_id' => $request['ref_id'],
                'ref_no' => $request['ref_no'],
                'allocated_quantity' => $allocateQty,
                'total_allocated' => $newAllocatedQty,
                'lock_id' => $lockResult['lock_record']['id'],
                'status' => $newStatus == 3 ? 'completed' : 'partial'
            ];

        } catch (Exception $e) {
            return [
                'code' => 1,
                'request_id' => $request['id'],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 验证是否允许反审
     */
    private function validateReverseAudit($refType, $refId)
    {
        switch ($refType) {
            case 'customer_order':
                return $this->validateSalesOrderReverseAudit($refId);
            case 'production_order':
                return $this->validateProductionOrderReverseAudit($refId);
            case 'purchase_order':
                return $this->validatePurchaseOrderReverseAudit($refId);
            case 'purchase_receipt':
                return $this->validatePurchaseReceiptReverseAudit($refId);
            default:
                throw new Exception('不支持的业务类型反审：' . $refType);
        }
    }

    /**
     * 销售订单反审验证
     */
    private function validateSalesOrderReverseAudit($orderId)
    {
        $order = Db::name('customer_order')->find($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 检查发货状态
        $deliveryCount = Db::name('customer_order_delivery_item')
            ->alias('d')
            ->join('oa_customer_order_delivery o', 'd.delivery_id = o.id')
            ->where('o.order_id', $orderId)
            ->where('o.status', '>', 0) // 已确认的发货单
            ->count();

        if ($deliveryCount > 0) {
            throw new Exception('订单已有发货记录，不允许反审');
        }

        return true;
    }

    /**
     * 生产订单反审验证
     */
    private function validateProductionOrderReverseAudit($orderId)
    {
        $order = Db::name('production_order')->find($orderId);
        if (!$order) {
            throw new Exception('生产订单不存在');
        }

        // 检查领料状态
        $materialIssueCount = Db::name('material_issue_detail')
            ->alias('d')
            ->join('material_issue o', 'd.issue_id = o.id')
            ->where('o.production_order_id', $orderId)
            ->where('o.status', '>', 0)
            ->count();

        if ($materialIssueCount > 0) {
            throw new Exception('生产订单已有领料记录，不允许反审');
        }

        return true;
    }

    /**
     * 采购订单反审验证
     */
    private function validatePurchaseOrderReverseAudit($orderId)
    {
        $order = Db::name('purchase_order')->find($orderId);
        if (!$order) {
            throw new Exception('采购订单不存在');
        }

        // 检查入库状态
        $inboundCount = Db::name('inbound_detail')
            ->alias('d')
            ->join('inbound o', 'd.inbound_id = o.id')
            ->where('o.purchase_order_id', $orderId)
            ->where('o.status', '>', 0)
            ->count();

        if ($inboundCount > 0) {
            throw new Exception('采购订单已有入库记录，不允许反审');
        }

        return true;
    }

    /**
     * 采购入库单反审验证
     */
    private function validatePurchaseReceiptReverseAudit($receiptId)
    {
        $receipt = Db::name('purchase_receipt')->find($receiptId);
        if (!$receipt) {
            throw new Exception('采购入库单不存在');
        }

        // 检查入库单状态
        if ($receipt['status'] != 1) {
            $statusText = $receipt['status'] == 0 ? '草稿' : '未知状态(' . $receipt['status'] . ')';
            throw new Exception('只有已入库状态的收货单才能反审核，当前状态：' . $statusText);
        }

        // 检查是否有后续出库记录
        $outboundCount = Db::name('outbound_detail')
            ->alias('od')
            ->join('outbound o', 'o.id = od.outbound_id')
            ->where('od.product_id', 'in', function($query) use ($receiptId) {
                $query->name('purchase_receipt_detail')
                      ->where('receipt_id', $receiptId)
                      ->field('product_id');
            })
            ->where('o.warehouse_id', $receipt['warehouse_id'])
            ->where('o.status', '>', 1) // 已审核的出库单
            ->where('o.create_time', '>', $receipt['update_time'])
            ->count();

        if ($outboundCount > 0) {
            throw new Exception('该入库单的产品已有出库记录，不允许反审核');
        }

        return true;
    }

    /**
     * 获取活跃的锁定记录
     */
    private function getActiveLockRecords($refType, $refId)
    {
        if ($refType === 'purchase_receipt') {
            // 对于入库单，需要通过分配历史记录找到相关的锁定记录
            return $this->getLockRecordsByReceipt($refId);
        }

        return Db::name('inventory_lock')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', InventoryLock::STATUS_LOCKED)
            ->select()
            ->toArray();
    }

    /**
     * 通过入库单获取相关的锁定记录（实际上应该恢复分配需求）
     */
    private function getLockRecordsByReceipt($receiptId)
    {
        // 对于入库单反审核，我们不应该释放锁定记录
        // 而应该恢复分配需求到入库前的状态
        // 这里返回空数组，实际的恢复逻辑在 restoreAllocationRequests 中处理
        return [];
    }

    /**
     * 恢复入库单相关的分配需求
     */
    private function restoreAllocationRequests($receiptId)
    {
        // 获取入库单信息
        $receipt = Db::name('purchase_receipt')->find($receiptId);
        if (!$receipt) {
            return ['code' => 1, 'msg' => '入库单不存在'];
        }

        // 获取入库单明细
        $receiptDetails = Db::name('purchase_receipt_detail')
            ->where('receipt_id', $receiptId)
            ->select()
            ->toArray();

        if (empty($receiptDetails)) {
            return ['code' => 0, 'msg' => '无入库明细需要处理'];
        }

        $restoredCount = 0;
        $releasedLocks = [];

        foreach ($receiptDetails as $detail) {
            // 1. 查找因为这次入库而被满足的分配需求
            // 通过分配历史记录找到相关的分配需求
            $allocationHistories = Db::name('inventory_allocation_history')
                ->where('product_id', $detail['product_id'])
                ->where('warehouse_id', $receipt['warehouse_id'])
                ->where('allocation_source', 'inbound')
                ->where('create_time', '>=', $receipt['update_time'])
                ->select()
                ->toArray();

            foreach ($allocationHistories as $history) {
                // 恢复分配需求状态
                $request = Db::name('inventory_allocation_request')->find($history['request_id']);
                if ($request && $request['status'] == 3) { // 完全分配状态
                    // 减少已分配数量
                    $newAllocatedQty = max(0, $request['allocated_quantity'] - $history['allocated_quantity']);
                    $newStatus = $newAllocatedQty >= $request['quantity'] ? 3 :
                                ($newAllocatedQty > 0 ? 2 : 1); // 3=完全分配, 2=部分分配, 1=待分配

                    Db::name('inventory_allocation_request')
                        ->where('id', $request['id'])
                        ->update([
                            'allocated_quantity' => $newAllocatedQty,
                            'status' => $newStatus,
                            'update_time' => time()
                        ]);

                    $restoredCount++;

                    // 2. 释放对应的锁定记录
                    if ($history['lock_id'] > 0) {
                        $lockRecord = Db::name('inventory_lock')->find($history['lock_id']);
                        if ($lockRecord && $lockRecord['status'] == 1) { // 锁定中
                            // 使用锁定服务来正确释放锁定（会同时更新实时库存）
                            $lockService = new InventoryLockServiceNew();
                            try {
                                $lockService->releaseLock($history['lock_id'], 0);
                                $releasedLocks[] = $history['lock_id'];
                            } catch (Exception $e) {
                                \think\facade\Log::error('释放锁定记录失败', [
                                    'lock_id' => $history['lock_id'],
                                    'error' => $e->getMessage()
                                ]);
                                // 继续处理其他记录，不中断整个流程
                            }
                        }
                    }
                }

                // 3. 删除分配历史记录
                Db::name('inventory_allocation_history')->where('id', $history['id'])->delete();
            }
        }

        return [
            'code' => 0,
            'msg' => '分配需求恢复成功',
            'restored_requests' => $restoredCount,
            'released_locks' => $releasedLocks
        ];
    }

    /**
     * 处理入库单反审核的特殊逻辑
     */
    private function handleReceiptReverseAudit($receiptId, $operatorId, $reason)
    {
        Db::startTrans();
        try {
            // 恢复分配需求到入库前的状态
            $restoreResult = $this->restoreAllocationRequests($receiptId);

            if ($restoreResult['code'] != 0) {
                throw new Exception('恢复分配需求失败：' . $restoreResult['msg']);
            }

            \think\facade\Log::info('入库单反审核恢复完成', [
                'receipt_id' => $receiptId,
                'operator_id' => $operatorId,
                'reason' => $reason,
                'restored_requests' => $restoreResult['restored_requests'],
                'released_locks' => $restoreResult['released_locks']
            ]);

            Db::commit();
            return [
                'code' => 0,
                'msg' => '入库单反审核处理成功',
                'data' => $restoreResult
            ];

        } catch (Exception $e) {
            Db::rollback();
            return [
                'code' => 1,
                'msg' => '入库单反审核处理失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理单个锁定记录的释放
     */
    private function processLockRelease($lock, $reason, $operatorId)
    {
        try {
            // 检查锁定状态
            if ($lock['status'] != InventoryLock::STATUS_LOCKED) {
                return [
                    'code' => 1,
                    'msg' => "锁定记录状态异常：{$lock['id']}"
                ];
            }

            // 释放锁定
            $lockService = new InventoryLockServiceNew();
            $result = $lockService->releaseLock($lock['id'], $operatorId);

            return [
                'code' => 0,
                'msg' => '释放成功',
                'lock_id' => $lock['id'],
                'product_id' => $lock['product_id'],
                'quantity' => $lock['quantity']
            ];

        } catch (Exception $e) {
            return [
                'code' => 1,
                'msg' => "释放失败：" . $e->getMessage(),
                'lock_id' => $lock['id']
            ];
        }
    }

    /**
     * 记录反审日志
     */
    private function logReverseAudit($refType, $refId, $lockRecords, $reason, $operatorId)
    {
        $refNo = '';
        $totalReleased = 0;

        // 获取业务单号
        switch ($refType) {
            case 'customer_order':
                $order = Db::name('order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
            case 'production_order':
                $order = Db::name('production_order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
            case 'purchase_order':
                $order = Db::name('purchase_order')->find($refId);
                $refNo = $order['order_no'] ?? '';
                break;
        }

        // 计算释放总量
        foreach ($lockRecords as $lock) {
            $totalReleased += $lock['quantity'];
        }

        // 获取操作人姓名
        $operator = Db::name('admin')->find($operatorId);
        $operatorName = $operator['name'] ?? '';

        Db::name('reverse_audit_log')->insert([
            'ref_type' => $refType,
            'ref_id' => $refId,
            'ref_no' => $refNo,
            'released_locks' => json_encode($lockRecords),
            'released_quantity' => $totalReleased,
            'reason' => $reason,
            'operator_id' => $operatorId,
            'operator_name' => $operatorName,
            'create_time' => time()
        ]);
    }

    /**
     * 删除分配需求记录（反审时使用）
     */
    private function deleteAllocationRequests($refType, $refId)
    {
        // 🔧 修正：反审时直接删除分配需求，而不是改状态
        // 因为反审的本质是撤销操作，应该像业务从未发生过一样

        // 1. 先获取要删除的记录（用于日志）
        $requestsToDelete = Db::name('inventory_allocation_request')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', 'in', [1, 2, 3]) // 待分配、部分分配、完全分配
            ->select()
            ->toArray();

        \think\facade\Log::info('查找待删除的分配需求', [
            'ref_type' => $refType,
            'ref_id' => $refId,
            'found_count' => count($requestsToDelete),
            'requests' => $requestsToDelete
        ]);

        if (!empty($requestsToDelete)) {
            // 2. 删除分配需求记录
            $deletedCount = Db::name('inventory_allocation_request')
                ->where('ref_type', $refType)
                ->where('ref_id', $refId)
                ->where('status', 'in', [1, 2, 3])
                ->delete();

            \think\facade\Log::info('执行删除分配需求', [
                'ref_type' => $refType,
                'ref_id' => $refId,
                'expected_count' => count($requestsToDelete),
                'actual_deleted_count' => $deletedCount
            ]);

            // 3. 同时删除相关的分配历史记录
            $historyDeletedCount = 0;
            foreach ($requestsToDelete as $request) {
                $count = Db::name('inventory_allocation_history')
                    ->where('request_id', $request['id'])
                    ->delete();
                $historyDeletedCount += $count;
            }

            // 4. 记录删除日志
            \think\facade\Log::info('反审删除分配需求完成', [
                'ref_type' => $refType,
                'ref_id' => $refId,
                'deleted_requests_count' => $deletedCount,
                'deleted_history_count' => $historyDeletedCount,
                'deleted_request_ids' => array_column($requestsToDelete, 'id')
            ]);
        } else {
            \think\facade\Log::info('无分配需求需要删除', [
                'ref_type' => $refType,
                'ref_id' => $refId
            ]);
        }
    }

    /**
     * 取消分配需求记录（业务取消时使用，保留记录）
     */
    private function cancelAllocationRequests($refType, $refId, $reason = '业务取消')
    {
        // 业务正常取消时，保留记录但改状态（用于统计分析）
        Db::name('inventory_allocation_request')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', 'in', [1, 2]) // 只取消待分配和部分分配的
            ->update([
                'status' => 4, // 已取消
                'notes' => Db::raw("CONCAT(IFNULL(notes, ''), '; 取消原因: " . $reason . "')"),
                'update_time' => time()
            ]);
    }

    /**
     * 检查是否存在重复的分配需求
     */
    public function checkDuplicateAllocation($refType, $refId)
    {
        $count = Db::name('inventory_allocation_request')
            ->where('ref_type', $refType)
            ->where('ref_id', $refId)
            ->where('status', 'in', [1, 2, 3]) // 非取消状态
            ->count();

        return $count > 0;
    }

    /**
     * 清理业务相关的所有分配记录（用于重复审核前的清理）
     */
    public function cleanupBusinessAllocation($refType, $refId, $reason = '重复操作清理')
    {
        // 1. 释放所有锁定
        $lockRecords = $this->getActiveLockRecords($refType, $refId);

        if (!empty($lockRecords)) {
            foreach ($lockRecords as $lock) {
                try {
                    $lockService = new InventoryLockServiceNew();
                    $lockService->releaseLock($lock['id'], 0); // 系统操作
                } catch (\Exception $e) {
                    // 记录错误但不中断流程
                    \think\facade\Log::error('清理锁定记录失败', [
                        'lock_id' => $lock['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        // 2. 删除所有分配需求（清理操作也应该删除）
        $this->deleteAllocationRequests($refType, $refId);

        // 3. 记录清理日志
        \think\facade\Log::info('清理业务分配记录', [
            'ref_type' => $refType,
            'ref_id' => $refId,
            'reason' => $reason,
            'cleaned_locks' => count($lockRecords)
        ]);

        return true;
    }

    /**
     * 重新分配释放的库存
     *
     * @param array $releasedLocks 释放的锁定记录
     * @param string $excludeRefType 排除的业务类型（避免重新分配给刚反审的业务）
     * @param int $excludeRefId 排除的业务ID
     */
    private function redistributeReleasedStock($releasedLocks, $excludeRefType = '', $excludeRefId = 0)
    {
        if (empty($releasedLocks)) {
            return;
        }

        // 按产品分组统计释放的库存
        $releasedStock = [];
        foreach ($releasedLocks as $lock) {
            $key = $lock['product_id'] . '_' . $lock['warehouse_id'];
            if (!isset($releasedStock[$key])) {
                $releasedStock[$key] = [
                    'product_id' => $lock['product_id'],
                    'warehouse_id' => $lock['warehouse_id'],
                    'quantity' => 0
                ];
            }
            $releasedStock[$key]['quantity'] += $lock['quantity'];
        }

        // 对每个产品检查是否有其他等待的需求
        foreach ($releasedStock as $stock) {
            try {
                // 🔧 修复：检查是否有其他等待的分配需求
                $pendingCount = Db::name('inventory_allocation_request')
                    ->where('product_id', $stock['product_id'])
                    ->where('warehouse_id', $stock['warehouse_id'])
                    ->where('status', 'in', [1, 2]) // 待分配和部分分配
                    ->where('quantity', '>', Db::raw('allocated_quantity'))
                    ->when($excludeRefType && $excludeRefId, function($query) use ($excludeRefType, $excludeRefId) {
                        // 排除刚反审的业务，避免立即重新分配给它
                        $query->where('ref_type', '<>', $excludeRefType)
                              ->whereOr('ref_id', '<>', $excludeRefId);
                    })
                    ->count();

                // 只有在有其他等待需求时才重新分配
                if ($pendingCount > 0) {
                    $this->autoAllocateOnInbound(
                        $stock['product_id'],
                        $stock['warehouse_id'],
                        $stock['quantity']
                    );

                    \think\facade\Log::info('反审后重新分配库存', [
                        'product_id' => $stock['product_id'],
                        'warehouse_id' => $stock['warehouse_id'],
                        'quantity' => $stock['quantity'],
                        'pending_requests' => $pendingCount
                    ]);
                } else {
                    \think\facade\Log::info('反审后无等待需求，跳过重新分配', [
                        'product_id' => $stock['product_id'],
                        'warehouse_id' => $stock['warehouse_id'],
                        'quantity' => $stock['quantity']
                    ]);
                }

            } catch (\Exception $e) {
                // 记录错误日志，但不影响整体流程
                \think\facade\Log::error("重新分配库存失败", [
                    'product_id' => $stock['product_id'],
                    'warehouse_id' => $stock['warehouse_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 获取产品的库存分配统计
     */
    public function getAllocationSummary($productId, $warehouseId = null)
    {
        $where = ['product_id' => $productId];
        if ($warehouseId) {
            $where['warehouse_id'] = $warehouseId;
        }

        $summary = Db::name('inventory_allocation_request')
            ->where($where)
            ->where('status', 'in', [1, 2, 3]) // 排除已取消的
            ->field([
                'ref_type',
                'COUNT(*) as request_count',
                'SUM(quantity) as total_quantity',
                'SUM(allocated_quantity) as allocated_quantity',
                'SUM(quantity - allocated_quantity) as pending_quantity'
            ])
            ->group('ref_type')
            ->select()
            ->toArray();

        return $summary;
    }

    /**
     * 批量分配库存
     */
    public function batchAllocateAndLock($requests)
    {
        $results = [];
        $hasError = false;

        Db::startTrans();
        try {
            foreach ($requests as $request) {
                $result = $this->allocateAndLock($request);
                $results[] = $result;

                if ($result['code'] !== 0) {
                    $hasError = true;
                }
            }

            if ($hasError) {
                Db::rollback();
                return ['code' => 1, 'msg' => '批量分配部分失败', 'details' => $results];
            }

            Db::commit();
            return ['code' => 0, 'msg' => '批量分配成功', 'details' => $results];

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
