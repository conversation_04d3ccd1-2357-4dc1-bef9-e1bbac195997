# MRP需求显示数据修复

## 问题描述
在生产订单详情页面的MRP需求显示中，出现数据不正确的问题：
- 已领取数量显示为0
- 明明已经有领取记录，却还显示有缺口
- 数据逻辑不一致

## 问题分析

### 接口位置
- **URL**: `http://tc.xinqiyu.cn:8830/api/index/ajax_Produce_mrp_summary?order_id=27`
- **文件**: `app/api/controller/Index.php`
- **方法**: `ajax_Produce_mrp_summary()`

### 问题根因
`getFedQuantity()` 方法使用了错误的数据表来查询已领料数量：

**错误的查询**：
```php
// 从投料明细中统计已领料数量
$fedQuantity = \think\facade\Db::name('production_feeding_detail')
    ->alias('pfd')
    ->leftJoin('production_feeding pf', 'pfd.feeding_id = pf.id')
    ->where('pf.production_order_id', $orderId)
    ->where('pfd.material_id', $materialId)
    ->where('pf.delete_time', 0)
    ->sum('pfd.actual_quantity');
```

**问题**：
1. 使用了 `production_feeding_detail` 表（投料明细表）
2. 但系统实际使用的是 `production_material_request_detail` 表（领料单明细表）
3. 两个表的业务含义不同：
   - `production_feeding_detail`: 投料记录（物料投入生产）
   - `production_material_request_detail`: 领料记录（从仓库领取物料）

## 修复方案

### 1. 修复已领料数量查询
**修复后的查询**：
```php
// 从生产领料单明细中统计已领料数量
$fedQuantity = \think\facade\Db::name('production_material_request_detail')
    ->alias('d')
    ->leftJoin('production_material_request r', 'd.request_no = r.request_no')
    ->where('r.production_order_id', $orderId)
    ->where('d.material_id', $materialId)
    ->where('r.status', '>=', 1) // 只统计已审核的领料单
    ->sum('d.actual_quantity');
```

### 2. 数据表结构说明

#### 生产领料单主表 (production_material_request)
- `id`: 主键
- `request_no`: 领料单号
- `production_order_id`: 生产订单ID
- `status`: 状态（0=待审核，1=已审核，2=已出库，3=已完成）

#### 生产领料单明细表 (production_material_request_detail)
- `id`: 主键
- `request_no`: 领料单号
- `material_id`: 物料ID
- `request_quantity`: 申请领料数量
- `actual_quantity`: 实际出库数量（这是我们需要统计的字段）

### 3. 业务逻辑说明

#### 领料流程：
1. **创建领料单** → `production_material_request`
2. **添加物料明细** → `production_material_request_detail`
3. **审核领料单** → `status = 1`
4. **仓库出库** → 更新 `actual_quantity`

#### MRP显示逻辑：
- **需求总量** = BOM用量 × 生产数量 × (1 + 损耗率)
- **已领数量** = 所有已审核领料单的 `actual_quantity` 汇总
- **缺口数量** = 需求总量 - 已领数量

## 修复效果

### 修复前：
- ✗ 已领数量显示为0（查询错误的表）
- ✗ 缺口数量 = 需求总量（因为已领数量为0）
- ✗ 数据不准确，影响生产决策

### 修复后：
- ✅ 已领数量正确显示（从正确的表查询）
- ✅ 缺口数量准确计算
- ✅ 数据逻辑一致，支持准确的生产决策

## 相关表结构

### 1. 生产领料单表
```sql
-- 主表
CREATE TABLE `oa_production_material_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_no` varchar(50) NOT NULL COMMENT '领料单号',
  `production_order_id` int(11) NOT NULL COMMENT '生产订单ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=待审核,1=已审核,2=已出库,3=已完成',
  -- 其他字段...
);

-- 明细表
CREATE TABLE `oa_production_material_request_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_no` varchar(50) NOT NULL COMMENT '领料单号',
  `material_id` int(11) NOT NULL COMMENT '物料ID',
  `request_quantity` decimal(12,4) NOT NULL COMMENT '申请领料数量',
  `actual_quantity` decimal(12,4) DEFAULT 0 COMMENT '实际出库数量',
  -- 其他字段...
);
```

### 2. 库存锁定表
```sql
CREATE TABLE `oa_inventory_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `ref_type` varchar(50) NOT NULL COMMENT '关联类型',
  `ref_id` int(11) NOT NULL COMMENT '关联ID',
  `quantity` decimal(12,4) NOT NULL COMMENT '锁定数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1=有效,0=无效',
  -- 其他字段...
);
```

## 测试验证

### 1. 接口测试
访问：`http://tc.xinqiyu.cn:8830/api/index/ajax_Produce_mrp_summary?order_id=27`

**预期结果**：
- `fed_quantity` 字段显示正确的已领料数量
- `shortage_quantity` 字段显示准确的缺口数量
- 数据逻辑一致

### 2. 数据验证SQL
```sql
-- 验证生产订单27的领料情况
SELECT 
    r.request_no,
    r.production_order_id,
    r.status,
    d.material_id,
    d.material_name,
    d.request_quantity,
    d.actual_quantity
FROM oa_production_material_request r
LEFT JOIN oa_production_material_request_detail d ON r.request_no = d.request_no
WHERE r.production_order_id = 27
AND r.status >= 1
ORDER BY d.material_id;
```

## 注意事项

### 1. 数据一致性
- 确保 `actual_quantity` 字段在出库时正确更新
- 领料单状态变更要与实际业务流程一致

### 2. 性能考虑
- 如果领料单数量很大，考虑添加索引优化查询
- 可以考虑缓存频繁查询的数据

### 3. 业务扩展
- 后续可能需要区分不同类型的领料（正常领料、补料、超领）
- 可以考虑增加领料时间、领料人等维度的统计

## 相关功能影响

### 1. 生产订单详情页面
- MRP需求表格数据显示正确
- 物料缺口分析准确

### 2. 生产计划
- 基于准确的物料需求数据制定计划
- 避免因数据错误导致的计划偏差

### 3. 库存管理
- 准确反映物料的实际消耗情况
- 支持库存预警和补货决策

## 总结

通过修复 `getFedQuantity()` 方法的数据查询逻辑，解决了MRP需求显示中已领料数量不准确的问题。修复后的系统能够：

1. **准确显示已领料数量**：基于实际的领料单记录
2. **正确计算缺口数量**：需求总量 - 实际已领数量
3. **支持生产决策**：提供准确的物料需求分析数据

这个修复确保了生产管理系统中物料需求数据的准确性和一致性。
