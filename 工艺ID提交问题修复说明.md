# 工艺ID提交问题修复说明

## 问题分析

从您提供的POST数据中发现，`process_template_id` 字段没有被提交到后端。经过分析，发现了以下问题：

### 1. 表单结构问题
工艺管理标签页没有被包含在表单中，导致其中的隐藏字段 `process_template_id` 无法被提交。

### 2. 字段收集问题
表单提交时的字段收集逻辑没有特别处理工艺模板ID字段。

## 修复方案

### 1. 修复表单结构
将工艺管理标签页包装在表单中：

```html
<!-- 工艺管理 -->
<div class="layui-tab-item">
    <form class="layui-form p-page">  <!-- 添加表单包装 -->
        <div style="margin-bottom: 15px;">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 80px;">
                    <a href="javascript:;" onclick="showProcessSelectDialog()" style="color: #1E9FFF; text-decoration: none;">
                        选择工艺
                    </a>
                </label>
                <div class="layui-input-block" style="margin-left: 100px;">
                    <div id="selected-process-info" style="padding: 8px 12px; background: #f8f8f8; border-radius: 4px; color: #999;">
                        请点击"选择工艺"来选择工艺路线
                    </div>
                    <input type="hidden" name="process_template_id" id="process-template-id" value="{$detail.process_template_id|default=''}">
                </div>
            </div>
        </div>
        <!-- 工序表格 -->
        <table class="layui-table" lay-skin="line" id="processTable">
            <!-- 表格内容 -->
        </table>
    </form>  <!-- 添加表单结束标签 -->
</div>
```

### 2. 强制包含工艺模板ID
在表单提交时强制包含工艺模板ID字段：

```javascript
// 确保工艺模板ID被包含
var processTemplateId = $('#process-template-id').val();
if (processTemplateId) {
    submitData.process_template_id = processTemplateId;
}

console.log('提交的数据:', submitData);
console.log('工艺模板ID:', processTemplateId);
```

### 3. 后端字段处理
确保后端保存逻辑包含 `process_template_id` 字段：

```php
// 添加可选字段（如果存在）
$optionalFields = [
    'specs', 'producer', 'purchase_cycle', 'stock', 'description', 'remark',
    'default_warehouse', 'min_order_qty', 'min_package_qty', 'material_level',
    'material_source', 'category', 'model', 'color', 'quality_management',
    'quality_exempt', 'quality_settings', 'reference_cost', 'sales_price',
    'min_sales_price', 'max_sales_price', 'material_images', 'attachments',
    'process_template_id'  // 确保包含工艺模板ID
];
```

## 验证步骤

### 1. 数据库准备
确保已执行SQL脚本添加字段：
```sql
ALTER TABLE `oa_product` 
ADD COLUMN `process_template_id` int(11) NOT NULL DEFAULT '0' COMMENT '工艺模板ID' AFTER `source_type`;
```

### 2. 功能测试
1. 打开物料档案编辑页面
2. 点击"选择工艺"，选择一个工艺路线
3. 保存表单
4. 检查浏览器控制台，确认 `process_template_id` 被包含在提交数据中
5. 检查数据库，确认字段值被正确保存

### 3. 调试信息
在浏览器控制台中查看：
- "提交的数据:" - 应该包含 `process_template_id` 字段
- "工艺模板ID:" - 应该显示选择的工艺ID

## 预期结果

修复后，提交表单时应该能看到类似以下的数据结构：

```javascript
{
    "id": "2165",
    "material_code": "1-0659B.0102.001",
    "title": "659下壳",
    "cate_id": "13",
    "unit": "个",
    "specs": "59*31*9mm",
    "source_type": "2",
    "status": "1",
    "process_template_id": "123",  // 工艺模板ID应该被包含
    // ... 其他字段
}
```

## 注意事项

1. **清理缓存**：修改后需要清理浏览器缓存和应用缓存
2. **权限检查**：确保用户有访问工艺路线模板的权限
3. **数据验证**：后端应该验证工艺模板ID的有效性
4. **错误处理**：添加适当的错误处理机制

## 后续优化

1. **表单验证**：添加工艺选择的必填验证（如果需要）
2. **数据同步**：考虑工艺模板变更时的数据同步问题
3. **性能优化**：大量数据时考虑分页或懒加载
4. **用户体验**：添加工艺选择的快捷操作功能
