<?php
/**
 * 生产报工控制器 - 修复版本
 * 解决 engineering_process 表不存在的问题
 * 改为使用 produce_order_process 表
 */

namespace app\Produce\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\View;

class Report extends BaseController
{
    /**
     * 报工添加页面
     */
    public function add()
    {
        if (request()->isAjax()) {
            // 处理报工提交
            return $this->saveReport();
        } else {
            // 显示报工页面
            $param = get_params();
            $order_id = isset($param['order_id']) ? intval($param['order_id']) : 0;
            
            // 获取生产订单列表
            $orders = Db::name('produce_order')
                ->where('status', 'in', [1, 2]) // 已排产、生产中
                ->field('id, order_no, product_name, quantity')
                ->select();
                
            // 获取工序列表（如果指定了订单ID）
            $processes = [];
            if ($order_id > 0) {
                $processes = Db::name('produce_order_process')
                    ->where('order_id', $order_id)
                    ->field('id, step_no, process_name, process_code')
                    ->order('step_no asc')
                    ->select()
                    ->toArray();
            }
            
            View::assign('orders', $orders);
            View::assign('processes', $processes);
            View::assign('order_id', $order_id);
            
            return view();
        }
    }
    
    /**
     * 保存报工记录
     */
    private function saveReport()
    {
        $param = get_params();
        
        // 验证必要参数
        if (empty($param['order_id']) || empty($param['process_id']) || empty($param['quantity'])) {
            return json(['code' => 1, 'msg' => '参数不完整']);
        }
        
        // 获取订单信息
        $order = Db::name('produce_order')->where('id', $param['order_id'])->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '订单不存在']);
        }
        
        // 获取工序信息
        $process = Db::name('produce_order_process')->where('id', $param['process_id'])->find();
        if (!$process) {
            return json(['code' => 1, 'msg' => '工序不存在']);
        }
        
        // 准备报工数据
        $reportData = [
            'order_id' => $param['order_id'],
            'process_id' => $param['process_id'],
            'step_id' => $process['step_no'],
            'step_name' => $process['process_name'],
            'product_id' => $order['product_id'],
            'product_name' => $order['product_name'],
            'quantity' => intval($param['quantity']),
            'qualified_qty' => intval($param['qualified_qty'] ?? $param['quantity']),
            'unqualified_qty' => intval($param['quantity']) - intval($param['qualified_qty'] ?? $param['quantity']),
            'work_time' => floatval($param['work_time'] ?? 0) * 60, // 转换为分钟
            'worker_id' => $param['worker_id'] ?? 0,
            'worker_name' => $param['worker_name'] ?? '',
            'report_date' => $param['report_date'] ?? date('Y-m-d'),
            'remark' => $param['remark'] ?? '',
            'status' => 1,
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 判断是否为最后工序
        $maxStepNo = Db::name('produce_order_process')
            ->where('order_id', $param['order_id'])
            ->max('step_no');
        
        $reportData['is_last_process'] = ($process['step_no'] == $maxStepNo) ? 1 : 0;
        
        // 开始事务
        Db::startTrans();
        try {
            // 插入报工记录
            $reportId = Db::name('production_work_report')->insertGetId($reportData);
            
            // 如果是最后工序，更新订单完成量
            if ($reportData['is_last_process']) {
                Db::name('produce_order')
                    ->where('id', $param['order_id'])
                    ->setInc('completed_qty', $reportData['qualified_qty']);
                    
                // 检查是否需要更新订单状态
                $updatedOrder = Db::name('produce_order')->where('id', $param['order_id'])->find();
                if ($updatedOrder['completed_qty'] >= $updatedOrder['quantity']) {
                    Db::name('produce_order')
                        ->where('id', $param['order_id'])
                        ->update(['status' => 3, 'progress' => 100]); // 已完成
                }
            }
            
            Db::commit();
            return json(['code' => 0, 'msg' => '报工成功', 'data' => ['report_id' => $reportId]]);
            
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 1, 'msg' => '报工失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取订单工序列表
     */
    public function getOrderProcesses()
    {
        $param = get_params();
        $order_id = isset($param['order_id']) ? intval($param['order_id']) : 0;
        
        if ($order_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取订单信息
        $order = Db::name('produce_order')->where('id', $order_id)->find();
        if (!$order) {
            return json(['code' => 1, 'msg' => '订单不存在']);
        }
        
        // 获取订单的工序列表
        $processes = Db::name('produce_order_process')
            ->where('order_id', $order_id)
            ->field('id, process_name as name, process_code as code, step_no')
            ->order('step_no asc')
            ->select()
            ->toArray();
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => [
            'order' => $order,
            'processes' => $processes
        ]]);
    }
    
    /**
     * 获取订单工序步骤
     */
    public function getProcessSteps()
    {
        $param = get_params();
        $order_id = isset($param['order_id']) ? intval($param['order_id']) : 0;
        
        if ($order_id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误']);
        }
        
        // 获取订单工序信息
        $steps = Db::name('produce_order_process')
            ->where('order_id', $order_id)
            ->field('id, step_no as order, process_code as code, process_name as name, standard_time, workers, description')
            ->order('step_no asc')
            ->select()
            ->toArray();
            
        if (empty($steps)) {
            return json(['code' => 1, 'msg' => '订单工序不存在']);
        }
        
        return json(['code' => 0, 'msg' => '获取成功', 'data' => ['steps' => $steps]]);
    }
}
