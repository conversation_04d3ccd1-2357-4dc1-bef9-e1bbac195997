# 简化执行出库功能说明

## 问题分析 - 执行出库显示无仓库

### 问题描述
用户反馈：在warehouse/InventoryLock/index锁定中有显示锁定仓库，库存分配中也是有/warehouse/AllocationManage/index分配仓库，为什么执行出库却显示没有仓库？

### 问题分析
通过代码分析发现以下问题：

1. **锁定记录查询问题**：在execute方法中查询锁定信息时，使用的是`$outbound->warehouse_id`作为仓库条件，但锁定记录中的warehouse_id可能与出库单的warehouse_id不一致。

2. **分配记录查询问题**：同样在查询分配信息时，也存在仓库ID匹配的问题。

3. **出库单warehouse_id设置问题**：出库单的warehouse_id可能没有正确设置，或者在业务流程中被错误修改。

### 根本原因
- 锁定和分配记录中的warehouse_id是根据实际库存所在仓库设置的
- 出库单的warehouse_id可能是业务层面的默认仓库或者没有正确设置
- 查询条件中同时使用了出库单的warehouse_id和锁定/分配记录的warehouse_id，导致查询不到数据

## 修复方案

### 1. 优化查询逻辑
在execute方法中，采用多种策略查询锁定和分配信息：

**策略1**：通过业务关联查找（ref_type + ref_id）
**策略2**：通过出库单关联查找（outbound + outbound_id）
**策略3**：查找该产品的任何相关记录（备选方案）

### 2. 动态确定仓库ID
在doExecute方法中，不再固定使用出库单的warehouse_id，而是：
1. 优先从锁定记录中获取实际仓库ID
2. 其次从分配记录中获取实际仓库ID
3. 最后使用出库单默认仓库ID

### 3. 移除仓库ID限制
在处理锁定和分配记录时，不再强制要求仓库ID匹配，而是：
1. 优先通过业务关联查找记录
2. 如果没有找到，通过出库单关联查找
3. 确保能够正确处理跨仓库的锁定和分配

## 代码修改详情

### 1. 修改execute方法查询逻辑
```php
// 查询锁定信息 - 多种查询策略
$lockInfo = null;

// 策略1：通过业务关联查找
if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
    $lockInfo = Db::name('inventory_lock')
        ->alias('il')
        ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
        ->where('il.product_id', $detail['product_id'])
        ->where('il.ref_type', $outbound->ref_type)
        ->where('il.ref_id', $outbound->ref_id)
        ->where('il.status', 1) // 已锁定状态
        ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
        ->find();
}

// 策略2：通过出库单关联查找
if (!$lockInfo) {
    $lockInfo = Db::name('inventory_lock')
        ->alias('il')
        ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
        ->where('il.product_id', $detail['product_id'])
        ->where('il.ref_type', 'outbound')
        ->where('il.ref_id', $outbound->id)
        ->where('il.status', 1) // 已锁定状态
        ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
        ->find();
}

// 策略3：查找该产品的任何锁定记录（最后的备选方案）
if (!$lockInfo) {
    $lockInfo = Db::name('inventory_lock')
        ->alias('il')
        ->leftJoin('warehouse w', 'il.warehouse_id = w.id')
        ->where('il.product_id', $detail['product_id'])
        ->where('il.status', 1) // 已锁定状态
        ->field('il.warehouse_id, w.name as warehouse_name, il.quantity as locked_quantity')
        ->order('il.create_time desc')
        ->find();
}
```

### 2. 添加determineWarehouseId方法
```php
/**
 * 确定实际的出库仓库ID
 */
private function determineWarehouseId($productId, $outbound)
{
    // 优先从锁定记录中获取仓库ID
    if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
        $lockRecord = Db::name('inventory_lock')
            ->where('product_id', $productId)
            ->where('ref_type', $outbound->ref_type)
            ->where('ref_id', $outbound->ref_id)
            ->where('status', 1) // 已锁定状态
            ->find();

        if ($lockRecord && $lockRecord['warehouse_id']) {
            return $lockRecord['warehouse_id'];
        }
    }

    // 其次从分配记录中获取仓库ID
    if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
        $allocationRecord = Db::name('inventory_allocation_request')
            ->where('product_id', $productId)
            ->where('ref_type', $outbound->ref_type)
            ->where('ref_id', $outbound->ref_id)
            ->where('status', 2) // 已分配状态
            ->find();

        if ($allocationRecord && $allocationRecord['warehouse_id']) {
            return $allocationRecord['warehouse_id'];
        }
    }

    // 最后使用出库单默认仓库ID
    return $outbound->warehouse_id;
}
```

### 3. 修改doExecute方法
```php
foreach ($details as $detail) {
    $productId = $detail['product_id'];
    $quantity = $detail['quantity'];

    // 确定实际的出库仓库ID
    $warehouseId = $this->determineWarehouseId($productId, $outbound);

    // 1. 检查库存是否充足
    if (!$inventoryService->checkStock($productId, $warehouseId, $quantity)) {
        throw new \Exception("产品【{$detail['product_name']}】库存不足");
    }

    // 2. 执行库存扣减（会自动记录流水）
    $inventoryService->decreaseStock(
        $productId,
        $warehouseId,  // 使用动态确定的仓库ID
        $quantity,
        'outbound',
        $outboundId,
        $outbound->outbound_no,
        '出库单执行出库',
        $this->uid
    );

    // ... 其他处理逻辑
}
```

## 测试验证

### 1. 测试场景
1. **有锁定记录的出库单**：验证能正确显示锁定仓库信息
2. **有分配记录的出库单**：验证能正确显示分配仓库信息
3. **同时有锁定和分配记录**：验证两种信息都能正确显示
4. **跨仓库的锁定分配**：验证不同仓库的锁定分配能正确处理

### 2. 测试步骤
1. 访问执行出库页面：`http://tc.xinqiyu.cn:8830/warehouse/outbound/execute.html?id=2`
2. 检查"涉及仓库分布"区域是否正确显示：
   - 锁定仓库：应显示实际锁定库存的仓库名称
   - 分配仓库：应显示实际分配库存的仓库名称
3. 检查明细表格中的仓库信息：
   - 锁定仓库列：应显示带有仓库名称的橙色标签
   - 分配仓库列：应显示带有仓库名称的蓝色标签
4. 执行出库操作，验证：
   - 能够成功执行出库
   - 库存从正确的仓库扣减
   - 锁定和分配记录状态正确更新

### 3. 预期结果
- 执行出库页面能正确显示锁定仓库和分配仓库信息
- 不再出现"显示没有仓库"的问题
- 出库操作能够正确执行，库存从实际的仓库扣减
- 系统能够处理跨仓库的锁定和分配场景

### 4. 故障排查
如果仍然显示"无仓库"，请检查：
1. 出库单的ref_type和ref_id字段是否正确设置
2. 锁定记录表中是否存在对应的记录且状态为1（已锁定）
3. 分配记录表中是否存在对应的记录且状态为2（已分配）
4. 数据库表结构是否完整，特别是warehouse表的关联

## 锁定库存处理修复

### 问题描述
执行出库后，锁定的数量没有减少，锁定记录状态没有更新为"已使用"。

### 根本原因
原来的代码使用`decreaseStock`方法，只是简单减少可用库存，没有正确处理锁定库存。应该使用`useLockedStock`方法来正确处理锁定库存的使用。

### 修复方案
1. **智能选择库存扣减方式**：
   - 如果有锁定库存，使用`useLockedStock`方法
   - 如果没有锁定库存，使用`decreaseStock`方法

2. **添加锁定库存检查方法**：
```php
private function hasLockedStock($productId, $warehouseId, $outbound)
{
    // 检查锁定记录表
    if (!empty($outbound->ref_type) && !empty($outbound->ref_id)) {
        $lockCount = Db::name('inventory_lock')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('ref_type', $outbound->ref_type)
            ->where('ref_id', $outbound->ref_id)
            ->where('status', 1) // 已锁定状态
            ->count();

        if ($lockCount > 0) {
            return true;
        }
    }

    // 检查实时库存表中的锁定数量
    $inventory = \app\warehouse\model\InventoryRealtime::where([
        'product_id' => $productId,
        'warehouse_id' => $warehouseId
    ])->find();

    return $inventory && $inventory->locked_quantity > 0;
}
```

3. **修改库存扣减逻辑**：
```php
// 检查是否有锁定库存，优先使用锁定库存
$hasLockedStock = $this->hasLockedStock($productId, $warehouseId, $outbound);

if ($hasLockedStock) {
    // 使用锁定库存（会自动减少锁定数量和总库存）
    $inventoryService->useLockedStock(
        $productId,
        $warehouseId,
        $quantity,
        'outbound',
        $outboundId,
        $outbound->outbound_no,
        '出库单执行出库（使用锁定库存）',
        $this->uid
    );
} else {
    // 没有锁定库存，直接减少可用库存
    $inventoryService->decreaseStock(
        $productId,
        $warehouseId,
        $quantity,
        'outbound',
        $outboundId,
        $outbound->outbound_no,
        '出库单执行出库',
        $this->uid
    );
}
```

### 修复效果
- 执行出库后，锁定数量会正确减少
- 锁定记录状态会更新为"已使用"（状态2）
- 实时库存表中的locked_quantity字段会正确减少
- 库存流水记录会正确标识为"使用锁定库存"

## 锁定记录状态更新修复

### 问题描述
虽然锁定库存数量减少了，但是库存锁定表中的记录状态没有正确更新为"已使用"状态，在warehouse/InventoryLock/index页面仍然显示为"锁定中"状态。

### 根本原因
1. **查询条件过于严格**：原来的查询条件要求完全匹配ref_type和ref_id，但实际数据可能不匹配
2. **字段名错误**：尝试更新不存在的字段（used_quantity、used_time）
3. **状态值错误**：使用了错误的状态值（3而不是2）

### 修复方案
1. **多策略查询锁定记录**：
   - 策略1：通过业务关联查找（ref_type + ref_id + warehouse_id）
   - 策略2：通过出库单关联查找（outbound + outbound_id + warehouse_id）
   - 策略3：只通过产品ID和仓库ID查找（最宽松的条件）

2. **正确的状态更新**：
```php
// 更新锁定记录状态为已使用
$updateResult = Db::name('inventory_lock')
    ->where('id', $lock['id'])
    ->update([
        'status' => InventoryLock::STATUS_USED, // 已使用（状态值2）
        'update_time' => time()
    ]);
```

3. **添加调试日志**：记录查询和更新过程，便于排查问题

### 状态值说明
根据InventoryLock模型定义：
- `STATUS_LOCKED = 1` - 锁定中
- `STATUS_USED = 2` - 已使用
- `STATUS_RELEASED = 3` - 已释放

## 待入库订单列表修复

### 问题描述
在待入库订单页面（warehouse/Receipt/pendingOrders.html），已经全部入库的订单仍然显示在列表中。

### 根本原因
虽然查询条件正确（只查询状态3和4的订单），但是：
1. 订单状态可能没有正确更新为5（已完成）
2. 缺少基于实际收货进度的二次过滤

### 修复方案
在获取订单列表后，添加基于实际收货进度的过滤：

```php
// 计算每个订单的收货情况，并过滤掉已完全入库的订单
$filteredList = [];
foreach ($list as $order) {
    // 获取订单明细
    $details = OrderDetailModel::where('order_id', $order->id)->select();
    $total_quantity = 0;
    $received_quantity = 0;

    foreach ($details as $detail) {
        $total_quantity += $detail->quantity;
        $received_quantity += $detail->received_quantity;
    }

    // 计算收货进度百分比
    $order->progress = $total_quantity > 0 ? round(($received_quantity / $total_quantity) * 100) : 0;
    $order->detail_count = count($details);

    // 只显示未完全入库的订单（进度小于100%）
    if ($order->progress < 100) {
        $filteredList[] = $order;
    }
}
```

### 修复效果
- 已完全入库的订单（进度100%）不再显示在待入库列表中
- 只显示真正需要入库的订单
- 提高了列表的准确性和用户体验

## 功能重新设计

### 设计理念
基于系统已有的锁库和分配机制，执行出库无需仓库人员填写出库数量，只需确认执行即可。数量在前面的审核和分配阶段已经确定。

### 业务流程
```
1. 业务单据创建 → 2. 库存分配 → 3. 库存锁定 → 4. 出库单审核 → 5. 执行出库 → 6. 完成
```

## 前端界面简化

### 1. 移除数量输入
**原设计**：仓库人员需要输入实际出库数量
**新设计**：直接显示已确定的出库数量，无需输入

### 2. 简化表格显示
```html
<table class="layui-table" lay-skin="line">
    <thead>
        <tr>
            <th>产品编码</th>
            <th>产品名称</th>
            <th>规格</th>
            <th>单位</th>
            <th>出库数量</th>        <!-- 显示确定的数量 -->
            <th>锁定状态</th>        <!-- 显示锁定状态 -->
            <th>分配状态</th>        <!-- 显示分配状态 -->
            <th>当前库存</th>        <!-- 显示实时库存 -->
        </tr>
    </thead>
</table>
```

### 3. 状态标识
- **锁定状态**：`已锁定` (橙色标签)
- **分配状态**：`已分配` (蓝色标签)
- **出库数量**：加粗显示，表示确定的数量

### 4. 确认按钮
```html
<button class="layui-btn layui-btn-normal layui-btn-lg" lay-submit>
    <i class="layui-icon layui-icon-ok"></i> 确认执行出库
</button>
```

## 后端逻辑重构

### 1. 简化参数处理
```php
// 原来需要处理明细数量
$details = $param['details'] ?? [];

// 现在只需要出库单ID
$outboundId = $param['outbound_id'] ?? 0;
```

### 2. 基于出库单明细执行
```php
// 直接获取出库单明细，数量已确定
$details = Db::name('outbound_detail')->where('outbound_id', $outboundId)->select();

foreach ($details as $detail) {
    $quantity = $detail['quantity']; // 使用明细中的确定数量
    // 执行出库操作
}
```

### 3. 使用库存服务
```php
$inventoryService = new \app\warehouse\service\InventoryRealtimeService();

// 1. 检查库存充足性
$inventoryService->checkStock($productId, $warehouseId, $quantity);

// 2. 执行库存扣减（自动记录流水）
$inventoryService->decreaseStock(
    $productId,
    $warehouseId,
    $quantity,
    'outbound',
    $outboundId,
    $outbound->outbound_no,
    '出库单执行出库',
    $this->uid
);
```

### 4. 处理锁定和分配记录
```php
// 处理锁定记录
private function processLockRecords($productId, $warehouseId, $quantity, $outbound)
{
    $lockRecords = Db::name('inventory_lock')
        ->where('product_id', $productId)
        ->where('ref_type', $outbound->ref_type)
        ->where('ref_id', $outbound->ref_id)
        ->where('status', 1) // 已锁定
        ->select();

    foreach ($lockRecords as $lock) {
        // 更新为已使用状态
        Db::name('inventory_lock')->update([
            'status' => 3, // 已使用
            'used_quantity' => $lock['quantity'],
            'used_time' => time()
        ]);
    }
}

// 处理分配记录
private function processAllocationRecords($productId, $warehouseId, $quantity, $outbound)
{
    $allocationRecords = Db::name('inventory_allocation_request')
        ->where('product_id', $productId)
        ->where('ref_type', $outbound->ref_type)
        ->where('ref_id', $outbound->ref_id)
        ->where('status', 2) // 已分配
        ->select();

    foreach ($allocationRecords as $allocation) {
        // 更新为已完成状态
        Db::name('inventory_allocation_request')->update([
            'status' => 3, // 已完成
            'completed_quantity' => $allocation['quantity'],
            'completed_time' => time()
        ]);
    }
}
```

## 状态管理

### 1. 锁定记录状态
- `1` - 已锁定：库存已锁定，等待使用
- `3` - 已使用：锁定的库存已被出库使用

### 2. 分配记录状态
- `2` - 已分配：库存已分配给业务需求
- `3` - 已完成：分配的库存已完成出库

### 3. 出库单状态
- `2` - 已审核：可以执行出库
- `4` - 全部出库：出库已完成

## 业务优势

### 1. 流程规范化
- **数量确定性**：出库数量在审核阶段就已确定
- **操作简化**：仓库人员只需确认执行，无需重新计算
- **错误减少**：避免仓库人员输入错误数量

### 2. 数据一致性
- **锁定机制**：确保库存在出库前已被锁定
- **分配记录**：明确库存分配给哪个业务需求
- **自动流水**：系统自动记录所有库存变动

### 3. 权责分明
- **业务部门**：负责确定出库数量和优先级
- **审核人员**：负责审核出库的合理性
- **仓库人员**：负责执行物理出库操作

### 4. 可追溯性
- **锁定记录**：可以追溯库存何时被锁定
- **分配记录**：可以追溯库存分配给哪个需求
- **出库流水**：可以追溯具体的出库操作

## 前后端交互

### 1. 页面加载
```javascript
// 加载出库单信息和明细
// 显示锁定状态和分配状态
// 显示确定的出库数量
```

### 2. 确认执行
```javascript
// 简化的确认对话框
layer.confirm('确定要执行出库操作吗？<br><br>系统将根据锁库和分配记录自动处理库存扣减。', {
    icon: 3,
    title: '确认执行出库'
}, function(index) {
    // 提交出库单ID即可
    $.ajax({
        url: '/warehouse/outbound/execute',
        data: {outbound_id: outboundId}
    });
});
```

### 3. 执行结果
```json
{
    "code": 0,
    "msg": "出库执行成功",
    "data": {
        "status": 4,
        "outbound_time": 1691234567
    }
}
```

## 异常处理

### 1. 库存不足
```php
if (!$inventoryService->checkStock($productId, $warehouseId, $quantity)) {
    throw new \Exception("产品【{$detail['product_name']}】库存不足");
}
```

### 2. 锁定记录异常
- 检查锁定记录是否存在
- 检查锁定数量是否匹配
- 处理锁定记录状态更新失败

### 3. 分配记录异常
- 检查分配记录是否存在
- 检查分配数量是否匹配
- 处理分配记录状态更新失败

## 扩展功能

### 1. 批量出库
支持选择多个出库单批量执行出库操作。

### 2. 出库确认单
执行出库后生成出库确认单，供仓库人员打印。

### 3. 异常处理流程
对于库存不足等异常情况，提供异常处理流程。

### 4. 移动端支持
提供移动端界面，方便仓库人员使用手机或平板执行出库。

通过这种简化设计，出库执行变得更加规范和高效，减少了人为错误，提高了数据准确性。
