<?php
/**
 * 修复所有使用 inventory_log 的代码
 * 将其改为使用新的 InventoryTransaction 模型
 */

// 需要修复的文件列表
$files_to_fix = [
    'app/purchase/controller/Inventory.php',
    'app/outsource/controller/Index.php',
    'app/warehouse/view/inventory/logs.html'
];

// 修复规则
$fix_rules = [
    // 1. 解冻流水记录
    [
        'file' => 'app/purchase/controller/Inventory.php',
        'search' => "Db::name('inventory_log')->insert([\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'product_id' => \$inventory['product_id'],\n                    'batch_no' => \$inventory['batch_no'],\n                    'type' => 7, // 解冻",
        'replace' => "\\app\\warehouse\\model\\InventoryTransaction::createTransaction([\n                    'product_id' => \$inventory['product_id'],\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'transaction_type' => 'unlock',\n                    'quantity' => 0,"
    ],
    
    // 2. 报废流水记录
    [
        'file' => 'app/purchase/controller/Inventory.php',
        'search' => "Db::name('inventory_log')->insert([\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'product_id' => \$inventory['product_id'],\n                    'batch_no' => \$inventory['batch_no'],\n                    'type' => 8, // 报废",
        'replace' => "\\app\\warehouse\\model\\InventoryTransaction::createTransaction([\n                    'product_id' => \$inventory['product_id'],\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'transaction_type' => 'out',\n                    'quantity' => -\$quantity,"
    ],
    
    // 3. 库位移动流水记录
    [
        'file' => 'app/purchase/controller/Inventory.php',
        'search' => "Db::name('inventory_log')->insert([\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'product_id' => \$inventory['product_id'],\n                    'batch_no' => \$inventory['batch_no'],\n                    'type' => 9, // 库位移动",
        'replace' => "\\app\\warehouse\\model\\InventoryTransaction::createTransaction([\n                    'product_id' => \$inventory['product_id'],\n                    'warehouse_id' => \$inventory['warehouse_id'],\n                    'transaction_type' => 'adjust',\n                    'quantity' => 0,"
    ],
    
    // 4. 统计查询修复
    [
        'file' => 'app/purchase/controller/Inventory.php',
        'search' => "->join('inventory_log il', 'il.product_id = p.id AND il.type = 2', 'left') // 2表示出库",
        'replace' => "->join('inventory_transaction it', 'it.product_id = p.id AND it.transaction_type = \"out\"', 'left')"
    ],
    
    // 5. 月度统计查询修复
    [
        'file' => 'app/purchase/controller/Inventory.php',
        'search' => "\$month_stat = Db::name('inventory_log')",
        'replace' => "\$month_stat = Db::name('inventory_transaction')"
    ]
];

echo "库存流水系统修复脚本\n";
echo "====================\n\n";

echo "发现的问题：\n";
echo "1. 系统中存在使用已删除的 inventory_log 表的代码\n";
echo "2. 需要将这些代码改为使用新的 InventoryTransaction 模型\n";
echo "3. 需要修复查询逻辑和字段映射\n\n";

echo "修复方案：\n";
echo "1. 将所有 Db::name('inventory_log') 改为使用 InventoryTransaction 模型\n";
echo "2. 修复字段映射关系\n";
echo "3. 统一流水类型常量\n\n";

echo "需要手动修复的代码位置：\n";
echo "1. app/purchase/controller/Inventory.php - 解冻流水记录 (约1662行)\n";
echo "2. app/purchase/controller/Inventory.php - 报废流水记录 (约1739行)\n";
echo "3. app/purchase/controller/Inventory.php - 库位移动流水记录 (约2753行)\n";
echo "4. app/purchase/controller/Inventory.php - 统计查询 (约2121行)\n";
echo "5. app/purchase/controller/Inventory.php - 月度统计查询 (约2143行)\n\n";

echo "修复后的代码示例：\n";
echo "// 旧代码\n";
echo "Db::name('inventory_log')->insert([\n";
echo "    'type' => 7, // 解冻\n";
echo "    'quantity' => 0,\n";
echo "    ...\n";
echo "]);\n\n";

echo "// 新代码\n";
echo "\\app\\warehouse\\model\\InventoryTransaction::createTransaction([\n";
echo "    'transaction_type' => 'unlock',\n";
echo "    'quantity' => 0,\n";
echo "    ...\n";
echo "]);\n\n";

echo "类型映射关系：\n";
echo "旧类型 -> 新类型\n";
echo "1 -> 'in' (入库)\n";
echo "2 -> 'out' (出库)\n";
echo "3 -> 'adjust' (盘点调整)\n";
echo "4 -> 'transfer_out' (调拨出库)\n";
echo "5 -> 'transfer_in' (调拨入库)\n";
echo "6 -> 'lock' (锁定)\n";
echo "7 -> 'unlock' (解锁)\n";
echo "8 -> 'out' (报废，作为出库处理)\n";
echo "9 -> 'adjust' (库位移动，作为调整处理)\n\n";

echo "字段映射关系：\n";
echo "旧字段 -> 新字段\n";
echo "operation_by -> created_by\n";
echo "operation_time -> create_time\n";
echo "related_bill_type -> ref_type\n";
echo "related_bill_id -> ref_id\n";
echo "related_bill_no -> ref_no\n";
echo "type -> transaction_type\n\n";

echo "注意事项：\n";
echo "1. 所有修改都需要在事务中进行\n";
echo "2. 确保数据完整性\n";
echo "3. 测试所有相关功能\n";
echo "4. 备份数据库\n\n";

echo "执行完成！请手动修复上述代码位置。\n";
?>
