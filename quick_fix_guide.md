# 物料档案添加功能快速修复指南

## 问题分析

根据错误信息 `method not exist:think\\db\\Query->create`，问题出现在控制器中使用了不兼容的数据库操作方法。

## 已修复的问题

### 1. 数据库操作方法
- **问题**：使用了 `ArchiveModel::create()` 方法
- **修复**：改为使用 `Db::name('product')->insertGetId()` 方法
- **原因**：某些ThinkPHP版本中模型的 `create()` 方法可能不存在或有兼容性问题

### 2. 字段处理优化
- **问题**：直接传递所有POST参数可能导致字段不匹配
- **修复**：只传递数据库表中存在的字段
- **优化**：添加了详细的调试日志

## 当前修复状态

✅ **已修复**：
- 数据库插入方法改为 `insertGetId()`
- 数据库更新方法改为标准的 `update()`
- 添加了字段过滤，只传递有效字段
- 增加了详细的调试日志
- 优化了JSON字段处理逻辑

## 测试步骤

### 1. 清理缓存
```bash
# 删除缓存文件
rm -rf runtime/cache/*
rm -rf runtime/temp/*
```

### 2. 检查日志
提交表单后，查看日志文件：
- 位置：`runtime/log/` 目录
- 查找包含 "Archive add/edit params" 的日志条目
- 查找包含 "Processed params for insert" 的日志条目

### 3. 验证数据库表结构
确保 `product` 表包含以下必需字段：
- `id` (主键)
- `title` (物料名称)
- `cate_id` (分类ID)
- `material_code` (物料编号)
- `unit` (单位)
- `source_type` (物料类型)
- `status` (状态)
- `admin_id` (创建人ID)
- `create_time` (创建时间)
- `update_time` (更新时间)

## 如果仍有问题

### 1. 检查表名
确认数据库中的表名是 `product` 还是 `oa_product`：
```sql
SHOW TABLES LIKE '%product%';
```

如果表名是 `oa_product`，需要修改控制器中的表名：
```php
// 将
Db::name('product')
// 改为
Db::name('oa_product')
```

### 2. 检查字段是否存在
```sql
DESCRIBE product;
-- 或
DESCRIBE oa_product;
```

### 3. 检查数据库连接
确认数据库配置正确，连接正常。

### 4. 查看完整错误信息
如果还有错误，请提供：
- 完整的错误信息
- 日志文件内容
- 数据库表结构信息

## 预期结果

修复后，提交表单应该：
1. 成功创建物料档案记录
2. 返回成功消息：`{"code": 0, "msg": "物料档案添加成功"}`
3. 在数据库中看到新增的记录
4. 供应商价格和外协价格数据正确保存到关联表

## 注意事项

1. **表名一致性**：确保控制器中使用的表名与实际数据库表名一致
2. **字段映射**：确保前端字段名与数据库字段名匹配
3. **数据类型**：确保传递的数据类型与数据库字段类型兼容
4. **必需字段**：确保所有必需字段都有值

如果按照以上步骤操作后仍有问题，请提供具体的错误信息和日志内容。
