{extend name="../../base/view/common/base" /}
<!-- 主体 -->
{block name="body"}

<div class="p-page">
	<form class="layui-form gg-form-bar" id="barsearchform">
		<div class="layui-input-inline" style="width:220px;">
			<input type="text" name="keywords" placeholder="输入分类名称" class="layui-input" autocomplete="off" />
		</div>
		<div class="layui-input-inline" style="width:150px">
			<button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="table-search"><i class="layui-icon layui-icon-search mr-1"></i>搜索</button>
			<button type="reset" class="layui-btn layui-btn-reset" lay-filter="table-reset">清空</button>
		</div>
	</form>
	<table class="layui-hide" id="table_category" lay-filter="table_category"></table>
</div>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
	<button class="layui-btn layui-btn-sm" lay-event="add">
		<span>+ 添加分类</span>
	</button>
  </div>
</script>

{/block}
<!-- /主体 -->

<!-- 脚本 -->
{block name="script"}
<script>
	const moduleInit = ['tool','tablePlus'];
	function gouguInit() {
		var table = layui.tablePlus, tool = layui.tool;
		
		layui.pageTable = table.render({
			elem: "#table_category"
			,title: "物料分类列表"
			,toolbar: "#toolbarDemo"
			,url: "/material/category/index"
			,page: true
			,limit: 20
			,cellMinWidth: 80
			,height: 'full-152'
			,cols: [[ //表头
				{
					field: 'id',
					title: 'ID',
					align: 'center',
					width: 80
				},{
					field: 'name',
					title: '分类名称',
					minWidth: 200
				},{
					field: 'sort',
					title: '排序',
					align: 'center',
					width: 100,
					sort: true
				},{
					field: 'status',
					title: '状态',
					align: 'center',
					width: 100,
					templet: function(d) {
						var statusClass = d.status == 1 ? 'layui-bg-green' : 'layui-bg-gray';
						return '<span class="layui-badge ' + statusClass + '">' + d.status_name + '</span>';
					}
				},{
					field: 'create_time',
					title: '创建时间',
					align: 'center',
					width: 160
				},{
					field: 'right',
					fixed:'right',
					title: '操作',
					width: 200,
					align: 'center',
					ignoreExport:true,
					templet: function (d) {
						var html = '<div class="layui-btn-group">';
						html += '<span class="layui-btn layui-btn-xs" lay-event="edit">编辑</span>';
						html += '<span class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</span>';
						var statusBtn = d.status == 1 ? 
							'<span class="layui-btn layui-btn-warm layui-btn-xs" lay-event="disable">禁用</span>' :
							'<span class="layui-btn layui-btn-normal layui-btn-xs" lay-event="enable">启用</span>';
						html += statusBtn;
						html += '</div>';
						return html;
					}						
				}
			]]
		});
		
		//表头工具栏事件
		table.on('toolbar(table_category)', function(obj){
			if (obj.event === 'add'){
				tool.side("/material/category/add");
				return;
			}
		});	
			
		table.on('tool(table_category)',function (obj) {
			var data = obj.data;
			if (obj.event === 'edit') {
				tool.side("/material/category/add?id="+data.id);
				return;
			}
			if (obj.event === 'del') {
				layer.confirm('确定要删除该分类吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							obj.del();
						}
					}
					tool.post("/material/category/delete", { id: data.id }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'disable') {
				layer.confirm('确定要禁用该分类吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/material/category/updateStatus", { id: data.id, status: 0 }, callback);
					layer.close(index);
				});
				return;
			}
			if (obj.event === 'enable') {
				layer.confirm('确定要启用该分类吗?', { icon: 3, title: '提示' }, function (index) {
					let callback = function (e) {
						layer.msg(e.msg);
						if (e.code == 0) {
							layui.pageTable.reload();
						}
					}
					tool.post("/material/category/updateStatus", { id: data.id, status: 1 }, callback);
					layer.close(index);
				});
				return;
			}
		});
	}
</script>
{/block}
<!-- /脚本 -->