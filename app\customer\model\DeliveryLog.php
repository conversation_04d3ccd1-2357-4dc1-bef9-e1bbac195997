<?php
namespace app\customer\model;

use think\Model;

class DeliveryLog extends Model
{
    // 设置当前模型对应的表名
    protected $name = 'customer_order_delivery_log';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false;
    
    /**
     * 关联发货指令
     */
    public function delivery()
    {
        return $this->belongsTo('Delivery', 'delivery_id', 'id');
    }
    
    /**
     * 关联操作人
     */
    public function operator()
    {
        return $this->belongsTo('app\\user\\model\\Admin', 'operator_id', 'id');
    }
} 