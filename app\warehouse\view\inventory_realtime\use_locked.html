{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="p-page">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>使用锁定库存</h3>
        </div>
        <div class="layui-card-body">
            <form class="layui-form" lay-filter="useLockedForm" style="padding: 20px;">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品</label>
                            <div class="layui-input-block">
                                {if condition="isset($product)"}
                                <!-- 当有预设产品时，显示为只读 -->
                                <input type="hidden" name="product_id" value="{$product.id}">
                                <input type="text" class="layui-input layui-disabled" readonly value="{$product.material_code} - {$product.title} ({$product.specs})">
                                {else}
                                <!-- 没有预设产品时，显示选择框 -->
                                <select name="product_id" lay-verify="required" lay-filter="product_select">
                                    <option value="">请选择产品</option>
                                    {volist name="products" id="item"}
                                    <option value="{$item.id}">
                                        {$item.material_code} - {$item.title} ({$item.specs})
                                    </option>
                                    {/volist}
                                </select>
                                {/if}
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">仓库</label>
                            <div class="layui-input-block">
                                {if condition="isset($warehouse)"}
                                <!-- 当有预设仓库时，显示为只读 -->
                                <input type="hidden" name="warehouse_id" value="{$warehouse.id}">
                                <input type="text" class="layui-input layui-disabled" readonly value="{$warehouse.code} - {$warehouse.name}">
                                {else}
                                <!-- 没有预设仓库时，显示选择框 -->
                                <select name="warehouse_id" lay-verify="required" lay-filter="warehouse_select">
                                    <option value="">请选择仓库</option>
                                    {volist name="warehouses" id="item"}
                                    <option value="{$item.id}">
                                        {$item.code} - {$item.name}
                                    </option>
                                    {/volist}
                                </select>
                                {/if}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">使用数量</label>
                            <div class="layui-input-block">
                                <input type="number" name="quantity" lay-verify="required|number" placeholder="请输入使用数量" class="layui-input" step="0.01" min="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-form-item">
                            <label class="layui-form-label">使用类型</label>
                            <div class="layui-input-block">
                                <select name="use_type">
                                    <option value="production">生产使用</option>
                                    <option value="sale">销售使用</option>
                                    <option value="transfer">调拨使用</option>
                                    <option value="other">其他使用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">使用说明</label>
                    <div class="layui-input-block">
                        <textarea name="notes" placeholder="请输入使用说明" class="layui-textarea" rows="3"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="use-locked-submit">确认使用</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" onclick="parent.layer.closeAll()">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool'];
    function gouguInit() {
        var form = layui.form, tool = layui.tool;

        // 产品数据映射（仅在没有预设产品时需要）
        {if condition="!isset($product)"}
        var productData = {};
        {volist name="products" id="item"}
        productData[{$item.id}] = {
            id: {$item.id},
            title: '{$item.title}',
            material_code: '{$item.material_code}',
            unit: '{$item.unit}',
            specs: '{$item.specs}'
        };
        {/volist}
        {/if}

        form.render('select');

        // 表单提交
        form.on('submit(use-locked-submit)', function(data){
            var formData = data.field;

            // 验证数据
            if (!formData.product_id) {
                layer.msg('请选择产品');
                return false;
            }
            if (!formData.warehouse_id) {
                layer.msg('请选择仓库');
                return false;
            }
            if (!formData.quantity || formData.quantity <= 0) {
                layer.msg('请输入正确的使用数量');
                return false;
            }

            // 提交数据
            var callback = function(res) {
                if (res.code == 0) {
                    layer.msg('使用成功', {icon: 1});
                    setTimeout(function() {
                        parent.layer.closeAll();
                        if (parent.layui && parent.layui.pageTable) {
                            parent.layui.pageTable.reload();
                        }
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            };

            tool.post('/warehouse/inventory_realtime/use_locked', formData, callback);
            return false;
        });
    }
</script>
{/block}
