<?php
declare (strict_types = 1);

namespace app\warehouse\service;

use app\warehouse\model\InventoryTransfer;
use app\warehouse\model\InventoryTransferDetail;
use think\facade\Db;
use think\Exception;

/**
 * 仓库间调拨服务类
 */
class InventoryTransferService
{
    /**
     * 创建调拨单
     * 
     * @param array $data 调拨单数据
     * @param array $details 调拨明细数据
     * @return InventoryTransfer
     * @throws Exception
     */
    public function createTransfer($data, $details)
    {
        if (empty($details)) {
            throw new Exception('调拨明细不能为空');
        }
        
        if ($data['from_warehouse_id'] == $data['to_warehouse_id']) {
            throw new Exception('源仓库和目标仓库不能相同');
        }
        
        // 检查所有产品的库存是否充足
        $inventoryService = new InventoryRealtimeService();
        foreach ($details as $detail) {
            if (!$inventoryService->checkStock($detail['product_id'], $data['from_warehouse_id'], $detail['quantity'])) {
                throw new Exception('产品库存不足，无法创建调拨单');
            }
        }
        
        return InventoryTransfer::createTransfer($data, $details);
    }
    
    /**
     * 审核调拨单
     * 
     * @param int $transferId 调拨单ID
     * @param int $approvedBy 审核人ID
     * @return bool
     * @throws Exception
     */
    public function approveTransfer($transferId, $approvedBy)
    {
        $transfer = InventoryTransfer::find($transferId);
        if (!$transfer) {
            throw new Exception('调拨单不存在');
        }
        
        if ($transfer->status != InventoryTransfer::STATUS_PENDING) {
            throw new Exception('调拨单状态不正确，当前状态: ' . $transfer->status_text);
        }
        
        // 获取调拨明细
        $details = InventoryTransferDetail::getDetailsByTransferId($transferId);
        
        // 再次检查库存是否充足
        $inventoryService = new InventoryRealtimeService();
        foreach ($details as $detail) {
            if (!$inventoryService->checkStock($detail->product_id, $transfer->from_warehouse_id, $detail->quantity)) {
                throw new Exception('产品库存不足，无法审核调拨单');
            }
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 锁定源仓库库存
            $lockService = new InventoryLockServiceNew();
            $lockItems = [];
            foreach ($details as $detail) {
                $lockItems[] = [
                    'product_id' => $detail->product_id,
                    'warehouse_id' => $transfer->from_warehouse_id,
                    'quantity' => $detail->quantity
                ];
            }
            
            $lockService->batchLockInventory(
                $lockItems,
                'transfer',
                $transferId,
                $transfer->transfer_no,
                '调拨单锁定',
                $approvedBy
            );
            
            // 更新调拨单状态
            InventoryTransfer::approveTransfer($transferId, $approvedBy);
            
            Db::commit();
            return true;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 执行调拨
     * 
     * @param int $transferId 调拨单ID
     * @param int $operatorId 操作人ID
     * @return bool
     * @throws Exception
     */
    public function executeTransfer($transferId, $operatorId)
    {
        $transfer = InventoryTransfer::find($transferId);
        if (!$transfer) {
            throw new Exception('调拨单不存在');
        }
        
        if ($transfer->status != InventoryTransfer::STATUS_APPROVED) {
            throw new Exception('调拨单状态不正确，当前状态: ' . $transfer->status_text);
        }
        
        // 获取调拨明细
        $details = InventoryTransferDetail::getDetailsByTransferId($transferId);
        
        // 开启事务
        Db::startTrans();
        try {
            $inventoryService = new InventoryRealtimeService();
            
            // 执行库存调拨
            foreach ($details as $detail) {
                $inventoryService->transferStock(
                    $detail->product_id,
                    $transfer->from_warehouse_id,
                    $transfer->to_warehouse_id,
                    $detail->quantity,
                    'transfer',
                    $transferId,
                    $transfer->transfer_no,
                    '调拨单执行',
                    $operatorId
                );
            }
            
            // 使用锁定的库存
            $lockService = new InventoryLockServiceNew();
            $lockService->batchUse('transfer', $transferId, $operatorId);
            
            // 更新调拨单状态
            InventoryTransfer::completeTransfer($transferId);
            
            Db::commit();
            return true;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 取消调拨单
     * 
     * @param int $transferId 调拨单ID
     * @param int $operatorId 操作人ID
     * @return bool
     * @throws Exception
     */
    public function cancelTransfer($transferId, $operatorId)
    {
        $transfer = InventoryTransfer::find($transferId);
        if (!$transfer) {
            throw new Exception('调拨单不存在');
        }
        
        if ($transfer->status == InventoryTransfer::STATUS_COMPLETED) {
            throw new Exception('已完成的调拨单不能取消');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 如果已审核，需要释放锁定的库存
            if ($transfer->status == InventoryTransfer::STATUS_APPROVED) {
                $lockService = new InventoryLockServiceNew();
                $lockService->batchRelease('transfer', $transferId, $operatorId);
            }
            
            // 更新调拨单状态
            InventoryTransfer::cancelTransfer($transferId);
            
            Db::commit();
            return true;
            
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 获取调拨单详情
     * 
     * @param int $transferId 调拨单ID
     * @return array
     * @throws Exception
     */
    public function getTransferDetail($transferId)
    {
        $transfer = InventoryTransfer::where('id', $transferId)
            ->with(['fromWarehouse', 'toWarehouse', 'creator', 'approver'])
            ->find();
            
        if (!$transfer) {
            throw new Exception('调拨单不存在');
        }
        
        $details = InventoryTransferDetail::where('transfer_id', $transferId)
            ->with(['product'])
            ->select();
        
        // 获取锁定信息
        $lockService = new InventoryLockServiceNew();
        $lockSummary = $lockService->getLockSummary('transfer', $transferId);
        
        return [
            'transfer' => $transfer,
            'details' => $details,
            'lock_summary' => $lockSummary
        ];
    }
    
    /**
     * 获取调拨统计信息
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function getTransferStatistics($params = [])
    {
        $where = [];
        
        // 时间范围
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $where[] = ['create_time', 'between', [strtotime($params['start_time']), strtotime($params['end_time'])]];
        }
        
        // 源仓库
        if (!empty($params['from_warehouse_id'])) {
            $where[] = ['from_warehouse_id', '=', $params['from_warehouse_id']];
        }
        
        // 目标仓库
        if (!empty($params['to_warehouse_id'])) {
            $where[] = ['to_warehouse_id', '=', $params['to_warehouse_id']];
        }
        
        // 状态统计
        $statusStats = InventoryTransfer::where($where)
            ->field('status, count(*) as count, sum(total_amount) as amount')
            ->group('status')
            ->select()
            ->toArray();
        
        // 总计
        $total = InventoryTransfer::where($where)->count();
        $totalAmount = InventoryTransfer::where($where)->sum('total_amount');
        
        // 按仓库统计
        $warehouseStats = InventoryTransfer::where($where)
            ->alias('t')
            ->join('warehouse fw', 't.from_warehouse_id = fw.id', 'left')
            ->join('warehouse tw', 't.to_warehouse_id = tw.id', 'left')
            ->field('t.from_warehouse_id, fw.name as from_warehouse_name, 
                     t.to_warehouse_id, tw.name as to_warehouse_name,
                     count(*) as count, sum(t.total_amount) as amount')
            ->group('t.from_warehouse_id, t.to_warehouse_id')
            ->select()
            ->toArray();
        
        return [
            'status_stats' => $statusStats,
            'warehouse_stats' => $warehouseStats,
            'total_count' => $total,
            'total_amount' => $totalAmount
        ];
    }
    
    /**
     * 批量审核调拨单
     * 
     * @param array $transferIds 调拨单ID数组
     * @param int $approvedBy 审核人ID
     * @return array
     */
    public function batchApprove($transferIds, $approvedBy)
    {
        $results = [];
        
        foreach ($transferIds as $transferId) {
            try {
                $this->approveTransfer($transferId, $approvedBy);
                $results[$transferId] = ['success' => true, 'message' => '审核成功'];
            } catch (Exception $e) {
                $results[$transferId] = ['success' => false, 'message' => $e->getMessage()];
            }
        }
        
        return $results;
    }
    
    /**
     * 批量执行调拨
     * 
     * @param array $transferIds 调拨单ID数组
     * @param int $operatorId 操作人ID
     * @return array
     */
    public function batchExecute($transferIds, $operatorId)
    {
        $results = [];
        
        foreach ($transferIds as $transferId) {
            try {
                $this->executeTransfer($transferId, $operatorId);
                $results[$transferId] = ['success' => true, 'message' => '执行成功'];
            } catch (Exception $e) {
                $results[$transferId] = ['success' => false, 'message' => $e->getMessage()];
            }
        }
        
        return $results;
    }
}
