# 物料档案系统后端实现总结

## 1. 前端字段分析

根据前端表单分析，系统包含以下主要字段：

### 基础资料字段
- `material_code` - 物料编号（支持自动生成）
- `title` - 物料名称
- `cate_id` - 物料分类ID
- `unit` - 基本单位
- `specs` - 物料规格
- `source_type` - 物料类型（1:自产 2:外购）
- `status` - 状态（0:禁用 1:启用）
- `producer` - 生产商
- `purchase_cycle` - 采购周期
- `stock` - 库存数量
- `default_warehouse` - 默认仓库
- `min_order_qty` - 最小起订量
- `min_package_qty` - 最小包装量
- `material_level` - 物料等级（A/B/C）
- `material_source` - 物料来源（自购/加工/客供/委外供料）
- `category` - 物料类别（主料/辅料）
- `model` - 型号
- `color` - 颜色
- `description` - 物料描述
- `remark` - 备注信息
- `material_images[]` - 物料图片（JSON数组）

### 质检信息字段
- `quality_management` - 质检管理（0:禁用 1:启用）
- `quality_exempt` - 免检设置（0:关闭 1:开启）
- `quality_settings[]` - 质检项目设置（JSON数组）

### 价格信息字段
- `reference_cost` - 参考成本价
- `sales_price` - 销售单价（含税）
- `min_sales_price` - 最低销售单价
- `max_sales_price` - 最高销售单价

### 工艺管理字段
- `supplier_prices[]` - 供应商价格信息（数组）
- `outsource_prices[]` - 外协价格信息（数组）
- `attachments[]` - 附件信息（JSON数组）

## 2. 数据库结构更新

### 2.1 oa_product表字段添加
执行 `database_updates.sql` 文件中的SQL语句，为 `oa_product` 表添加以下字段：
- `default_warehouse` - 默认仓库ID
- `min_order_qty` - 最小起订量
- `min_package_qty` - 最小包装量
- `material_level` - 物料等级
- `material_source` - 物料来源
- `category` - 物料类别
- `model` - 型号
- `color` - 颜色
- `remark` - 备注信息
- `material_images` - 物料图片URLs（JSON格式）
- `material_drawing` - 物料图纸URL
- `quality_management` - 质检管理
- `quality_exempt` - 免检设置
- `quality_settings` - 质检项目设置（JSON格式）
- `reference_cost` - 参考成本价
- `sales_price` - 销售单价
- `min_sales_price` - 最低销售单价
- `max_sales_price` - 最高销售单价
- `attachments` - 附件URLs（JSON格式）

### 2.2 新建关联表
- `oa_unit` - 单位管理表（包含常用单位及其精度设置）
- `material_supplier_price` - 供应商价格表
- `material_outsource_price` - 外协价格表
- `material_code_sequence` - 物料编号序列表

## 3. 后端代码实现

### 3.1 模型更新 (app/material/model/Archive.php)
- 更新字段映射 `$schema` 包含所有前端字段
- 配置JSON字段处理 `$json = ['quality_settings', 'material_images', 'attachments']`
- 添加获取器和修改器处理JSON字段的序列化/反序列化
- 保持与 `oa_product` 表的正确映射关系

### 3.2 控制器更新 (app/material/controller/Archive.php)
- 添加 `processJsonFields()` 方法处理JSON字段和默认值
- 更新 `generateMaterialCode()` 方法支持自动编号生成
- 完善 `add()` 方法的数据处理和事务管理
- 完善供应商价格和外协价格的关联数据处理
- 添加完整的错误处理和日志记录

### 3.3 验证器更新 (app/material/validate/Archive.php)
- 更新验证规则匹配新的字段结构
- 修正表名为 `product`（对应 `oa_product`）
- 添加完整的字段验证和错误消息
- 配置新增和编辑场景的验证字段
- 添加单位验证方法，确保选择的单位在 `oa_unit` 表中存在且启用

### 3.4 单位管理模型 (app/common/model/Unit.php)
- 新建单位管理模型，映射 `oa_unit` 表
- 提供单位的基本信息管理（名称、精度、类型等）
- 支持单位的启用/禁用状态管理
- 提供格式化的获取器方法

### 3.5 前端模板更新 (app/material/view/archive/add.html)
- 更新基本单位选择框，从固定选项改为动态加载 `oa_unit` 表数据
- 保持与后端数据的一致性
- 支持单位的动态管理和扩展

## 4. 手工操作步骤

### 4.1 数据库操作
1. 备份当前数据库
2. 执行 `database_updates.sql` 中的SQL语句
3. 检查表结构是否正确添加了所有字段
4. 验证新建的关联表是否创建成功

### 4.2 代码部署
1. 确认所有PHP文件已更新
2. 清理缓存：删除 `runtime/cache/` 目录下的缓存文件
3. 测试前端表单提交功能
4. 验证数据保存和读取是否正常

## 5. 测试验证

### 5.1 功能测试
- 测试物料档案的新增功能
- 测试物料档案的编辑功能
- 测试自动编号生成功能
- 测试供应商价格和外协价格的保存
- 测试文件上传和附件管理
- 测试质检设置的保存和读取

### 5.2 数据验证
- 检查JSON字段是否正确序列化
- 验证关联表数据是否正确保存
- 确认字段验证规则是否生效
- 测试错误处理和提示信息

## 6. 注意事项

1. **字段映射**：确保前端字段名与数据库字段名一致
2. **JSON处理**：复杂数据结构使用JSON格式存储，需要正确的序列化/反序列化
3. **事务管理**：涉及多表操作时使用数据库事务确保数据一致性
4. **验证规则**：根据业务需求设置合适的字段验证规则
5. **错误处理**：添加完整的异常处理和用户友好的错误提示
6. **性能优化**：为常用查询字段添加数据库索引

## 7. 后续优化建议

1. 添加物料档案的批量导入功能
2. 实现物料档案的版本管理
3. 添加物料档案的审批流程
4. 优化图片和附件的存储管理
5. 实现物料档案的数据统计和报表功能
