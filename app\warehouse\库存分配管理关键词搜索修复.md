# 库存分配管理关键词搜索修复

## 🔍 问题描述

库存分配管理页面（`http://tc.xinqiyu.cn:8830/warehouse/AllocationManage/index`）的关键词搜索功能不可用，无法通过产品名称、物料编码或业务单号进行搜索。

## 🎯 问题原因

### 前端配置正确
前端模板中已经正确配置了关键词搜索框：
```html
<input type="text" name="keywords" placeholder="输入关键字，产品名称/业务单号" class="layui-input" autocomplete="off" />
```

### 后端缺少处理逻辑
后端控制器 `AllocationManage.php` 的 `index()` 方法中没有处理 `keywords` 参数，导致搜索功能失效。

## 🛠️ 解决方案

### 添加关键词搜索逻辑

在 `app\warehouse\controller\AllocationManage.php` 的 `index()` 方法中添加关键词搜索处理：

```php
// 关键词搜索：支持产品名称、物料编码、业务单号搜索
if (!empty($param['keywords'])) {
    $keywords = trim($param['keywords']);
    
    // 查询匹配的产品ID
    $productIds = Db::name('product')
        ->where('title|material_code', 'like', '%' . $keywords . '%')
        ->column('id');
    
    // 添加关键词搜索条件（产品ID或业务单号）
    if (!empty($productIds)) {
        // 如果找到匹配的产品，搜索这些产品或匹配的业务单号
        $where[] = function($query) use ($productIds, $keywords) {
            $query->whereIn('product_id', $productIds)
                  ->whereOr('ref_no', 'like', '%' . $keywords . '%');
        };
    } else {
        // 如果没有匹配的产品，只搜索业务单号
        $where[] = ['ref_no', 'like', '%' . $keywords . '%'];
    }
}
```

## 📋 搜索功能说明

### 支持的搜索字段

1. **产品名称**：通过产品表的 `title` 字段匹配
2. **物料编码**：通过产品表的 `material_code` 字段匹配
3. **业务单号**：通过分配请求表的 `ref_no` 字段匹配

### 搜索逻辑

#### 1. 产品信息搜索
```php
// 先在产品表中搜索匹配的产品
$productIds = Db::name('product')
    ->where('title|material_code', 'like', '%' . $keywords . '%')
    ->column('id');
```

#### 2. 组合搜索条件
- **找到匹配产品**：搜索这些产品的分配请求 OR 业务单号包含关键词的请求
- **未找到匹配产品**：只搜索业务单号包含关键词的请求

#### 3. OR条件实现
```php
$where[] = function($query) use ($productIds, $keywords) {
    $query->whereIn('product_id', $productIds)          // 匹配的产品
          ->whereOr('ref_no', 'like', '%' . $keywords . '%'); // 或业务单号
};
```

## 🎨 用户体验

### 搜索框提示
```
输入关键字，产品名称/业务单号
```

### 搜索示例

#### 1. 按产品名称搜索
- 输入：`螺丝`
- 匹配：所有产品名称包含"螺丝"的分配请求

#### 2. 按物料编码搜索
- 输入：`M001`
- 匹配：所有物料编码包含"M001"的产品的分配请求

#### 3. 按业务单号搜索
- 输入：`PO2025`
- 匹配：所有业务单号包含"PO2025"的分配请求

#### 4. 组合搜索
- 输入：`螺丝`
- 结果：产品名称包含"螺丝"的分配请求 + 业务单号包含"螺丝"的分配请求

## 🔧 技术实现细节

### 查询优化

#### 1. 分步查询
```php
// 第一步：查询匹配的产品ID
$productIds = Db::name('product')
    ->where('title|material_code', 'like', '%' . $keywords . '%')
    ->column('id');

// 第二步：基于产品ID查询分配请求
if (!empty($productIds)) {
    // 有匹配产品时的查询逻辑
} else {
    // 无匹配产品时的查询逻辑
}
```

#### 2. 条件构建
```php
// 使用闭包函数构建OR条件
$where[] = function($query) use ($productIds, $keywords) {
    $query->whereIn('product_id', $productIds)
          ->whereOr('ref_no', 'like', '%' . $keywords . '%');
};
```

### 性能考虑

1. **索引优化**：确保 `product.title`、`product.material_code`、`inventory_allocation_request.ref_no` 字段有适当的索引
2. **查询分离**：先查询产品ID，避免复杂的JOIN操作
3. **条件优化**：根据是否找到匹配产品采用不同的查询策略

## 📊 测试验证

### 测试用例

#### 1. 产品名称搜索
- **输入**：产品名称的部分关键词
- **预期**：返回相关产品的所有分配请求

#### 2. 物料编码搜索
- **输入**：物料编码的部分字符
- **预期**：返回相关物料的所有分配请求

#### 3. 业务单号搜索
- **输入**：业务单号的部分字符
- **预期**：返回匹配的分配请求

#### 4. 无匹配结果
- **输入**：不存在的关键词
- **预期**：返回空列表

#### 5. 组合搜索
- **输入**：既匹配产品又匹配业务单号的关键词
- **预期**：返回两种匹配的合并结果

### 验证步骤

1. 访问 `http://tc.xinqiyu.cn:8830/warehouse/AllocationManage/index`
2. 在关键词搜索框中输入测试关键词
3. 点击搜索按钮
4. 验证搜索结果的准确性和完整性

## 🎯 修复效果

### 修复前
- ❌ 关键词搜索功能完全不可用
- ❌ 只能通过下拉框进行有限的筛选
- ❌ 无法快速定位特定的分配请求

### 修复后
- ✅ 支持产品名称、物料编码、业务单号的模糊搜索
- ✅ 智能的OR条件搜索，提高搜索覆盖率
- ✅ 用户友好的搜索体验
- ✅ 快速定位和筛选分配请求

## 🚀 后续优化建议

1. **搜索高亮**：在搜索结果中高亮显示匹配的关键词
2. **搜索历史**：记录用户的搜索历史，提供快速选择
3. **自动完成**：提供搜索建议和自动完成功能
4. **高级搜索**：支持多字段组合搜索和精确匹配

修复完成后，库存分配管理页面的搜索功能将完全可用，大大提高用户的操作效率。
