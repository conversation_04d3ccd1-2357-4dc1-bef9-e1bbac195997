<?php
namespace app\common\model\customer;

use think\Model;

class DeliveryLog extends Model
{
    // 设置表名
    protected $name = 'customer_delivery_log';
    
    // 设置主键
    protected $pk = 'id';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'create_time';
    protected $updateTime = false;
    
    /**
     * 关联发货指令
     */
    public function delivery()
    {
        return $this->belongsTo('Delivery', 'delivery_id', 'id');
    }
    
    /**
     * 关联操作人
     */
    public function operator()
    {
        return $this->belongsTo('app\\common\\model\\Admin', 'operator_id', 'id');
    }
} 