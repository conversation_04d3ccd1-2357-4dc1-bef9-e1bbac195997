# 采购订单收货状态问题修复

## 问题描述
收货全部收完了，但采购订单状态还是显示『部分入库』，而不是『已完成』。

## 问题分析

### 原始检查逻辑
在`app/warehouse/controller/Receipt.php`的`submit`方法中，检查采购订单是否全部收货完成的逻辑如下：

```php
// 原始逻辑：只统计合格品的收货数量
$qualifiedReceivedQuantity = Db::name('purchase_receipt_detail')
    ->alias('rd')
    ->join('purchase_receipt r', 'r.id = rd.receipt_id')
    ->where([
        'rd.order_detail_id' => $od->id,
        'rd.quality_status' => 2, // 只统计合格品
        'r.status' => 1 // 已入库的收货单
    ])
    ->sum('rd.quantity');
```

### 问题根因
1. **质检限制**：原逻辑只统计`quality_status = 2`（合格品）的收货数量
2. **业务需求**：实际业务中收货不做质检，全部入库
3. **状态不匹配**：如果收货明细的`quality_status`不是2，就不会被统计

## 解决方案

### 修改检查逻辑
将检查逻辑从"只统计合格品"改为"统计所有已入库的收货数量"：

```php
// 修改后的逻辑：统计所有已入库的收货数量
$totalReceivedQuantity = Db::name('purchase_receipt_detail')
    ->alias('rd')
    ->join('purchase_receipt r', 'r.id = rd.receipt_id')
    ->where([
        'rd.order_detail_id' => $od->id,
        'r.status' => 1 // 已入库的收货单
    ])
    ->sum('rd.quantity');
```

### 关键变化
1. **移除质检限制**：不再检查`quality_status`字段
2. **统计所有收货**：只要是已入库的收货单，都统计在内
3. **添加调试日志**：记录检查过程，便于问题排查

## 修复内容

### 1. 更新检查逻辑
```php
// 更新订单状态（统计所有已入库的收货数量）
$allReceived = true;
foreach ($orderDetails as $od) {
    // 获取该订单明细下所有已入库的收货数量（不区分质量状态）
    $totalReceivedQuantity = Db::name('purchase_receipt_detail')
        ->alias('rd')
        ->join('purchase_receipt r', 'r.id = rd.receipt_id')
        ->where([
            'rd.order_detail_id' => $od->id,
            'r.status' => 1 // 已入库的收货单
        ])
        ->sum('rd.quantity');
    
    trace('检查订单明细收货状态：订单明细ID=' . $od->id . 
          ', 订单数量=' . $od->quantity . 
          ', 已收货数量=' . $totalReceivedQuantity, 'debug');
    
    if ($totalReceivedQuantity < $od->quantity) {
        $allReceived = false;
        break;
    }
}
```

### 2. 添加状态日志
```php
if ($allReceived) {
    $order->status = 5; // 已完成
    trace('采购订单状态更新为已完成：订单ID=' . $order->id, 'info');
} else {
    $order->status = 4; // 部分入库
    trace('采购订单状态更新为部分入库：订单ID=' . $order->id, 'info');
}
```

### 3. 添加调试方法
新增`debugOrderStatus`方法，用于检查采购订单的收货状态：

```php
public function debugOrderStatus()
{
    $orderId = input('order_id/d', 0);
    // 返回详细的收货状态信息
}
```

## 调试方法使用

### 访问调试接口
```
GET /warehouse/Receipt/debugOrderStatus?order_id=123
```

### 返回信息
```json
{
    "code": 0,
    "data": {
        "order_id": 123,
        "order_status": 4,
        "order_status_text": "部分入库",
        "details": [
            {
                "detail_id": 456,
                "product_id": 789,
                "product_name": "产品名称",
                "order_quantity": 100,
                "received_quantity_in_detail": 100,
                "received_quantity_calculated": 100,
                "is_fully_received": true,
                "gap": 0
            }
        ]
    }
}
```

## 状态说明

### 采购订单状态
- `1`：草稿
- `2`：待审核
- `3`：已审核
- `4`：部分入库
- `5`：已完成
- `6`：已取消

### 收货单状态
- `0`：草稿
- `1`：已入库

### 质量状态（如果使用）
- `1`：待质检
- `2`：合格
- `3`：不合格

## 业务流程

### 正常收货流程
1. **创建收货单**：状态为草稿（0）
2. **填写收货明细**：记录收货数量
3. **提交收货单**：状态变为已入库（1）
4. **更新库存**：增加库存数量
5. **更新订单明细**：累加`received_quantity`
6. **检查订单状态**：
   - 如果所有明细都收货完成 → 订单状态变为已完成（5）
   - 如果还有明细未收货完成 → 订单状态变为部分入库（4）

### 问题场景
1. **收货完成但状态错误**：所有明细都收货了，但订单状态还是部分入库
2. **可能原因**：
   - 质检状态设置错误
   - 收货明细的`quality_status`不是2
   - 重复提交导致数据不一致

## 注意事项

### 1. 数据一致性
- 确保`purchase_order_detail.received_quantity`与实际收货数量一致
- 定期检查数据完整性

### 2. 状态同步
- 收货单提交后立即更新订单状态
- 避免状态更新的延迟

### 3. 质检流程
- 如果需要质检，需要相应调整检查逻辑
- 明确质检合格的标准和流程

### 4. 日志记录
- 记录详细的状态变更日志
- 便于问题排查和数据追踪

## 测试建议

### 1. 功能测试
- 创建采购订单
- 分批收货测试
- 全部收货测试
- 检查状态变更

### 2. 边界测试
- 超量收货
- 部分收货
- 多次收货

### 3. 数据验证
- 使用调试接口检查数据
- 对比订单数量与收货数量
- 验证状态逻辑正确性

通过这些修复，采购订单的收货状态应该能够正确反映实际的收货情况。
