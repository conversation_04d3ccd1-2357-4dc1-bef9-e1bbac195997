{extend name="../../base/view/common/base" /}

{block name="body"}
<div class="layui-row layui-col-space10">
    <!-- 左侧产品列表 -->
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <span style="color: #FF5722;">产品列表</span>
                <div class="layui-btn-group" style="float: right;">
                    <button type="button" class="layui-btn layui-btn-normal layui-btn-xs" onclick="uploadDrawing()">上传图纸</button>
                </div>
            </div>
            <div class="layui-card-body" style="padding: 10px;">
                <!-- 搜索框 -->
                <div class="layui-form-item">
                    <div class="layui-input-group">
                        <input type="text" id="productSearch" placeholder="请输入产品名称/编号" class="layui-input">
                        <div class="layui-input-split layui-input-suffix">
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="searchProduct()">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 产品树 -->
                <div class="product-tree">
                    <div class="product-item active" data-product-id="0">
                        <span class="product-name">全部产品</span>
                    </div>
                    <div id="productTreeContainer"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 中间工序列表 -->
    <div class="layui-col-md2">
        <div class="layui-card">
            <div class="layui-card-header">
                <span style="color: #FF5722;">工序列表</span>
            </div>
            <div class="layui-card-body" style="padding: 10px;">
                <!-- 工序搜索框 -->
                <div class="layui-form-item">
                    <input type="text" id="processSearch" placeholder="搜索工序" class="layui-input">
                </div>
                
                <!-- 工序列表 -->
                <div class="process-list">
                    <div class="process-item active" data-process-id="0">
                        <span class="process-name">全部工序</span>
                    </div>
                    <div id="processListContainer"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧图纸列表 -->
    <div class="layui-col-md7">
        <div class="layui-card">
            <div class="layui-card-header">
                <span style="color: #FF5722;">图纸列表</span>
                <div class="layui-btn-group" style="float: right;">
                    <button type="button" class="layui-btn layui-btn-sm" onclick="batchDelete()">关联图纸</button>
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="refreshList()">刷新</button>
                </div>
            </div>
            <div class="layui-card-body">
                <!-- 搜索栏 -->
                <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
                    <div class="layui-form-item">
                        <div class="layui-input-inline" style="width: 300px;">
                            <input type="text" name="keywords" placeholder="请输入产品/物料/工序名称" class="layui-input">
                        </div>
                        <div class="layui-input-inline">
                            <button type="button" class="layui-btn layui-btn-normal" lay-submit lay-filter="searchForm">搜索</button>
                        </div>
                    </div>
                </div>
                
                <!-- 图纸表格 -->
                <table class="layui-hide" id="drawingTable" lay-filter="drawingTable"></table>
            </div>
        </div>
    </div>
</div>

<!-- 表格操作按钮模板 -->
<script type="text/html" id="drawingTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 图纸状态模板 -->
<script type="text/html" id="drawingStatusTpl">
    {{# if(d.file_path){ }}
        <span class="layui-badge layui-bg-green">已上传</span>
    {{# } else { }}
        <span class="layui-badge layui-bg-gray">未上传</span>
    {{# } }}
</script>
{/block}

{block name="script"}
<script>
    const moduleInit = ['tool', 'table', 'form', 'tree'];
    function gouguInit() {
        var table = layui.table, form = layui.form, tool = layui.tool, tree = layui.tree;
        
        // 当前选中的产品ID和工序ID
        var currentProductId = 0;
        var currentProcessId = 0;
        
        // 初始化表格
        var tableIns = table.render({
            elem: '#drawingTable',
            url: '/Produce/drawing/getList',
            toolbar: false,
            defaultToolbar: [],
            cols: [[
                {type: 'checkbox', width: 50, fixed: 'left'},
                {field: 'name', title: '工序名称', width: 200},
                {field: 'product_name', title: '产品名称', width: 200},
                {field: 'material_name', title: '物料名称', width: 150},
                {field: 'drawing_no', title: '图纸编号', width: 120},
                {field: 'file_size_format', title: '文件大小', width: 100},
                {field: 'create_time_format', title: '创建时间', width: 160},
                {title: '操作', width: 120, align: 'center', fixed: 'right', toolbar: '#drawingTableBar'}
            ]],
            page: true,
            limit: 15,
            limits: [15, 30, 50, 100],
            loading: true,
            where: {
                product_id: currentProductId,
                process_id: currentProcessId
            }
        });
        
        // 加载产品列表
        loadProductList();
        
        function loadProductList() {
            tool.get("/engineering/product/datalist", {}, function (data) {
                if (data.code === 0) {
                    var html = '';
                    layui.each(data.data, function(index, item) {
                        html += '<div class="product-item" data-product-id="' + item.id + '">';
                        html += '<span class="product-name">' + item.title + '</span>';
                        if (item.material_code) {
                            html += '<span class="product-code">(' + item.material_code + ')</span>';
                        }
                        html += '</div>';
                    });
                    $('#productTreeContainer').html(html);
                }
            });
        }
        
        // 产品点击事件
        $(document).on('click', '.product-item', function() {
            $('.product-item').removeClass('active');
            $(this).addClass('active');
            
            currentProductId = $(this).data('product-id');
            
            // 加载对应的工序列表
            loadProcessList(currentProductId);
            
            // 重新加载表格数据
            reloadTable();
        });
        
        // 工序点击事件
        $(document).on('click', '.process-item', function() {
            $('.process-item').removeClass('active');
            $(this).addClass('active');
            
            currentProcessId = $(this).data('process-id');
            
            // 重新加载表格数据
            reloadTable();
        });
        
        // 加载工序列表
        function loadProcessList(productId) {
            tool.get('/Produce/process/getProcessByProduct', {product_id: productId}, function(res) {
                if (res.code === 0) {
                    var html = '<div class="process-item active" data-process-id="0"><span class="process-name">全部工序</span></div>';
                    layui.each(res.data, function(index, item) {
                        html += '<div class="process-item" data-process-id="' + item.id + '">';
                        html += '<span class="process-name">' + item.name + '</span>';
                        html += '</div>';
                    });
                    $('#processListContainer').html(html);
                    currentProcessId = 0; // 重置工序选择
                }
            });
        }
        
        // 重新加载表格
        function reloadTable() {
            tableIns.reload({
                where: {
                    product_id: currentProductId,
                    process_id: currentProcessId,
                    keywords: $('input[name="keywords"]').val()
                },
                page: {
                    curr: 1
                }
            });
        }
        
        // 搜索表单提交
        form.on('submit(searchForm)', function(data) {
            reloadTable();
            return false;
        });
        
        // 表格行工具事件
        table.on('tool(drawingTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                editDrawing(data.id);
            } else if (obj.event === 'del') {
                deleteDrawing(data.id);
            }
        });
        
        // 编辑图纸
        window.editDrawing = function(id) {
            tool.side('/Produce/drawing/add?id=' + id, '编辑图纸', '800px', '600px');
        };
        
        // 删除图纸
        window.deleteDrawing = function(id) {
            layer.confirm('确定要删除这个图纸吗？', {icon: 3, title: '提示'}, function(index) {
                tool.post('/Produce/drawing/delete', {id: id}, function(res) {
                    layer.msg(res.msg);
                    if (res.code === 0) {
                        tableIns.reload();
                    }
                });
                layer.close(index);
            });
        };
        
        // 上传图纸
        window.uploadDrawing = function() {
            tool.side('/Produce/drawing/add', '上传图纸', '800px', '600px');
        };
        
        // 批量删除
        window.batchDelete = function() {
            var checkStatus = table.checkStatus('drawingTable');
            if (checkStatus.data.length === 0) {
                layer.msg('请选择要删除的图纸');
                return;
            }
            
            layer.confirm('确定要删除选中的图纸吗？', {icon: 3, title: '提示'}, function(index) {
                var ids = [];
                layui.each(checkStatus.data, function(i, item) {
                    ids.push(item.id);
                });
                
                tool.post('/Produce/drawing/batchDelete', {ids: ids}, function(res) {
                    layer.msg(res.msg);
                    if (res.code === 0) {
                        tableIns.reload();
                    }
                });
                layer.close(index);
            });
        };
        
        // 刷新列表
        window.refreshList = function() {
            tableIns.reload();
            layer.msg('刷新成功');
        };
        
        // 产品搜索
        window.searchProduct = function() {
            var keyword = $('#productSearch').val();
            if (keyword) {
                $('.product-item').each(function() {
                    var productName = $(this).find('.product-name').text();
                    var productCode = $(this).find('.product-code').text();
                    if (productName.indexOf(keyword) !== -1 || productCode.indexOf(keyword) !== -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            } else {
                $('.product-item').show();
            }
        };
        
        // 工序搜索
        $('#processSearch').on('input', function() {
            var keyword = $(this).val();
            if (keyword) {
                $('.process-item').each(function() {
                    var processName = $(this).find('.process-name').text();
                    if (processName.indexOf(keyword) !== -1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            } else {
                $('.process-item').show();
            }
        });
        
        // 回车搜索
        $('#productSearch').keypress(function(e) {
            if (e.which === 13) {
                searchProduct();
            }
        });
        
        // 初始化加载工序列表
        loadProcessList(0);
        
        // 暴露表格实例给全局
        window.drawingTableIns = tableIns;
    }
</script>
{/block}

{block name="style"}
<style>
.product-tree, .process-list {
    max-height: 600px;
    overflow-y: auto;
}

.product-item, .process-item {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s;
}

.product-item:hover, .process-item:hover {
    background-color: #f8f9fa;
}

.product-item.active, .process-item.active {
    background-color: #1E9FFF;
    color: white;
}

.product-name, .process-name {
    flex: 1;
    font-size: 14px;
}

.product-code {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
}

.product-item.active .product-code {
    color: rgba(255, 255, 255, 0.8);
}

/* 搜索框样式 */
.layui-input-group {
    position: relative;
}

.layui-input-suffix {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    padding-right: 1px;
}

.layui-input-suffix .layui-btn {
    border-radius: 0 2px 2px 0;
}

/* 表格样式优化 */
.layui-table-cell {
    height: auto !important;
    white-space: normal;
}

/* 状态标签样式 */
.layui-badge {
    font-size: 12px;
}

/* 树形结构样式 */
#productTreeContainer {
    margin-top: 10px;
}

#productTreeContainer .layui-tree-txt {
    font-size: 14px;
}

#productTreeContainer .layui-tree-entry {
    height: 28px;
    line-height: 28px;
}

#productTreeContainer .layui-tree-entry:hover {
    background-color: #f8f9fa;
}
</style>
{/block}