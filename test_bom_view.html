<!DOCTYPE html>
<html>
<head>
    <title>BOM视图测试</title>
    <style>
        .form-container {
            padding: 20px;
            background: #fff;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1E9FFF;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .material-table {
            margin-top: 20px;
        }
        .layui-table {
            width: 100%;
            border-collapse: collapse;
        }
        .layui-table th, .layui-table td {
            border: 1px solid #e6e6e6;
            padding: 8px;
            text-align: left;
        }
        .layui-table th {
            background-color: #f2f2f2;
        }
        .bom-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: #fff;
        }
        .status-approved { background-color: #5FB878; }
        
        /* BOM层级样式 */
        .sub-bom-row {
            background-color: #f8f9fa;
        }
        .sub-bom-row.level-2 {
            background-color: #f1f3f4;
        }
        .sub-bom-row.level-3 {
            background-color: #e8eaed;
        }
        .bom-level-indent {
            padding-left: 20px;
        }
        .bom-level-indent.level-2 {
            padding-left: 40px;
        }
        .bom-level-indent.level-3 {
            padding-left: 60px;
        }
        .layui-btn {
            padding: 4px 8px;
            background: #1E9FFF;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .layui-btn-warm {
            background: #FFB800;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <!-- 基础资料 -->
        <div class="section-title">基础资料</div>
        <div style="display: flex; gap: 20px; margin-bottom: 15px;">
            <div>
                <span class="info-label">BOM编号：</span>
                <span class="info-value">BOM202508020001</span>
            </div>
            <div>
                <span class="info-label">BOM名称：</span>
                <span class="info-value">S-18音响BOM</span>
            </div>
            <div>
                <span class="info-label">产品名称：</span>
                <span class="info-value">S-18音响SS6</span>
            </div>
        </div>
        
        <div style="display: flex; gap: 20px; margin-bottom: 15px;">
            <div>
                <span class="info-label">客户名称：</span>
                <span class="info-value">深圳科技有限公司</span>
            </div>
            <div>
                <span class="info-label">审核状态：</span>
                <span class="bom-status status-approved">已审核</span>
            </div>
            <div>
                <span class="info-label">创建人：</span>
                <span class="info-value">张三</span>
            </div>
        </div>
        
        <div style="display: flex; gap: 20px; margin-bottom: 15px;">
            <div>
                <span class="info-label">创建时间：</span>
                <span class="info-value">2025-08-02 10:30:00</span>
            </div>
            <div>
                <span class="info-label">更新时间：</span>
                <span class="info-value">2025-08-02 15:45:00</span>
            </div>
        </div>
        
        <!-- 物料列表 -->
        <div class="material-table">
            <div class="section-title">物料列表</div>
            
            <table class="layui-table" id="materialTable">
                <thead>
                    <tr>
                        <th width="60">ID</th>
                        <th width="80">BOM等级</th>
                        <th width="150">物料编号</th>
                        <th width="200">物料名称</th>
                        <th width="80">图片</th>
                        <th width="100">物料分类</th>
                        <th width="100">规格</th>
                        <th width="80">型号</th>
                        <th width="80">数量</th>
                        <th width="80">损耗率</th>
                        <th width="100">物料来源</th>
                        <th width="100">操作</th>
                    </tr>
                </thead>
                <tbody id="materialTableBody">
                    <tr data-id="1" data-material-id="789" data-level="1" class="material-row">
                        <td>1</td>
                        <td>一级</td>
                        <td>M001</td>
                        <td>电阻器</td>
                        <td><img src="/static/images/default.png" width="40" height="40" style="background: #ccc;"></td>
                        <td>电子元件</td>
                        <td>10K欧姆</td>
                        <td>RES-10K</td>
                        <td>2</td>
                        <td>5%</td>
                        <td>自购</td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-xs" onclick="expandBom(this, '789')">展开</button>
                        </td>
                    </tr>
                    <tr data-id="2" data-material-id="790" data-level="1" class="material-row">
                        <td>2</td>
                        <td>一级</td>
                        <td>M002</td>
                        <td>电容器</td>
                        <td><img src="/static/images/default.png" width="40" height="40" style="background: #ccc;"></td>
                        <td>电子元件</td>
                        <td>100uF</td>
                        <td>CAP-100UF</td>
                        <td>1</td>
                        <td>3%</td>
                        <td>自购</td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-xs" onclick="expandBom(this, '790')">展开</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 操作按钮 -->
        <div style="margin-top: 20px; text-align: center;">
            <button type="button" class="layui-btn">编辑</button>
            <button type="button" class="layui-btn">复制</button>
            <button type="button" class="layui-btn">导出</button>
            <button type="button" class="layui-btn">打印</button>
            <button type="button" class="layui-btn">返回</button>
        </div>
    </div>

    <script>
        // 重新编号物料列表
        function reorderMaterials() {
            const rows = document.querySelectorAll('#materialTableBody tr');
            rows.forEach((row, index) => {
                row.querySelector('td:first-child').textContent = index + 1;
            });
        }
        
        // 获取层级文本
        function getLevelText(level) {
            const levelMap = {1: '一级', 2: '二级', 3: '三级', 4: '四级', 5: '五级'};
            return levelMap[level] || level + '级';
        }
        
        // 获取缩进
        function getIndent(level) {
            let indent = '';
            for (let i = 1; i < level; i++) {
                indent += '&nbsp;&nbsp;&nbsp;&nbsp;';
            }
            return indent;
        }
        
        // 展开下级BOM
        function expandBom(btn, materialId) {
            const row = btn.closest('tr');
            const currentLevel = parseInt(row.dataset.level) || 1;
            const nextLevel = currentLevel + 1;

            // 检查是否已经展开
            if (btn.textContent === '收起') {
                // 收起下级BOM
                collapseBom(row, materialId);
                btn.textContent = '展开';
                btn.classList.remove('layui-btn-warm');
                reorderMaterials();
                return;
            }

            // 模拟获取下级BOM数据
            const mockSubBomData = [
                {
                    id: 101,
                    material_id: 1001,
                    material_code: 'M001-1',
                    material_name: '电阻器组件',
                    material_category: '电子元件',
                    specifications: '5K欧姆',
                    model: 'RES-5K',
                    quantity: 1,
                    loss_rate: 2,
                    material_source: '自制'
                },
                {
                    id: 102,
                    material_id: 1002,
                    material_code: 'M001-2',
                    material_name: '电阻器外壳',
                    material_category: '塑料件',
                    specifications: '标准外壳',
                    model: 'SHELL-STD',
                    quantity: 1,
                    loss_rate: 1,
                    material_source: '自购'
                }
            ];

            // 添加下级BOM到表格
            addSubBomToTable(mockSubBomData, row, nextLevel, materialId);
            btn.textContent = '收起';
            btn.classList.add('layui-btn-warm');
            reorderMaterials();
        }

        // 收起下级BOM
        function collapseBom(parentRow, parentMaterialId) {
            let nextRow = parentRow.nextElementSibling;
            while (nextRow && nextRow.dataset.parent == parentMaterialId) {
                const currentRow = nextRow;
                nextRow = nextRow.nextElementSibling;
                currentRow.remove();
            }
        }

        // 添加下级BOM到表格
        function addSubBomToTable(bomData, parentRow, level, parentMaterialId) {
            const levelText = getLevelText(level);
            const indent = getIndent(level);

            bomData.forEach((bom, index) => {
                const row = document.createElement('tr');
                row.dataset.id = bom.id;
                row.dataset.level = level;
                row.dataset.parent = parentMaterialId;
                row.dataset.materialId = bom.material_id;
                row.className = `sub-bom-row level-${level}`;
                
                row.innerHTML = `
                    <td></td>
                    <td class="bom-level-indent level-${level}">${indent}${levelText}</td>
                    <td>${bom.material_code}</td>
                    <td>${bom.material_name}</td>
                    <td><img src="/static/images/default.png" width="40" height="40" style="background: #ccc;"></td>
                    <td>${bom.material_category || ''}</td>
                    <td>${bom.specifications || ''}</td>
                    <td>${bom.model || ''}</td>
                    <td>${bom.quantity}</td>
                    <td>${bom.loss_rate}%</td>
                    <td>${bom.material_source}</td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="expandBom(this, '${bom.material_id}')">展开</button>
                    </td>
                `;
                
                parentRow.insertAdjacentElement('afterend', row);
                parentRow = row;
            });
        }
        
        // 初始化时重新编号
        reorderMaterials();
    </script>
</body>
</html>
