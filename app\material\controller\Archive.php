<?php

namespace app\material\controller;

use app\base\BaseController;
use app\material\model\Archive as ArchiveModel;
use app\material\model\Category as CategoryModel;
use app\material\validate\Archive as ArchiveValidate;
use app\engineering\model\Product as ProductModel;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Archive extends BaseController
{
    /**
     * 物料档案列表页面
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            
            
             if (!empty($param['keywords'])) {
                $where[] = ['p.title|p.material_code|p.specs', 'like', '%' . $param['keywords'] . '%'];
            }
            if (isset($param['status']) && $param['status'] !== '') {
                $where[] = ['p.status', '=', $param['status']];
            }
            if (!empty($param['category_id'])) {
                // 检查是否需要包含子分类
                $includeChildren = isset($param['include_children']) ? $param['include_children'] : true;

                if ($includeChildren) {
                    // 递归查询该分类及其所有子分类的物料
                    $cate_id_array = get_cate_son('product_cate', $param['category_id']);
                    $where[] = ['p.cate_id', 'in', $cate_id_array];
                } else {
                    // 只查询当前分类的物料，不包含子分类
                    $where[] = ['p.cate_id', '=', $param['category_id']];
                }
            }
            
            // 直接使用数据库查询获取分页数据
            $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
            $order = empty($param['order']) ? 'p.id desc' : $param['order'];
            
            $list = Db::name('product')
                ->field('p.*, pc.title as cate, pc.id as cate_id, pc.pid as cate_pid,p.unit,p.specs')
                ->alias('p')
                ->leftJoin('oa_product_cate pc', 'pc.id = p.cate_id')
                ->where($where)
                ->order($order)
                ->paginate(['list_rows' => $rows])
                ->each(function ($item, $key) {
                    $item['admin_name'] = Db::name('Admin')->where('id', $item['admin_id'])->value('name') ?? '';

                    // 获取完整的分类路径
                    if (!empty($item['cate_id'])) {
                        $item['category_name'] = $item['cate'] ?? '未分类';
                    } else {
                        $item['category_name'] = '未分类';
                    }

                    // 统计产品关联的有效BOM数量
                    try {
                        $validBomCount = Db::name('material_bom')->where('product_id', $item['id'])
                            ->where('status', 1)
                            ->count();
                        $item['has_bom'] = $validBomCount > 0 ? 1 : 0;
                    } catch (\Exception $e) {
                        // 如果BOM表不存在，设置为0
                        $item['has_bom'] = 0;
                    }

                    return $item;
                });
            
            return table_assign(0, '', $list);
        } else {
            // 只获取第一级分类，子分类通过异步加载
            $categories = CategoryModel::where('status', 1)
                ->where('pid', 0)
                ->order('sort asc, id asc')
                ->select()
                ->toArray();

            // 检查每个分类是否有子分类
            foreach ($categories as &$category) {
                $hasChildren = CategoryModel::where('pid', $category['id'])
                    ->where('status', 1)
                    ->count() > 0;
                $category['has_children'] = $hasChildren;
            }

            View::assign('categoryTree', $categories);
            return view();
        }
    }
    
    /**
     * 添加/编辑物料档案
     */
    public function add()
    {
        $param = get_params();
        if (request()->isPost()) {
            // 调试信息
            \think\facade\Log::info('Archive add/edit params: ' . json_encode($param));
  
            if (!empty($param['id']) && $param['id'] > 0) {
                
                // 编辑时，如果没有物料编号或启用了自动编号，则生成编号
                if ((empty($param['material_code']) || $param['material_code'] === 'AUTO_GENERATE') && !empty($param['use_system_code'])) {
                    $param['material_code'] = $this->generateMaterialCode();
                    \think\facade\Log::info('Generated material code for edit: ' . $param['material_code']);
                }

                // 验证物料编号
                if (empty($param['material_code'])) {
                    \think\facade\Log::error('Material code is empty for edit');
                    return to_assign(1, "物料编号不能为空");
                }

                try {
                    // 设置默认值，避免验证失败
                    $param['unit'] = $param['unit'] ?? '';
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;
                    
                    validate(ArchiveValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }

                $param['update_time'] = time();
                $param['update_id'] = session('admin.id');

                // 处理JSON字段
                $this->processJsonFields($param);

                // 开启事务
                Db::startTrans();
                try {
                    // 设置默认值，避免验证失败
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;

                    validate(ArchiveValidate::class)->scene('edit')->check($param);

                    $materialId = $param['id'];

                    // 准备更新数据，排除不属于product表的字段
                    $updateData = $param;
                    unset($updateData['id']);
                    unset($updateData['supplier_prices']);
                    unset($updateData['outsource_prices']);

                    // 更新物料档案
                    $updateResult = Db::name('product')->where('id', $materialId)->update($updateData);
                    if ($updateResult === false) {
                        throw new \Exception('物料档案更新失败');
                    }
                    
                    // 删除旧的供应商价格数据
                    Db::name('material_supplier_price')->where('material_id', $materialId)->delete();
                    
                    // 处理新的供应商价格数据
                    if (!empty($param['supplier_prices']) && is_array($param['supplier_prices'])) {
                        foreach ($param['supplier_prices'] as $supplierPrice) {
                            if (!empty($supplierPrice['supplier_id'])) {
                                $supplierPriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $supplierPrice['supplier_id'],
                                    'supplier_code' => $supplierPrice['supplier_code'] ?? '',
                                    'priority' => $supplierPrice['priority'] ?? '',
                                    'min_qty' => $supplierPrice['min_qty'] ?? 0,
                                    'max_qty' => $supplierPrice['max_qty'] ?? 0,
                                    'tax_price' => $supplierPrice['tax_price'] ?? 0,
                                    'tax_rate' => $supplierPrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $supplierPrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('material_supplier_price')->insert($supplierPriceData);
                            }
                        }
                    }
                    
                    // 删除旧的外协价格数据
                    Db::name('material_outsource_price')->where('material_id', $materialId)->delete();
                    
                    // 处理新的外协价格数据
                    if (!empty($param['outsource_prices']) && is_array($param['outsource_prices'])) {
                        foreach ($param['outsource_prices'] as $outsourcePrice) {
                            if (!empty($outsourcePrice['supplier_id'])) {
                                $outsourcePriceData = [
                                    'material_id' => $materialId,
                                    'supplier_id' => $outsourcePrice['supplier_id'],
                                    'outsource_code' => $outsourcePrice['outsource_code'] ?? '',
                                    'priority' => $outsourcePrice['priority'] ?? '',
                                    'min_qty' => $outsourcePrice['min_qty'] ?? 0,
                                    'max_qty' => $outsourcePrice['max_qty'] ?? 0,
                                    'tax_price' => $outsourcePrice['tax_price'] ?? 0,
                                    'tax_rate' => $outsourcePrice['tax_rate'] ?? 0,
                                    'no_tax_price' => $outsourcePrice['no_tax_price'] ?? 0,
                                    'create_time' => time(),
                                    'update_time' => time()
                                ];
                                Db::name('material_outsource_price')->insert($outsourcePriceData);
                            }
                        }
                    }
                    
                    // 提交事务
                    Db::commit();
                    add_log('edit', $param['id'], $param);
             return json(['code' => 0, 'msg' => '物料档案编辑成功', 'action' => '', 'url' => '', 'data' => ['id' => $param['id']]]);

                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    \think\facade\Log::error('Archive edit error: ' . $e->getMessage());
                    return to_assign(1, "物料档案编辑失败：" . $e->getMessage());
                }
            } else {
               
                // 新增时，如果没有物料编号或启用了自动编号，则生成编号
                if ((empty($param['material_code']) || $param['material_code'] === 'AUTO_GENERATE') && !empty($param['use_system_code'])) {
                    $param['material_code'] = $this->generateMaterialCode();
                    \think\facade\Log::info('Generated material code for add: ' . $param['material_code']);
                }
 
                // 验证物料编号
                if (empty($param['material_code'])) {
                    \think\facade\Log::error('Material code is empty for add. Params: ' . json_encode([
                        'material_code' => $param['material_code'] ?? 'not set',
                        'auto_material_code' => $param['auto_material_code'] ?? 'not set'
                    ]));
                    return to_assign(1, "物料编号不能为空");
                }

                try {
                    // 设置默认值，避免验证失败
                    $param['unit'] = $param['unit'] ?? '';
                    $param['default_warehouse'] = $param['default_warehouse'] ?? 0;
                    $param['min_order_qty'] = $param['min_order_qty'] ?? 0;
                    $param['min_package_qty'] = $param['min_package_qty'] ?? 0;
                    
                    validate(ArchiveValidate::class)->scene('add')->check($param);
                     
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }

                $param['create_time'] = time();
                $param['update_time'] = time();
                $param['admin_id'] = $this->uid; // 如果获取不到用户ID，默认使用1

                // 处理JSON字段
                $this->processJsonFields($param);

                // 记录处理后的参数
                \think\facade\Log::info('Processed params for insert: ' . json_encode($param, JSON_UNESCAPED_UNICODE));

                // 开启事务
                Db::startTrans();
                try {
                    // 准备基本的插入数据，只包含必需字段
                    $insertData = [
                        'title' => $param['title'],
                        'cate_id' => $param['cate_id'],
                        'material_code' => $param['material_code'],
                        'unit' => $param['unit'],
                        'source_type' => $param['source_type'],
                        'status' => $param['status'],
                        'admin_id' => $param['admin_id'],
                        'create_time' => $param['create_time'],
                        'update_time' => $param['update_time'],
                    ];

                    // 添加可选字段（如果存在）
                    $optionalFields = [
                        'specs', 'producer', 'purchase_cycle', 'stock', 'description', 'remark',
                        'default_warehouse', 'min_order_qty', 'min_package_qty', 'material_level',
                        'material_source', 'category', 'model', 'color', 'quality_management',
                        'quality_exempt', 'quality_settings', 'reference_cost', 'sales_price',
                        'min_sales_price', 'max_sales_price', 'material_images', 'attachments',
                        'process_template_id'
                    ];

                    foreach ($optionalFields as $field) {
                        if (isset($param[$field])) {
                            $insertData[$field] = $param[$field];
                        }
                    }

                    \think\facade\Log::info('Final insert data: ' . json_encode($insertData, JSON_UNESCAPED_UNICODE));

                    // 创建物料档案
                    $materialId = Db::name('product')->insertGetId($insertData);
                    if (!$materialId) {
                        throw new \Exception('物料档案创建失败');
                    }

                    \think\facade\Log::info('Material created with ID: ' . $materialId);
                    
                    // 处理供应商价格数据
                    if (!empty($param['supplier_prices']) && is_array($param['supplier_prices'])) {
                        foreach ($param['supplier_prices'] as $supplierPrice) {
                            if (!empty($supplierPrice['supplier_id'])) {
                                try {
                                    $supplierPriceData = [
                                        'material_id' => $materialId,
                                        'supplier_id' => $supplierPrice['supplier_id'],
                                        'supplier_code' => $supplierPrice['supplier_code'] ?? '',
                                        'priority' => $supplierPrice['priority'] ?? '',
                                        'min_qty' => $supplierPrice['min_qty'] ?? 0,
                                        'max_qty' => $supplierPrice['max_qty'] ?? 0,
                                        'tax_price' => $supplierPrice['tax_price'] ?? 0,
                                        'tax_rate' => $supplierPrice['tax_rate'] ?? 0,
                                        'no_tax_price' => $supplierPrice['no_tax_price'] ?? 0,
                                        'create_time' => time(),
                                        'update_time' => time()
                                    ];
                                    Db::name('material_supplier_price')->insert($supplierPriceData);
                                } catch (\Exception $e) {
                                    \think\facade\Log::warning('Supplier price insert failed: ' . $e->getMessage());
                                    // 供应商价格插入失败不影响主流程，只记录日志
                                }
                            }
                        }
                    }
                    
                    // 处理外协价格数据
                    if (!empty($param['outsource_prices']) && is_array($param['outsource_prices'])) {
                        foreach ($param['outsource_prices'] as $outsourcePrice) {
                            if (!empty($outsourcePrice['supplier_id'])) {
                                try {
                                    $outsourcePriceData = [
                                        'material_id' => $materialId,
                                        'supplier_id' => $outsourcePrice['supplier_id'],
                                        'outsource_code' => $outsourcePrice['outsource_code'] ?? '',
                                        'priority' => $outsourcePrice['priority'] ?? '',
                                        'min_qty' => $outsourcePrice['min_qty'] ?? 0,
                                        'max_qty' => $outsourcePrice['max_qty'] ?? 0,
                                        'tax_price' => $outsourcePrice['tax_price'] ?? 0,
                                        'tax_rate' => $outsourcePrice['tax_rate'] ?? 0,
                                        'no_tax_price' => $outsourcePrice['no_tax_price'] ?? 0,
                                        'create_time' => time(),
                                        'update_time' => time()
                                    ];
                                    Db::name('material_outsource_price')->insert($outsourcePriceData);
                                } catch (\Exception $e) {
                                    \think\facade\Log::warning('Outsource price insert failed: ' . $e->getMessage());
                                    // 外协价格插入失败不影响主流程，只记录日志
                                }
                            }
                        }
                    }
                    
                    // 提交事务
                    Db::commit();

                    // 记录操作日志（如果函数存在）
                    if (function_exists('add_log')) {
                        try {
                            add_log('add', $materialId, $param);
                        } catch (\Exception $e) {
                            \think\facade\Log::warning('Add log failed: ' . $e->getMessage());
                        }
                    }

                    return json(['code' => 0, 'msg' => '物料档案添加成功', 'action' => '', 'url' => '', 'data' => ['id' => $materialId]]);
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    \think\facade\Log::error('Archive add error: ' . $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine());
                    return to_assign(1, "物料档案添加失败：" . $e->getMessage());
                } catch (\Throwable $e) {
                    // 回滚事务
                    Db::rollback();
                    \think\facade\Log::error('Archive add throwable error: ' . $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine());
                    return to_assign(1, "物料档案添加失败：" . $e->getMessage());
                }
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = [];
            $supplierPrices = [];
            $outsourcePrices = [];

            if ($id > 0) {
                $detail = ArchiveModel::where('id', $id)->find();
                if (empty($detail)) {
                    throw new \think\exception\HttpException(404, '物料档案不存在');
                }
                $detail = $detail->toArray();

                // 获取供应商价格数据
                $supplierPrices = Db::name('material_supplier_price')
                    ->alias('msp')
                    ->leftJoin('purchase_supplier ps', 'msp.supplier_id = ps.id')
                    ->where('msp.material_id', $id)
                    ->field('msp.*, ps.name as supplier_name')
                    ->select()
                    ->toArray();

                // 获取外协价格数据
                $outsourcePrices = Db::name('material_outsource_price')
                    ->alias('mop')
                    ->leftJoin('purchase_supplier ps', 'mop.supplier_id = ps.id')
                    ->where('mop.material_id', $id)
                    ->field('mop.*, ps.name as supplier_name')
                    ->select()
                    ->toArray();
            }
            
            // 获取物料分类
            $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select()->toArray();

            // 获取供应商数据
            $suppliers = Db::name('purchase_supplier')->where('status', 1)->field('id,code,name')->order('id desc')->select()->toArray();

            // 获取仓库数据
            $warehouses = Db::name('warehouse')->where('status', 1)->field('id,code,name')->order('is_default desc, id asc')->select()->toArray();

            // 获取单位数据
            $units = Db::name('unit')->where('status', 1)->field('id,name,precision,type')->order('id asc')->select()->toArray();

            View::assign('detail', $detail);
            View::assign('categories', $categories);
            View::assign('suppliers', $suppliers);
            View::assign('warehouses', $warehouses);
            View::assign('units', $units);
            View::assign('supplier_prices', $supplierPrices);
            View::assign('outsource_prices', $outsourcePrices);
            View::assign('id', $id);
            return view();
        }
    }

    /**
     * 查看物料档案详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        // 直接使用Db查询获取原始数据，避免模型的JSON自动转换
        $detail = Db::name('product')->where('id', $id)->find();
        if (empty($detail)) {
            throw new \think\exception\HttpException(404, '物料档案不存在');
        }

        // 获取分类名称
        if (!empty($detail['cate_id'])) {
            $categoryInfo = Db::name('product_cate')->where('id', $detail['cate_id'])->find();
            $detail['category_name'] = $categoryInfo ? $categoryInfo['title'] : '-';
        } else {
            $detail['category_name'] = '-';
        }

        // 获取供应商价格数据
        $supplierPrices = Db::name('material_supplier_price')
            ->alias('msp')
            ->leftJoin('purchase_supplier s', 'msp.supplier_id = s.id')
            ->where('msp.material_id', $id)
            ->field('msp.*, s.name as supplier_name')
            ->select()
            ->toArray();

        // 获取外协价格数据
        $outsourcePrices = Db::name('material_outsource_price')
            ->alias('mop')
            ->leftJoin('purchase_supplier s', 'mop.supplier_id = s.id')
            ->where('mop.material_id', $id)
            ->field('mop.*, s.name as supplier_name')
            ->select()
            ->toArray();

        View::assign('detail', $detail);
        View::assign('supplierPrices', $supplierPrices);
        View::assign('outsourcePrices', $outsourcePrices);
        return view();
    }
    
    /**
     * 删除物料档案
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id <= 0) {
            return json(['code' => 1, 'msg' => '参数错误', 'action' => '', 'url' => '', 'data' => []]);
        }

        $detail = ArchiveModel::where('id', $id)->find();
        if (empty($detail)) {
            return json(['code' => 1, 'msg' => '物料档案不存在', 'action' => '', 'url' => '', 'data' => []]);
        }

        // 开启事务
        Db::startTrans();
        try {
            // 删除相关的供应商价格数据
            Db::name('material_supplier_price')->where('material_id', $id)->delete();

            // 删除相关的外协价格数据
            Db::name('material_outsource_price')->where('material_id', $id)->delete();

            // 删除物料档案
            $res = ArchiveModel::destroy($id);
            if (!$res) {
                throw new \Exception('物料档案删除失败');
            }

            // 提交事务
            Db::commit();

            // 记录日志
            if (function_exists('add_log')) {
                try {
                    add_log('delete', $id);
                } catch (\Exception $e) {
                    \think\facade\Log::warning('Add log failed: ' . $e->getMessage());
                }
            }

            return json(['code' => 0, 'msg' => '物料档案删除成功', 'action' => '', 'url' => '', 'data' => []]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            \think\facade\Log::error('Archive delete error: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '物料档案删除失败：' . $e->getMessage(), 'action' => '', 'url' => '', 'data' => []]);
        }
    }
    
    /**
     * 获取分类列表
     */
    public function getCategoryList()
    {
        $categories = CategoryModel::where('status', 1)->order('sort asc, id asc')->select();
        return json(['code' => 0, 'data' => $categories]);
    }

    /**
     * 获取分类完整路径
     */
    private function getCategoryPath($categoryId)
    {
        if (empty($categoryId)) {
            return '';
        }

        $path = [];
        $currentId = $categoryId;

        // 最多查询5级，防止无限循环
        $maxLevel = 5;
        $level = 0;

        while ($currentId && $level < $maxLevel) {
            $category = Db::name('product_cate')
                ->where('id', $currentId)
                ->where('status', 1)
                ->find();

            if (!$category) {
                break;
            }

            array_unshift($path, $category['title']);
            $currentId = $category['pid'];
            $level++;
        }

        return implode(' > ', $path);
    }
    
    /**
     * 构建分类树形结构
     */
    private function buildCategoryTree($categories, $pid = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['pid'] == $pid) {
                $children = $this->buildCategoryTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                    $category['has_children'] = true;
                } else {
                    $category['has_children'] = false;
                }
                $tree[] = $category;
            }
        }
        return $tree;
    }
    
    /**
     * 导入物料档案
     */
    public function import()
    {
        if (request()->isPost()) {
            $param = get_params();
            // 处理导入逻辑
            return to_assign(0, "导入成功");
        } else {
            return view();
        }
    }

    /**
     * 打印二维码
     */
    public function printBarcode()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }

        // 获取物料信息
        $material = Db::name('oa_product')
            ->field('p.*, pc.title as cate')
            ->alias('p')
            ->leftJoin('oa_product_cate pc', 'pc.id = p.cate_id')
            ->where('p.id', $id)
            ->find();

        if (!$material) {
            return to_assign(1, "物料不存在");
        }

        // 构建二维码内容
        $qrContent = "物料名称:" . $material['title'] . "\n" .
                    "物料编号:" . $material['material_code'] . "\n" .
                    "物料规格:" . ($material['specs'] ?: '-') . "\n" .
                    "单位:" . ($material['unit'] ?: '-') . "\n" .
                    "打印时间:" . date('Y-m-d H:i') . "\n" .
                    "备注:";

        // 使用endroid/qr-code库直接生成二维码base64
        $qrImageUrl = '';
        if (class_exists('Endroid\QrCode\QrCode')) {
            try {
                $qrCode = new \Endroid\QrCode\QrCode($material['material_code']);
                $qrCode->setSize(180);
                $qrCode->setMargin(10);

                $writer = new \Endroid\QrCode\Writer\PngWriter();
                $result = $writer->write($qrCode);
                $qrImageUrl = $result->getDataUri();
            } catch (\Exception $e) {
                // 如果生成失败，使用URL方式
                $qrImageUrl = '/material/archive/generateQrCode?content=' . urlencode($qrContent);
            }
        } else {
            // 如果库不存在，使用URL方式
            $qrImageUrl = '/material/archive/generateQrCode?content=' . urlencode($qrContent);
        }

        // 返回打印页面
        View::assign('material', $material);
        View::assign('qrImageUrl', $qrImageUrl);
        View::assign('printTime', date('Y-m-d H:i'));

        return view('print_barcode');
    }

    /**
     * 打印条形码
     */
    public function printBarcodeID()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if ($id <= 0) {
            return to_assign(1, "参数错误");
        }

        // 获取物料信息
        $material = Db::name('oa_product')
            ->field('p.*, pc.title as cate')
            ->alias('p')
            ->leftJoin('oa_product_cate pc', 'pc.id = p.cate_id')
            ->where('p.id', $id)
            ->find();

        if (!$material) {
            return to_assign(1, "物料不存在");
        }

        // 生成条形码（使用物料编码）
        $barcodeContent = $material['material_code'];

        // 返回打印页面
        View::assign('material', $material);
        View::assign('barcodeContent', $barcodeContent);
        View::assign('printTime', date('Y-m-d H:i'));

        return view('print_barcode_id');
    }

    /**
     * 生成二维码图片
     */
    public function generateQrCode()
    {
        $content = request()->param('content', '');
        if (empty($content)) {
            $content = 'No Content';
        }

        try {
            // 使用endroid/qr-code库生成二维码
            if (class_exists('Endroid\QrCode\QrCode')) {
                $qrCode = new \Endroid\QrCode\QrCode($content);
                $qrCode->setSize(180);
                $qrCode->setMargin(10);

                $writer = new \Endroid\QrCode\Writer\PngWriter();
                $result = $writer->write($qrCode);

                // 输出图片
                header('Content-Type: image/png');
                header('Cache-Control: no-cache, no-store, must-revalidate');
                header('Pragma: no-cache');
                header('Expires: 0');
                echo $result->getString();
                exit;
            } else {
                // 如果库不存在，生成占位图
                $this->generatePlaceholderImage();
            }
        } catch (\Exception $e) {
            // 如果生成失败，生成占位图
            $this->generatePlaceholderImage();
        }
    }

    /**
     * 生成占位图片
     */
    private function generatePlaceholderImage()
    {
        // 创建一个180x180的图片
        $image = imagecreate(180, 180);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);

        // 填充背景
        imagefill($image, 0, 0, $white);

        // 画边框
        imagerectangle($image, 0, 0, 179, 179, $black);

        // 添加文字
        imagestring($image, 3, 60, 85, 'QR Code', $black);

        // 输出图片
        header('Content-Type: image/png');
        imagepng($image);
        imagedestroy($image);
        exit;
    }

    /**
     * 批量打印二维码
     */
    public function batchPrintBarcode()
    {
        $param = get_params();
        $ids = isset($param['ids']) ? $param['ids'] : '';

        if (empty($ids)) {
            return to_assign(1, "请选择要打印的物料");
        }

        $idArray = explode(',', $ids);

        // 获取物料信息
        $materials = Db::name('oa_product')
            ->field('p.*, pc.title as cate')
            ->alias('p')
            ->leftJoin('oa_product_cate pc', 'pc.id = p.cate_id')
            ->where('p.id', 'in', $idArray)
            ->select()
            ->toArray();

        if (empty($materials)) {
            return to_assign(1, "未找到物料信息");
        }

        // 为每个物料构建二维码内容和图片URL
        foreach ($materials as &$material) {
            $qrContent = "物料名称:" . $material['title'] . "\n" .
                        "物料编号:" . $material['material_code'] . "\n" .
                        "物料规格:" . ($material['specs'] ?: '-') . "\n" .
                        "单位:" . ($material['unit'] ?: '-') . "\n" .
                        "打印时间:" . date('Y-m-d H:i') . "\n" .
                        "备注:";
            $material['qr_content'] = $qrContent;
            $material['qr_image_url'] = '/material/archive/generateQrCode?content=' . urlencode($qrContent);
        }

        View::assign('materials', $materials);
        View::assign('printTime', date('Y-m-d H:i'));

        return view('batch_print_barcode');
    }
    
    /**
     * 导出物料档案
     */
    public function export()
    {
        $param = get_params();
        $where = [];
        
        if (!empty($param['category_id'])) {
            $where[] = ['category_id', '=', $param['category_id']];
        }
        
        if (!empty($param['keywords'])) {
            $where[] = ['material_name|material_code', 'like', '%' . $param['keywords'] . '%'];
        }
        
        $data = ArchiveModel::where($where)->with(['category'])->select()->toArray();
        
        // 导出Excel逻辑
        return to_assign(0, "导出成功");
    }
    
    /**
     * 批量删除
     */
    public function batchDelete()
    {
        $param = get_params();
        $ids = isset($param['ids']) ? $param['ids'] : '';
        if (empty($ids)) {
            return json(['code' => 1, 'msg' => '请选择要删除的物料档案', 'action' => '', 'url' => '', 'data' => []]);
        }

        $ids = explode(',', $ids);

        // 开启事务
        Db::startTrans();
        try {
            // 删除相关的供应商价格数据
            Db::name('material_supplier_price')->where('material_id', 'in', $ids)->delete();

            // 删除相关的外协价格数据
            Db::name('material_outsource_price')->where('material_id', 'in', $ids)->delete();

            // 批量删除物料档案
            $res = ArchiveModel::destroy($ids);
            if (!$res) {
                throw new \Exception('批量删除失败');
            }

            // 提交事务
            Db::commit();

            // 记录日志
            if (function_exists('add_log')) {
                try {
                    add_log('batch_delete', 0, ['ids' => $ids]);
                } catch (\Exception $e) {
                    \think\facade\Log::warning('Add log failed: ' . $e->getMessage());
                }
            }

            return json(['code' => 0, 'msg' => '批量删除成功', 'action' => '', 'url' => '', 'data' => []]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            \think\facade\Log::error('Archive batch delete error: ' . $e->getMessage());
            return json(['code' => 1, 'msg' => '批量删除失败：' . $e->getMessage(), 'action' => '', 'url' => '', 'data' => []]);
        }
    }

    /**
     * 处理JSON字段
     */
    private function processJsonFields(&$param)
    {
        // 处理质检设置 - 从POST参数中收集所有质检项目
        $qualitySettings = [];
        if (isset($param['quality_settings']) && is_array($param['quality_settings'])) {
            // 去除空值并重新索引
            $qualitySettings = array_values(array_filter($param['quality_settings']));
        }
        $param['quality_settings'] = json_encode($qualitySettings);

        // 处理物料图片
        if (isset($param['material_images']) && is_array($param['material_images'])) {
            $param['material_images'] = json_encode($param['material_images']);
        } else {
            $param['material_images'] = json_encode([]);
        }

        // 处理附件
        if (isset($param['attachments']) && is_array($param['attachments'])) {
            $param['attachments'] = json_encode($param['attachments']);
        } else {
            $param['attachments'] = json_encode([]);
        }

        // 处理复选框字段
        $param['quality_management'] = isset($param['quality_management']) ? 1 : 0;
        $param['quality_exempt'] = isset($param['quality_exempt']) ? 1 : 0;

        // 处理数值字段的默认值
        $numericFields = [
            'default_warehouse', 'min_order_qty', 'min_package_qty',
            'reference_cost', 'sales_price', 'min_sales_price', 'max_sales_price',
            'purchase_cycle', 'stock', 'source_type', 'type'
        ];

        foreach ($numericFields as $field) {
            if (!isset($param[$field]) || $param[$field] === '') {
                $param[$field] = 0;
            }
        }

        // 处理字符串字段的默认值
        $stringFields = [
            'material_level', 'material_source', 'category', 'model', 'color',
            'remark', 'material_drawing', 'barcode', 'brand', 'producer', 'description',
            'specs', 'unit', 'title', 'material_code'
        ];

        foreach ($stringFields as $field) {
            if (!isset($param[$field])) {
                $param[$field] = '';
            }
        }

        // 确保必需字段有值
        if (empty($param['cate_id'])) {
            $param['cate_id'] = 0;
        }

        // 移除不需要的字段
        unset($param['use_system_code'], $param['file']);
    }

    /**
     * 生成物料编号
     */
    private function generateMaterialCode()
    {
        $prefix = 'WL'; // 物料前缀
        $date = date('Ymd'); // 当前日期

        // 查询当天最大的编号
        $maxCode = Db::name('product')
            ->where('material_code', 'like', $prefix . $date . '%')
            ->max('material_code');

        if ($maxCode) {
            // 提取序号部分
            $sequence = intval(substr($maxCode, -4)) + 1;
        } else {
            $sequence = 1;
        }

        // 生成新编号：WL + 日期 + 4位序号
        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 删除物料档案（别名方法）
     */
    public function deletes()
    {
        return $this->batchDelete();
    }

    /**
     * 获取产品的BOM列表
     */
    public function getBomList()
    {
        $param = get_params();
        $product_id = $param['product_id'] ?? 0;

        if ($product_id <= 0) {
            return json(['code' => 1, 'msg' => '产品ID不能为空', 'data' => []]);
        }

        try {
            // 获取该产品的BOM列表
            $bomList = Db::name('material_bom')
                ->where('product_id', $product_id)
                ->where('delete_time', 0)
                ->field('id, bom_code, bom_name, version, status, create_time')
                ->order('create_time desc')
                ->select()
                ->toArray();

            // 格式化时间
            foreach ($bomList as &$bom) {
                $bom['create_time_formatted'] = date('Y-m-d H:i:s', $bom['create_time']);
            }

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $bomList]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 获取工艺模板列表
     */
    public function getProcessTemplates()
    {
        $param = get_params();
        $where = [];
        $where[] = ['delete_time', '=', 0];

        // 搜索条件
        if (!empty($param['keywords'])) {
            $where[] = ['name|template_no', 'like', '%' . $param['keywords'] . '%'];
        }

        try {
            // 从工艺路线模板表获取
            $templates = Db::name('process_template')
                ->where($where)
                ->field('id, template_no, name, remark, create_time, steps')
                ->order('create_time desc')
                ->select()
                ->toArray();

            // 处理数据，解析工序信息
            foreach ($templates as &$template) {
                $template['create_time_format'] = date('Y-m-d H:i:s', $template['create_time']);

                // 解析工艺步骤数量
                $stepCount = 0;
                if (!empty($template['steps'])) {
                    $steps = json_decode($template['steps'], true);
                    if (is_array($steps)) {
                        $stepCount = count($steps);
                    }
                }
                $template['step_count'] = $stepCount;
                $template['step_count_text'] = $stepCount > 0 ? $stepCount . '个工序' : '暂无工序';
            }

            return json(['code' => 0, 'msg' => '获取成功', 'data' => $templates]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage(), 'data' => []]);
        }
    }

    /**
     * 获取工艺步骤
     */
    public function getProcessSteps()
    {
        $param = get_params();
        $template_id = $param['template_id'] ?? 0;

        if ($template_id <= 0) {
            return json(['code' => 1, 'msg' => '工艺模板ID不能为空', 'data' => []]);
        }

        try {
            // 首先尝试从工艺路线模板表获取
            $template = Db::name('process_template')
                ->where('id', $template_id)
                ->where('delete_time', 0)
                ->find();

            if ($template && !empty($template['steps'])) {
                // 解析JSON格式的工序数据
                $steps = json_decode($template['steps'], true);
                if (is_array($steps)) {
                    // 格式化工序数据
                    $processSteps = [];
                    foreach ($steps as $index => $step) {
                        $processSteps[] = [
                            'name' => $step['name'] ?? '工序' . ($index + 1),
                            'price' => $step['price'] ?? '-',
                            'defect_items' => $step['defect_items'] ?? '-',
                            'type' => $step['type'] ?? '数据记录'
                        ];
                    }
                    return json(['code' => 0, 'msg' => '获取成功', 'data' => $processSteps]);
                }
            }

            // 如果工艺路线模板表没有数据，尝试从工程工艺表获取
            $engineeringProcess = Db::name('engineering_process')
                ->where('id', $template_id)
                ->where('delete_time', 0)
                ->find();

            if ($engineeringProcess && !empty($engineeringProcess['steps'])) {
                $steps = json_decode($engineeringProcess['steps'], true);
                if (is_array($steps)) {
                    $processSteps = [];
                    foreach ($steps as $index => $step) {
                        $processSteps[] = [
                            'name' => $step['name'] ?? '工序' . ($index + 1),
                            'price' => $step['price'] ?? '-',
                            'defect_items' => $step['defect_items'] ?? '-',
                            'type' => $step['type'] ?? '数据记录'
                        ];
                    }
                    return json(['code' => 0, 'msg' => '获取成功', 'data' => $processSteps]);
                }
            }

            return json(['code' => 0, 'msg' => '该工艺暂无工序数据', 'data' => []]);
        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage(), 'data' => []]);
        }
    }
}
