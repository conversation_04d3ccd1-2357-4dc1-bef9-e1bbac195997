<?php
namespace app\api\model;

use think\Model;

class Inventory extends Model {
    // 添加以下方法到现有库存模型中
    
    /**
     * 计算可用数量，考虑预占
     * 
     * @return float
     */
    public function getAvailableQuantity() {
        return $this->quantity - $this->locked_quantity - $this->reserved_quantity;
    }
    
    /**
     * 更新预占数量
     * 
     * @param float $amount 变动数量(正数增加，负数减少)
     * @return bool
     */
    public function updateReservedQuantity($amount) {
        $this->reserved_quantity += $amount;
        return $this->save();
    }
    
    /**
     * 检查是否有足够的可用数量
     * 
     * @param float $quantity 所需数量
     * @return bool
     */
    public function hasEnoughAvailable($quantity) {
        return $this->getAvailableQuantity() >= $quantity;
    }
    
    /**
     * 关联预占记录
     */
    public function reserves() {
        return $this->hasMany(InventoryReserve::class, 'inventory_id');
    }
    
    /**
     * 关联活跃的预占记录(状态为1=已预占)
     */
    public function activeReserves() {
        return $this->hasMany(InventoryReserve::class, 'inventory_id')
            ->where('status', 1);
    }
}
