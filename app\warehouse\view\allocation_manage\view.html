{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    .detail-card { margin-bottom: 15px; }
    .detail-item { margin-bottom: 10px; }
    .detail-label { display: inline-block; width: 120px; font-weight: bold; }
    .status-badge { padding: 2px 8px; border-radius: 3px; color: white; font-size: 12px; }
    .status-pending { background-color: #ff9800; }
    .status-partial { background-color: #2196f3; }
    .status-completed { background-color: #4caf50; }
    .status-cancelled { background-color: #f44336; }
    .history-item { border-left: 3px solid #e0e0e0; padding-left: 15px; margin-bottom: 15px; }
    .history-time { color: #666; font-size: 12px; }
</style>
{/block}

{block name="body"}
<div class="p-page">
    <!-- 基本信息 -->
    <div class="layui-card detail-card">
        <div class="layui-card-header">
            <h3>分配需求详情</h3>
        </div>
        <div class="layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-md6">
                        <div class="detail-item">
                            <span class="detail-label">需求ID：</span>
                            <span>{$allocation_request.id}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">产品名称：</span>
                            <span>{$allocation_request.product.title ?? '未知产品'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">产品编码：</span>
                            <span>{$allocation_request.product.material_code ?? '无'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">仓库：</span>
                            <span>{$allocation_request.warehouse.name ?? '未知仓库'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">需求数量：</span>
                            <span>{$allocation_request.quantity} {$allocation_request.product.unit ?? '件'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">已分配数量：</span>
                            <span>{$allocation_request.allocated_quantity} {$allocation_request.product.unit ?? '件'}</span>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="detail-item">
                            <span class="detail-label">状态：</span>
                            {switch name="allocation_request.status"}
                                {case value="1"}<span class="status-badge status-pending">待分配</span>{/case}
                                {case value="2"}<span class="status-badge status-partial">部分分配</span>{/case}
                                {case value="3"}<span class="status-badge status-completed">已完成</span>{/case}
                                {case value="4"}<span class="status-badge status-cancelled">已取消</span>{/case}
                                {default /}<span class="status-badge">未知状态</span>
                            {/switch}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">优先级：</span>
                            <span>
                                {switch name="allocation_request.priority"}
                                    {case value="1"}低{/case}
                                    {case value="2"}中{/case}
                                    {case value="3"}高{/case}
                                    {case value="4"}紧急{/case}
                                    {default /}普通
                                {/switch}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">关联类型：</span>
                            <span>{$allocation_request.ref_type|default='无'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">关联单号：</span>
                            <span>{$allocation_request.ref_no|default='无'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建人：</span>
                            <span>{$allocation_request.creator.name|default='未知'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">创建时间：</span>
                            <span>{$allocation_request.create_time|date='Y-m-d H:i:s'}</span>
                        </div>
                    </div>
                </div>
                
                {if condition="$allocation_request.notes"}
                <div class="detail-item">
                    <span class="detail-label">备注：</span>
                    <span>{$allocation_request.notes}</span>
                </div>
                {/if}
            </div>
        </div>

        <!-- 当前库存状态 -->
        <div class="layui-card detail-card">
            <div class="layui-card-header">
                <h3>当前库存状态</h3>
            </div>
            <div class="layui-card-body">
                {if condition="$current_inventory"}
                <div class="layui-row">
                    <div class="layui-col-md4">
                        <div class="detail-item">
                            <span class="detail-label">可用库存：</span>
                            <span style="color: #4caf50; font-weight: bold;">{$current_inventory.quantity} {$allocation_request.product.unit|default='件'}</span>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="detail-item">
                            <span class="detail-label">锁定库存：</span>
                            <span style="color: #ff9800; font-weight: bold;">{$current_inventory.locked_quantity|default=0} {$allocation_request.product.unit|default='件'}</span>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <div class="detail-item">
                            <span class="detail-label">更新时间：</span>
                            <span>{$current_inventory.update_time|date='Y-m-d H:i:s'}</span>
                        </div>
                    </div>
                </div>
                {else /}
                <div class="layui-empty">
                    <div class="layui-empty-icon">📦</div>
                    <p class="layui-empty-text">暂无库存记录</p>
                </div>
                {/if}
            </div>
        </div>

        <!-- 分配历史 -->
        <div class="layui-card detail-card">
            <div class="layui-card-header">
                <h3>分配历史</h3>
            </div>
            <div class="layui-card-body">
                {if condition="$allocation_history && count($allocation_history) > 0"}
                {volist name="allocation_history" id="history"}
                <div class="history-item">
                    <div style="margin-bottom: 5px;">
                        <strong>分配数量：</strong>{$history.allocated_quantity} {$allocation_request.product.unit|default='件'}
                        <span style="margin-left: 20px;"><strong>操作人：</strong>{$history.creator.name|default='未知'}</span>
                        <span class="history-time" style="float: right;">{$history.create_time|date='Y-m-d H:i:s'}</span>
                    </div>
                    {if condition="$history.notes"}
                    <div style="color: #666; font-size: 14px;">
                        <strong>备注：</strong>{$history.notes}
                    </div>
                    {/if}
                </div>
                {/volist}
                {else /}
                <div class="layui-empty">
                    <div class="layui-empty-icon">📋</div>
                    <p class="layui-empty-text">暂无分配历史</p>
                </div>
                {/if}
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="layui-card detail-card">
            <div class="layui-card-body" style="text-align: center;">
                {if condition="$allocation_request.status == 1 || $allocation_request.status == 2"}
                <button type="button" class="layui-btn" onclick="allocateStock()">
                    <i class="layui-icon layui-icon-ok"></i> 分配库存
                </button>
                {/if}
                
                {if condition="$allocation_request.status == 1 || $allocation_request.status == 2"}
                <button type="button" class="layui-btn layui-btn-danger" onclick="cancelRequest()">
                    <i class="layui-icon layui-icon-close"></i> 取消需求
                </button>
                {/if}
                
                <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">
                    <i class="layui-icon layui-icon-return"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
    const moduleInit = ['layer'];
    function gouguInit() {
        var layer = layui.layer;
            
            // 分配库存
            window.allocateStock = function() {
                layer.prompt({
                    title: '分配库存',
                    formType: 0,
                    value: '',
                    area: ['300px', '150px']
                }, function(value, index){
                    if (!value || isNaN(value) || parseFloat(value) <= 0) {
                        layer.msg('请输入有效的分配数量');
                        return;
                    }
                    
                    // 发送分配请求
                    layer.load();
                    $.post('/warehouse/allocation_manage/allocate', {
                        id: '{$allocation_request.id}',
                        quantity: value
                    }, function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg('分配成功', {icon: 1}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg || '分配失败', {icon: 2});
                        }
                    }, 'json');
                    
                    layer.close(index);
                });
            };
            
            // 取消需求
            window.cancelRequest = function() {
                layer.prompt({
                    title: '取消原因',
                    formType: 2,
                    area: ['400px', '200px']
                }, function(value, index){
                    layer.load();
                    $.post('/warehouse/allocation_manage/cancel', {
                        id: '{$allocation_request.id}',
                        reason: value
                    }, function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg('取消成功', {icon: 1}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg(res.msg || '取消失败', {icon: 2});
                        }
                    }, 'json');
                    
                    layer.close(index);
                });
            };
    }
</script>
{/block}
