<?php

namespace app\engineering\model;
use think\Model;
use think\facade\Db;

class Product extends Model
{
    // 在模型中
protected function getCreateTimeAttr($value)
{
    return $value ? date('Y-m-d H:i:s', $value) : '';
}

// 获取器：code字段 (返回material_code的值)
protected function getCodeAttr($value, $data)
{
    return isset($data['material_code']) ? $data['material_code'] : '';
}

// 获取器：name字段 (返回title的值)
protected function getNameAttr($value, $data)
{
    return isset($data['title']) ? $data['title'] : '';
}

// 获取器：spec字段 (返回specs的值)
protected function getSpecAttr($value, $data)
{
    return isset($data['specs']) ? $data['specs'] : '';
}
    /**
     * 获取分页列表
     * @param $where
     * @param $param
     */
    public function datalist($where, $param)
    {
        $rows = empty($param['limit']) ? get_config('app.page_size') : $param['limit'];
        $order = empty($param['order']) ? 'p.id desc' : $param['order'];
        try {
            $list = Db::name('product')
                ->field('p.*, pc.title as cate')
                ->alias('p')
                ->leftJoin('product_cate pc', 'pc.id = p.cate_id')
                ->where($where)
                ->order($order)
                ->paginate(['list_rows' => $rows])
                ->each(function ($item, $key) {
                    $item['admin_name'] = Db::name('Admin')->where('id', $item['admin_id'])->value('name');

                    // 统计产品关联的有效BOM数量
                    $item['has_bom'] = has_product_bom($item['id']) ? 1 : 0;

                    // 查询库存信息
                    $inventoryInfo = $this->getProductInventoryInfo($item['id']);
                    $item['stock'] = $inventoryInfo['total_stock']; // 库存数量
                    $item['locked_stock'] = $inventoryInfo['locked_stock']; // 锁定库存数
                    $item['pending_allocation'] = $inventoryInfo['pending_allocation']; // 等待分配数
                    $item['available_stock'] = $inventoryInfo['available_stock']; // 可用库存

                    return $item;
                });

            return $list;
        } catch (\Exception $e) {
            // 记录错误日志
            trace('Product datalist error: ' . $e->getMessage(), 'error');
            return [];
        }
    }

    /**
     * 获取产品库存信息
     * @param int $productId 产品ID
     * @return array
     */
    private function getProductInventoryInfo($productId)
    {
        try {
            // 先从实时库存表查询
            $inventory = Db::name('inventory_realtime')
                ->where('product_id', $productId)
                ->field('
                    SUM(quantity) as total_stock,
                    SUM(locked_quantity) as locked_stock,
                    SUM(available_quantity) as available_stock
                ')
                ->find();

            // 查询等待分配数量（从分配需求表）
            // 注意：表名可能有前缀，需要检查实际表名
            $pendingAllocation = Db::name('inventory_allocation_request')
                ->where('product_id', $productId)
                ->where('status', 'in', [1, 2]) // 1=待分配，2=部分分配
                ->field('SUM(quantity - allocated_quantity) as pending_qty')
                ->find();

            $pending_allocation = (int)($pendingAllocation['pending_qty'] ?? 0);

            // 返回库存信息（包括等待分配数）
            return [
                'total_stock' => (int)($inventory['total_stock'] ?? 0),
                'locked_stock' => (int)($inventory['locked_stock'] ?? 0),
                'pending_allocation' => $pending_allocation,
                'available_stock' => (int)($inventory['available_stock'] ?? 0)
            ];

        } catch (\Exception $e) {
            trace('获取产品库存信息失败: ' . $e->getMessage(), 'error');
        }

        // 默认返回0
        return [
            'total_stock' => 0,
            'locked_stock' => 0,
            'pending_allocation' => 0,
            'available_stock' => 0
        ];
    }

    /**
     * 添加数据
     * @param $param
     */
    public function add($param)
    {
        $insertId = 0;
        try {
            $param['create_time'] = time();
            $insertId = self::strict(false)->field(true)->insertGetId($param);
            add_log('add', $insertId, $param);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign(0, '操作成功', ['aid' => $insertId]);
    }

    /**
     * 编辑信息
     * @param $param
     */
    public function edit($param)
    {
        try {
            $param['update_time'] = time();
            self::where('id', $param['id'])->strict(false)->field(true)->update($param);
            add_log('edit', $param['id'], $param);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign();
    }    

    /**
     * 根据id获取信息
     * @param $id
     */
    public function getById($id)
    {
        $info = self::find($id);
        return $info;
    }

    /**
     * 删除信息
     * @param $id
     * @param $type
     * @return array
     */
    public function delById($id, $type = 0)
    {
        try {
            self::where('id', $id)->update(['delete_time' => time()]);
            add_log('delete', $id);
        } catch(\Exception $e) {
            return to_assign(1, '操作失败，原因：'.$e->getMessage());
        }
        return to_assign();
    }
} 