<?php
declare(strict_types = 1);

namespace app\api\controller;

use app\api\BaseController;
use think\facade\Db;

/**
 * 仓库API控制器
 */
class Warehouse extends BaseController
{
    /**
     * 获取仓库列表
     * 
     * @return \think\Response
     */
    public function getList()
    {
        // 获取参数
        $param = get_params();
        
        // 构建查询条件
        $where = [];
        
        // 按关键词搜索（编码、名称、地址）
        if (!empty($param['keywords'])) {
            $where[] = ['code|name|address', 'like', '%' . $param['keywords'] . '%'];
        }
        
        // 按状态搜索
        if (isset($param['status']) && $param['status'] !== '') {
            $where[] = ['status', '=', intval($param['status'])];
        }
        
        // 按类型搜索
        if (isset($param['type']) && $param['type'] !== '') {
            $where[] = ['type', '=', intval($param['type'])];
        }
        
        // 执行查询
        $list = Db::name('warehouse')
            ->where($where)
            ->field('id, code, name, type, contact, phone, address, 
                    is_default, status, notes, create_time')
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? $param['limit'] : 15,
                'page' => isset($param['page']) ? $param['page'] : 1,
            ]);
        
        $data = $list->toArray();
        
        // 处理数据
        $typeArr = [
            1 => '普通仓库',
            2 => '退货仓库',
            3 => '虚拟仓库'
        ];
        
        foreach ($data['data'] as &$item) {
            $item['type_text'] = isset($typeArr[$item['type']]) ? $typeArr[$item['type']] : '未知';
            $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
            $item['is_default_text'] = $item['is_default'] == 1 ? '是' : '否';
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'count' => $data['total'],
            'data' => $data['data']
        ]);
    }
    
    /**
     * 获取仓库详情
     * 
     * @return \think\Response
     */
    public function getDetail()
    {
        // 获取参数
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '参数错误',
                'data' => []
            ]);
        }
        
        // 查询仓库信息
        $warehouse = Db::name('warehouse')
            ->field('id, code, name, type, contact, phone, address, 
                    is_default, status, notes, create_time, created_by')
            ->where('id', $id)
            ->find();
        
        if (!$warehouse) {
            return json([
                'code' => 1,
                'msg' => '仓库不存在',
                'data' => []
            ]);
        }
        
        // 处理数据
        $typeArr = [
            1 => '普通仓库',
            2 => '退货仓库',
            3 => '虚拟仓库'
        ];
        
        $warehouse['type_text'] = isset($typeArr[$warehouse['type']]) ? $typeArr[$warehouse['type']] : '未知';
        $warehouse['status_text'] = $warehouse['status'] == 1 ? '启用' : '禁用';
        $warehouse['is_default_text'] = $warehouse['is_default'] == 1 ? '是' : '否';
        $warehouse['create_time'] = date('Y-m-d H:i:s', $warehouse['create_time']);
        
        // 获取创建人信息
        if (!empty($warehouse['created_by'])) {
            $creator = Db::name('admin')
                ->field('id, name')
                ->where('id', $warehouse['created_by'])
                ->find();
            $warehouse['creator'] = $creator ? $creator : ['name' => '未知'];
        } else {
            $warehouse['creator'] = ['name' => '未知'];
        }
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $warehouse
        ]);
    }
    
    /**
     * 新增仓库
     * 
     * @return \think\Response
     */
    public function add()
    {
        // 获取参数
        $param = get_params();
        
        // 验证必填参数
        if (empty($param['code'])) {
            return json([
                'code' => 1,
                'msg' => '仓库编码不能为空',
                'data' => []
            ]);
        }
        
        if (empty($param['name'])) {
            return json([
                'code' => 1,
                'msg' => '仓库名称不能为空',
                'data' => []
            ]);
        }
        
        // 检查编码是否已存在
        $exists = Db::name('warehouse')->where('code', $param['code'])->find();
        if ($exists) {
            return json([
                'code' => 1,
                'msg' => '仓库编码已存在',
                'data' => []
            ]);
        }
        
        // 如果设置为默认仓库，先取消其他默认仓库
        if (isset($param['is_default']) && $param['is_default'] == 1) {
            Db::name('warehouse')->where('is_default', 1)->update(['is_default' => 0]);
        }
        
        // 设置默认值
        $data = [
            'code' => $param['code'],
            'name' => $param['name'],
            'type' => isset($param['type']) ? intval($param['type']) : 1,
            'contact' => isset($param['contact']) ? $param['contact'] : '',
            'phone' => isset($param['phone']) ? $param['phone'] : '',
            'address' => isset($param['address']) ? $param['address'] : '',
            'is_default' => isset($param['is_default']) ? intval($param['is_default']) : 0,
            'status' => isset($param['status']) ? intval($param['status']) : 1,
            'notes' => isset($param['notes']) ? $param['notes'] : '',
            'created_by' => get_login_admin_id(), // 获取当前登录用户ID
            'create_time' => time(),
            'update_time' => time()
        ];
        
        // 写入数据库
        $id = Db::name('warehouse')->insertGetId($data);
        
        if ($id > 0) {
            return json([
                'code' => 0,
                'msg' => '新增成功',
                'data' => ['id' => $id]
            ]);
        } else {
            return json([
                'code' => 1,
                'msg' => '新增失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 更新仓库
     * 
     * @return \think\Response
     */
    public function update()
    {
        // 获取参数
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '参数错误',
                'data' => []
            ]);
        }
        
        // 检查仓库是否存在
        $warehouse = Db::name('warehouse')->where('id', $id)->find();
        if (!$warehouse) {
            return json([
                'code' => 1,
                'msg' => '仓库不存在',
                'data' => []
            ]);
        }
        
        // 检查编码是否已存在（排除自身）
        if (!empty($param['code'])) {
            $exists = Db::name('warehouse')
                ->where('code', $param['code'])
                ->where('id', '<>', $id)
                ->find();
            if ($exists) {
                return json([
                    'code' => 1,
                    'msg' => '仓库编码已存在',
                    'data' => []
                ]);
            }
        }
        
        // 如果设置为默认仓库，先取消其他默认仓库
        if (isset($param['is_default']) && $param['is_default'] == 1) {
            Db::name('warehouse')->where('is_default', 1)->update(['is_default' => 0]);
        }
        
        // 更新数据
        $data = [];
        
        if (isset($param['code'])) $data['code'] = $param['code'];
        if (isset($param['name'])) $data['name'] = $param['name'];
        if (isset($param['type'])) $data['type'] = intval($param['type']);
        if (isset($param['contact'])) $data['contact'] = $param['contact'];
        if (isset($param['phone'])) $data['phone'] = $param['phone'];
        if (isset($param['address'])) $data['address'] = $param['address'];
        if (isset($param['is_default'])) $data['is_default'] = intval($param['is_default']);
        if (isset($param['status'])) $data['status'] = intval($param['status']);
        if (isset($param['notes'])) $data['notes'] = $param['notes'];
        
        $data['update_time'] = time();
        
        // 写入数据库
        $result = Db::name('warehouse')->where('id', $id)->update($data);
        
        if ($result !== false) {
            return json([
                'code' => 0,
                'msg' => '更新成功',
                'data' => []
            ]);
        } else {
            return json([
                'code' => 1,
                'msg' => '更新失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 删除仓库
     * 
     * @return \think\Response
     */
    public function delete()
    {
        // 获取参数
        $param = get_params();
        $id = isset($param['id']) ? intval($param['id']) : 0;
        
        if ($id <= 0) {
            return json([
                'code' => 1,
                'msg' => '参数错误',
                'data' => []
            ]);
        }
        
        // 检查仓库是否存在
        $warehouse = Db::name('warehouse')->where('id', $id)->find();
        if (!$warehouse) {
            return json([
                'code' => 1,
                'msg' => '仓库不存在',
                'data' => []
            ]);
        }
        
        // 检查是否有关联的库存
        $inventory = Db::name('inventory')->where('warehouse_id', $id)->find();
        if ($inventory) {
            return json([
                'code' => 1,
                'msg' => '该仓库存在库存记录，不能删除',
                'data' => []
            ]);
        }
        
        // 检查是否有关联的采购订单或入库单
        $order = Db::name('purchase_order')->where('warehouse_id', $id)->find();
        if ($order) {
            return json([
                'code' => 1,
                'msg' => '该仓库已关联采购订单，不能删除',
                'data' => []
            ]);
        }
        
        $receipt = Db::name('purchase_receipt')->where('warehouse_id', $id)->find();
        if ($receipt) {
            return json([
                'code' => 1,
                'msg' => '该仓库已关联入库单，不能删除',
                'data' => []
            ]);
        }
        
        // 删除仓库
        $result = Db::name('warehouse')->where('id', $id)->delete();
        
        if ($result) {
            return json([
                'code' => 0,
                'msg' => '删除成功',
                'data' => []
            ]);
        } else {
            return json([
                'code' => 1,
                'msg' => '删除失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 更新仓库状态
     * 
     * @return \think\Response
     */
    public function updateStatus()
    {
        // 获取参数
        $param = get_params();
        
        // 验证必填参数
        if (empty($param['id'])) {
            return json([
                'code' => 1,
                'msg' => '仓库ID不能为空',
                'data' => []
            ]);
        }
        
        if (!isset($param['status']) || !in_array($param['status'], [0, 1])) {
            return json([
                'code' => 1,
                'msg' => '状态参数错误',
                'data' => []
            ]);
        }
        
        // 查询仓库是否存在
        $warehouse = Db::name('warehouse')->where('id', $param['id'])->find();
        if (!$warehouse) {
            return json([
                'code' => 1,
                'msg' => '仓库不存在',
                'data' => []
            ]);
        }
        
        // 更新状态
        $result = Db::name('warehouse')
            ->where('id', $param['id'])
            ->update([
                'status' => $param['status'],
                'update_time' => time()
            ]);
        
        if ($result !== false) {
            return json([
                'code' => 0,
                'msg' => '状态更新成功',
                'data' => []
            ]);
        } else {
            return json([
                'code' => 1,
                'msg' => '状态更新失败',
                'data' => []
            ]);
        }
    }
    
    /**
     * 获取所有启用的仓库（用于下拉选择）
     * 
     * @return \think\Response
     */
    public function getOptions()
    {
        // 查询所有启用的仓库
        $warehouses = Db::name('warehouse')
            ->where('status', 1)
            ->field('id, code, name')
            ->order('is_default', 'desc')
            ->order('create_time', 'asc')
            ->select()
            ->toArray();
        
        return json([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $warehouses
        ]);
    }
} 