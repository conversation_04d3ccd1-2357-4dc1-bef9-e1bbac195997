<?php
declare (strict_types = 1);

namespace app\Produce\controller;

use app\base\BaseController;
use app\Produce\model\ProcessModel;
use app\Produce\validate\ProcessValidate;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;

class Process extends BaseController
{
    /**
     * 工序管理首页
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();
            $where = [];
            if (!empty($param['keywords'])) {
                $where[] = ['name|code', 'like', '%' . $param['keywords'] . '%'];
            }
            if (!empty($param['group_id'])) {
                $where[] = ['group_id', '=', $param['group_id']];
            }
            if (!empty($param['pricing_method'])) {
                $where[] = ['pricing_method', '=', $param['pricing_method']];
            }
            
            $list = ProcessModel::where($where)
                ->field('*')
                ->order('create_time desc')
                ->paginate([
                    'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 20,
                    'page' => isset($param['page']) ? (int)$param['page'] : 1,
                    'query' => $param
                ])
                ->each(function ($item) {
                    $item['pricing_method_name'] = $item['pricing_method'] == 1 ? '按件计价' : '按时计价';
                    $item['group_name'] = Db::name('process_group')->where('id', $item['group_id'])->value('name') ?: '';
                    $item['create_time'] = date('Y-m-d H:i:s', (int)$item['create_time']);
                });
            
            return table_assign(0, '', $list);
        } else {
            return view();
        }
    }

    /**
     * 添加/编辑工序
     */
    public function add()
    {
        $param = get_params();
        if (request()->isPost()) {
            if (!empty($param['id']) && $param['id'] > 0) {
                try {
                    validate(ProcessValidate::class)->scene('edit')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }
                
                // 处理报工用户字段
                if (isset($param['report_user_names'])) {
                    unset($param['report_user_names']); // 移除显示名称字段，只保存ID
                }
                
                $param['update_time'] = time();
                $res = ProcessModel::where('id', $param['id'])->strict(false)->field(true)->update($param);
                if ($res) {
                    add_log('edit', $param['id'], $param);
                    return to_assign(0, "工序编辑成功");
                } else {
                    return to_assign(1, "工序编辑失败");
                }
            } else {
                try {
                    validate(ProcessValidate::class)->scene('add')->check($param);
                } catch (ValidateException $e) {
                    return to_assign(1, $e->getError());
                }
                
                // 如果没有提供工序编号，自动生成
                if (empty($param['code'])) {
                    $param['code'] = $this->generateProcessCode();
                } else {
                    // 检查工序编号是否重复
                    $check = ProcessModel::where('code', $param['code'])->find();
                    if ($check) {
                        return to_assign(1, "工序编号已存在");
                    }
                    // 验证工序编号格式
                    if (strlen($param['code']) > 50) {
                        return to_assign(1, "工序编号不能超过50个字符");
                    }
                }
                
                // 处理报工用户字段
                if (isset($param['report_user_names'])) {
                    unset($param['report_user_names']); // 移除显示名称字段，只保存ID
                }
                
                $param['admin_id'] = $this->uid;
                $param['create_time'] = time();
                $param['update_time'] = time();
                $insertId = ProcessModel::strict(false)->field(true)->insertGetId($param);
                if ($insertId) {
                    add_log('add', $insertId, $param);
                    return to_assign(0, "工序添加成功", ['aid' => $insertId]);
                } else {
                    return to_assign(1, "工序添加失败");
                }
            }
        } else {
            $id = isset($param['id']) ? $param['id'] : 0;
            $detail = [];
            if ($id > 0) {
                $detail = ProcessModel::where('id', $id)->find();
                if (empty($detail)) {
                    throw new \think\exception\HttpException(404, '找不到信息');
                }
                $detail = $detail->toArray();
                
                // 处理报工用户显示名称
                if (!empty($detail['report_user'])) {
                    $userIds = explode(',', $detail['report_user']);
                    $userNames = [];
                    foreach($userIds as $userId) {
                        if($userId) {
                            $user = Db::name('admin')->where('id', $userId)->field('name,nickname')->find();
                            if($user) {
                                $userNames[] = $user['name'] . ($user['nickname'] ? '(' . $user['nickname'] . ')' : '');
                            }
                        }
                    }
                    $detail['report_user_names'] = implode(',', $userNames);
                } else {
                    $detail['report_user_names'] = '';
                }
                
                View::assign('detail', $detail);
            }
            
            // 获取工序组列表
            $groups = Db::name('process_group')->field('id,name')->order('id asc')->select()->toArray();
            View::assign('groups', $groups);
            
            View::assign('id', $id);
            return view();
        }
    }

    /**
     * 查看工序详情
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        $detail = ProcessModel::where('id', $id)->find();
        if (empty($detail)) {
            throw new \think\exception\HttpException(404, '找不到信息');
        }
        $detail = $detail->toArray();
        $detail['pricing_method_name'] = $detail['pricing_method'] == 1 ? '按件计价' : '按时计价';
        $detail['group_name'] = Db::name('process_group')->where('id', $detail['group_id'])->value('name') ?: '';
        $detail['create_time'] = date('Y-m-d H:i:s', (int)$detail['create_time']);
        $detail['update_time'] = date('Y-m-d H:i:s', (int)$detail['update_time']);
        
        // 处理报工用户信息
        if (!empty($detail['report_user'])) {
            $userIds = explode(',', $detail['report_user']);
            $userNames = [];
            foreach($userIds as $userId) {
                if($userId) {
                    $user = Db::name('admin')->where('id', $userId)->field('name,nickname')->find();
                    if($user) {
                        $userNames[] = $user['name'] . ($user['nickname'] ? '(' . $user['nickname'] . ')' : '');
                    }
                }
            }
            $detail['report_user_names'] = implode('、', $userNames);
        } else {
            $detail['report_user_names'] = '未指定';
        }
        
        View::assign('detail', $detail);
        return view();
    }

    /**
     * 删除工序
     */
    public function delete()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;
        if ($id > 0) {
            // 检查是否有关联的生产订单使用此工序
            $check = Db::name('order_process')->where('process_id', $id)->find();
            if ($check) {
                return to_assign(1, "该工序已被生产订单使用，无法删除");
            }
            
            $res = ProcessModel::destroy($id);
            if ($res) {
                add_log('delete', $id);
                return to_assign(0, "删除成功");
            } else {
                return to_assign(1, "删除失败");
            }
        } else {
            return to_assign(1, "参数错误");
        }
    }

    /**
     * 获取工序列表（用于下拉选择）
     */
    public function getProcessList()
    {
        $list = ProcessModel::field('id,name')
            ->order('id asc')
            ->select();
        
        return json(['code' => 0, 'data' => $list]);
    }
    
    /**
     * 根据产品获取相关工序
     */
    public function getProcessByProduct()
    {
        $param = get_params();
        $productId = $param['product_id'] ?? 0;
        
        if ($productId == 0) {
            // 返回所有工序
            $list = ProcessModel::field('id,name')
                ->order('id asc')
                ->select();
        } else {
            // 根据产品获取相关工序（这里可以根据实际业务逻辑调整）
            // 暂时返回所有工序，后期可以根据产品-工序关联表来筛选
            $list = ProcessModel::field('id,name')
                ->order('id asc')
                ->select();
        }
        
        return json(['code' => 0, 'data' => $list]);
    }

    /**
     * 获取工作组列表
     */
    public function getGroupList()
    {
        $list = Db::name('process_group')
            ->field('id,name')
            ->order('id asc')
            ->select()
            ->toArray();
            
        return to_assign(0, '', $list);
    }

    /**
     * 获取用户列表（用于选择报工用户）
     */
    public function getUserList()
    {
        $param = get_params();
        $where = [['status', '=', 1]];
        
        if (!empty($param['keywords'])) {
            $where[] = ['name|nickname|mobile', 'like', '%' . $param['keywords'] . '%'];
        }
        
        $list = Db::name('admin')
            ->where($where)
            ->field('id,name,nickname,mobile,department_id')
            ->order('id desc')
            ->paginate([
                'list_rows' => isset($param['limit']) ? (int)$param['limit'] : 20,
                'page' => isset($param['page']) ? (int)$param['page'] : 1,
                'query' => $param
            ])
            ->each(function ($item) {
                // 获取部门名称
                $item['department_name'] = Db::name('department')->where('id', $item['department_id'])->value('title') ?: '';
                $item['show_name'] = $item['name'] . ($item['nickname'] ? '(' . $item['nickname'] . ')' : '');
            });
        
        return table_assign(0, '', $list);
    }

    /**
     * 选择报工用户页面
     */
    public function selectUsers()
    {
        $param = get_params();
        $selected_ids = isset($param['selected_ids']) ? $param['selected_ids'] : '';
        
        View::assign('selected_ids', $selected_ids);
        return view('select_users');
    }

    /**
     * 根据用户ID获取用户信息
     */
    public function getUsersByIds()
    {
        $param = get_params();
        $ids = isset($param['ids']) ? $param['ids'] : '';
        
        if (empty($ids)) {
            return to_assign(0, '', []);
        }
        
        $idArray = explode(',', $ids);
        $idArray = array_filter($idArray);
        
        if (empty($idArray)) {
            return to_assign(0, '', []);
        }
        
        $list = Db::name('admin')
            ->whereIn('id', $idArray)
            ->where('status', 1)
            ->field('id,name,nickname,mobile,department_id')
            ->select()
            ->each(function ($item) {
                $item['department_name'] = Db::name('department')->where('id', $item['department_id'])->value('title') ?: '';
                $item['show_name'] = $item['name'] . ($item['nickname'] ? '(' . $item['nickname'] . ')' : '');
            })
            ->toArray();
        
        return to_assign(0, '', $list);
    }

    /**
     * 自动生成工序编号
     * 格式：GX + 3位递增数字
     */
    private function generateProcessCode()
    {
        $prefix = 'GX';
        
        // 查询最大编号
        $maxCode = ProcessModel::where('code', 'like', $prefix . '%')
            ->order('code', 'desc')
            ->value('code');
        
        if ($maxCode) {
            // 提取后3位数字并加1
            $serialNumber = intval(substr($maxCode, -3)) + 1;
        } else {
            // 第一个编号
            $serialNumber = 1;
        }
        
        // 使用 sprintf 格式化为3位数字
        return $prefix . sprintf('%03d', $serialNumber);
    }

    /**
     * 获取可用工序列表（用于选择添加）
     */
    public function getAvailableProcesses()
    {
        $param = get_params();
        $name = $param['name'] ?? '';

        try {
            $where = [];
            $where[] = ['delete_time', '=', 0];

            // 名称搜索
            if (!empty($name)) {
                $where[] = ['name', 'like', '%' . $name . '%'];
            }

            $processes = Db::name('produce_process')
                ->where($where)
                ->field('id, code, name, group_id, standard_price, efficiency, pricing_method, description')
                ->order('id asc')
                ->select()
                ->toArray();

            // 格式化数据，添加一些默认值以兼容前端
            foreach ($processes as &$process) {
                $process['type'] = '数据记录'; // 默认类型
                $process['processing_type'] = '自制'; // 默认加工类型
                $process['inspection_method'] = '免检'; // 默认检验方式
                $process['standard_time'] = 0; // 默认工时
            }

            return json(['code' => 0, 'data' => $processes]);

        } catch (\Exception $e) {
            return json(['code' => 1, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }


}